<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>summerfarm-wms</artifactId>
        <groupId>net.summerfarm</groupId>
        <version>0.0.1</version>
    </parent>

    <groupId>net.summerfarm</groupId>
    <artifactId>wms-facade</artifactId>
    <version>0.0.1</version>

    <dependencies>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-pms-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>wms-common</artifactId>
        </dependency>
        <dependency>
            <groupId>net.manage.client</groupId>
            <artifactId>manage-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>ofc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>tms-client</artifactId>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.apache.dubbo</groupId>-->
<!--            <artifactId>dubbo-registry-nacos</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-manage-client</artifactId>
        </dependency>


        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>erp-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>common-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-pms-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu.open</groupId>
            <artifactId>open-platform-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>summerfarm-inventory-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.summerfarm.wms</groupId>
            <artifactId>summerfarm-wms-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 外部 -->
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>jindie-sdk</artifactId>
        </dependency>

    </dependencies>
</project>
