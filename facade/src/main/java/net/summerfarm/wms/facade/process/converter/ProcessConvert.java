package net.summerfarm.wms.facade.process.converter;

import net.summerfarm.manage.client.ding.dto.*;
import net.summerfarm.wms.facade.process.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ProcessConvert {
    ProcessConvert INSTANCE = Mappers.getMapper(ProcessConvert.class);

    ProcessInstanceCreateDTO convert(ProcessCreateFacadeDTO processCreateFacadeDTO);

    DingdingFormDTO convert(ProcessFormFacadeDTO processFormFacadeDTO);

    ProcessFinishDTO convert(ProcessFinishFacadeDTO processFinishFacadeDTO);

    ProcessDetailDTO convert(ProcessDetailFacadeDTO processDetailFacadeDTO);

    ProcessDetailFacadeResDTO convert(ProcessDetailResDTO processDetailResDTO);
}
