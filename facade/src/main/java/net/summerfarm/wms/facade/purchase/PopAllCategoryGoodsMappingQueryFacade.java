package net.summerfarm.wms.facade.purchase;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.pms.client.provider.pop.PopAllCategoryGoodsMappingQueryProvider;
import net.summerfarm.pms.client.req.pop.PopAllCategoryGoodsMappingQueryReq;
import net.summerfarm.pms.client.resp.pop.PopAllCategoryGoodsMappingResp;
import net.summerfarm.wms.facade.purchase.dto.PopAllCategoryGoodsMappingFacadeDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description 
 * @Date 2025/8/29 14:01
 * @<AUTHOR>
 */
@Component
@Slf4j
public class PopAllCategoryGoodsMappingQueryFacade {

    @DubboReference
    private PopAllCategoryGoodsMappingQueryProvider goodsMappingQueryProvider;

    public List<PopAllCategoryGoodsMappingFacadeDTO> queryMappingList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        PopAllCategoryGoodsMappingQueryReq queryReq = PopAllCategoryGoodsMappingQueryReq.builder()
                .allCategorySkuList(skuList).build();
        DubboResponse<PopAllCategoryGoodsMappingResp> response = goodsMappingQueryProvider.queryByAllCategorySku(queryReq);
        if (Objects.isNull(response) || !response.isSuccess()) {
            log.error("查询pop全品类映射关系失败 skuList:{}，resp:{} \n", JSON.toJSONString(skuList), JSON.toJSONString(response));
            return Lists.newArrayList();
        }
        return response.getData().getMappingDTOList().stream()
                .map(mappingDTO -> PopAllCategoryGoodsMappingFacadeDTO.builder()
                        .popSku(mappingDTO.getPopSku())
                        .allCategorySku(mappingDTO.getAllCategorySku())
                        .build()).collect(Collectors.toList());


    }


}
