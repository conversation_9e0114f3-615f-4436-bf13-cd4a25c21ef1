package net.summerfarm.wms.facade.inventory;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.inventory.dto.OrderOccupyBySpecifyWarehouseReqDTO;
import net.summerfarm.wms.facade.inventory.dto.OrderOccupySkuDetailReqDTO;
import net.summerfarm.wms.facade.inventory.dto.OrderReleaseBySpecifySkuReqDTO;
import net.summerfarm.wms.facade.inventory.dto.OrderReleaseSkuDetailReqDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterCommandProvider;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderOccupyResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderReleaseResDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 库存中心调用
 * @date 2024/1/31
 */
@Component
@Slf4j
public class SaleInventoryCenterCommandFacade {

    @DubboReference
    private SaleInventoryCenterCommandProvider saleInventoryCenterCommandProvider;

    /**
     * 库存中心占用接口
     * @param orderOccupyBySpecifyWarehouseReqDTO
     */
    public void orderOccupyBySpecifyWarehouseAndSku(OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO) {
        if (null == orderOccupyBySpecifyWarehouseReqDTO || CollectionUtils.isEmpty(orderOccupyBySpecifyWarehouseReqDTO.getOrderOccupySkuDetailReqDTOS())) {
            throw new BizException("调用库存中心占用接口异常，入参为空");
        }
        net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTOForInventory = new net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyBySpecifyWarehouseReqDTO();
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setTenantId(orderOccupyBySpecifyWarehouseReqDTO.getTenantId());
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setWarehouseNo(orderOccupyBySpecifyWarehouseReqDTO.getWarehouseNo());
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setOrderNo(orderOccupyBySpecifyWarehouseReqDTO.getOrderNo());
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setOrderSubNo(orderOccupyBySpecifyWarehouseReqDTO.getOrderSubNo());
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setOrderType(orderOccupyBySpecifyWarehouseReqDTO.getOrderType());
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setIdempotentNo(orderOccupyBySpecifyWarehouseReqDTO.getIdempotentNo());
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setOperatorName(orderOccupyBySpecifyWarehouseReqDTO.getOperatorName());
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setOperatorNo(orderOccupyBySpecifyWarehouseReqDTO.getOperatorNo());
        List<net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOForInventoryList = new ArrayList<>();
        for (OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO : orderOccupyBySpecifyWarehouseReqDTO.getOrderOccupySkuDetailReqDTOS()) {
            net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTOForInventory = new net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupySkuDetailReqDTO();
            orderOccupySkuDetailReqDTOForInventory.setSkuCode(orderOccupySkuDetailReqDTO.getSkuCode());
            orderOccupySkuDetailReqDTOForInventory.setOccupyQuantity(orderOccupySkuDetailReqDTO.getOccupyQuantity());
            orderOccupySkuDetailReqDTOForInventoryList.add(orderOccupySkuDetailReqDTOForInventory);
        }
        orderOccupyBySpecifyWarehouseReqDTOForInventory.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOForInventoryList);
        log.info("调用库存中心库存占用请求：{}", JSON.toJSONString(orderOccupyBySpecifyWarehouseReqDTOForInventory));
        DubboResponse<OrderOccupyResDTO> orderOccupyResDTODubboResponse = saleInventoryCenterCommandProvider.orderOccupyBySpecifyWarehouseAndSku(orderOccupyBySpecifyWarehouseReqDTOForInventory);
        log.info("调用库存中心库存占用返回：{}", JSON.toJSONString(orderOccupyResDTODubboResponse));
        if (null == orderOccupyResDTODubboResponse || !orderOccupyResDTODubboResponse.isSuccess()) {
            throw new BizException("调用库存中心占用接口失败");
        }
    }

    /**
     * 库存中心释放接口
     * @param orderReleaseBySpecifySkuReqDTO
     */
    public void orderReleaseBySpecifySku(OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO) {
        if (null == orderReleaseBySpecifySkuReqDTO || CollectionUtils.isEmpty(orderReleaseBySpecifySkuReqDTO.getOrderReleaseSkuDetailReqDTOS())) {
            throw new BizException("调用库存中心释放接口异常，入参为空");
        }
        net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTOForInventory = new net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO();
        orderReleaseBySpecifySkuReqDTOForInventory.setOrderNo(orderReleaseBySpecifySkuReqDTO.getOrderNo());
        orderReleaseBySpecifySkuReqDTOForInventory.setOrderType(orderReleaseBySpecifySkuReqDTO.getOrderType());
        orderReleaseBySpecifySkuReqDTOForInventory.setTenantId(orderReleaseBySpecifySkuReqDTO.getTenantId());
        orderReleaseBySpecifySkuReqDTOForInventory.setIdempotentNo(orderReleaseBySpecifySkuReqDTO.getIdempotentNo());
        orderReleaseBySpecifySkuReqDTOForInventory.setOperatorNo(orderReleaseBySpecifySkuReqDTO.getOperatorNo());
        orderReleaseBySpecifySkuReqDTOForInventory.setOperatorName(orderReleaseBySpecifySkuReqDTO.getOperatorName());
        List<net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseSkuDetailReqDTO> orderReleaseSkuDetailReqDTOForInventoryList = new ArrayList<>();
        for (OrderReleaseSkuDetailReqDTO orderReleaseSkuDetailReqDTO : orderReleaseBySpecifySkuReqDTO.getOrderReleaseSkuDetailReqDTOS()) {
            net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseSkuDetailReqDTO orderReleaseSkuDetailReqDTOForInventory = new net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseSkuDetailReqDTO();
            orderReleaseSkuDetailReqDTOForInventory.setSkuCode(orderReleaseSkuDetailReqDTO.getSkuCode());
            orderReleaseSkuDetailReqDTOForInventory.setReleaseQuantity(orderReleaseSkuDetailReqDTO.getReleaseQuantity());
            orderReleaseSkuDetailReqDTOForInventory.setWarehouseNo(orderReleaseSkuDetailReqDTO.getWarehouseNo());
            orderReleaseSkuDetailReqDTOForInventoryList.add(orderReleaseSkuDetailReqDTOForInventory);
        }
        orderReleaseBySpecifySkuReqDTOForInventory.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOForInventoryList);
        log.info("调用库存中心库存释放请求：{}", JSON.toJSONString(orderReleaseBySpecifySkuReqDTOForInventory));
        DubboResponse<OrderReleaseResDTO> orderReleaseResDTODubboResponse = saleInventoryCenterCommandProvider.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTOForInventory);
        log.info("调用库存中心库存释放返回：{}", JSON.toJSONString(orderReleaseResDTODubboResponse));
        if (null == orderReleaseResDTODubboResponse || !orderReleaseResDTODubboResponse.isSuccess()) {
            throw new BizException("调用库存中心释放接口失败");
        }
    }

}
