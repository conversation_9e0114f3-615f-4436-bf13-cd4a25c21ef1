package net.summerfarm.wms.facade.product;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.CrossBizGoodsMappingEnum;
import net.summerfarm.goods.client.provider.CrossBizGoodsMappingQueryProvider;
import net.summerfarm.goods.client.req.CrossBizGoodsMappingQueryReq;
import net.summerfarm.goods.client.resp.CrossBizGoodsMappingResp;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.facade.product.dto.GoodsSkuMappingDTO;
import net.summerfarm.wms.facade.product.dto.ProductSkuBaseDTO;
import net.summerfarm.wms.facade.product.input.QueryProductSkuBaseInfoInput;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 货品映射关系
 * date: 2025/4/29 10:47<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class CrossBizGoodsMappingQueryFacade {

    @DubboReference
    private CrossBizGoodsMappingQueryProvider crossBizGoodsMappingQueryProvider;
    @Resource
    private GoodsFacade goodsFacade;

    public List<GoodsSkuMappingDTO> queryMappingConfig(List<String> skus) {
        if(CollectionUtils.isEmpty(skus)){
            return Collections.emptyList();
        }

        CrossBizGoodsMappingQueryReq req = new CrossBizGoodsMappingQueryReq();
        req.setBizType(CrossBizGoodsMappingEnum.BizType.XIANMU_TO_POP.getValue());
        req.setTargetSkuList(skus);
        DubboResponse<List<CrossBizGoodsMappingResp>> dubboResponse = crossBizGoodsMappingQueryProvider.queryMappingConfig(req);
        if (dubboResponse == null || !dubboResponse.isSuccess()) {
            log.error("\n crossBizGoodsMappingQueryProvider queryMappingConfig error \n");
            return Collections.emptyList();
        }

        List<CrossBizGoodsMappingResp> dataList = dubboResponse.getData();
        if(CollectionUtils.isEmpty(dataList)){
            return Collections.emptyList();
        }

        List<GoodsSkuMappingDTO> goodsSkuMappingDTOList = dataList.stream().map(e -> {
            GoodsSkuMappingDTO goodsSkuMappingDTO = new GoodsSkuMappingDTO();
            goodsSkuMappingDTO.setXmSku(e.getSrcSku());
            goodsSkuMappingDTO.setPopSku(e.getTargetSku());

            return goodsSkuMappingDTO;
        }).collect(Collectors.toList());

        return goodsSkuMappingDTOList;
    }


    public Map<String,GoodsSkuMappingDTO> queryPopToXianMuMapping(List<String> skus){
        if(CollectionUtils.isEmpty(skus)){
            return Collections.emptyMap();
        }

        List<GoodsSkuMappingDTO> goodsSkuMappingDTOS = this.queryMappingConfig(skus);

        if(CollectionUtils.isEmpty(goodsSkuMappingDTOS)){
            return Collections.emptyMap();
        }
        List<String> xianmuSkuList = goodsSkuMappingDTOS.stream().map(GoodsSkuMappingDTO::getXmSku).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(xianmuSkuList)){
            return Collections.emptyMap();
        }
        QueryProductSkuBaseInfoInput input = new QueryProductSkuBaseInfoInput();
        input.setTenantId(WmsConstant.XIANMU_TENANT_ID);
        input.setSkuList(xianmuSkuList);
        List<ProductSkuBaseDTO> productSkuBaseDTOS = goodsFacade.queryProductSkuBaseInfo(input);
        if(CollectionUtils.isEmpty(productSkuBaseDTOS)){
            return Collections.emptyMap();
        }
        Map<String, ProductSkuBaseDTO> skuBaseDTOMap = productSkuBaseDTOS.stream().collect(Collectors.toMap(ProductSkuBaseDTO::getSku, Function.identity(), (a, b) -> a));

        goodsSkuMappingDTOS.forEach(goodsSkuMappingDTO -> {
            ProductSkuBaseDTO productSkuBaseDTO = skuBaseDTOMap.get(goodsSkuMappingDTO.getXmSku());
            if(productSkuBaseDTO == null){
                return;
            }
            goodsSkuMappingDTO.setXmTitle(productSkuBaseDTO.getTitle());
            goodsSkuMappingDTO.setXmSpecification(productSkuBaseDTO.getSpecification());
        });


        Map<String, GoodsSkuMappingDTO> popSkuMappingDTOMap = goodsSkuMappingDTOS.stream()
                .filter(pmsSkuMappingDTO -> pmsSkuMappingDTO.getPopSku() != null)
                .collect(Collectors.toMap(
                        GoodsSkuMappingDTO::getPopSku,
                        goodsSkuMappingDTO -> goodsSkuMappingDTO,
                        (existing, replacement) -> existing
                ));


        return popSkuMappingDTOMap;
    }
}
