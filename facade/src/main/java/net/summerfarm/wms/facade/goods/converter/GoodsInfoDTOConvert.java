package net.summerfarm.wms.facade.goods.converter;

import net.summerfarm.goods.client.enums.CategoryTypeEnum;
import net.summerfarm.goods.client.enums.GuaranteeUnitEnum;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;

public class GoodsInfoDTOConvert {

    public static GoodsInfoDTO convertXM(ProductSkuDetailResp resp) {
        if ( resp == null ) {
            return null;
        }

        GoodsInfoDTO goodsInfoDTO = new GoodsInfoDTO();

        goodsInfoDTO.setSkuId( resp.getSkuId() );
        goodsInfoDTO.setTenantId( resp.getTenantId() );
        goodsInfoDTO.setSpuId( resp.getSpuId() );
        goodsInfoDTO.setSku( resp.getSku() );
        goodsInfoDTO.setAgentType( resp.getAgentType() );
        goodsInfoDTO.setSubAgentType( resp.getSubAgentType() );
        goodsInfoDTO.setPlaceType( resp.getPlaceType() );
        goodsInfoDTO.setVolumeUnit( resp.getVolumeUnit() );
        goodsInfoDTO.setVolume( resp.getVolume() );
        goodsInfoDTO.setSpecification( resp.getSpecification() );
        goodsInfoDTO.setAssociated( resp.getAssociated() );
        goodsInfoDTO.setWeightNotes( resp.getWeightNotes() );
        goodsInfoDTO.setWeight( resp.getWeight() );
        goodsInfoDTO.setSpecificationUnit( resp.getSpecificationUnit() );
        goodsInfoDTO.setCreateTime( resp.getCreateTime() );
        goodsInfoDTO.setUpdateTime( resp.getUpdateTime() );
        goodsInfoDTO.setTaxRateValue( resp.getTaxRateValue() );
        goodsInfoDTO.setCustomSkuCode( resp.getCustomSkuCode() );
        goodsInfoDTO.setUseFlag( resp.getUseFlag() );
        goodsInfoDTO.setCreateType( resp.getCreateType() );
        goodsInfoDTO.setSkuPicture( resp.getSkuPicture() );
        goodsInfoDTO.setSkuTitle( resp.getSkuTitle() );
        goodsInfoDTO.setOwnerId( resp.getOwnerId() );
        goodsInfoDTO.setFirstCategory( resp.getFirstCategory() );
        goodsInfoDTO.setFirstCategoryId( resp.getFirstCategoryId() );
        goodsInfoDTO.setSecondCategory( resp.getSecondCategory() );
        goodsInfoDTO.setSecondCategoryId( resp.getSecondCategoryId() );
        goodsInfoDTO.setCategoryId( resp.getCategoryId() );
        goodsInfoDTO.setCategoryName( resp.getCategoryName() );
        goodsInfoDTO.setCategoryType( resp.getCategoryType() );
        goodsInfoDTO.setTitle( resp.getTitle() );
        goodsInfoDTO.setSubTitle( resp.getSubTitle() );
        goodsInfoDTO.setMainPicture( resp.getMainPicture() );
        goodsInfoDTO.setDetailPicture( resp.getDetailPicture() );
        goodsInfoDTO.setStorageTemperature( resp.getStorageTemperature() );
        goodsInfoDTO.setGuaranteePeriod( resp.getGuaranteePeriod() );
        goodsInfoDTO.setGuaranteeUnit( resp.getGuaranteeUnit() );
        goodsInfoDTO.setOrigin( resp.getOrigin() );
        goodsInfoDTO.setBrandName( resp.getBrandName() );
        goodsInfoDTO.setCustomSpuCode( resp.getCustomSpuCode() );
        goodsInfoDTO.setSkuMapping( resp.getSkuMapping() );
        goodsInfoDTO.setMaterialType( resp.getMaterialType() );
        goodsInfoDTO.setBuyerId( resp.getBuyerId());
        goodsInfoDTO.setBuyerName( resp.getBuyerName() );
        goodsInfoDTO.setExtType( resp.getExtType() );
        goodsInfoDTO.setNetWeightNum( resp.getNetWeightNum() );
        goodsInfoDTO.setNetWeightUnit( resp.getNetWeightUnit() );
        goodsInfoDTO.setQuoteType( resp.getQuoteType() );

        goodsInfoDTO.setStorageLocation( net.summerfarm.goods.client.enums.StorageLocationEnum.saas2Xm(resp.getStorageLocation()) );

        goodsInfoDTO.setSkuPdId(resp.getSkuMapping() == null ? null :
                resp.getSkuMapping().getAgentSpuId());

        goodsInfoDTO.setPropertyValueRespList(resp.getPropertyValueRespList());

        // 保鲜期时长
        goodsInfoDTO.setWarnTimePeriodDay(resp.getWarnTime());
        if (goodsInfoDTO.getGuaranteePeriod() != null && goodsInfoDTO.getGuaranteeUnit() != null &&
                resp.getWarnTime() != null && resp.getCategoryType() != null &&
                CategoryTypeEnum.FRUIT.getValue().equals(resp.getCategoryType())){

            Integer guaranteePeriodDay = 0;
            if (GuaranteeUnitEnum.YEAR.getValue().equals(goodsInfoDTO.getGuaranteeUnit())){
                guaranteePeriodDay = goodsInfoDTO.getGuaranteePeriod() * 365;
            } else if (GuaranteeUnitEnum.MONTH.getValue().equals(goodsInfoDTO.getGuaranteeUnit())){
                guaranteePeriodDay = goodsInfoDTO.getGuaranteePeriod() * 30;
            } else if (GuaranteeUnitEnum.DAY.getValue().equals(goodsInfoDTO.getGuaranteeUnit())){
                guaranteePeriodDay = goodsInfoDTO.getGuaranteePeriod();
            }
            Integer warnTime = resp.getWarnTime();
            goodsInfoDTO.setFreshnessPeriodDay(guaranteePeriodDay - warnTime);
        }
        return goodsInfoDTO;
    }

    public static GoodsInfoDTO convert(ProductSkuDetailResp resp) {
        if ( resp == null ) {
            return null;
        }

        GoodsInfoDTO goodsInfoDTO = new GoodsInfoDTO();

        goodsInfoDTO.setSkuId( resp.getSkuId() );
        goodsInfoDTO.setTenantId( resp.getTenantId() );
        goodsInfoDTO.setSpuId( resp.getSpuId() );
        goodsInfoDTO.setSku( resp.getSku() );
        goodsInfoDTO.setAgentType( resp.getAgentType() );
        goodsInfoDTO.setSubAgentType( resp.getSubAgentType() );
        goodsInfoDTO.setPlaceType( resp.getPlaceType() );
        goodsInfoDTO.setVolumeUnit( resp.getVolumeUnit() );
        goodsInfoDTO.setVolume( resp.getVolume() );
        goodsInfoDTO.setSpecification( resp.getSpecification() );
        goodsInfoDTO.setAssociated( resp.getAssociated() );
        goodsInfoDTO.setWeightNotes( resp.getWeightNotes() );
        goodsInfoDTO.setWeight( resp.getWeight() );
        goodsInfoDTO.setSpecificationUnit( resp.getSpecificationUnit() );
        goodsInfoDTO.setCreateTime( resp.getCreateTime() );
        goodsInfoDTO.setUpdateTime( resp.getUpdateTime() );
        goodsInfoDTO.setTaxRateValue( resp.getTaxRateValue() );
        goodsInfoDTO.setCustomSkuCode( resp.getCustomSkuCode() );
        goodsInfoDTO.setUseFlag( resp.getUseFlag() );
        goodsInfoDTO.setCreateType( resp.getCreateType() );
        goodsInfoDTO.setSkuPicture( resp.getSkuPicture() );
        goodsInfoDTO.setSkuTitle( resp.getSkuTitle() );
        goodsInfoDTO.setOwnerId( resp.getOwnerId() );
        goodsInfoDTO.setFirstCategory( resp.getFirstCategory() );
        goodsInfoDTO.setFirstCategoryId( resp.getFirstCategoryId() );
        goodsInfoDTO.setSecondCategory( resp.getSecondCategory() );
        goodsInfoDTO.setSecondCategoryId( resp.getSecondCategoryId() );
        goodsInfoDTO.setCategoryId( resp.getCategoryId() );
        goodsInfoDTO.setCategoryName( resp.getCategoryName() );
        goodsInfoDTO.setCategoryType( resp.getCategoryType() );
        goodsInfoDTO.setTitle( resp.getTitle() );
        goodsInfoDTO.setSubTitle( resp.getSubTitle() );
        goodsInfoDTO.setMainPicture( resp.getMainPicture() );
        goodsInfoDTO.setDetailPicture( resp.getDetailPicture() );
        goodsInfoDTO.setStorageLocation( resp.getStorageLocation() );
        goodsInfoDTO.setStorageTemperature( resp.getStorageTemperature() );
        goodsInfoDTO.setGuaranteePeriod( resp.getGuaranteePeriod() );
        goodsInfoDTO.setGuaranteeUnit( resp.getGuaranteeUnit() );
        goodsInfoDTO.setOrigin( resp.getOrigin() );
        goodsInfoDTO.setBrandName( resp.getBrandName() );
        goodsInfoDTO.setCustomSpuCode( resp.getCustomSpuCode() );
        goodsInfoDTO.setSkuMapping( resp.getSkuMapping() );
        goodsInfoDTO.setMaterialType( resp.getMaterialType() );
        goodsInfoDTO.setBuyerName( resp.getBuyerName() );
        goodsInfoDTO.setExtType( resp.getExtType() );
        goodsInfoDTO.setNetWeightNum( resp.getNetWeightNum() );
        goodsInfoDTO.setNetWeightUnit( resp.getNetWeightUnit() );
        goodsInfoDTO.setQuoteType( resp.getQuoteType() );

        goodsInfoDTO.setSkuPdId(resp.getSkuMapping() == null ? null :
                resp.getSkuMapping().getAgentSpuId());

        goodsInfoDTO.setPropertyValueRespList(resp.getPropertyValueRespList());

        // 保鲜期时长
        goodsInfoDTO.setWarnTimePeriodDay(resp.getWarnTime());
        if (goodsInfoDTO.getGuaranteePeriod() != null && goodsInfoDTO.getGuaranteeUnit() != null &&
                resp.getWarnTime() != null && resp.getCategoryType() != null &&
                CategoryTypeEnum.FRUIT.getValue().equals(resp.getCategoryType())){

            Integer guaranteePeriodDay = 0;
            if (GuaranteeUnitEnum.YEAR.getValue().equals(goodsInfoDTO.getGuaranteeUnit())){
                guaranteePeriodDay = goodsInfoDTO.getGuaranteePeriod() * 365;
            } else if (GuaranteeUnitEnum.MONTH.getValue().equals(goodsInfoDTO.getGuaranteeUnit())){
                guaranteePeriodDay = goodsInfoDTO.getGuaranteePeriod() * 30;
            } else if (GuaranteeUnitEnum.DAY.getValue().equals(goodsInfoDTO.getGuaranteeUnit())){
                guaranteePeriodDay = goodsInfoDTO.getGuaranteePeriod();
            }
            Integer warnTime = resp.getWarnTime();
            goodsInfoDTO.setFreshnessPeriodDay(guaranteePeriodDay - warnTime);
        }
        return goodsInfoDTO;
    }
}
