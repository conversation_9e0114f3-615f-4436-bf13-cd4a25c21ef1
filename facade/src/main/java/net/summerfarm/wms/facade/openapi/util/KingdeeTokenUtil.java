package net.summerfarm.wms.facade.openapi.util;

import com.alibaba.fastjson.JSON;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.data.api.AppTokenApi;
import com.kingdee.service.data.entity.AsterAppTokenRes;
import com.kingdee.service.unit.SHAUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.config.KingdeeConnectConfig;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Base64;


/**
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Component
@Slf4j
public class KingdeeTokenUtil {

    @Resource
    private KingdeeConnectConfig kingdeeConnectConfig;

    public String getToken(ApiClient apiClient) {
        try {
            AppTokenApi appTokenApi = new AppTokenApi(apiClient);
            String appSignature = getApiSignature();
            AsterAppTokenRes asterAppTokenRes = appTokenApi.asterAppToken(kingdeeConnectConfig.getZcwAppKey(), appSignature, null);
            log.info("获取金蝶token：{}", JSON.toJSONString(asterAppTokenRes));
            if(asterAppTokenRes == null || StringUtils.isBlank(asterAppTokenRes.getAppToken())) {
                throw new BizException("获取金蝶token失败");
            }
            return asterAppTokenRes.getAppToken();
        } catch (ApiException e) {
            log.error("获取金蝶token失败", e);
            throw new BizException("获取金蝶token失败");
        }
    }

    public String getApiSignature() {
        String appSignature = SHAUtil.SHA256HMAC(kingdeeConnectConfig.getZcwAppKey(), kingdeeConnectConfig.getZcwAppSecret());
        appSignature = Base64.getEncoder().encodeToString(appSignature.getBytes());
        return appSignature;
    }

}
