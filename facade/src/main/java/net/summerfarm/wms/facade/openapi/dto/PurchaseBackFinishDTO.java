package net.summerfarm.wms.facade.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * @Date 2023/10/23 15:18
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseBackFinishDTO implements Serializable {

    private static final long serialVersionUID = 703705968001853234L;
    /**
     * 采购单号
     */
    private String outPurchaseNo;
    /**
     * 采退单号
     */
    private String outPurchaseBackNo;
    /**
     * 出库仓
     */
    private Integer outWarehouseNo;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 明细
     */
    private List<StockTaskProcessDetailDTO> processDetailList;

}
