package net.summerfarm.wms.facade.purchase;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.purchase.PurchaseArrangeProvider;
import net.summerfarm.manage.client.purchase.PurchaseProvider;
import net.summerfarm.manage.client.purchase.dto.PurchaseArrangeReqDTO;
import net.summerfarm.manage.client.purchase.dto.PurchaseArrangeResDTO;
import net.summerfarm.manage.client.purchase.dto.PurchaseReqDTO;
import net.summerfarm.manage.client.purchase.dto.PurchaseResDTO;
import net.summerfarm.pms.client.provider.PurchaseQueryProvider;
import net.summerfarm.pms.client.req.purchase.PurchasePlanBatchQueryReq;
import net.summerfarm.pms.client.req.purchase.PurchasePlanQueryReq;
import net.summerfarm.pms.client.resp.purchase.PurchasePlanBatchResp;
import net.summerfarm.pms.client.resp.purchase.PurchasePlanResp;
import net.summerfarm.wms.common.util.RedisCacheUtil;
import net.summerfarm.wms.facade.purchase.dto.PurchaseFacadeResDTO;
import net.summerfarm.wms.facade.purchase.dto.PurchasePlanQueryReqDTO;
import net.summerfarm.wms.facade.purchase.dto.PurchasePlanResqDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购防腐
 */
@Component
@Slf4j
public class PurchaseFacade {

    @Resource
    private PurchaseProvider purchaseProvider;

    @Resource
    private PurchaseArrangeProvider purchaseArrangeProvider;

    @DubboReference(check = false, timeout = 6000, protocol = "dubbo")
    private PurchaseQueryProvider purchaseQueryProvider;

    @Resource
    private RedisCacheUtil redisCacheUtil;


    public List<PurchaseFacadeResDTO> queryPurchase(List<String> purchaseNos, List<String> skus) {
        DubboResponse<List<PurchaseResDTO>> dubboResponse = purchaseProvider.queryPurchase(PurchaseReqDTO.builder().skus(skus).purchaseNos(purchaseNos).build());
        List<PurchaseResDTO> purchaseResDTO = dubboResponse.getData();
        if (Objects.isNull(purchaseResDTO)) {
            return Lists.newArrayList();
        }
        return purchaseResDTO.stream()
                .map(item -> PurchaseFacadeResDTO.builder()
                        .sku(item.getSku())
                        .cost(new BigDecimal(item.getCost()))
                        .purchaseNo(item.getPurchaseNo())
                        .marketCost(item.getMarketCost())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 查询采购预约单号
     *
     * @param inStoreTaskId 入库单id
     * @return 预约单号
     */
    public Long queryPurchaseArrangeId(Long inStoreTaskId) {
        DubboResponse<PurchaseArrangeResDTO> purchaseArrangeResDTODubboResponse =
                purchaseArrangeProvider.queryPurchase(PurchaseArrangeReqDTO.builder().inStoreTaskId(inStoreTaskId).build());
        PurchaseArrangeResDTO data = purchaseArrangeResDTODubboResponse.getData();
        if (Objects.isNull(data)) {
            return null;
        }
        return data.getArrangeId();
    }


    /**
     * 获取批次供应商key
     *
     * @param purchaseNo
     * @param sku
     * @return
     */
    public static String mapPurchasePlanBatchKey(String purchaseNo, String sku) {
        return purchaseNo + "_" + sku;
    }

    /**
     * 获取批次供应商map
     *
     * @param requestList
     * @return
     */
    public Map<String, PurchasePlanResqDTO> mapPurchasePlanBatch(List<PurchasePlanQueryReqDTO> requestList) {
        List<PurchasePlanResqDTO> resqDTOList = queryPurchasePlanBatch(requestList);
        return resqDTOList.stream()
                .collect(Collectors.toMap(
                        s -> mapPurchasePlanBatchKey(s.getPurchaseNo(), s.getSku()),
                        Function.identity(),
                        (a, b) -> a
                ));
    }

    /**
     * 获取批次供应商cache
     * @param purchaseNo
     * @param sku
     * @return
     */
    public PurchasePlanResqDTO getPurchasePlanBatchCache(String purchaseNo, String sku) {
        return redisCacheUtil.getCacheObjectValueRefreshByHalfTime(
                "wms:PurchaseFacade:getPurchasePlanBatch:" + purchaseNo + ":" + sku,
                60,
                () -> getPurchasePlanBatch(purchaseNo, sku),
                PurchasePlanResqDTO.class
        );
    }

    /**
     * 获取批次供应商
     * @param purchaseNo
     * @param sku
     * @return
     */
    public PurchasePlanResqDTO getPurchasePlanBatch(String purchaseNo, String sku) {
        if (StringUtils.isEmpty(purchaseNo) || StringUtils.isEmpty(sku)){
            return null;
        }

        List<PurchasePlanQueryReqDTO> requestList = new ArrayList<>();
        PurchasePlanQueryReqDTO reqDTO = new PurchasePlanQueryReqDTO();
        reqDTO.setPurchaseNo(purchaseNo);
        reqDTO.setSku(sku);
        requestList.add(reqDTO);

        List<PurchasePlanResqDTO> resqDTOList = queryPurchasePlanBatch(requestList);
        return resqDTOList.stream().findFirst().orElse(null);
    }


    /**
     * 获取批次供应商list
     *
     * @param requestList
     * @return
     */
    public List<PurchasePlanResqDTO> queryPurchasePlanBatch(List<PurchasePlanQueryReqDTO> requestList) {
        if (CollectionUtils.isEmpty(requestList)) {
            return Collections.emptyList();
        }


        List<PurchasePlanQueryReq> purchasePlanQueryReqList = new ArrayList<>();
        for (PurchasePlanQueryReqDTO purchasePlanQueryReqDTO : requestList) {
            PurchasePlanQueryReq req = new PurchasePlanQueryReq();
            req.setPurchaseNo(purchasePlanQueryReqDTO.getPurchaseNo());
            req.setSku(purchasePlanQueryReqDTO.getSku());

            purchasePlanQueryReqList.add(req);
        }
        PurchasePlanBatchQueryReq batchQueryReq = new PurchasePlanBatchQueryReq();
        batchQueryReq.setPurchasePlanQueryReqList(purchasePlanQueryReqList);

        try {
            DubboResponse<PurchasePlanBatchResp> resp =
                    purchaseQueryProvider.queryPurchasePlanBatch(batchQueryReq);
            if (resp == null || !resp.isSuccess()) {
                log.error("查询供应商信息失败 supplierReq:{}", JSONUtil.toJsonStr(batchQueryReq), new Exception());
                return new ArrayList<>();
            }

            PurchasePlanBatchResp data = resp.getData();
            if (Objects.isNull(data)) {
                return new ArrayList<>();
            }

            List<PurchasePlanResqDTO> result = new ArrayList<>();
            for (PurchasePlanResp purchasePlanResp : data.getPurchasePlanRespList()) {
                PurchasePlanResqDTO resDTO = new PurchasePlanResqDTO();

                resDTO.setPurchaseNo(purchasePlanResp.getPurchaseNo());
                resDTO.setSku(purchasePlanResp.getSku());
                resDTO.setSupplierId(purchasePlanResp.getSupplierId());
                resDTO.setSupplierName(purchasePlanResp.getSupplierName());
                resDTO.setPurchaser(purchasePlanResp.getPurchaser());
                resDTO.setPurchaserId(purchasePlanResp.getPurchaserId());

                result.add(resDTO);
            }
            return result;
        } catch (Throwable throwable) {
            log.error("查询供应商批次信息失败", throwable);
            throw new BizException("查询供应商批次信息失败");
        }
    }
}
