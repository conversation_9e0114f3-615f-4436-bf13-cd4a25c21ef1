package net.summerfarm.wms.facade.product;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.erp.client.category.provider.CategoryQueryProvider;
import com.cosfo.erp.client.category.resp.CategoryLevelResultResp;
import com.cosfo.manage.client.product.ProductProvider;
import com.cosfo.manage.client.product.req.ProductQueryReq;
import com.cosfo.manage.client.product.req.SaasSkuMappingQueryReq;
import com.cosfo.manage.client.product.resp.ProductInfoResp;
import com.cosfo.manage.client.product.resp.ProductSkuResp;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.inventory.InventoryWriteProvider;
import net.summerfarm.manage.client.inventory.dto.req.InventoryUpdateReqDTO;
import net.summerfarm.pms.client.provider.SupplierStockPackupProvider;
import net.summerfarm.pms.client.req.SupplierStockPackupReq;
import net.summerfarm.pms.client.resp.SupplierStockPackupDTO;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.facade.product.dto.ProductQualityTimeTypeDTO;
import net.summerfarm.wms.facade.product.dto.SupplierStockProductReqDTO;
import net.summerfarm.wms.facade.product.dto.SupplierStockProductResDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 商品相关
 * @date 2023/3/17
 */
@Slf4j
@Component
public class ProductFacade {

    @Resource
    private InventoryWriteProvider inventoryWriteProvider;
    @DubboReference
    private ProductProvider productProvider;
    @DubboReference
    private CategoryQueryProvider categoryQueryProvider;
    @DubboReference
    private SupplierStockPackupProvider supplierStockPackupProvider;

    /**
     * 批量更新商品保质期类型
     *
     * @param productQualityTimeTypeDTOList
     * @return
     */
    public Boolean batchUpdateQualityTime(List<ProductQualityTimeTypeDTO> productQualityTimeTypeDTOList) {
        if (CollectionUtils.isEmpty(productQualityTimeTypeDTOList)) {
            return false;
        }
        if (productQualityTimeTypeDTOList.size() > WmsConstant.BATCH_SIZE_LIMIT) {
            return false;
        }
        List<InventoryUpdateReqDTO> inventoryUpdateReqDTOList = productQualityTimeTypeDTOList.stream().map(item -> {
            InventoryUpdateReqDTO inventoryUpdateReqDTO = new InventoryUpdateReqDTO();
            inventoryUpdateReqDTO.setSku(item.getSku());
            inventoryUpdateReqDTO.setQualityTimeType(item.getQualityTimeType());
            return inventoryUpdateReqDTO;
        }).collect(Collectors.toList());
        log.info("批量更新商品保质期类型入参：{}", JSON.toJSONString(inventoryUpdateReqDTOList));
        DubboResponse<Boolean> dubboResponse = inventoryWriteProvider.batchUpdateQualityTime(inventoryUpdateReqDTOList);
        log.info("批量更新商品保质期类型返回：{}", JSON.toJSONString(dubboResponse));
        if (null != dubboResponse && Boolean.TRUE.equals(dubboResponse.getData())) {
            return true;
        }
        return false;
    }

    public Long saasSkuId2SkuId(Long saasSkuId) {
        SaasSkuMappingQueryReq req = new SaasSkuMappingQueryReq();
        req.setSkuIds(Lists.newArrayList(saasSkuId));
        DubboResponse<ProductInfoResp> response = productProvider.batchQueryBySaasSkuIds(req);
        if (Objects.nonNull(response) && response.isSuccess() && response.getData() != null) {
            Map<Long, Long> skuMappingMap = response.getData().getSkuMappingMap();
            return skuMappingMap.get(saasSkuId);
        }
        log.warn("invoke productProvider.batchQueryBySaasSkuIds fail, request:{}, resp:{}",
                JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(response));
        return null;
    }

    public Map<Long/*skuId*/, Long/*saasSkuId*/> skuId2SaasSkuIdList(List<Long> skuIdList) {
        Map<Long, Long> skuIdMap = Maps.newHashMap();
        ProductQueryReq productQueryReq = new ProductQueryReq();
        productQueryReq.setSummerfarmSkuIds(skuIdList);
        DubboResponse<ProductInfoResp> productInfoRespDubboResponse = productProvider.batchQueryBySummerfarmSkuIds(productQueryReq);
        Map<Long, Long> skuMappingMap = Maps.newHashMap();
        if (Objects.nonNull(productInfoRespDubboResponse) && productInfoRespDubboResponse.isSuccess() && productInfoRespDubboResponse.getData() != null) {
            skuMappingMap = productInfoRespDubboResponse.getData().getSkuMappingMap();
        }
        for (Long skuId : skuIdList) {
            Long saasSkuId = skuMappingMap.get(skuId);
            skuIdMap.put(skuId, saasSkuId);
        }
        return skuIdMap;
    }

    public Map<Long, String> batchSelectWholeCategoryName(List<Long> categoryIdList) {
        DubboResponse<Map<Long, CategoryLevelResultResp>> response = categoryQueryProvider.batchSelectWholeCategory(categoryIdList);
        if (!response.isSuccess()) {
            log.error("批量查询类目信息失败, req:{}, msg:{}", JSONUtil.toJsonStr(categoryIdList), response.getMsg());
            ExceptionUtil.wrapAndThrow("查询类目信息异常" + response.getMsg());
        }
        Map<Long, String> result = Maps.newHashMap();
        Map<Long, CategoryLevelResultResp> categoryMap = response.getData();
        categoryMap.forEach((categoryId, categoryInfo) -> {
            if (Objects.nonNull(categoryInfo)) {
                result.put(categoryId, categoryInfo.getCategoryStr());
            }
        });

        return result;
    }

    public ProductSkuResp querySkuInfo(Long saasSkuId) {
        try {
            log.info("查询sku信息请求参数{}", JSONUtil.toJsonStr(saasSkuId));
            if (Objects.isNull(saasSkuId)) {
                return null;
            }
            ProductQueryReq req = new ProductQueryReq();
            req.setSkuId(saasSkuId);
            DubboResponse<ProductSkuResp> productSkuRespDubboResponse = productProvider.querySkuInfo(req);
            if (!productSkuRespDubboResponse.isSuccess()) {
                log.error("查询sku信息异常, req:{}, msg:{}", JSONUtil.toJsonStr(saasSkuId), productSkuRespDubboResponse.getMsg());
                ExceptionUtil.wrapAndThrow("查询商品sku信息异常" + productSkuRespDubboResponse.getMsg());
            }
            return productSkuRespDubboResponse.getData();
        } catch (Exception e) {
            log.error("查询sku信息异常", e);
        }
        return null;
    }


    /**
     * 查询提报商品信息
     *
     * @param req 查询条件信息
     * @return 返回提报商品信息列表 key sku value SupplierStockProductResDTO
     */
    public Map<String, SupplierStockProductResDTO> querySupplierStockProductList(SupplierStockProductReqDTO req) {
        log.info("querySupplierStockProductList提报商品信息查询请求参数{}", JSONUtil.toJsonStr(req));
        if (Objects.isNull(req) || Objects.isNull(req.getWarehouseNo()) || CollectionUtils.isEmpty(req.getSkuList())) {
            return new HashMap<>();
        }
        List<String> skuList = req.getSkuList();
        try {
            // 查询sku数量小于等于100个
            if (skuList.size() <= 100) {
                return getSupplierStockProductList(req);
            }
            // 下标记
            int index = 0;
            SupplierStockPackupReq stockPackupReq = new SupplierStockPackupReq();
            stockPackupReq.setWarehouseNo(req.getWarehouseNo());
            List<String> tmpSkuList = Lists.newArrayList();
            Map<String, SupplierStockProductResDTO> resMap = new HashMap<>();
            // 超过100查询情况信息
            for (String sku : skuList) {
                tmpSkuList.add(sku);
                index++;
                if (index >= 100) {
                    stockPackupReq.setSkuCodeList(tmpSkuList);
                    Map<String, SupplierStockProductResDTO> tmpResMap = getSupplierStockProductList(req);
                    resMap.putAll(tmpResMap);
                    // 重置记录下标
                    index = 0;
                }
            }
            if (index > 0) {
                stockPackupReq.setSkuCodeList(tmpSkuList);
                Map<String, SupplierStockProductResDTO> tmpResMap = getSupplierStockProductList(req);
                resMap.putAll(tmpResMap);
            }
            return resMap;
        } catch (Exception e) {
            log.error("querySupplierStockProductList查询提报商品信息信息异常", e);
        }
        return new HashMap<>();
    }

    /**
     * 查询提报商品信息
     *
     * @param req 查询条件信息
     * @return 返回提报商品信息列表
     */
    private Map<String, SupplierStockProductResDTO> getSupplierStockProductList(SupplierStockProductReqDTO req) {
        log.info("getSupplierStockProductList提报商品信息查询请求参数{}", JSONUtil.toJsonStr(req));
        if (Objects.isNull(req) || Objects.isNull(req.getWarehouseNo()) || CollectionUtils.isEmpty(req.getSkuList())) {
            return new HashMap<>();
        }
        List<String> skuList = req.getSkuList();
        try {
            SupplierStockPackupReq stockPackupReq = new SupplierStockPackupReq();
            stockPackupReq.setSkuCodeList(skuList);
            stockPackupReq.setWarehouseNo(req.getWarehouseNo());
            DubboResponse<List<SupplierStockPackupDTO>> supplierStockPackListResp = supplierStockPackupProvider.queryList(stockPackupReq);
            if (!supplierStockPackListResp.isSuccess()) {
                log.error("查询提报商品信息异常, req:{}, msg:{}", JSONUtil.toJsonStr(stockPackupReq), supplierStockPackListResp.getMsg());
                ExceptionUtil.wrapAndThrow("查询提报商品信息异常" + supplierStockPackListResp.getMsg());
            }
            List<SupplierStockPackupDTO> data = supplierStockPackListResp.getData();
            if (CollectionUtils.isEmpty(data)) {
                log.info("查询提报商品信息无返回结果集, req:{}", JSONUtil.toJsonStr(stockPackupReq));
                return new HashMap<>();
            }
            return data.stream().map(it -> {
                return SupplierStockProductResDTO.builder()
                        .warehouseNo(it.getWarehouseNo())
                        .sku(it.getSku())
                        .productionDate(it.getProductionDate())
                        .build();
            }).collect(Collectors.toMap(SupplierStockProductResDTO::getSku, Function.identity(), (a, b) -> a));
        } catch (Exception e) {
            log.error("查询提报商品信息信息异常", e);
        }
        return new HashMap<>();
    }
}
