package net.summerfarm.wms.facade.deliverypath;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.delivery.provider.TmsLackApprovedQueryProvider;
import net.summerfarm.tms.client.delivery.resp.TmsLackApprovedResp;
import net.summerfarm.wms.facade.deliverypath.dto.LackGoodDTO;
import net.xianmu.common.result.DubboResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2022/11/20  10:45
 * <p>
 * 获取配送缺货信息
 */
@Component
@Slf4j
public class TmsLackGoodsApprovedFacade {

    @Resource
    private TmsLackApprovedQueryProvider tmsLackApprovedQueryProvider;

    /**
     * 获取缺货何尊信息
     *
     * @param sourceId
     * @return
     */
    public LackGoodDTO getTmsLackGoodDTO(Long sourceId) {
        try {
            DubboResponse<TmsLackApprovedResp> tmsLackApprovedRespDubboResponse = tmsLackApprovedQueryProvider.queryLackGoodDetailById(sourceId);
            TmsLackApprovedResp data = tmsLackApprovedRespDubboResponse.getData();
            if (Objects.isNull(data)) {
                return null;
            }
            return LackGoodDTO.builder().lackRemark(data.getRemark())
                    .lackType(data.getLackType())
                    .pic(data.getPic()).build();
        } catch (Exception e) {
            log.warn("dubbo查询缺货核准信息失败", e);
            return null;
        }
    }
}
