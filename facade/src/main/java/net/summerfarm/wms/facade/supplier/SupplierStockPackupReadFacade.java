package net.summerfarm.wms.facade.supplier;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.pms.client.provider.SupplierStockPackupProvider;
import net.summerfarm.pms.client.req.SupplierStockPackupReq;
import net.summerfarm.pms.client.req.SupplierStockPackupWarehouseNoSkuReq;
import net.summerfarm.pms.client.resp.SupplierStockPackupDTO;
import net.summerfarm.wms.facade.supplier.converter.SupplierStockPeriodInfoDTOConvert;
import net.summerfarm.wms.facade.supplier.dto.SupplierStockPeriodInfoDTO;
import net.summerfarm.wms.facade.supplier.dto.SupplierStockPeriodQueryReqDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class SupplierStockPackupReadFacade {

    @DubboReference
    private SupplierStockPackupProvider supplierStockPackupProvider;

    public static String warehouseSkuMapKey(Integer warehouseNo, String sku){
        return warehouseNo + "_" + sku;
    }

    /**
     * 查询供货生产map
     *
     * @param warehouseNo
     * @param skuList
     * @return
     */
    public Map<String, SupplierStockPeriodInfoDTO> mapByWnoAndSkuList(
            Integer warehouseNo, List<String> skuList){
        List<SupplierStockPeriodInfoDTO> list = listByWnoAndSkuList(warehouseNo, skuList);
        return list.stream()
                .collect(Collectors.toMap(
                        s -> warehouseSkuMapKey(s.getWarehouseNo(), s.getSku()),
                        Function.identity(),
                        (a, b) -> a
                ));
    }

    /**
     * 查询供货生产列表
     *
     * @param reqDTOList
     * @return
     */
    public List<SupplierStockPeriodInfoDTO> listByWnoSkuList(List<SupplierStockPeriodQueryReqDTO> reqDTOList) {
        try {
            if (CollectionUtils.isEmpty(reqDTOList)) {
                return new ArrayList<>();
            }

            List<SupplierStockPackupWarehouseNoSkuReq> reqs = new ArrayList<>();
            for (SupplierStockPeriodQueryReqDTO reqDTO : reqDTOList) {
                SupplierStockPackupWarehouseNoSkuReq req = new SupplierStockPackupWarehouseNoSkuReq();
                req.setWarehouseNo(reqDTO.getWarehouseNo());
                req.setSkuCode(reqDTO.getSkuCode());

                reqs.add(req);
            }

            DubboResponse<List<SupplierStockPackupDTO>> listDubboResponse =
                    supplierStockPackupProvider.queryListByWarehouseNoAndSku(reqs);
            if (listDubboResponse == null || !listDubboResponse.isSuccess()) {
                return new ArrayList<>();
            }

            List<SupplierStockPackupDTO> list = listDubboResponse.getData();
            if (!CollectionUtils.isEmpty(list)) {
                list = list.stream()
                        .filter(s -> s != null &&
                                !StringUtils.isEmpty(s.getSku()) &&
                                s.getProductionDate() != null)
                        .distinct()
                        .collect(Collectors.toList());
            }

            return SupplierStockPeriodInfoDTOConvert.INSTANCE.convertList(list);
        } catch (Throwable throwable){
            log.error("查询供货生产列表异常", throwable);
            return new ArrayList<>();
        }
    }

    /**
     * 查询供货生产列表
     *
     * @param warehouseNo
     * @param skuList
     * @return
     */
    public List<SupplierStockPeriodInfoDTO> listByWnoAndSkuList(Integer warehouseNo, List<String> skuList) {
        try {
            if (warehouseNo == null || CollectionUtils.isEmpty(skuList)) {
                return new ArrayList<>();
            }

            SupplierStockPackupReq req = new SupplierStockPackupReq();
            req.setWarehouseNo(warehouseNo);
            req.setSkuCodeList(skuList);
            DubboResponse<List<SupplierStockPackupDTO>> listDubboResponse =
                    supplierStockPackupProvider.queryList(req);
            if (listDubboResponse == null || !listDubboResponse.isSuccess()) {
                return new ArrayList<>();
            }

            List<SupplierStockPackupDTO> list = listDubboResponse.getData();

            return SupplierStockPeriodInfoDTOConvert.INSTANCE.convertList(list);
        } catch (Throwable throwable){
            log.error("查询供货生产列表异常", throwable);
            return new ArrayList<>();
        }
    }

}
