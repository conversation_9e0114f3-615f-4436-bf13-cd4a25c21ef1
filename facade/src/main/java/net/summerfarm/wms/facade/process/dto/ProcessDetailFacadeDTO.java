package net.summerfarm.wms.facade.process.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessDetailFacadeDTO implements Serializable {
    private static final long serialVersionUID = -2862721087232345563L;
    /**
     * 业务id
     */
    @NotNull
    private Long bizId;

    /**
     * 业务类型
     */
    @NotNull
    private Integer bizType;

    /**
     * 渠道0、钉钉 1、CRM小程序
     */
    private Integer channel;
}
