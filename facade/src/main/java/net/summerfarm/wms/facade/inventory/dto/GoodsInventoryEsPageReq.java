package net.summerfarm.wms.facade.inventory.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 货品库存es查询dto
 */
@Data
public class GoodsInventoryEsPageReq implements Serializable {


    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 仓所属租户
     */
    private Long warehouseTenantId;
    /**
     * 货品租户ID
     */
    private Long skuTenantId;
    /**
     * skuCode列表
     */
    private List<String> skuCodeList;
    /**
     * 仓库号列表
     */
    private List<Integer> warehouseNos;
    /**
     * 是否售罄
     */
    private Boolean saleOut;
    /**
     * 库存同步状态，true：同步，false：不同步，null：查询全部
     */
    private Boolean sync;
    /**
     * 是否有出入库流水，true：有，false：没有，null：查询全部
     */
    private Boolean inOutRecord;
}
