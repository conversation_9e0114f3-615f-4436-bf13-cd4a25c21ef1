package net.summerfarm.wms.facade.admin;

import com.google.common.collect.Lists;
import net.summerfarm.manage.client.admin.AdminProvider;
import net.summerfarm.manage.client.admin.dto.AdminDTO;
import net.summerfarm.manage.client.admin.resp.AdminQueryResp;
import net.summerfarm.manage.client.base.PageRes;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.util.DubboResponseUtil;
import net.summerfarm.wms.facade.admin.converter.AdminConvert;
import net.summerfarm.wms.facade.admin.dto.AdminPageFacadeDTO;
import net.summerfarm.wms.facade.admin.input.AdminQueryFacade;
import net.summerfarm.wms.facade.base.PageFacadeRes;
import net.summerfarm.wms.facade.msg.dto.AdminFacadeDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class AdminFacade {

    @Resource
    private AdminProvider adminProvider;

    public AdminFacadeDTO queryAdmin(AdminFacadeDTO adminFacadeDTO) {
        DubboResponse<AdminDTO> admin = adminProvider.getAdmin(AdminConvert.INSTANCE.convert(adminFacadeDTO));
        DubboResponseUtil.isSuccess(admin, ErrorCode.ADMIN_GET_ERROR);
        return AdminConvert.INSTANCE.convert(admin.getData());
    }

    public PageFacadeRes<AdminFacadeDTO> queryAllAdmin(AdminPageFacadeDTO adminPageFacadeDTO) {
        DubboResponse<PageRes<AdminDTO>> pageResDubboResponse = adminProvider.pageAdmin(AdminConvert.INSTANCE.convert(adminPageFacadeDTO));
        DubboResponseUtil.isSuccess(pageResDubboResponse, ErrorCode.ADMIN_GET_ERROR);
        PageRes<AdminDTO> data = pageResDubboResponse.getData();
        return new PageFacadeRes<>(data.getContent().stream().map(AdminConvert.INSTANCE::convert).collect(Collectors.toList())
                , data.getPageSize(), data.getPageNum(), data.getTotal());
    }

    /**
     * 查询指定的管理员信息
     *
     * @param adminQueryFacade 管理员查询条件
     * @return 返回查询到的管理员信息
     */
    public List<AdminFacadeDTO> queryAdminList(AdminQueryFacade adminQueryFacade) {
        DubboResponse<AdminQueryResp> adminRespDubboResponse = adminProvider.queryAdmins(AdminConvert.INSTANCE.convert(adminQueryFacade));
        DubboResponseUtil.isSuccess(adminRespDubboResponse, ErrorCode.ADMIN_GET_ERROR);
        AdminQueryResp data = adminRespDubboResponse.getData();
        List<AdminDTO> adminDTOList = data.getAdminDTOList();
        if (CollectionUtils.isEmpty(adminDTOList)) {
            return Lists.newArrayList();
        }
        return adminDTOList.stream().map(AdminConvert.INSTANCE::convert).collect(Collectors.toList());
    }
}
