package net.summerfarm.wms.facade.openapi.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import net.summerfarm.wms.openapi.stockout.jp.req.OrderLineCallbackBatch;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 订单出库回告明细
 * @date 2024/4/23
 */
@Data
public class OrderLineCallbackLocal implements Serializable {

    private static final long serialVersionUID = -8526666269002318509L;

    /**
     * 单据行号
     */
    private String orderLineNo;

    /**
     * 商品编码，必填
     */
    private String itemCode;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 库存类型（ZP=正品，CC=残次，默认为ZP）
     */
    private String inventoryType;

    /**
     * 实发商品数量，必填
     */
    private Integer actualQty;

    /**
     * 批次编号
     */
    private String batchCode;

    /**
     * 生产日期
     */
    private String productDate;

    /**
     * 过期日期
     */
    private String expireDate;

    /**
     * 同一行号下多批次支持
     */
    private List<OrderLineCallbackBatch> batchs;
}
