package net.summerfarm.wms.facade.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.open.client.provider.event.BizEventInvokeProvider;
import net.xianmu.open.client.req.BizEventInvokerRequest;
import net.xianmu.open.client.resq.BizEventInvokeResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.security.ProviderException;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 外部出库调用
 * @date 2024/5/6
 */
@Component
@Slf4j
public class OpenPlatformFacade {

    @DubboReference
    private BizEventInvokeProvider bizEventInvokeProvider;

    /**
     * 同步调用
     *
     * @param bizId     业务id
     * @param eventType 事件类型
     * @param spiKey    spiKey
     * @param data      业务数据
     * @param urlParams url参数
     */
    public void syncInvokeEvent(String bizId, String eventType, String spiKey, String data,
                                Map<String, String> urlParams,
                                Map<String, String> headerParams) {
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(bizId);
        bizEventInvokerRequest.setEventType(eventType);
        bizEventInvokerRequest.setSpiKey(spiKey);
        bizEventInvokerRequest.setMediaType("JSON");
        bizEventInvokerRequest.setData(JSON.parseObject(data));
        bizEventInvokerRequest.setUrlParams(urlParams);
        bizEventInvokerRequest.setHeaderParams(headerParams);
        bizEventInvokerRequest.setTimeout(6L);
        log.info("调用开放平台-同步调用入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeDubboResponse = bizEventInvokeProvider.syncInvokeEvent(bizEventInvokerRequest);
        if (null == bizEventInvokeDubboResponse || !bizEventInvokeDubboResponse.isSuccess()) {
            log.error("调用开放平台-同步调用失败：{}", JSON.toJSONString(bizEventInvokeDubboResponse));
            throw new ProviderException("调用开放平台-同步调用失败");
        }
        // 校验返回
        this.validateResponse(bizEventInvokeDubboResponse.getData());
        log.info("调用开放平台-同步调用返回：{}", JSON.toJSONString(bizEventInvokeDubboResponse));
    }

    /**
     * 同步调用
     *
     * @param bizId     业务id
     * @param eventType 事件类型
     * @param spiKey    spiKey
     * @param data      业务数据
     * @param urlParams url参数
     */
    public void syncInvokeEventThrowBizEx(String bizId, String eventType, String spiKey, String data,
                                Map<String, String> urlParams,
                                Map<String, String> headerParams) {
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(bizId);
        bizEventInvokerRequest.setEventType(eventType);
        bizEventInvokerRequest.setSpiKey(spiKey);
        bizEventInvokerRequest.setMediaType("JSON");
        bizEventInvokerRequest.setData(JSON.parseObject(data));
        bizEventInvokerRequest.setUrlParams(urlParams);
        bizEventInvokerRequest.setHeaderParams(headerParams);
        bizEventInvokerRequest.setTimeout(6L);
        log.info("调用开放平台-同步调用入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeDubboResponse = bizEventInvokeProvider.syncInvokeEvent(bizEventInvokerRequest);
        log.info("调用开放平台-同步调用返回：{}", JSON.toJSONString(bizEventInvokeDubboResponse));
        if (null == bizEventInvokeDubboResponse || !bizEventInvokeDubboResponse.isSuccess()) {
            log.error("调用开放平台-同步调用失败");
            throw new BizException("调用开放平台-同步调用失败");
        }
        // 校验返回
        this.validateResponseThrowBizEx(bizEventInvokeDubboResponse.getData());
        log.info("调用开放平台-同步调用返回：{}", JSON.toJSONString(bizEventInvokeDubboResponse));
    }

    /**
     * 同步调用 并且有返回结果
     *
     * @param bizId     业务id
     * @param eventType 事件类型
     * @param spiKey    spiKey
     * @param data      业务数据
     * @param urlParams url参数
     * @return 返回数据
     */
    public String syncInvokeEventAndReturnData(String bizId, String eventType, String spiKey, String data,
                                               Map<String, String> urlParams,
                                               Map<String, String> headerParams) {
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(bizId);
        bizEventInvokerRequest.setEventType(eventType);
        bizEventInvokerRequest.setSpiKey(spiKey);
        bizEventInvokerRequest.setMediaType("JSON");
        bizEventInvokerRequest.setData(JSON.parseObject(data));
        bizEventInvokerRequest.setUrlParams(urlParams);
        bizEventInvokerRequest.setHeaderParams(headerParams);
        bizEventInvokerRequest.setTimeout(10L);
        log.info("调用开放平台-同步调用入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeDubboResponse = bizEventInvokeProvider.syncInvokeEvent(bizEventInvokerRequest);
        if (null == bizEventInvokeDubboResponse || !bizEventInvokeDubboResponse.isSuccess()) {
            log.error("调用开放平台-同步调用失败：{}", JSON.toJSONString(bizEventInvokeDubboResponse));
            throw new ProviderException("调用开放平台-同步调用失败");
        }
        // 校验返回
        BizEventInvokeResponse bizEventInvokeResponse = bizEventInvokeDubboResponse.getData();
        if (null == bizEventInvokeResponse) {
            throw new ProviderException("调用开放平台-同步调用失败，返回为空");
        }
        if (StringUtils.isEmpty(bizEventInvokeResponse.getResponse())) {
            throw new ProviderException("调用开放平台-同步调用失败，返回为空");
        }
        DubboResponse<String> apiResponse = JSONObject.parseObject(bizEventInvokeResponse.getResponse(), new TypeReference<DubboResponse<String>>() {
        });
        if (null == apiResponse || !apiResponse.isSuccess() || !DubboResponse.SUCCESS_STATUS.toString().equals(apiResponse.getCode())) {
            throw new ProviderException("调用开放平台-同步调用失败，外部接口返回失败" + (apiResponse == null ? null : apiResponse.getMsg()));
        }
        return apiResponse.getData();
    }

    private void validateResponse(BizEventInvokeResponse bizEventInvokeResponse) {
        if (null == bizEventInvokeResponse) {
            throw new ProviderException("调用开放平台-同步调用失败，返回为空");
        }
        if (StringUtils.isEmpty(bizEventInvokeResponse.getResponse())) {
            throw new ProviderException("调用开放平台-同步调用失败，返回为空");
        }
        DubboResponse<Boolean> apiResponse = JSONObject.parseObject(bizEventInvokeResponse.getResponse(), new TypeReference<DubboResponse<Boolean>>() {
        });
        if (null == apiResponse || !apiResponse.isSuccess() || !DubboResponse.SUCCESS_STATUS.toString().equals(apiResponse.getCode())) {
            throw new ProviderException("调用开放平台-同步调用失败，外部接口返回失败" + (apiResponse == null ? null : apiResponse.getMsg()));
        }
    }

    private void validateResponseThrowBizEx(BizEventInvokeResponse bizEventInvokeResponse) {
        if (null == bizEventInvokeResponse) {
            throw new BizException("调用开放平台-同步调用失败，返回为空");
        }
        if (StringUtils.isEmpty(bizEventInvokeResponse.getResponse())) {
            throw new BizException("调用开放平台-同步调用失败，返回为空");
        }
        DubboResponse<Boolean> apiResponse = JSONObject.parseObject(bizEventInvokeResponse.getResponse(), new TypeReference<DubboResponse<Boolean>>() {
        });
        if (null == apiResponse || !apiResponse.isSuccess() || !DubboResponse.SUCCESS_STATUS.toString().equals(apiResponse.getCode())) {
            throw new BizException("调用开放平台-同步调用失败，外部接口返回失败" + (apiResponse == null ? null : apiResponse.getMsg()));
        }
    }

}
