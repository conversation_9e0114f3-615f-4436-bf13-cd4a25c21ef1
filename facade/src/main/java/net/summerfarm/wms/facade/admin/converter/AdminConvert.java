package net.summerfarm.wms.facade.admin.converter;

import net.summerfarm.manage.client.admin.dto.AdminDTO;
import net.summerfarm.manage.client.admin.dto.AdminPageDTO;
import net.summerfarm.manage.client.admin.req.QueryAdminReq;
import net.summerfarm.wms.facade.admin.dto.AdminPageFacadeDTO;
import net.summerfarm.wms.facade.admin.input.AdminQueryFacade;
import net.summerfarm.wms.facade.msg.dto.AdminFacadeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AdminConvert {
    AdminConvert INSTANCE = Mappers.getMapper(AdminConvert.class);

    AdminDTO convert(AdminFacadeDTO adminFacadeDTO);

    AdminPageDTO convert(AdminPageFacadeDTO adminPageFacadeDTO);

    AdminFacadeDTO convert(AdminDTO adminFacadeDTO);

    QueryAdminReq convert(AdminQueryFacade adminQueryFacade);
}
