package net.summerfarm.wms.facade.saas;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.OrderItemStatusEnum;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAggQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderAggResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.saas.converter.SaasOrderConverter;
import net.summerfarm.wms.facade.saas.dto.OrderDetailDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:saas订单查询 <br/>
 * date: 2024/11/21 16:04<br/>
 *
 * <AUTHOR> />
 */
@Component
@Slf4j
public class SaasOrderQueryFacade {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;


    public List<OrderDetailDTO> queryOrder(List<String> orderNoList) {
        if(CollectionUtils.isEmpty(orderNoList)){
            return Collections.emptyList();
        }
        // 订单按照每50个查询
        List<List<String>> partition = Lists.partition(orderNoList, 50);

        List<OrderAggResp> orderAggResps = new ArrayList<>();

        partition.forEach(orderNos -> {
            OrderAggQueryReq req = new OrderAggQueryReq();
            req.setOrderNos(orderNos);
            DubboResponse<List<OrderAggResp>> resp = orderQueryProvider.queryOrderAggByNos(req);

            if (Objects.isNull(resp) || !DubboResponse.COMMON_SUCCESS_CODE.equals(resp.getCode())) {
                BizException exception = new BizException("调用查询Saas根据订单nos查询 订单聚合信息，包含订单项、下单地址异常");
                log.error("SaasOrderQueryFacade[]queryOrderAggByNos[]error[]OrderAggQueryReq:{}, cause:{}",
                        JSON.toJSONString(req), JSON.toJSONString(resp),exception);
                throw exception;
            }

            if(!CollectionUtils.isEmpty(resp.getData())){
                orderAggResps.addAll(resp.getData());
            }
        });

        return SaasOrderConverter.resp2DTO(orderAggResps);
    }


    /**
     * 查询有效的订单项
     * @param orderNoList 订单号
     * @return 结果
     */
    public List<OrderDetailDTO> queryOrderWithEffectiveItem(List<String> orderNoList) {
        List<OrderDetailDTO> orderDetailDTOS = this.queryOrder(orderNoList);
        if (CollectionUtils.isEmpty(orderDetailDTOS)) {
            return Collections.emptyList();
        }
        // 根据订单分组
        Map<String, List<OrderDetailDTO>> orderNoToOrderDetailDTOListMap = orderDetailDTOS.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderNo));

        List<Integer> effectiveItemStatus = Arrays.asList(OrderItemStatusEnum.PAID.getCode(), OrderItemStatusEnum.FINISHED.getCode());

        BigDecimal notEffectiveTotalPrice = orderDetailDTOS.stream()
                .filter(e -> !effectiveItemStatus.contains(e.getItemStatus()))
                .map(OrderDetailDTO::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        return orderDetailDTOS.stream().filter(e -> effectiveItemStatus.contains(e.getItemStatus())).collect(Collectors.toList());
    }

}
