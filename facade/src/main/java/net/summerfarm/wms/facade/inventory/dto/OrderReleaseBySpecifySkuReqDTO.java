package net.summerfarm.wms.facade.inventory.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/5
 */
@Data
public class OrderReleaseBySpecifySkuReqDTO implements Serializable {

    private static final long serialVersionUID = 93576755709252271L;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单子编号，分段调度库存场景使用（省心送）
     */
    private String orderSubNo;

    /**
     * 订单类别
     */
    private String orderType;

    /**
     * 操作单号
     */
    private String operatorNo;

    /**
     * 幂等单号
     */
    private String idempotentNo;

    /**
     * 订单释放明细
     */
    private List<OrderReleaseSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS;

    /**
     * 操作人名称
     */
    private String operatorName;
}
