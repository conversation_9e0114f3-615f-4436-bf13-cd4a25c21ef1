package net.summerfarm.wms.facade.purchase.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PurchasePlanResqDTO implements Serializable {

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 商品编码
     */
    private String sku;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 采购员
     */
    private String purchaser;

    /**
     * 采购员id
     */
    private Long purchaserId;

}