package net.summerfarm.wms.facade.user;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountQueryProvider;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Component
public class UserFacade {

    @DubboReference
    private TenantAccountQueryProvider tenantAccountQueryProvider;

    /**
     * 获取租户账号信息
     *
     * @param accountId
     * @return
     */
    public TenantAccountResultResp getTenantAccountInfoById(Long accountId) {
        log.info("invoke tenantAccountQueryProvider.getTenantAccountById:{}", accountId);
        DubboResponse<TenantAccountResultResp> response = tenantAccountQueryProvider.getTenantAccountById(accountId);
        log.info("result tenantAccountQueryProvider.getTenantAccountById:{}", JSONUtil.toJsonStr(response));
        if (!response.isSuccess()) {
            throw new BizException("获取账号信息失败");
        }
        return response.getData();
    }

    /**
     * 批量查询(未提供批量接口，临时使用；待提供方提供批量接口后替换)
     * @param accountIdList
     * @return
     */
    public List<TenantAccountResultResp> queryTenantAccountList(List<Long> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return Lists.newArrayList();
        }
        try {
            List<TenantAccountResultResp> result = Lists.newArrayList();
            accountIdList.forEach(accountId -> {
                TenantAccountResultResp tenantAccount = this.getTenantAccountInfoById(accountId);
                result.add(tenantAccount);
            });
            return result;
        } catch (Exception e) {
            log.error("根据accountId查询账户信息异常，accountIdList:{}", accountIdList, e);
            return Lists.newArrayList();
        }

    }


}

