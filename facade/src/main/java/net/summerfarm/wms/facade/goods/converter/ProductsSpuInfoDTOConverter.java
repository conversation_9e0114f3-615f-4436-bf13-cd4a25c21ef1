package net.summerfarm.wms.facade.goods.converter;

import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.goods.client.resp.ProductSpuDetailResp;
import net.summerfarm.wms.facade.goods.dto.ProductSkuDetailDTO;
import net.summerfarm.wms.facade.goods.dto.ProductsSpuInfoDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductsSpuInfoDTOConverter {

    public static ProductsSpuInfoDTO convert(ProductSpuDetailResp resp, Long xmSpuId) {
        if (resp == null){
            return null;
        }

        ProductsSpuInfoDTO dto = new ProductsSpuInfoDTO();
        dto.setSpuId(resp.getSpuId());
        dto.setTenantId(resp.getTenantId());
        dto.setCustomSpuCode(resp.getCustomSpuCode());
        dto.setXmSpuId(resp.getXmSpuId() == null ? xmSpuId : resp.getXmSpuId());
        dto.setSpu(resp.getSpu());

        dto.setTitle(resp.getTitle());
        dto.setSubTitle(resp.getSubTitle());
        dto.setMainPicture(resp.getMainPicture());
        dto.setThirdCategory(resp.getThirdCategory());
        dto.setCategoryId(resp.getCategoryId());

        if (!CollectionUtils.isEmpty(resp.getSkuDetailList())) {
            List<ProductSkuDetailDTO> productSkuDetailDTOList = new ArrayList<>();
            for (ProductSkuDetailResp productSkuDetailResp : resp.getSkuDetailList()) {
                ProductSkuDetailDTO detailDto = new ProductSkuDetailDTO();

                detailDto.setSkuId(productSkuDetailResp.getSkuId());
                detailDto.setTenantId(productSkuDetailResp.getTenantId());
                detailDto.setSpuId(productSkuDetailResp.getSpuId());
                detailDto.setSku(productSkuDetailResp.getSku());
                detailDto.setAgentType(productSkuDetailResp.getAgentType());
                detailDto.setSubAgentType(productSkuDetailResp.getSubAgentType());
                detailDto.setPlaceType(productSkuDetailResp.getPlaceType());
                detailDto.setVolumeUnit(productSkuDetailResp.getVolumeUnit());
                detailDto.setVolume(productSkuDetailResp.getVolume());
                detailDto.setSpecification(productSkuDetailResp.getSpecification());
                detailDto.setAssociated(productSkuDetailResp.getAssociated());
                detailDto.setWeightNotes(productSkuDetailResp.getWeightNotes());
                detailDto.setWeight(productSkuDetailResp.getWeight());
                detailDto.setSpecificationUnit(productSkuDetailResp.getSpecificationUnit());
                detailDto.setSpecificationType(productSkuDetailResp.getSpecificationType());
                detailDto.setCreateTime(productSkuDetailResp.getCreateTime());
                detailDto.setUpdateTime(productSkuDetailResp.getUpdateTime());

                detailDto.setTaxRateValue(productSkuDetailResp.getTaxRateValue());
                detailDto.setCustomSkuCode(productSkuDetailResp.getCustomSkuCode());
                detailDto.setUseFlag(productSkuDetailResp.getUseFlag());
                detailDto.setCreateType(productSkuDetailResp.getCreateType());
                detailDto.setSkuPicture(productSkuDetailResp.getSkuPicture());
                detailDto.setSkuTitle(productSkuDetailResp.getSkuTitle());
                detailDto.setOwnerId(productSkuDetailResp.getOwnerId());
                detailDto.setFirstCategory(productSkuDetailResp.getFirstCategory());
                detailDto.setFirstCategoryId(productSkuDetailResp.getFirstCategoryId());

                detailDto.setSecondCategory(productSkuDetailResp.getSecondCategory());
                detailDto.setSecondCategoryId(productSkuDetailResp.getSecondCategoryId());
                detailDto.setCategoryId(productSkuDetailResp.getCategoryId());
                detailDto.setCategoryName(productSkuDetailResp.getCategoryName());
                detailDto.setCategoryType(productSkuDetailResp.getCategoryType());


                detailDto.setTitle(productSkuDetailResp.getTitle());
                detailDto.setSubTitle(productSkuDetailResp.getSubTitle());

                detailDto.setMainPicture(productSkuDetailResp.getMainPicture());
                detailDto.setDetailPicture(productSkuDetailResp.getDetailPicture());
                detailDto.setStorageLocation(productSkuDetailResp.getStorageLocation());
                detailDto.setStorageTemperature(productSkuDetailResp.getStorageTemperature());
                detailDto.setGuaranteePeriod(productSkuDetailResp.getGuaranteePeriod());
                detailDto.setGuaranteeUnit(productSkuDetailResp.getGuaranteeUnit());
                detailDto.setOrigin(productSkuDetailResp.getOrigin());
                detailDto.setBrandName(productSkuDetailResp.getBrandName());

                detailDto.setCustomSpuCode(productSkuDetailResp.getCustomSpuCode());
                detailDto.setSkuMapping(productSkuDetailResp.getSkuMapping());
                detailDto.setBasicSpecUnit(productSkuDetailResp.getBasicSpecUnit());
                detailDto.setExtType(productSkuDetailResp.getExtType());

                productSkuDetailDTOList.add(detailDto);
            }

            dto.setProductSkuDetailDTOList(productSkuDetailDTOList);
        }
        return dto;
    }
}
