package net.summerfarm.wms.facade.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@NacosConfigurationProperties(prefix = "feishu.bot", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class FeishuBotUrlConfig {

    /**
     * saas客户外部失败通知 - 飞书预警url
     */
    private String saasExternalFailedFeiShuUrl;

}
