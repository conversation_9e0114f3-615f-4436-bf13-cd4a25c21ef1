package net.summerfarm.wms.facade.supplier;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.pms.client.provider.SupplierProvider;
import net.summerfarm.pms.client.req.SupplierReq;
import net.summerfarm.pms.client.resp.SupplierDTO;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @Date 2023/10/30 16:00
 * @<AUTHOR>
 */
@Component
@Slf4j
public class SupplierFacade {

    @DubboReference
    private SupplierProvider supplierProvider;

    /**
     * 供应商long
     * @param supplierId
     * @return
     */
    public String querySupplierLong(Long supplierId) {
        if (supplierId == null){
            return "";
        }

        Map<Long, String> map = querySupplierMapLong(Collections.singletonList(supplierId));
        String name = map.get(supplierId);
        return name == null ? "" : name;
    }

    /**
     * 供应商long
     * @param supplierIdList
     * @return
     */
    public Map<Long, String> querySupplierMapLong(List<Long> supplierIdList) {
        if (CollectionUtils.isEmpty(supplierIdList)) {
            return new HashMap<>();
        }
        supplierIdList = supplierIdList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<Integer> supplierIds = supplierIdList.stream().map(Long::intValue).collect(Collectors.toList());
        Map<Integer, String> supplierIdMap = querySupplierMap(supplierIds);
        return supplierIdMap.entrySet().stream().collect(Collectors.toMap(
                s -> s.getKey().longValue(), Map.Entry::getValue, (a, b) -> a));
    }
    /**
     * 根据供应商列表查询供应商信息
     * ps 供应商提供的是分页接口，需要兼容
     *
     * <AUTHOR>
     * @date 2023/11/1 16:48
     */
    public Map<Integer, String> querySupplierMap(List<Integer> supplierIdList) {
        if (CollectionUtils.isEmpty(supplierIdList)){
            return new HashMap<>();
        }

        SupplierReq supplierReq = new SupplierReq();
        supplierReq.setSupplierIdList(supplierIdList);
        supplierReq.setPageIndex(1);
        supplierReq.setPageSize(supplierIdList.size());
        DubboResponse<PageInfo<SupplierDTO>> resp = supplierProvider.querySupplierPage(supplierReq);
        if (!resp.isSuccess()) {
            log.error("查询供应商信息失败 supplierReq:{}", JSONUtil.toJsonStr(supplierReq), new Exception());
            throw new ProviderException(resp.getMsg());
        }
        List<SupplierDTO> supplierDTOList = resp.getData().getList();
        if (CollectionUtils.isEmpty(supplierDTOList)) {
            return Maps.newHashMap();
        }
        return supplierDTOList.stream().collect(Collectors.toMap(SupplierDTO::getSupplierId, SupplierDTO::getSupplierName, (a, b) -> a));
    }

}
