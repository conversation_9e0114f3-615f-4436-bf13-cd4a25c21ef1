package net.summerfarm.wms.facade.goods;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsSpuQueryProvider;
import net.summerfarm.goods.client.req.ProductSpuDetailQueryReq;
import net.summerfarm.goods.client.resp.ProductSpuDetailResp;
import net.summerfarm.wms.facade.goods.converter.ProductsSpuInfoDTOConverter;
import net.summerfarm.wms.facade.goods.dto.ProductsSpuInfoDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ProductsSpuQueryFacade {

    @DubboReference
    private ProductsSpuQueryProvider productsSpuQueryProvider;

    /**
     * 根据xmspuid查询货品spu信息
     * @param tenantId
     * @param xmSpuId
     * @return
     */
    public ProductsSpuInfoDTO queryProductsSpuByXmSpuId(Long tenantId, Long xmSpuId) {
        if (tenantId == null  || xmSpuId == null){
            throw new BizException("参数缺失");
        }
        ProductSpuDetailQueryReq productSpuDetailQuery = new ProductSpuDetailQueryReq();
        productSpuDetailQuery.setXmSpuId(xmSpuId);
        productSpuDetailQuery.setTenantId(tenantId);
        productSpuDetailQuery.setGlobalSearch(true);
        DubboResponse<ProductSpuDetailResp> productSpuDetailResponse = productsSpuQueryProvider
                .selectProductSpuDetail(productSpuDetailQuery);
        if (productSpuDetailResponse == null || !productSpuDetailResponse.isSuccess()) {
            log.error("请求货品服务发生异常 {}", productSpuDetailQuery);
            throw new BizException("请求货品服务发生异常");
        }

        ProductSpuDetailResp resp = productSpuDetailResponse.getData();
        return ProductsSpuInfoDTOConverter.convert(resp, xmSpuId);
    }

    /**
     * 根据spuno查询货品spu信息
     * @param tenantId
     * @param spuNo
     * @return
     */
    public ProductsSpuInfoDTO queryProductsSpuBySpuNo(Long tenantId, String spuNo) {
        if (tenantId == null  || spuNo == null){
            throw new BizException("参数缺失");
        }
        ProductSpuDetailQueryReq productSpuDetailQuery = new ProductSpuDetailQueryReq();
        productSpuDetailQuery.setSpu(spuNo);
        productSpuDetailQuery.setTenantId(tenantId);
        productSpuDetailQuery.setGlobalSearch(true);

        DubboResponse<ProductSpuDetailResp> productSpuDetailResponse = productsSpuQueryProvider
                .selectProductSpuDetail(productSpuDetailQuery);
        if (productSpuDetailResponse == null || !productSpuDetailResponse.isSuccess()) {
            log.error("请求货品服务发生异常 {}", productSpuDetailQuery);
            throw new BizException("请求货品服务发生异常");
        }

        ProductSpuDetailResp resp = productSpuDetailResponse.getData();
        return ProductsSpuInfoDTOConverter.convert(resp, null);
    }
}
