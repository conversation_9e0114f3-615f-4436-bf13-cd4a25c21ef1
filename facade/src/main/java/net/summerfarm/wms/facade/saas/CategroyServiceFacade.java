package net.summerfarm.wms.facade.saas;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.erp.client.category.provider.CategoryQueryProvider;
import com.cosfo.erp.client.category.req.CategoryQueryReq;
import com.cosfo.erp.client.category.resp.CategoryDetailResultResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.wms.facade.saas.converter.CategoryConverter;
import net.summerfarm.wms.facade.saas.dto.CategoryVO;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023-08-02
 **/
@Component
@Slf4j
public class CategroyServiceFacade {

	@DubboReference
	private CategoryQueryProvider cateGoryQueryProvider;

	/**
	 * 根据id查询子类目集合
	 *
	 * @param id
	 * @return
	 */
	public List<CategoryVO> queryChildCategoryList(Long id) {
		try {
			CategoryQueryReq req = new CategoryQueryReq();
			req.setId(id);
			DubboResponse<List<CategoryDetailResultResp>> response = cateGoryQueryProvider.list(req);
			if (!response.isSuccess()) {
				throw new DefaultServiceException(response.getMsg());
			}
			List<CategoryDetailResultResp> resultRespList = response.getData();
			if (CollectionUtils.isEmpty(resultRespList)) {
				return new ArrayList<>();
			}
			List<CategoryVO> categoryVOList = CategoryConverter.cateResultRespList2CateGoryVO(resultRespList);
			log.error("子类目集合:{}", JSONObject.toJSONString(categoryVOList));
			List<CategoryVO> newCategoryList = new ArrayList<>();
			getAllList(categoryVOList, newCategoryList);
			Set<CategoryVO> newCategorySet = new HashSet<>();
			Map<Long, Set<CategoryVO>> categoryVOMap = new HashMap<>();
			// 保存当前id对应的CategoryVO子集合
			CategoryVO parentCategoryVO = new CategoryVO();
			for (CategoryVO categoryVO : newCategoryList) {
				if (id != null && id.equals(categoryVO.getId())) {
					parentCategoryVO = categoryVO;
					newCategorySet.add(parentCategoryVO);
				}
				if (ObjectUtils.isEmpty(categoryVOMap.get(categoryVO.getParentId()))) {
					Set<CategoryVO> categoryVOS = new HashSet<>();
					categoryVOS.add(categoryVO);
					categoryVOMap.put(categoryVO.getParentId(), categoryVOS);
				} else {
					categoryVOMap.get(categoryVO.getParentId()).add(categoryVO);
				}
			}
			getChildList(categoryVOMap, newCategorySet, id);
			List<CategoryVO> childrenList = Arrays.asList(newCategorySet.toArray(new CategoryVO[0]));
			return childrenList;
		} catch (Exception e) {
			log.error("查询类目接口异常", e);
		}
		return null;
	}

	private void getChildList(Map<Long, Set<CategoryVO>> categoryVOMap, Set<CategoryVO> categoryVOSet, Long id) {
		if (ObjectUtils.isNotEmpty(categoryVOMap.get(id))) {
			categoryVOSet.addAll(categoryVOMap.get(id));
			for (CategoryVO categoryVO : categoryVOMap.get(id)) {
				if (ObjectUtils.isNotEmpty(categoryVOMap.get(categoryVO.getId()))) {
					getChildList(categoryVOMap, categoryVOSet, categoryVO.getId());
				}
			}
		}

	}

	private List<CategoryVO> getAllList(List<CategoryVO> categoryVOList, List<CategoryVO> newCategoryList) {
		newCategoryList.addAll(categoryVOList);
		for (CategoryVO categoryVO : categoryVOList) {
			if (ObjectUtils.isNotEmpty(categoryVO.getCategoryVOS())) {
				getAllList(categoryVO.getCategoryVOS(), newCategoryList);
			}
		}
		return newCategoryList;
	}

}
