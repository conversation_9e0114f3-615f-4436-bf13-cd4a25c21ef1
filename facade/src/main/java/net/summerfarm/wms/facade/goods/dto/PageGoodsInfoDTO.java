package net.summerfarm.wms.facade.goods.dto;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PageGoodsInfoDTO {
    /**
     * 仓库编号
     */
    Long warehouseNo;

    /**
     * 租户ID
     */
    Long tenantId;

    /**
     * SKU列表
     */
    List<String> skus;

    /**
     * 商品ID列表
     */
    List<Long> pdIds;


    /**
     * 体积是否为空
     **/
    Boolean isNullVolume;

    /**
     * 重量是否是空
     **/
    Boolean isNullWeight;

    Integer pageIndex;

    Integer pageSize;
}
