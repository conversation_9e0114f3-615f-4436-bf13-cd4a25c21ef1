package net.summerfarm.wms.facade.process.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 钉钉审批完成
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProcessFinishFacadeDTO implements Serializable {

    private static final long serialVersionUID = 7159106275418876165L;

    /**
     * 钉钉审批回调中的key-url
     */
    @NotNull
    private String url;

    /**
     * 审批id
     */
    @NotNull
    private String processInstanceId;

    /**
     * 审批状态
     */
    @NotNull
    private Integer newProcessStatus;
}
