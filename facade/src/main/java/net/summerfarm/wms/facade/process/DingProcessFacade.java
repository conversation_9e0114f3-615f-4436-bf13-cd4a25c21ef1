package net.summerfarm.wms.facade.process;

import com.google.common.collect.Lists;
import net.summerfarm.manage.client.ding.DingProcessProvider;
import net.summerfarm.manage.client.ding.dto.ProcessDetailResDTO;
import net.summerfarm.wms.common.util.DubboResponseUtil;
import net.summerfarm.wms.facade.process.converter.ProcessConvert;
import net.summerfarm.wms.facade.process.dto.ProcessCreateFacadeDTO;
import net.summerfarm.wms.facade.process.dto.ProcessDetailFacadeDTO;
import net.summerfarm.wms.facade.process.dto.ProcessDetailFacadeResDTO;
import net.summerfarm.wms.facade.process.dto.ProcessFinishFacadeDTO;
import net.xianmu.common.result.DubboResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Deprecated
public class DingProcessFacade {

    @Resource
    private DingProcessProvider dingProcessProvider;

    public void createProcess(ProcessCreateFacadeDTO processCreateFacadeDTO) {
        DubboResponse<Boolean> process = dingProcessProvider.createProcess(ProcessConvert.INSTANCE.convert(processCreateFacadeDTO));
        DubboResponseUtil.isSuccess(process);
    }

    public void processEnd(ProcessFinishFacadeDTO processFinishFacadeDTO) {
        DubboResponse<Boolean> booleanDubboResponse = dingProcessProvider.processFinish(ProcessConvert.INSTANCE.convert(processFinishFacadeDTO));
        DubboResponseUtil.isSuccess(booleanDubboResponse);
    }

    public List<ProcessDetailFacadeResDTO> processDetail(ProcessDetailFacadeDTO processDetailFacadeDTO) {
        DubboResponse<List<ProcessDetailResDTO>> processDetail =
                dingProcessProvider.getProcessDetail(ProcessConvert.INSTANCE.convert(processDetailFacadeDTO));
        if (!processDetail.isSuccess()) {
            return Lists.newArrayList();
        }
        List<ProcessDetailResDTO> data = processDetail.getData();
        return data.stream().map(ProcessConvert.INSTANCE::convert).collect(Collectors.toList());
    }
}
