package net.summerfarm.wms.facade.inventory;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.req.ProductSkuBasicReq;
import net.summerfarm.goods.client.resp.ProductSkuBasicResp;
import net.summerfarm.wms.facade.goods.dto.GoodsPropertyDTO;
import net.summerfarm.wms.facade.inventory.dto.GoodsInventoryEsPageReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.goodsinventory.GoodsInventoryEsQueryProvider;
import net.xianmu.inventory.client.goodsinventory.dto.GoodInventoryAggregateResDTO;
import net.xianmu.inventory.client.goodsinventory.dto.GoodInventoryPageResDTO;
import net.xianmu.inventory.client.goodsinventory.dto.GoodsInventoryEsPageReqDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class GoodsInventoryEsQueryFacade {

    @DubboReference(check = false, timeout = 6000, protocol = "dubbo")
    private GoodsInventoryEsQueryProvider goodsInventoryEsQueryProvider;

    /**
     * 查询货品库存聚合
     *
     * @param req
     * @return
     */
    public GoodInventoryAggregateResDTO queryInventoryAggregate(GoodsInventoryEsPageReq req) {
        if (req == null) {
            return null;
        }

        GoodsInventoryEsPageReqDTO reqDTO = new GoodsInventoryEsPageReqDTO();
        reqDTO.setWarehouseTenantId(req.getWarehouseTenantId());
        reqDTO.setSkuTenantId(req.getSkuTenantId());
        reqDTO.setSkuCodeList(req.getSkuCodeList());
        reqDTO.setWarehouseNos(req.getWarehouseNos());
        reqDTO.setSaleOut(req.getSaleOut());
        reqDTO.setSync(req.getSync());
        reqDTO.setInOutRecord(req.getInOutRecord());

        DubboResponse<GoodInventoryAggregateResDTO> listDubboResponse = goodsInventoryEsQueryProvider
                .queryInventoryAggregate(reqDTO);
        if (listDubboResponse == null || !listDubboResponse.isSuccess()) {
            log.error("请求货品库存服务发生异常 {}", reqDTO);
            throw new BizException("请求货品库存服务发生异常");
        }

        return listDubboResponse.getData();
    }

    /**
     * 查询货品库存分页
     *
     * @param req
     * @return
     */
    public PageInfo<GoodInventoryPageResDTO> queryInventoryPage(GoodsInventoryEsPageReq req) {
        if (req == null || req.getPageSize() == null || req.getPageNum() == null) {
            PageInfo<GoodInventoryPageResDTO> result = new PageInfo<>();
            result.setPageSize(req.getPageSize());
            result.setPageNum(req.getPageNum());
            result.setTotal(0);
            result.setList(new ArrayList<>());

            return result;
        }

        GoodsInventoryEsPageReqDTO reqDTO = new GoodsInventoryEsPageReqDTO();
        reqDTO.setPageSize(req.getPageSize());
        reqDTO.setPageNum(req.getPageNum());
        reqDTO.setWarehouseTenantId(req.getWarehouseTenantId());
        reqDTO.setSkuTenantId(req.getSkuTenantId());
        reqDTO.setSkuCodeList(req.getSkuCodeList());
        reqDTO.setWarehouseNos(req.getWarehouseNos());
        reqDTO.setSaleOut(req.getSaleOut());
        reqDTO.setSync(req.getSync());
        reqDTO.setInOutRecord(req.getInOutRecord());

        DubboResponse<PageInfo<GoodInventoryPageResDTO>>  listDubboResponse = goodsInventoryEsQueryProvider
                .queryInventoryPage(reqDTO);
        if (listDubboResponse == null || !listDubboResponse.isSuccess()) {
            log.error("请求货品库存服务发生异常 {}", reqDTO);
            throw new BizException("请求货品库存服务发生异常");
        }

        return listDubboResponse.getData();
    }
}
