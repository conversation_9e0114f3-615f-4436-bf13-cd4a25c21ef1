package net.summerfarm.wms.facade.supplier.converter;

import net.summerfarm.pms.client.resp.SupplierStockPackupDTO;
import net.summerfarm.wms.facade.supplier.dto.SupplierStockPeriodInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SupplierStockPeriodInfoDTOConvert {

    SupplierStockPeriodInfoDTOConvert INSTANCE = Mappers.getMapper(SupplierStockPeriodInfoDTOConvert.class);

    SupplierStockPeriodInfoDTO convert(SupplierStockPackupDTO resp);

    List<SupplierStockPeriodInfoDTO> convertList(List<SupplierStockPackupDTO> respList);
}
