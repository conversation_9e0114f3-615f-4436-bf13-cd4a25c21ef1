package net.summerfarm.wms.facade.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Configuration
@NacosConfigurationProperties(prefix = "kd.external.connect", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class KingdeeConnectConfig {

    /**
     * clientId
     */
    private String zcwClientId;

    /**
     * clientSecret
     */
    private String zcwClientSecret;

    /**
     * appkey
     */
    private String zcwAppKey;;

    /**
     * 密钥
     */
    private String zcwAppSecret;

    /**
     * 仓库列表
     */
    private List<Integer> warehouseNoList;

    /**
     * 开始时间
     */
    private String createTimeStart;

    /**
     * 结束时间
     */
    private String createTimeEnd;

}
