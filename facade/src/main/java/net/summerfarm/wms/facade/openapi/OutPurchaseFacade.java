package net.summerfarm.wms.facade.openapi;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.openapi.dto.PurchaseInPartDTO;
import net.xianmu.open.client.provider.event.BizEventPushProvider;
import net.xianmu.open.client.req.BizEventPushRequest;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static net.summerfarm.wms.facade.openapi.enums.EventTypeEnum.PURCHASE_IN_PART_NOTICE;

/**
 * @Description
 * @Date 2023/10/11 17:07
 * @<AUTHOR>
 */
@Component
@Slf4j
public class OutPurchaseFacade {

    @DubboReference
    private BizEventPushProvider bizEventPushProvider;


    /**
     * 部分回传
     *
     * <AUTHOR>
     * @date 2023/10/23 15:15
     */
    public void noticePurchaseInPart(PurchaseInPartDTO inPartDTO, Long tenantId) {
        if (Objects.isNull(inPartDTO) || Objects.isNull(tenantId)) {
            log.error("代仓对接，外部采购单部分入库回传外部系统失败，未查询到入库信息 inPartDTO:{}, tenantId:{}", JSONUtil.toJsonStr(inPartDTO), tenantId);
            return;
        }
        try {
            BizEventPushRequest request = new BizEventPushRequest();
            request.setBizId(inPartDTO.getIdempotentNo());
            request.setEventType(PURCHASE_IN_PART_NOTICE.getType());
            request.setAccountId(tenantId);
            request.setData(inPartDTO);
            bizEventPushProvider.pushBizEvent(request);
            log.info("代仓对接，外部采购单部分入库回传成功，request:{}", JSONUtil.toJsonStr(request));
        } catch (Exception e) {
            log.error("代仓对接，外部采购单部分入库回传外部系统失败，inPartDTO:{}", JSONUtil.toJsonStr(inPartDTO), e);
        }
    }

}
