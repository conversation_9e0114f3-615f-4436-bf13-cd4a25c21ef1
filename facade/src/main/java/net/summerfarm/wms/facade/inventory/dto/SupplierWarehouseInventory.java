package net.summerfarm.wms.facade.inventory.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierWarehouseInventory implements Serializable {

    /**
     * 仓库编码
     */
    private Long warehouseNo;

    /**
     * 供应商编码ID
     */
    private Integer supplierId;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 仓库数量
     */
    private Integer quantity;

    /**
     * 安全仓库数量
     */
    private Integer safeQuantity;
}
