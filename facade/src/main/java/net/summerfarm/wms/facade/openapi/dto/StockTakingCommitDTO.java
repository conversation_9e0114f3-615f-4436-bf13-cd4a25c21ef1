package net.summerfarm.wms.facade.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * @Description
 * @Date 2023/10/23 15:32
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTakingCommitDTO implements Serializable {


    private static final long serialVersionUID = 8316188236574256202L;
    /**
     * sku
     */
    private String skuCode;
    /**
     * 客户sku
     */
    private String customerSkuCode;
    /**
     * 规格单位
     */
    private String customerSkuspecificationUnit;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 保质期
     */
    private LocalDate qualityDate;
    /**
     * 系统库存
     */
    private Integer quantity;
    /**
     * 盘点库存
     */
    private Integer stockTakingQuantity;
    /**
     * 备注
     */
    private String remark;

}
