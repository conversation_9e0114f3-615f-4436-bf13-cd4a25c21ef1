package net.summerfarm.wms.facade.deliverypath;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.delivery.AfterDeliveryPatchProvider;
import net.summerfarm.manage.client.delivery.dto.req.AfterDeliveryPatchQueryReq;
import net.summerfarm.manage.client.delivery.dto.res.AfterDeliveryPatchDTO;
import net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider;
import net.summerfarm.tms.client.dist.req.DistOrderQueryReq;
import net.summerfarm.tms.client.dist.resp.DistOrderResp;
import net.xianmu.common.result.DubboResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2022/11/20  10:33
 * 获取配送信息
 */
@Component
@Slf4j
public class DeliveryPathFacade {

    @Resource
    private AfterDeliveryPatchProvider afterDeliveryPatchProvider;

    @Resource
    private TmsDistOrderQueryProvider tmsDistOrderQueryProvider;

    //获取配送信息
    public List<AfterDeliveryPatchDTO> getDeliveryPath(String orderNo, LocalDate deliveryDate, Integer storeNo, Integer type) {
        if (Objects.isNull(deliveryDate)) {
            return Lists.newArrayList();
        }
        AfterDeliveryPatchQueryReq queryReq = AfterDeliveryPatchQueryReq.builder().
                orderNo(orderNo)
                .deliveryTime(deliveryDate).storeNo(storeNo)
                .type(type)
                .build();
        try {
            DubboResponse<List<AfterDeliveryPatchDTO>> listDubboResponse = afterDeliveryPatchProvider.queryByOrderNo(queryReq);
            return listDubboResponse.getData();
        } catch (Exception e) {
            log.warn("dubbo查询配送信息失败", e);
            return Lists.newArrayList();
        }

    }

//    public List<AfterDeliveryPatchDTO> getDeliveryPathByAfterSaleNo(String afterOrderNo, Integer tmsSourceType) {
//        try {
//
//            DubboResponse<DistOrderResp> distOrderRespDubboResponse = tmsDistOrderQueryProvider.queryDetail(DistOrderQueryReq.builder()
//                    .outerOrderId(afterOrderNo)
//                    .source(tmsSourceType)
//                    .build());
//            return distOrderRespDubboResponse.getData();
//        } catch (Exception e) {
//            log.warn("dubbo查询配送信息失败", e);
//            return Lists.newArrayList();
//        }
//
//    }

}
