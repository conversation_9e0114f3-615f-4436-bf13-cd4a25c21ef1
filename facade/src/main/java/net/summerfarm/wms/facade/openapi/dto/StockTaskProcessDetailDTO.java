package net.summerfarm.wms.facade.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * @Description
 * @Date 2023/10/23 15:20
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskProcessDetailDTO implements Serializable {


    private static final long serialVersionUID = 46849097096351243L;
    /**
     * sku
     */
    private String skuCode;
    /**
     * 客户sku
     */
    private String customerSkuCode;
    /**
     * 规格单位
     */
    private String customerSkuspecificationUnit;
    /**
     * 采购单号
     */
    private String outPurchaseNo;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 保质期
     */
    private LocalDate qualityDate;
    /**
     * 数量
     */
    private Integer quantity;

}
