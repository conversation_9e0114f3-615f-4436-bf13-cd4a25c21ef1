package net.summerfarm.wms.facade.product;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.wms.GoodsCheckTaskProvider;
import net.summerfarm.manage.client.wms.dto.req.CreateGoodsCheckTaskReq;
import net.summerfarm.wms.facade.product.dto.CreateGoodsCheckTaskDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 商品校验任务
 * @date 2023/3/20
 */
@Slf4j
@Component
public class GoodsCheckTaskFacade {

    @Resource
    private GoodsCheckTaskProvider goodsCheckTaskProvider;

    /**
     * 创建商品校验任务
     * @param createGoodsCheckTaskDTOList
     * @return
     */
    public List<Long> createGoodsCheckTask(List<CreateGoodsCheckTaskDTO> createGoodsCheckTaskDTOList) {
        if (CollectionUtils.isEmpty(createGoodsCheckTaskDTOList)) {
            return new ArrayList<>();
        }
        List<CreateGoodsCheckTaskReq> createGoodsCheckTaskReqList = createGoodsCheckTaskDTOList.stream().map(createGoodsCheckTaskDTO -> {
            CreateGoodsCheckTaskReq createGoodsCheckTaskReq = new CreateGoodsCheckTaskReq();
            createGoodsCheckTaskReq.setWarehouseNo(createGoodsCheckTaskDTO.getWarehouseNo());
            createGoodsCheckTaskReq.setSku(createGoodsCheckTaskDTO.getSku());
            createGoodsCheckTaskReq.setOperator(createGoodsCheckTaskDTO.getOperator());
            return createGoodsCheckTaskReq;
        }).collect(Collectors.toList());
        log.info("创建商品校验任务入参：{}", JSON.toJSONString(createGoodsCheckTaskReqList));
        DubboResponse<List<Long>> goodsCheckTaskResp = goodsCheckTaskProvider.batchCreateGoodsCheckTask(createGoodsCheckTaskReqList);
        log.info("创建商品校验任务返回：{}", JSON.toJSONString(goodsCheckTaskResp));
        if (null != goodsCheckTaskResp && !CollectionUtils.isEmpty(goodsCheckTaskResp.getData())) {
            return goodsCheckTaskResp.getData();
        } else {
            log.error("创建商品校验任务失败，{}", JSON.toJSONString(createGoodsCheckTaskReqList));
        }
        return new ArrayList<>();
    }

}
