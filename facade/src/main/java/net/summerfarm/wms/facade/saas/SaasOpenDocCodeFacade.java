package net.summerfarm.wms.facade.saas;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.enums.OpenDocCodeMappingRespEnum;
import com.cosfo.manage.client.open.OpenDocCodeMappingProvider;
import com.cosfo.manage.client.open.req.OpenDocCodeMappingQueryReq;
import com.cosfo.manage.client.open.resp.OpenDocCodeMappingResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.saas.converter.SaasOpenDocCodeMappingConverter;
import net.summerfarm.wms.facade.saas.dto.SaasOpenDocCodeMapping;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
@Component
@Slf4j
public class SaasOpenDocCodeFacade {

    @DubboReference
    private OpenDocCodeMappingProvider openDocCodeMappingProvider;

    /**
     * 查询帆台订单关联外部单据（金蝶）
     * @param orderNo
     * @return
     */
    public SaasOpenDocCodeMapping queryKingdeeOpenDocCode(String orderNo, Long tenantId) {
        OpenDocCodeMappingQueryReq openDocCodeMappingQueryReq = new OpenDocCodeMappingQueryReq();
        openDocCodeMappingQueryReq.setDocCode(orderNo);
        openDocCodeMappingQueryReq.setTenantId(tenantId);
        openDocCodeMappingQueryReq.setDocType(OpenDocCodeMappingRespEnum.DocType.ORDER.getValue());
        openDocCodeMappingQueryReq.setOpenType(OpenDocCodeMappingRespEnum.OpenType.JINDIE.getValue());
        log.info("查询帆台订单关联外部单据入参：{}", JSON.toJSONString(openDocCodeMappingQueryReq));
        DubboResponse<OpenDocCodeMappingResp> openDocCodeMappingRespDubboResponse = openDocCodeMappingProvider.queryByCondition(openDocCodeMappingQueryReq);
        log.info("查询帆台订单关联外部单据返回：{}", JSON.toJSONString(openDocCodeMappingRespDubboResponse));
        if (null == openDocCodeMappingRespDubboResponse || null == openDocCodeMappingRespDubboResponse.getData()) {
            log.info("查询帆台订单关联外部单据返回为空，订单号：{}", orderNo);
            return null;
        }
        return SaasOpenDocCodeMappingConverter.convert(openDocCodeMappingRespDubboResponse.getData());
    }


}
