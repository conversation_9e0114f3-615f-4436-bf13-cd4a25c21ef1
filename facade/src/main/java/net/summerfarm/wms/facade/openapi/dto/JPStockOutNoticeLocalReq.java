package net.summerfarm.wms.facade.openapi.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import net.summerfarm.wms.openapi.stockout.jp.req.DeliveryOrderNotice;
import net.summerfarm.wms.openapi.stockout.jp.req.OrderLineNotice;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/23
 */
@Data
@JacksonXmlRootElement(localName = "request")
public class JPStockOutNoticeLocalReq implements Serializable {

    private static final long serialVersionUID = -890414044473559406L;

    /**
     * 绝配订单出库通知
     */
    @JacksonXmlProperty(localName = "deliveryOrder")
    private DeliveryOrderNotice deliveryOrder;

    /**
     * 订单通知明细
     */
    @JacksonXmlElementWrapper(localName = "orderLines")
    @JacksonXmlProperty(localName = "orderLine")
    private List<OrderLineNotice> orderLines;

}
