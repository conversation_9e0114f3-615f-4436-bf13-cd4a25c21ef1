package net.summerfarm.wms.facade.goods.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.common.constant.WmsConstant;

import java.util.List;
import java.util.Objects;

/**
 * 货品信息查询对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GoodsInfoQueryDTO {
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * sku列表
     */
    List<Long> saasSkuIds;

    /**
     * sku列表
     */
    List<String> skus;

    /**
     * 商品id
     */
    List<Long> pdIds;

    /**
     * 货品名称
     */
    String pdName;

    /**
     * 租户id
     */
    @Builder.Default
    Long tenantId = 1L;

    public Long getTenantId() {
        if (Objects.isNull(tenantId)) {
            return WmsConstant.XIANMU_TENANT_ID;
        }
        return tenantId;
    }

}
