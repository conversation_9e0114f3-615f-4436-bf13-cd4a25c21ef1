package net.summerfarm.wms.facade.openapi.dto;

import lombok.Data;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/21
 */
@Data
public class PLApiResponse implements Serializable {

    private static final long serialVersionUID = -7830421392819011086L;

    private PLApiResponseInfo response;

    public boolean isSuccess() {
        return response != null && (StringUtils.equals(response.getFlag(), "success") || DubboResponse.SUCCESS_STATUS.toString().equals(response.getCode()));
    }

}
