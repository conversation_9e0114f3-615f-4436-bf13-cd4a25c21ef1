package net.summerfarm.wms.facade.openapi.dto;

import lombok.Data;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 订单出库回告
 * @date 2024/4/23
 */
@Data
public class DeliveryOrderCallbackLocal implements Serializable {

    private static final long serialVersionUID = -6906532806752676605L;

    /**
     * 单据总行数
     */
    private Integer totalOrderLines;

    /**
     * 出库单号，必填
     */
    private String deliveryOrderCode;

    /**
     * 仓储系统出库单号
     */
    private String deliveryOrderId;

    /**
     * 仓库编码，必填
     */
    private String warehouseCode;

    /**
     * 出库单类型，必填（PTCK=普通出库单，DBCK=调拨出库 ，B2BCK=B2B出库，QTCK=其他出库，，CGTH=采购退货出库单）
     */
    private String orderType;

    /**
     * 出库单状态，(PARTDELIVERED-部分发货完成,  DELIVERED-发货完成,) , (只传英文编码)
     */
    private String status;

    private String confirmType;

    /**
     * 物流公司编码，（JPSCM=绝配供应链（绝配运输），KHZT=客户自提）
     */
    private String logisticsCode;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 运单号
     */
    private String expressCode;

    /**
     * 订单完成时间
     */
    private String orderConfirmTime;

    private String outBizCode;
}
