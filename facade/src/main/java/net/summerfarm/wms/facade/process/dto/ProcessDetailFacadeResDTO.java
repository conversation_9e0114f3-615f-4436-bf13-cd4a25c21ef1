package net.summerfarm.wms.facade.process.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 审批返回对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProcessDetailFacadeResDTO implements Serializable {
    private static final long serialVersionUID = 3232828879462975052L;

    String name;

    String auditResult;

    String auditAt;

    Boolean isAuditor;

    String auditRemark;
}
