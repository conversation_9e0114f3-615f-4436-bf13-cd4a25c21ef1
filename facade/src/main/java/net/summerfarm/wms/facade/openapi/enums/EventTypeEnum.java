package net.summerfarm.wms.facade.openapi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description
 * @Date 2023/10/31 10:36
 * @<AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum EventTypeEnum {

    PURCHASE_IN_PART_NOTICE("purchase.in.part.notice", "采购入库部分回传"),
    PURCHASE_BACK_FINISH_NOTICE("purchase.back.finish.notice", "采退出库完结回传"),
    STOCK_TAKING_FINISH_NOTICE("stock.taking.finish.notice", "盘点完成结果回传"),
    ;

    private String type;
    private String desc;


}
