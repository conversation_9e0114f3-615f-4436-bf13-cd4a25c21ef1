package net.summerfarm.wms.facade.product.converter;

import com.google.common.collect.Lists;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.wms.facade.product.dto.ProductSkuBaseDTO;
import net.summerfarm.wms.facade.product.dto.ProductSkuDetailDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class GoodsConverter {

    public static List<ProductSkuBaseDTO> convertToProductSkuBaseDTOList(List<ProductSkuBaseResp> respList) {
        if (CollectionUtils.isEmpty(respList)) {
            return Lists.newArrayList();
        }
        return respList.stream().map(GoodsConverter::convertToProductSkuBaseDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ProductSkuBaseDTO convertToProductSkuBaseDTO(ProductSkuBaseResp resp) {
        if (resp == null) {
            return null;
        }
        ProductSkuBaseDTO productSkuBaseDTO = new ProductSkuBaseDTO();
        productSkuBaseDTO.setSku(resp.getSku());
        productSkuBaseDTO.setTitle(resp.getTitle());
        productSkuBaseDTO.setSpecification(resp.getSpecification());
        return productSkuBaseDTO;
    }

    public static List<ProductSkuDetailDTO> convertToProductSkuDetailDTOList(List<ProductSkuDetailResp> respList) {
        if (CollectionUtils.isEmpty(respList)) {
            return Lists.newArrayList();
        }
        return respList.stream().map(GoodsConverter::convertToProductSkuDetailDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ProductSkuDetailDTO convertToProductSkuDetailDTO(ProductSkuDetailResp resp) {
        if (resp == null) {
            return null;
        }
        ProductSkuDetailDTO productSkuDetailDTO = new ProductSkuDetailDTO();
        productSkuDetailDTO.setSkuId(resp.getSkuId());
        productSkuDetailDTO.setSku(resp.getSku());
        productSkuDetailDTO.setTitle(resp.getTitle());
        productSkuDetailDTO.setSpecification(resp.getSpecification());
        productSkuDetailDTO.setSpecificationUnit(resp.getSpecificationUnit());
        productSkuDetailDTO.setFirstCategory(resp.getFirstCategory());
        productSkuDetailDTO.setSecondCategory(resp.getSecondCategory());
        productSkuDetailDTO.setCategoryName(resp.getCategoryName());
        return productSkuDetailDTO;
    }
}
