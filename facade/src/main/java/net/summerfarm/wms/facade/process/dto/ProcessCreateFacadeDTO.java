package net.summerfarm.wms.facade.process.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import net.summerfarm.manage.client.ding.enums.BizTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 钉钉审批工作流-流程实例参数
 */
@Data
@Builder
@AllArgsConstructor
public class ProcessCreateFacadeDTO implements Serializable {

    private static final long serialVersionUID = -676163025029515493L;

    /**
     * 发起审批的业务
     * @see BizTypeEnum
     */
    private Integer bizType;

    /**
     * 发起审批的业务数据id
     */
    private Long bizId;

    /**
     * 发起审批的系统用户id
     */
    private Integer adminId;

    /**
     * 审批流表单信息-必传
     */
    private List<ProcessFormFacadeDTO> dingdingForms;

    /**
     * 实例发起人的userid-钉钉中的用户id
     */
    private String originatorUserId;

    /**
     * 发起人所在的部门，如果发起人属于根部门，传-1。
     */
    private Long deptId;

    /**
     * 审批人userid列表，最大列表长度20。
     * 多个审批人用逗号分隔，按传入的顺序依次审批。
     */
    private String approvers;

    /**
     * 抄送人
     */
    private String ccList;

    public ProcessCreateFacadeDTO() {
    }
}
