package net.summerfarm.wms.facade.product.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @author: dongcheng
 * @date: 2023/10/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupplierStockProductResDTO {

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 商品的sku
     */
    private String sku;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

}
