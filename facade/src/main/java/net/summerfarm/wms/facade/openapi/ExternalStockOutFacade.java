package net.summerfarm.wms.facade.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.Configuration;
import com.kingdee.service.data.api.*;
import com.kingdee.service.data.entity.*;
import com.taobao.api.Constants;
import com.taobao.api.internal.util.TaobaoUtils;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.JsonXmlUtil;
import net.summerfarm.wms.common.util.PLSignUtils;
import net.summerfarm.wms.facade.config.KingdeeConnectConfig;
import net.summerfarm.wms.facade.openapi.dto.JPStockOutNoticeLocalReq;
import net.summerfarm.wms.facade.openapi.dto.PLApiResponse;
import net.summerfarm.wms.facade.openapi.util.KingdeeTokenUtil;
import net.summerfarm.wms.openapi.stockout.jp.req.JPStockOutNoticeReq;
import net.summerfarm.wms.openapi.stockout.pl.req.PLStockOutNoticeReq;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCreateNoticeReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.open.client.provider.event.BizEventInvokeProvider;
import net.xianmu.open.client.req.BizEventInvokerRequest;
import net.xianmu.open.client.resq.BizEventInvokeResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 外部出库调用
 * @date 2024/5/6
 */
@Component
@Slf4j
public class ExternalStockOutFacade {

    @DubboReference
    private BizEventInvokeProvider bizEventInvokeProvider;
    @Resource
    private KingdeeTokenUtil kingdeeTokenUtil;
    @Resource
    private KingdeeConnectConfig kingdeeConnectConfig;

    public void jpStockOutCreateNotice(JPStockOutNoticeReq jpStockOutNoticeReq, StockOutCreateNoticeReq stockOutCreateNoticeReq, String appKey, String secret, String customerId) {
        if (null == jpStockOutNoticeReq || null == stockOutCreateNoticeReq) {
            return;
        }
        log.info("转换绝配出库创建通知实体入参：{}", JSON.toJSONString(jpStockOutNoticeReq));
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(stockOutCreateNoticeReq.getStockOutNo());
        bizEventInvokerRequest.setEventType(ExternalCodeConstant.STOCK_OUT_CREATE_NOTICE);
        bizEventInvokerRequest.setSpiKey(ExternalCodeConstant.JP_APP_KEY);
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("method", "stockout.create");
        urlParams.put("timestamp", DateUtil.formatDate(new Date()));
        urlParams.put("format", "xml");
        urlParams.put("app_key", appKey);
        urlParams.put("v", "2.0");
        urlParams.put("sign_method", "md5");
        urlParams.put("customerId", customerId);
        String sign = "";
        String xml = "";
        try {
            JPStockOutNoticeLocalReq jpStockOutNoticeLocalReq = new JPStockOutNoticeLocalReq();
            jpStockOutNoticeLocalReq.setDeliveryOrder(jpStockOutNoticeReq.getDeliveryOrder());
            jpStockOutNoticeLocalReq.setOrderLines(jpStockOutNoticeReq.getOrderLines());
            xml = JsonXmlUtil.json2xml(JSON.toJSONString(jpStockOutNoticeLocalReq), JPStockOutNoticeLocalReq.class);
            log.info("转换xml结果：{}", xml);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        try {
            sign = TaobaoUtils.signTopRequest(urlParams, xml, secret, Constants.SIGN_METHOD_MD5);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        urlParams.put("sign", sign);
        bizEventInvokerRequest.setMediaType("xml");
        bizEventInvokerRequest.setData(xml);
        bizEventInvokerRequest.setUrlParams(urlParams);

        log.info("调用开放平台-绝配出库通知入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeResponseDubboResponse = bizEventInvokeProvider.asyncInvokeEvent(bizEventInvokerRequest);
        log.info("调用开放平台-绝配出库通知返回：{}", JSON.toJSONString(bizEventInvokeResponseDubboResponse));
    }

    public void plStockOutCreateNotice(PLStockOutNoticeReq plStockOutNoticeReq, StockOutCreateNoticeReq stockOutCreateNoticeReq, String appKey, String secret, String customerId) {
        if (null == plStockOutNoticeReq || null == stockOutCreateNoticeReq) {
            return;
        }
        log.info("转换普冷出库创建通知实体入参：{}", JSON.toJSONString(plStockOutNoticeReq));
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(stockOutCreateNoticeReq.getStockOutNo());
        bizEventInvokerRequest.setEventType(ExternalCodeConstant.STOCK_OUT_CREATE_NOTICE);
        bizEventInvokerRequest.setSpiKey(ExternalCodeConstant.PL_APP_KEY);
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("method", "stockout.create");
        urlParams.put("timestamp", DateUtil.formatDate(new Date()));
        urlParams.put("format", "json");
        urlParams.put("app_key", appKey);
        urlParams.put("sign_method", "md5");
        urlParams.put("customerId", customerId);
        String sign = "";
        String json = "";
        try {
            json = JSON.toJSONString(JSON.parseObject(JSON.toJSONString(plStockOutNoticeReq)));
            log.info("转换json结果：{}", json);
            sign = PLSignUtils.signTopRequest(urlParams, json, secret);
            log.info("生成sign结果：{}", sign);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        urlParams.put("sign", sign);
        bizEventInvokerRequest.setMediaType("json");
        bizEventInvokerRequest.setTimeout(6L);
        bizEventInvokerRequest.setData(JSON.parseObject(json));
        bizEventInvokerRequest.setUrlParams(urlParams);

        log.info("调用开放平台-普冷出库通知入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeResponseDubboResponse = bizEventInvokeProvider.syncInvokeEvent(bizEventInvokerRequest);
        log.info("调用开放平台-普冷出库通知返回：{}", JSON.toJSONString(bizEventInvokeResponseDubboResponse));
        validatePLResponseThrowBizEx(bizEventInvokeResponseDubboResponse.getData());
    }

    public String kdStockOutBillNotice(SalOutBoundSaveReq salOutBoundSaveReq) {
        log.info("推送三方金蝶保存销售出库单入参：{}", JSON.toJSONString(salOutBoundSaveReq));
        if (null == salOutBoundSaveReq) {
            log.info("推送三方金蝶保存销售出库单入参为空，返回");
            return null;
        }
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        SalOutBoundApi salOutBoundApi = new SalOutBoundApi(apiClient);
        try {
            SaveReply saveReply = salOutBoundApi.salOutBoundSave(salOutBoundSaveReq);
            log.info("推送三方金蝶保存销售出库单返回：{}", JSON.toJSONString(saveReply));
            if (null == saveReply || CollectionUtils.isEmpty(saveReply.getIds())) {
                throw new BizException("推送三方金蝶保存销售出库单返回id为空");
            }
            return saveReply.getIds().get(0);
        } catch (ApiException e) {
            log.error("推送三方金蝶保存销售出库单异常", e);
            throw new BizException("推送三方金蝶保存销售出库单异常");
        }
    }

    public String kdPurchaseOutBillNotice(RtnSaveReq rtnSaveReq) {
        log.info("推送三方金蝶保存采退出库单入参：{}", JSON.toJSONString(rtnSaveReq));
        if (null == rtnSaveReq) {
            log.info("推送三方金蝶保存采退出库单入参为空，返回");
            return null;
        }
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        PurRtnApi purRtnApi = new PurRtnApi(apiClient);
        try {
            SaveReply saveReply = purRtnApi.purRtnSave(rtnSaveReq);
            log.info("推送三方金蝶保存采退出库单返回：{}", JSON.toJSONString(saveReply));
            if (null == saveReply || CollectionUtils.isEmpty(saveReply.getIds())) {
                throw new BizException("推送三方金蝶保存采退出库单返回id为空");
            }
            return saveReply.getIds().get(0);
        } catch (ApiException e) {
            log.error("推送三方金蝶保存采退出库单异常", e);
            throw new BizException("推送三方金蝶保存采退出库单异常");
        }
    }


    public SalOrderDetailReply kdSalOrderDetail(String orderNo) {
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        SalOrderApi salOrderApi = new SalOrderApi(apiClient);
        try {
            SalOrderDetailReply salOrderDetailReply = salOrderApi.salOrderDetail(null, orderNo);
            log.info("推送三方金蝶获取销售订单信息返回：{}", JSON.toJSONString(salOrderDetailReply));
            if (null == salOrderDetailReply || StringUtils.isEmpty(salOrderDetailReply.getMaterialEntity())) {
                throw new BizException("推送三方金蝶获取销售订单信息为空");
            }
            return salOrderDetailReply;
        } catch (ApiException e) {
            log.error("推送三方金蝶获取销售订单信息异常", e);
            throw new BizException("推送三方金蝶获取销售订单信息异常");
        }
    }


    public SalOutBoundDetailReply kdStockOutBillDetail(String srcBillId) {
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        SalOutBoundApi salOutBoundApi = new SalOutBoundApi(apiClient);
        try {
            SalOutBoundDetailReply salOutBoundDetailReply = salOutBoundApi.salOutBoundDetail(srcBillId, null);
            log.info("推送三方金蝶获取销售出库单信息返回：{}", JSON.toJSONString(salOutBoundDetailReply));
            if (null == salOutBoundDetailReply || StringUtils.isEmpty(salOutBoundDetailReply.getMaterialEntity())) {
                throw new BizException("推送三方金蝶获取销售出库单信息为空");
            }
            return salOutBoundDetailReply;
        } catch (ApiException e) {
            log.error("推送三方金蝶获取销售出库单信息异常", e);
            throw new BizException("推送三方金蝶获取销售出库单信息异常");
        }
    }


    private void validatePLResponseThrowBizEx(BizEventInvokeResponse bizEventInvokeResponse) {
        if (null == bizEventInvokeResponse) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        if (StringUtils.isEmpty(bizEventInvokeResponse.getResponse())) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        PLApiResponse plApiResponse = JSONObject.parseObject(bizEventInvokeResponse.getResponse(), PLApiResponse.class);
        if (null == plApiResponse) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        if (!plApiResponse.isSuccess()) {
            String msg = null == plApiResponse.getResponse() ? "" : plApiResponse.getResponse().getMessage();
            throw new BizException("调用普冷失败，外部返回信息-" + msg);
        }
    }

}
