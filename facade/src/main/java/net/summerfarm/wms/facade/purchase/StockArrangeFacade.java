package net.summerfarm.wms.facade.purchase;

import net.summerfarm.pms.client.provider.ArrangeProvider;
import net.summerfarm.pms.client.req.PurchasesArrangeQueryReq;
import net.summerfarm.pms.client.resp.PurchasesArrangeResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2022/11/29  14:25
 */
@Component
public class StockArrangeFacade {
    @DubboReference
    private ArrangeProvider arrangeProvider;

    public Boolean queryStockArrangeState(Long taskId) {
        if (null == taskId) {
            throw new BizException("查询预约单任务id不能为空");
        }
        PurchasesArrangeQueryReq purchasesArrangeQueryReq = new PurchasesArrangeQueryReq();
        purchasesArrangeQueryReq.setStockTaskId(taskId.intValue());
        DubboResponse<PurchasesArrangeResp> purchasesArrangeRespDubboResponse = arrangeProvider.queryPurchasesArrange(purchasesArrangeQueryReq);
        if(purchasesArrangeRespDubboResponse.isSuccess()){
            PurchasesArrangeResp data = purchasesArrangeRespDubboResponse.getData();
            if (null == data) {
                return Boolean.FALSE;
            }
            if(Objects.equals(data.getState(),2)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }
}
