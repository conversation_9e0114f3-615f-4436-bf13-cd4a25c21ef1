package net.summerfarm.wms.facade.warehouse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.common.util.RedisCacheUtil;
import net.summerfarm.wms.common.util.TenantIdCheckUtil;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseStorageListQueryReq;
import net.summerfarm.wnc.client.enums.WarehouseSourceEnum;
import net.summerfarm.wnc.client.req.WarehouseStorageQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseStorageResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/04/18
 * 仓库服务 Facade
 */
@Slf4j
@Component
public class WarehouseWmsFacade {

    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    /**
     * 根据租户获取仓库id list
     * @param tenantId 租户id
     * @return 仓库id list
     */
    public List<Integer> getTenantWarehouseNoList(Long tenantId) {
        WarehouseStorageListQueryReq req = new WarehouseStorageListQueryReq();
        req.setTenantId(tenantId);
        if (TenantIdCheckUtil.isXm(tenantId)) {
            req.setWarehouseSource(WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode());
        } else {
            req.setWarehouseSource(WarehouseSourceEnum.SAAS_WAREHOUSE.getCode());
        }
        log.info("根据租户获取仓库信息入参：{}", JSON.toJSONString(req));
        DubboResponse<List<WarehouseStorageResp>> warehouseStorageList = warehouseStorageQueryProvider.queryWarehouseStorageList(req);
        log.info("根据租户获取仓库信息返回：{}", JSON.toJSONString(warehouseStorageList));
        if (Objects.isNull(warehouseStorageList) || Objects.isNull(warehouseStorageList.getData())) {
            return Lists.newArrayList();
        }
        return warehouseStorageList.getData()
                .stream()
                .map(WarehouseStorageResp::getWarehouseNo)
                .collect(Collectors.toList());
    }

    /**
     * 查询仓库名称
     * @param warehouseNo
     * @return
     */
    public String queryWarehouseName(Integer warehouseNo) {
        if (null == warehouseNo) {
            return "";
        }
        WarehouseStorageQueryReq warehouseStorageQueryReq = new WarehouseStorageQueryReq();
        warehouseStorageQueryReq.setWarehouseNo(warehouseNo);
        log.info("根据仓库编码获取仓库信息入参：{}", JSON.toJSONString(warehouseStorageQueryReq));
        DubboResponse<WarehouseStorageResp> dubboResponse = warehouseStorageQueryProvider.queryOneWarehouseStorage(warehouseStorageQueryReq);
        log.info("根据仓库编码获取仓库信息返回：{}", JSON.toJSONString(dubboResponse));
        if (null == dubboResponse || null == dubboResponse.getData()) {
            return "";
        }
        return dubboResponse.getData().getWarehouseName();
    }

    /**
     * 查询仓库名称
     * @param warehouseNoList 仓库编号列表
     * @return 返回仓库名称和编码的映射关系 key warehouseNo value warehouseName
     */
    public Map<Integer, String> queryWarehouseNameMap(List<Integer> warehouseNoList) {
        List<WarehouseStorageResp> storageRespList = queryWarehouseList(warehouseNoList);
        if (CollectionUtils.isEmpty(storageRespList)) {
            return Maps.newHashMap();
        }
        HashMap<Integer, String> warehouseMap = Maps.newHashMap();
        storageRespList.forEach(it -> {
            warehouseMap.put(it.getWarehouseNo(), it.getWarehouseName());
        });
        return warehouseMap;
    }

    /**
     * 查询仓库列表
     * @param warehouseNoList 仓库编号列表
     * @return
     */
    public List<WarehouseStorageResp> queryWarehouseList(List<Integer> warehouseNoList) {
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            return Lists.newArrayList();
        }
        WarehouseStorageListQueryReq warehouseStorageListQueryReq = new WarehouseStorageListQueryReq();
        warehouseStorageListQueryReq.setWarehouseNos(warehouseNoList);
        log.info("根据仓库编码获取仓库信息入参：{}", JSON.toJSONString(warehouseStorageListQueryReq));
        DubboResponse<List<WarehouseStorageResp>> dubboResponse = warehouseStorageQueryProvider.queryWarehouseStorageList(warehouseStorageListQueryReq);
        log.info("根据仓库编码获取仓库信息返回：{}", JSON.toJSONString(dubboResponse));
        if (null == dubboResponse || null == dubboResponse.getData()) {
            return Lists.newArrayList();
        }
        if (!dubboResponse.isSuccess()) {
            log.error("根据仓库编码获取仓库信息返回失败：{}", JSONObject.toJSONString(dubboResponse));
            return Lists.newArrayList();
        }
        return dubboResponse.getData();
    }

    /**
     * 查询仓库名称（缓存）
     * @param warehouseNo
     * @return
     */
    public String queryWarehouseNameForCache(Integer warehouseNo) {
        if (null == warehouseNo) {
            return "";
        }
        return redisCacheUtil.getCacheValueRefreshByHalfTime(
                "wms:warehouse:queryWarehouseNameForCache:" + warehouseNo,
                60,
                () -> queryWarehouseName(warehouseNo),
                String.class
        );
    }

    public Integer queryWarehouseNo(String warehouseName) {
        if (StringUtils.isBlank(warehouseName)) {
            return null;
        }
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNameForSummerfarm(warehouseName);
        if (null == warehouseStorageCenter) {
            return null;
        }
        return warehouseStorageCenter.getWarehouseNo();
    }

    public List<Integer> queryWarehouseCodeListByTenantId(Long tenantId) {
        tenantId = Objects.isNull(tenantId) ? WmsConstant.XIANMU_TENANT_ID : tenantId;
        Integer warehouseSource = tenantId > 1 ? WarehouseSourceEnum.SAAS_WAREHOUSE.getCode()
                : WarehouseSourceEnum.SUMMERFARM_WAREHOUSE.getCode();

        try {
            WarehouseStorageListQueryReq queryReq = new WarehouseStorageListQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setWarehouseSource(warehouseSource);
            DubboResponse<List<WarehouseStorageResp>> storageList = warehouseStorageQueryProvider.queryWarehouseStorageList(queryReq);
            if (Objects.isNull(storageList) || Objects.isNull(storageList.getData())) {
                log.warn("租户下未查询到关联仓库信息, tenantId:{}, warehouseSource:{}", tenantId, warehouseSource);
                throw new BizException("租户下未查询到关联仓库信息");
            }

            return storageList.getData().stream()
                    .map(WarehouseStorageResp::getWarehouseNo)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("请求仓库信息错误", e);
            throw new BizException("请求仓库信息错误");
        }
    }

    public List<Integer> queryPermissionWarehouseNoList(Long tenantId, Integer warehouseSource) {
        log.info("查询租户下有权限的库存仓列表 tenantId:{} warehouseSource:{}", tenantId, warehouseSource);
        // 参数校验
        if (Objects.isNull(tenantId) || Objects.isNull(warehouseSource)) {
            log.error("查询参数缺失 tenantId:{}, warehouseSource:{}", tenantId, warehouseSource);
            return Lists.newArrayList();
        }

        List<Integer> result = Lists.newArrayList();
        try {
            WarehouseStorageListQueryReq queryReq = new WarehouseStorageListQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setWarehouseSource(warehouseSource);
            DubboResponse<List<WarehouseStorageResp>> response = warehouseStorageQueryProvider.queryWarehouseStorageList(queryReq);
            if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
                return Lists.newArrayList();
            }
            result = response.getData().stream().map(WarehouseStorageResp::getWarehouseNo).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("请求仓库信息错误 queryWarehouseStorageList error tenantId:{}, warehouseSource:{}", tenantId, warehouseSource, e);
            ExceptionUtil.wrapAndThrow("请求仓库信息异常");
        }
        return result;
    }
}
