package net.summerfarm.wms.facade.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * @Date 2023/10/23 15:30
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StocktakingFinishDTO implements Serializable {


    private static final long serialVersionUID = 483818785774028505L;
    /**
     * 盘点单号
     */
    private String outStockTakingNo;
    /**
     * 库存仓
     */
    private Integer warehouseNo;
    /**
     * 明细
     */
    private List<StockTakingCommitDTO> commitList;

}
