package net.summerfarm.wms.facade.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Description
 * @Date 2023/10/23 14:49
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InBoundOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 5898507691790965828L;
    /**
     * sku
     */
    private String skuCode;
    /**
     * 客户sku
     */
    private String customerSkuCode;
    /**
     * 规格单位
     */
    private String customerSkuspecificationUnit;
    /**
     * 采购单号
     */
    private String outPurchaseNo;
    /**
     * 生产日期
     */
    private LocalDate productionDate;
    /**
     * 保质期
     */
    private LocalDate qualityDate;
    /**
     * 入库数量
     */
    private Integer quantity;
}
