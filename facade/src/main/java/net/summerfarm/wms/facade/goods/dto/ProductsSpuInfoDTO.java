package net.summerfarm.wms.facade.goods.dto;

import lombok.Data;
import net.summerfarm.wms.common.constant.WmsConstant;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class ProductsSpuInfoDTO implements Serializable {
    /** spuId **/
    private Long spuId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * spu自由编码
     */
    private String customSpuCode;

    /** 鲜沐spuId(满足鲜沐侧使用) **/
    private Long xmSpuId;
    /** spu编码(满足鲜沐侧使用) **/
    private String spu;

    /**
     * 主标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 三级类目
     **/
    private String thirdCategory;
    /**
     * 三级类目id
     */
    private Long categoryId;

    /**
     * sku明细
     */
    private List<ProductSkuDetailDTO> productSkuDetailDTOList;


    public String getSpu() {
        // 鲜沐
        if (WmsConstant.XIANMU_TENANT_ID.equals(this.getTenantId())) {
            return spu;
        }

        // 代仓
        if (!CollectionUtils.isEmpty(productSkuDetailDTOList)){
            ProductSkuDetailDTO productSkuDetail = productSkuDetailDTOList.get(0);
            return productSkuDetail == null || productSkuDetail.getSkuMapping() == null ?
                    null : productSkuDetail.getSkuMapping().getSpu();
        }

        return spu;
    }

    public Long getXmSpuId() {
        // 鲜沐
        if (WmsConstant.XIANMU_TENANT_ID.equals(this.getTenantId())) {
            return xmSpuId;
        }

        // 代仓
        if (!CollectionUtils.isEmpty(productSkuDetailDTOList)){
            ProductSkuDetailDTO productSkuDetail = productSkuDetailDTOList.get(0);
            return productSkuDetail == null || productSkuDetail.getSkuMapping() == null ?
                    null : productSkuDetail.getSkuMapping().getAgentSpuId();
        }

        return xmSpuId;
    }
}
