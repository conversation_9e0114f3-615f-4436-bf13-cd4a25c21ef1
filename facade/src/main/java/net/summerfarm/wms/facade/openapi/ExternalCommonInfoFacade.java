package net.summerfarm.wms.facade.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.Configuration;
import com.kingdee.service.data.api.StoreApi;
import com.kingdee.service.data.entity.StoreDetailRes;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.GuaranteeUnitEnum;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.enums.StorageLocationEnum;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.PLSignUtils;
import net.summerfarm.wms.facade.config.FeishuBotUrlConfig;
import net.summerfarm.wms.facade.config.KingdeeConnectConfig;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.openapi.dto.PLApiResponse;
import net.summerfarm.wms.facade.openapi.util.KingdeeTokenUtil;
import net.summerfarm.wms.openapi.goods.pl.req.PLSkuSyncReq;
import net.summerfarm.wms.openapi.goods.pl.req.SkuSync;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.open.client.provider.event.BizEventInvokeProvider;
import net.xianmu.open.client.req.BizEventInvokerRequest;
import net.xianmu.open.client.resq.BizEventInvokeResponse;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 外部基础信息调用
 * @date 2025/4/18
 */
@Component
@Slf4j
public class ExternalCommonInfoFacade {

    @DubboReference
    private BizEventInvokeProvider bizEventInvokeProvider;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private FeishuBotUrlConfig feishuBotUrlConfig;
    @Resource
    private KingdeeTokenUtil kingdeeTokenUtil;
    @Resource
    private KingdeeConnectConfig kingdeeConnectConfig;

    public void plSyncSkuInfo(List<String> skuCodeList, Long tenantId, String ownerCode, String appKey, String secret, String customerId) {
        if (CollectionUtils.isEmpty(skuCodeList)) {
            return;
        }
        Map<String, GoodsInfoDTO> skuGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(tenantId, skuCodeList);
        for (String skuCode : skuCodeList) {
            GoodsInfoDTO goodsInfoDTO = skuGoodsInfoDTOMap.get(skuCode);
            if (null == goodsInfoDTO) {
                throw new BizException("skuCode" + skuCode + "对应的商品信息为空");
            }
            // 未维护自有编码告警报错
            if (StringUtils.isEmpty(goodsInfoDTO.getCustomSkuCode())) {
                String message = "租户id：" + tenantId + "，skuid：" + goodsInfoDTO.getSkuId() + "未维护货品自有编码，无法推送三方系统，请及时维护";
                FeishuBotUtil.sendMarkdownMsgAndAtAll(feishuBotUrlConfig.getSaasExternalFailedFeiShuUrl(), message);
                throw new BizException("skuCode：" + skuCode + "，skuid：" + goodsInfoDTO.getSkuId() + "未维护自有编码");
            }
            PLSkuSyncReq plSkuSyncReq = new PLSkuSyncReq();
            SkuSync skuSync = new SkuSync();
            plSkuSyncReq.setOwnerCode(ownerCode);
            skuSync.setItemCode(goodsInfoDTO.getCustomSkuCode());
            skuSync.setItemName(goodsInfoDTO.getTitle());
            skuSync.setStockUnit(goodsInfoDTO.getSpecificationUnit());
            if (GuaranteeUnitEnum.DAY.getValue().equals(goodsInfoDTO.getGuaranteeUnit())) {
                skuSync.setShelfLife(goodsInfoDTO.getGuaranteePeriod() * 24);
            } else if (GuaranteeUnitEnum.MONTH.getValue().equals(goodsInfoDTO.getGuaranteeUnit())) {
                skuSync.setShelfLife(goodsInfoDTO.getGuaranteePeriod() * 30 * 24);
            } else if (GuaranteeUnitEnum.YEAR.getValue().equals(goodsInfoDTO.getGuaranteeUnit())) {
                skuSync.setShelfLife(goodsInfoDTO.getGuaranteePeriod() * 365 * 24);
            }
            skuSync.setPcs(goodsInfoDTO.getSpecification());
            if (null != goodsInfoDTO.getStorageLocation()) {
                skuSync.setTemperatureCondition(StorageLocationEnum.getDescByCode(goodsInfoDTO.getStorageLocation()));
            }
            plSkuSyncReq.setItem(skuSync);
            // 执行同步
            plExeSyncSkuInfo(plSkuSyncReq, appKey, secret, customerId);
        }
    }

    private void plExeSyncSkuInfo(PLSkuSyncReq plSkuSyncReq, String appKey, String secret, String customerId) {
        log.info("转换普冷sku同步实体入参：{}", JSON.toJSONString(plSkuSyncReq));
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(plSkuSyncReq.getItem().getItemCode());
        bizEventInvokerRequest.setEventType(ExternalCodeConstant.SKU_CREATE);
        bizEventInvokerRequest.setSpiKey(ExternalCodeConstant.PL_APP_KEY);
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("method", "singleitem.synchronize");
        urlParams.put("timestamp", DateUtil.formatDate(new Date()));
        urlParams.put("format", "json");
        urlParams.put("app_key", appKey);
        urlParams.put("sign_method", "md5");
        urlParams.put("customerId", customerId);
        String sign = "";
        String json = "";
        try {
            json = JSON.toJSONString(JSON.parseObject(JSON.toJSONString(plSkuSyncReq)));
            log.info("转换json结果：{}", json);
            sign = PLSignUtils.signTopRequest(urlParams, json, secret);
            log.info("生成sign结果：{}", sign);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        urlParams.put("sign", sign);
        bizEventInvokerRequest.setMediaType("json");
        bizEventInvokerRequest.setTimeout(6L);
        bizEventInvokerRequest.setData(JSON.parseObject(json));
        bizEventInvokerRequest.setUrlParams(urlParams);

        log.info("调用开放平台-普冷sku同步入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeResponseDubboResponse = bizEventInvokeProvider.syncInvokeEvent(bizEventInvokerRequest);
        log.info("调用开放平台-普冷sku同步返回：{}", JSON.toJSONString(bizEventInvokeResponseDubboResponse));
        validatePLResponseThrowBizEx(bizEventInvokeResponseDubboResponse.getData());
    }

    public String kdStockId(String stockNumber) {
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        StoreApi storeApi = new StoreApi(apiClient);
        try {
            StoreDetailRes storeDetailRes = storeApi.storeStoreDetail(null, stockNumber);
            log.info("推送三方金蝶获取仓库信息返回：{}", JSON.toJSONString(storeDetailRes));
            if (null == storeDetailRes || StringUtils.isEmpty(storeDetailRes.getId())) {
                throw new BizException("推送三方金蝶获取仓库信息返回id为空");
            }
            return storeDetailRes.getId();
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    private void validatePLResponseThrowBizEx(BizEventInvokeResponse bizEventInvokeResponse) {
        if (null == bizEventInvokeResponse) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        if (StringUtils.isEmpty(bizEventInvokeResponse.getResponse())) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        PLApiResponse plApiResponse = JSONObject.parseObject(bizEventInvokeResponse.getResponse(), PLApiResponse.class);
        if (null == plApiResponse) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        if (!plApiResponse.isSuccess()) {
            String msg = null == plApiResponse.getResponse() ? "" : plApiResponse.getResponse().getMessage();
            throw new BizException("调用普冷失败，外部返回信息-" + msg);
        }
    }

}
