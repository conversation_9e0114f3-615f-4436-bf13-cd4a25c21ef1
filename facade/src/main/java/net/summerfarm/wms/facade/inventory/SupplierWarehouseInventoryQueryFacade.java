package net.summerfarm.wms.facade.inventory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.inventory.dto.SupplierWarehouseInventory;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.supplierinventory.SupplierWarehouseInventoryQueryProvider;
import net.xianmu.inventory.client.supplierinventory.dto.req.SupplierInventoryReqDTO;
import net.xianmu.inventory.client.supplierinventory.dto.res.SupplierCoverOnlineResDTO;
import net.xianmu.inventory.client.supplierinventory.dto.res.SupplierInventoryDetailResDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class SupplierWarehouseInventoryQueryFacade {

    @DubboReference(check = false, timeout = 3000, protocol = "dubbo")
    private SupplierWarehouseInventoryQueryProvider supplyWarehouseInventoryQueryProvider;

    public SupplierWarehouseInventory queryQuantityByWnoAndSku(Integer warehouseNo, String skuCode){
        if (warehouseNo == null || skuCode == null){
            return null;
        }

        List<SupplierWarehouseInventory> supplierWarehouses = queryInventoryByWnoAndSku(warehouseNo, skuCode);
        SupplierWarehouseInventory result = new SupplierWarehouseInventory();
        result.setWarehouseNo(warehouseNo.longValue());
        result.setSkuCode(skuCode);
        result.setQuantity(supplierWarehouses.stream()
                .map(SupplierWarehouseInventory::getQuantity)
                .reduce( Integer::sum)
                .orElse(0));
        result.setSafeQuantity(supplierWarehouses.stream()
                .map(SupplierWarehouseInventory::getSafeQuantity)
                .reduce( Integer::sum)
                .orElse(0));
        return result;
    }

    public List<SupplierWarehouseInventory> queryInventoryByWnoAndSku(Integer warehouseNo, String skuCode){
        if (warehouseNo == null || skuCode == null){
            return new ArrayList<>();
        }

        SupplierInventoryReqDTO reqDTO = new SupplierInventoryReqDTO();
        reqDTO.setWarehouseNo(warehouseNo);
        reqDTO.setSkuCodeList(Arrays.asList(skuCode));
        DubboResponse<SupplierCoverOnlineResDTO> resDto = supplyWarehouseInventoryQueryProvider
                .queryInventoryByWnoAndSku(reqDTO);
        if (resDto == null || !resDto.isSuccess() || resDto.getData() == null ||
            resDto.getData().getSupplierInventoryDetailList() == null){
            return new ArrayList<>();
        }

        List<SupplierInventoryDetailResDTO> list = resDto.getData().getSupplierInventoryDetailList();

        List<SupplierWarehouseInventory> result = new ArrayList<>();
        for (SupplierInventoryDetailResDTO supplierInventoryDetailResDTO : list) {
            SupplierWarehouseInventory dto = new SupplierWarehouseInventory();

            dto.setSupplierId(supplierInventoryDetailResDTO.getSupplierId());
            dto.setWarehouseNo(supplierInventoryDetailResDTO.getWarehouseNo());
            dto.setSkuCode(supplierInventoryDetailResDTO.getSkuCode());
            dto.setQuantity(supplierInventoryDetailResDTO.getQuantity());
            dto.setSafeQuantity(supplierInventoryDetailResDTO.getSafeQuantity());

            result.add(dto);
        }

        return result;
    }
}
