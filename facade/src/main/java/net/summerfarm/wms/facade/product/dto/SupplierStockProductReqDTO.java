package net.summerfarm.wms.facade.product.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 提报商品查询条件
 *
 * @author: dongcheng
 * @date: 2023/10/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupplierStockProductReqDTO {

    /**
     * 仓库编码 必填
     */
    private Integer warehouseNo;

    /**
     * sku  必填
     */
    private List<String> skuList;
}
