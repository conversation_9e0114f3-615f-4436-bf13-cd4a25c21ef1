package net.summerfarm.wms.facade.openapi;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.openapi.dto.StocktakingFinishDTO;
import net.summerfarm.wms.facade.openapi.enums.EventTypeEnum;
import net.xianmu.open.client.provider.event.BizEventPushProvider;
import net.xianmu.open.client.req.BizEventPushRequest;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import java.util.Objects;

/**
 * @Description
 * @Date 2023/10/23 14:42
 * @<AUTHOR>
 */
@Component
@Slf4j
public class OutStocktakingFacade {

    @DubboReference
    private BizEventPushProvider bizEventPushProvider;

    /**
     * 盘点回传
     *
     * <AUTHOR>
     * @date 2023/10/23 15:39
     */
    public void noticeStocktakingFinishDTO(StocktakingFinishDTO finishDTO, Long tenantId) {
        if (Objects.isNull(finishDTO) || Objects.isNull(tenantId)) {
            log.error("代仓对接，外部盘点单提交回传外部系统失败，未查询到盘点结果信息 inPartDTO:{}, tenantId:{}", JSONUtil.toJsonStr(finishDTO), tenantId);
            return;
        }
        try {
            BizEventPushRequest request = new BizEventPushRequest();
            request.setBizId(finishDTO.getOutStockTakingNo());
            request.setEventType(EventTypeEnum.STOCK_TAKING_FINISH_NOTICE.getType());
            request.setAccountId(tenantId);
            request.setData(finishDTO);
            bizEventPushProvider.pushBizEvent(request);
            log.info("代仓对接，外部盘点单提交回传外部系统成功，request:{}", JSONUtil.toJsonStr(request));
        } catch (Exception e) {
            log.error("代仓对接，外部盘点单提交回传外部系统失败，finishDTO:{}", JSONUtil.toJsonStr(finishDTO), e);
        }
    }

}
