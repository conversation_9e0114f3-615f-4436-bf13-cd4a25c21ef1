package net.summerfarm.wms.facade.inventory.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/5
 */
@Data
public class OrderReleaseSkuDetailReqDTO implements Serializable {

    private static final long serialVersionUID = 3987656365390329452L;

    /**
     * 仓库编码，可选，输入则指定
     */
    private Long warehouseNo;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 释放数量
     */
    private Integer releaseQuantity;
}
