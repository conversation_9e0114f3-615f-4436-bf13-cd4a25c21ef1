package net.summerfarm.wms.facade.openapi;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.facade.openapi.dto.PurchaseBackFinishDTO;
import net.xianmu.open.client.provider.event.BizEventPushProvider;
import net.xianmu.open.client.req.BizEventPushRequest;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static net.summerfarm.wms.facade.openapi.enums.EventTypeEnum.PURCHASE_BACK_FINISH_NOTICE;

/**
 * @Description
 * @Date 2023/10/23 14:42
 * @<AUTHOR>
 */
@Component
@Slf4j
public class OutPurchaseBackFacade {

    @DubboReference
    private BizEventPushProvider bizEventPushProvider;

    /**
     * 采退回传
     *
     * <AUTHOR>
     * @date 2023/10/23 15:38
     */
    public void noticePurchaseBackFinish(PurchaseBackFinishDTO backFinishDTO, Long tenantId) {
        if (Objects.isNull(backFinishDTO) || Objects.isNull(tenantId)) {
            log.error("代仓对接，外部采购退货单 出库完结回传外部系统失败，未查询到出库信息 backFinishDTO:{}, tenantId:{}", JSONUtil.toJsonStr(backFinishDTO), tenantId);
            return;
        }
        try {
            BizEventPushRequest request = new BizEventPushRequest();
            request.setBizId(backFinishDTO.getOutPurchaseBackNo());
            request.setEventType(PURCHASE_BACK_FINISH_NOTICE.getType());
            request.setAccountId(tenantId);
            request.setData(backFinishDTO);
            bizEventPushProvider.pushBizEvent(request);
            log.info("代仓对接，外部采购退货出库单完成出库回传，request:{}", JSONUtil.toJsonStr(request));
        } catch (Exception e) {
            log.error("代仓对接，外部采购退货出库单完成出库回传外部系统失败，backFinishDTO:{}，tenantId:{}", JSONUtil.toJsonStr(backFinishDTO), tenantId, e);
        }
    }

}
