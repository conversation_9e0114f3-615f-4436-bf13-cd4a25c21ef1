package net.summerfarm.wms.facade.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kingdee.service.ApiClient;
import com.kingdee.service.ApiException;
import com.kingdee.service.Configuration;
import com.kingdee.service.data.api.PurInboundApi;
import com.kingdee.service.data.api.PurOrderApi;
import com.kingdee.service.data.api.SalInBoundApi;
import com.kingdee.service.data.entity.*;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.PLSignUtils;
import net.summerfarm.wms.facade.config.KingdeeConnectConfig;
import net.summerfarm.wms.facade.openapi.dto.PLApiResponse;
import net.summerfarm.wms.facade.openapi.util.KingdeeTokenUtil;
import net.summerfarm.wms.openapi.stockin.pl.req.PLStockInCancelReq;
import net.summerfarm.wms.openapi.stockin.pl.req.PLStockInNoticeReq;
import net.summerfarm.wms.openapi.stockin.xm.req.StockInCreateNoticeReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.open.client.provider.event.BizEventInvokeProvider;
import net.xianmu.open.client.req.BizEventInvokerRequest;
import net.xianmu.open.client.resq.BizEventInvokeResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 外部入库调用
 * @date 2025/4/18
 */
@Component
@Slf4j
public class ExternalStockInFacade {

    @DubboReference
    private BizEventInvokeProvider bizEventInvokeProvider;
    @Resource
    private KingdeeTokenUtil kingdeeTokenUtil;
    @Resource
    private KingdeeConnectConfig kingdeeConnectConfig;

    public void plStockInCreateNotice(PLStockInNoticeReq plStockInNoticeReq, StockInCreateNoticeReq stockInCreateNoticeReq, String appKey, String secret, String customerId) {
        if (null == plStockInNoticeReq || null == stockInCreateNoticeReq) {
            return;
        }
        log.info("转换普冷入库创建通知实体入参：{}", JSON.toJSONString(plStockInNoticeReq));
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(stockInCreateNoticeReq.getInboundTaskId());
        bizEventInvokerRequest.setEventType(ExternalCodeConstant.STOCK_IN_CREATE_NOTICE);
        bizEventInvokerRequest.setSpiKey(ExternalCodeConstant.PL_APP_KEY);
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("method", "entryorder.create");
        urlParams.put("timestamp", DateUtil.formatDate(new Date()));
        urlParams.put("format", "json");
        urlParams.put("app_key", appKey);
        urlParams.put("sign_method", "md5");
        urlParams.put("customerId", customerId);
        String sign = "";
        String json = "";
        try {
            json = JSON.toJSONString(JSON.parseObject(JSON.toJSONString(plStockInNoticeReq)));
            log.info("转换json结果：{}", json);
            sign = PLSignUtils.signTopRequest(urlParams, json, secret);
            log.info("生成sign结果：{}", sign);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        urlParams.put("sign", sign);
        bizEventInvokerRequest.setMediaType("json");
        bizEventInvokerRequest.setTimeout(6L);
        bizEventInvokerRequest.setData(JSON.parseObject(json));
        bizEventInvokerRequest.setUrlParams(urlParams);

        log.info("调用开放平台-普冷入库通知入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeResponseDubboResponse = bizEventInvokeProvider.syncInvokeEvent(bizEventInvokerRequest);
        log.info("调用开放平台-普冷入库通知返回：{}", JSON.toJSONString(bizEventInvokeResponseDubboResponse));
        validatePLResponseThrowBizEx(bizEventInvokeResponseDubboResponse.getData());
    }

    public void plStockInCancelNotice(PLStockInCancelReq plStockInCancelReq, String appKey, String secret, String customerId) {
        if (null == plStockInCancelReq) {
            return;
        }
        log.info("转换普冷入库取消通知实体入参：{}", JSON.toJSONString(plStockInCancelReq));
        BizEventInvokerRequest bizEventInvokerRequest = new BizEventInvokerRequest();
        bizEventInvokerRequest.setBizId(plStockInCancelReq.getOrderCode());
        bizEventInvokerRequest.setEventType(ExternalCodeConstant.STOCK_IN_CANCEL);
        bizEventInvokerRequest.setSpiKey(ExternalCodeConstant.PL_APP_KEY);
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("method", "order.cancel");
        urlParams.put("timestamp", DateUtil.formatDate(new Date()));
        urlParams.put("format", "json");
        urlParams.put("app_key", appKey);
        urlParams.put("sign_method", "md5");
        urlParams.put("customerId", customerId);
        String sign = "";
        String json = "";
        try {
            json = JSON.toJSONString(JSON.parseObject(JSON.toJSONString(plStockInCancelReq)));
            log.info("转换json结果：{}", json);
            sign = PLSignUtils.signTopRequest(urlParams, json, secret);
            log.info("生成sign结果：{}", sign);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        urlParams.put("sign", sign);
        bizEventInvokerRequest.setMediaType("json");
        bizEventInvokerRequest.setTimeout(6L);
        bizEventInvokerRequest.setData(JSON.parseObject(json));
        bizEventInvokerRequest.setUrlParams(urlParams);

        log.info("调用开放平台-普冷入库取消入参：{}", JSON.toJSONString(bizEventInvokerRequest));
        DubboResponse<BizEventInvokeResponse> bizEventInvokeResponseDubboResponse = bizEventInvokeProvider.syncInvokeEvent(bizEventInvokerRequest);
        log.info("调用开放平台-普冷入库取消返回：{}", JSON.toJSONString(bizEventInvokeResponseDubboResponse));
        validatePLResponseThrowBizEx(bizEventInvokeResponseDubboResponse.getData());
    }

    public String kdStockInBillNotice(SalInBoundSaveReq salInBoundSaveReq) {
        log.info("推送三方金蝶保存销退入库单入参：{}", JSON.toJSONString(salInBoundSaveReq));
        if (null == salInBoundSaveReq) {
            log.info("推送三方金蝶保存销退入库单入参为空，返回");
            return null;
        }
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        SalInBoundApi salInBoundApi = new SalInBoundApi(apiClient);
        try {
            SaveReply saveReply = salInBoundApi.salInBoundSave(salInBoundSaveReq);
            log.info("推送三方金蝶保存销退入库单返回：{}", JSON.toJSONString(saveReply));
            if (null == saveReply || CollectionUtils.isEmpty(saveReply.getIds())) {
                throw new BizException("推送三方金蝶保存销退入库单返回id为空");
            }
            return saveReply.getIds().get(0);
        } catch (ApiException e) {
            log.error("推送三方金蝶保存销退入库单异常", e);
            throw new BizException("推送三方金蝶保存销退入库单异常");
        }
    }

    public String kdPurchaseInBillNotice(InboundSaveReq inboundSaveReq) {
        log.info("推送三方金蝶保存采购入库单入参：{}", JSON.toJSONString(inboundSaveReq));
        if (null == inboundSaveReq) {
            log.info("推送三方金蝶保存采购入库单入参为空，返回");
            return null;
        }
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        PurInboundApi purInboundApi = new PurInboundApi(apiClient);
        try {
            SaveReply saveReply = purInboundApi.purInboundSave(inboundSaveReq);
            log.info("推送三方金蝶保存采购入库单返回：{}", JSON.toJSONString(saveReply));
            if (null == saveReply || CollectionUtils.isEmpty(saveReply.getIds())) {
                throw new BizException("推送三方金蝶保存采购入库单返回id为空");
            }
            return saveReply.getIds().get(0);
        } catch (ApiException e) {
            log.error("推送三方金蝶保存采购入库单异常", e);
            throw new BizException("推送三方金蝶保存采购入库单异常");
        }
    }

    public InboundDetailReply kdInboundDetail(String externalInboundNo) {
        log.info("推送三方金蝶获取采购入库单入参：{}", externalInboundNo);
        if (StringUtils.isEmpty(externalInboundNo)) {
            log.info("推送三方金蝶保存采购入库单入参为空，返回");
            return null;
        }
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        PurInboundApi purInboundApi = new PurInboundApi(apiClient);

        try {
            InboundDetailReply inboundDetailReply = purInboundApi.purInboundDetail(externalInboundNo, null);
            log.info("推送三方金蝶获取采购入库单返回：{}", JSON.toJSONString(inboundDetailReply));
            if (null == inboundDetailReply || StringUtils.isEmpty(inboundDetailReply.getMaterialEntity())) {
                throw new BizException("推送三方金蝶获取采购订单为空");
            }
            return inboundDetailReply;
        } catch (ApiException e) {
            log.error("推送三方金蝶获取采购订单异常", e);
            throw new BizException("推送三方金蝶获取采购订单异常");
        }
    }

    public OrderDetailReply kdPurchaseDetail(String purchaseNo) {
        log.info("推送三方金蝶获取采购订单入参：{}", purchaseNo);
        if (StringUtils.isEmpty(purchaseNo)) {
            log.info("推送三方金蝶保存采购入库单入参为空，返回");
            return null;
        }
        ApiClient apiClient = Configuration.getDefaultApiClient();
        apiClient.setClientId(kingdeeConnectConfig.getZcwClientId());
        apiClient.setClientSecret(kingdeeConnectConfig.getZcwClientSecret());
        apiClient.setAppToken(kingdeeTokenUtil.getToken(apiClient));
        PurOrderApi purOrderApi = new PurOrderApi(apiClient);

        try {
            OrderDetailReply orderDetailReply = purOrderApi.purOrderDetail(null, purchaseNo);
            log.info("推送三方金蝶获取采购订单返回：{}", JSON.toJSONString(orderDetailReply));
            if (null == orderDetailReply || StringUtils.isEmpty(orderDetailReply.getMaterialEntity())) {
                throw new BizException("推送三方金蝶获取采购订单为空");
            }
            return orderDetailReply;
        } catch (ApiException e) {
            log.error("推送三方金蝶获取采购订单异常", e);
            throw new BizException("推送三方金蝶获取采购订单异常");
        }
    }

    private void validatePLResponseThrowBizEx(BizEventInvokeResponse bizEventInvokeResponse) {
        if (null == bizEventInvokeResponse) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        if (StringUtils.isEmpty(bizEventInvokeResponse.getResponse())) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        PLApiResponse plApiResponse = JSONObject.parseObject(bizEventInvokeResponse.getResponse(), PLApiResponse.class);
        if (null == plApiResponse) {
            throw new BizException("调用普冷失败，外部返回为空");
        }
        if (!plApiResponse.isSuccess()) {
            String msg = null == plApiResponse.getResponse() ? "" : plApiResponse.getResponse().getMessage();
            throw new BizException("调用普冷失败，外部返回信息-" + msg);
        }
    }

}
