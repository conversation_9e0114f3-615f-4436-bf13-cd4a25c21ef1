package net.summerfarm.wms.facade.product;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.req.ProductSkuBasicReq;
import net.summerfarm.goods.client.req.ProductSkuListReq;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.goods.client.resp.ProductSkuBasicResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.wms.common.util.ExceptionUtil;
import net.summerfarm.wms.facade.product.converter.GoodsConverter;
import net.summerfarm.wms.facade.product.dto.ProductSkuBaseDTO;
import net.summerfarm.wms.facade.product.dto.ProductSkuBasic;
import net.summerfarm.wms.facade.product.dto.ProductSkuDetailDTO;
import net.summerfarm.wms.facade.product.input.QueryProductSkuBaseInfoInput;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/11/8
 */
@Slf4j
@Component
public class GoodsFacade {

    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;

    public List<ProductSkuBasic> selectSkuSubTypeListBySkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        log.info("selectSkuSubTypeListBySkuList:请求:{}", skuList);
        ProductSkuBasicReq basicReq = new ProductSkuBasicReq();
        basicReq.setSkuList(skuList);
        DubboResponse<List<ProductSkuBasicResp>> dubboResponse =
                productsSkuQueryProvider.selectSkuSubTypeList(basicReq);

        if (dubboResponse == null || !dubboResponse.isSuccess()) {
            log.info("查询goods-center返回数据失败");
            return Lists.newArrayList();
        }
        List<ProductSkuBasicResp> data = dubboResponse.getData();

        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        return data.stream().map(it -> {
            return ProductSkuBasic.builder()
                    .sku(it.getSku())
                    .subAgentType(it.getSubAgentType())
                    .build();
        }).collect(Collectors.toList());
    }

    public Map<String, ProductSkuBasic> selectSkuSubTypeMapBySkuList(List<String> skuList) {
        List<ProductSkuBasic> productSkuBasicList = selectSkuSubTypeListBySkuList(skuList);
        Map<String, ProductSkuBasic> productSkuBasicMap = productSkuBasicList.stream().collect(Collectors.toMap(ProductSkuBasic::getSku, Function.identity(), (a, b) -> a));
        return productSkuBasicMap;
    }

    public List<ProductSkuBaseDTO> queryProductSkuBaseInfo(QueryProductSkuBaseInfoInput input) {
        ExceptionUtil.checkAndThrow(input.getTenantId() != null, "租户id不能为空");
        if (CollectionUtils.isEmpty(input.getSkuIds()) && CollectionUtils.isEmpty(input.getSkuList())) {
            return Lists.newArrayList();
        }
        ProductSkuListReq req = new ProductSkuListReq();
        req.setTenantId(input.getTenantId());
        req.setSkuIds(input.getSkuIds());
        req.setSkuList(input.getSkuList());
        DubboResponse<List<ProductSkuBaseResp>> dubboResponse = productsSkuQueryProvider.selectProductSkuBaseList(req);
        ExceptionUtil.checkDubboResponse(dubboResponse);
        return GoodsConverter.convertToProductSkuBaseDTOList(dubboResponse.getData());
    }

    public List<ProductSkuDetailDTO> queryProductSkuDetailBySkuIds(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Lists.newArrayList();
        }
        DubboResponse<List<ProductSkuDetailResp>> dubboResponse = productsSkuQueryProvider.selectProductSkuDetailById(skuIds);
        ExceptionUtil.checkDubboResponse(dubboResponse);
        return GoodsConverter.convertToProductSkuDetailDTOList(dubboResponse.getData());
    }
}
