package net.summerfarm.wms.facade.goods.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductsPropertyValueDTO {

    /**
     * sku
     **/
    private String sku;

    /**
     * 警告时间
     */
    private Integer warnTime;

    /**
     * pd_id
     */
    private Integer pdId;

    /**
     * 属性id
     */
    private Integer productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    public String findPdId() {
        if (Objects.isNull(pdId)) {
            return null;
        }
        return pdId.toString();
    }

}
