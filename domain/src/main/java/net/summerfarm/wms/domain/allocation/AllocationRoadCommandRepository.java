package net.summerfarm.wms.domain.allocation;

import net.summerfarm.wms.domain.allocation.domainobject.InventoryAllocationRoadCostBatch;

import java.util.List;

public interface AllocationRoadCommandRepository {
    void batchInsert(List<InventoryAllocationRoadCostBatch> recordList);

    int updateRoadQuantityById(Integer changeRoadQuantity, Long id);

    void removeByIdList(List<Long> idList);

}
