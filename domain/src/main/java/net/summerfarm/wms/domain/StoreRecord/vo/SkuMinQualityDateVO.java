package net.summerfarm.wms.domain.StoreRecord.vo;

import lombok.Data;
import net.summerfarm.wms.common.util.DateUtil;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class SkuMinQualityDateVO implements Serializable {

    /**
     * sku编码列表
     */
    private String sku;


    /**
     * 保质期
     */
    private LocalDate minQualityDate;

    /**
     * 保质期long
     */
    private Long minShelfLife;

    public LocalDate getMinQualityDate() {
        if (this.minQualityDate == null && minShelfLife != null){
            this.minQualityDate = DateUtil.toLocalDate(minShelfLife);
        }

        return minQualityDate;
    }

}
