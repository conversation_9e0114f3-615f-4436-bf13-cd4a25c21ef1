package net.summerfarm.wms.domain.processingtask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProcessingTaskProduct implements Serializable {

    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 原料sku
     */
    private String materialSkuCode;
    /**
     * 原料sku名称
     */
    private String materialSkuName;
    /**
     * 原料sku重量
     */
    private BigDecimal materialSkuWeight;
    /**
     * 原料sku单位
     */
    private String materialSkuUnit;
    /**
     * 原料sku领料数量
     */
    private Integer materialSkuReceiveQuantity;
    /**
     * 原料sku归还数量
     */
    private Integer materialSkuRestoreQuantity;
    /**
     * 原料sku领料总重
     */
    private BigDecimal materialSkuReceiveWeight;
    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 成品sku名称
     */
    private String productSkuName;
    /**
     * 成品sku重量
     */
    private BigDecimal productSkuWeight;
    /**
     * 成品sku单位
     */
    private String productSkuUnit;
    /**
     * 成品sku规格描述
     */
    private String productSkuUnitDesc;
    /**
     * 成品sku所需数量
     */
    private Integer productSkuNeedQuantity;
    /**
     * 成品sku生产数量
     */
    private Integer productSkuFinishQuantity;
    /**
     * 成品总重
     */
    private BigDecimal productSkuSpecFinishWeight;
    /**
     * 废料损耗重量
     */
    private BigDecimal wasteLossWeight;
    /**
     * 规格损耗重量
     */
    private BigDecimal specLossWeight;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;

    /**
     * 任务状态：0未加工、1已加工
     */
    private Integer status;

    /**
     * 完成备注
     */
    private String finishRemark;

    /**
     * 原料sku单位描述-规格
     */
    private String materialSkuUnitDesc;

    /**
     * 原料模式，0-单原料，1-多原料
     */
    private Integer materialModel;

    /**
     * 原料配置快照
     */
    private String materialConfigJson;

    /**
     * 成品sku比例数量
     */
    private Integer productSkuRatioNum;
}
