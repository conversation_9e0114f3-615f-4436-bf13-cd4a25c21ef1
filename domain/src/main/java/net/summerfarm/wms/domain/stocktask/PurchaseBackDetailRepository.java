package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.PurchasesBackDetail;
import net.summerfarm.wms.domain.stocktask.domainobject.manage.PurchasesBackDetailVO;

import java.util.List;

public interface PurchaseBackDetailRepository {

    List<PurchasesBackDetail> selectByNo(String purchasesBackNo);

    List<PurchasesBackDetail> selectByBatch(String batch);

    Integer sumOutQuantity(String purchasesBackNo, String sku);

    Integer selectLockBatch(PurchasesBackDetailVO purchasesBackDetailVO);
}
