package net.summerfarm.wms.domain.materialManage.enums;


public enum MaterialBindingStatusEnum {

    VALID(1, "有效"),
    NOT_VALID(0, "无效"),
    ;

    private Integer code;
    private String desc;

    MaterialBindingStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(Integer code) {
        for (MaterialBindingStatusEnum statusEnum : MaterialBindingStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
