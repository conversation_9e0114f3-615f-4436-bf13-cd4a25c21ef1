package net.summerfarm.wms.domain.processingtask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessingTaskProductRecordUpdate implements Serializable {


    /**
     * 加工成品规格ID
     */
    private Long processingTaskProductSpecId;

    /**
     * 加工规格数量
     */
    private Integer submitSpecQuantity;

    /**
     * 加工重量
     */
    private BigDecimal submitWeight;
}
