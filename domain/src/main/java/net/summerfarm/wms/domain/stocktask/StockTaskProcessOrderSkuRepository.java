package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskProcessOrderSku;

import java.util.List;

/**
 * @author: dongcheng
 * @date: 2024/1/16
 */
public interface StockTaskProcessOrderSkuRepository {

    /**
     * 查询出库单详情信息
     *
     * @param stockTaskIdList        出库任务id列表
     * @param goodsSupplyOrderNoList 货品供应单号码
     * @return 返回出库单详情
     */
    List<StockTaskProcessOrderSku> findByStockTaskIdAndGoodsSupplyOrderNoList(List<Long> stockTaskIdList, List<String> goodsSupplyOrderNoList);
}
