package net.summerfarm.wms.domain.materialManage.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskDetailQueryParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:27
* @version 1.0
*
*/
public interface WmsMaterialTaskDetailQueryRepository {

    PageInfo<WmsMaterialTaskDetailEntity> getPage(WmsMaterialTaskDetailQueryParam param);

    WmsMaterialTaskDetailEntity selectById(Long id);

    List<WmsMaterialTaskDetailEntity> selectByCondition(WmsMaterialTaskDetailQueryParam param);

    List<WmsMaterialTaskDetailEntity> listByTaskCode(String materialTaskCode);
}