package net.summerfarm.wms.domain.stocktask.domainobject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @author: dongcheng
 * @date: 2023/7/21
 */
@Data
public class StockShipmentItemDetail implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "出库条目id")
    private Integer stockShipmentItemId;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    @ApiModelProperty(value = "实际调出数量")
    private Integer actualOutQuantity;

    @ApiModelProperty(value = "保质期")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate qualityDate;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate productionDate;

    @ApiModelProperty(value = "货位编号")
    private String glNo;

    @ApiModelProperty(value = "库位编码")
    private String cabinetCode;
}
