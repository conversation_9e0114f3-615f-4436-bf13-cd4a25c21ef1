package net.summerfarm.wms.domain.skucodetrace.param;

import com.alibaba.druid.sql.visitor.functions.Locate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Description: 唯一溯源码创建参数<br/>
 * date: 2024/8/9 11:13<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuBatchCodeTraceCreateCommandParam {

    /**
     * sku编码
     */
    private String sku;

    /**
     * 批次唯一码
     */
    private String skuBatchOnlyCode;

    /**
     * 入库任务id
     */
    private Long stockTaskStorageId;

    /**
     * 批次日期
     */
    private LocalDate batchDate;

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku数量
     */
    private Integer skuQuantity;

    /**
     * OFC采购供应单编号
     */
    private String psoNo;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

}
