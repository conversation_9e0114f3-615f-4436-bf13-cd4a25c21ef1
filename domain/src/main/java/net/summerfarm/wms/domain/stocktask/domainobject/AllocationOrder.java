package net.summerfarm.wms.domain.stocktask.domainobject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AllocationOrder implements Serializable {

    private static final long serialVersionUID = 2900829375020065663L;
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "调拨单号")
    private String listNo;

    @ApiModelProperty(value = "调拨发起人admin_id")
    private Integer createAdmin;

    @ApiModelProperty(value = "调拨发起人")
    private String createAdminName;

    @ApiModelProperty(value = "审核人admin_id")
    private Integer auditAdmin;

    @ApiModelProperty(value = "审核人")
    private String auditAdminName;

    @ApiModelProperty(value = "调出仓")
    private Integer outStore;

    @ApiModelProperty(value = "调出仓名")
    private String outStoreName;

    @ApiModelProperty(value = "调入仓")
    private Integer inStore;

    @ApiModelProperty(value = "调入仓名")
    private String inStoreName;

    @ApiModelProperty(value = "入库仓库负责人admin_id")
    private Integer inStoreAdmin;

    @ApiModelProperty(value = "入库仓库负责人")
    private String inStoreAdminName;

    @ApiModelProperty(value = "出库仓库负责人admin_id")
    private Integer outStoreAdmin;

    @ApiModelProperty(value = "出库仓库负责人")
    private String outStoreAdminName;

    /**
     * 调出仓负责人确认,0待确认，1通过2不通过
     */
    private Integer outStatus;

    /**
     * 调入仓负责人确认,0待确认，1通过2不通过
     */
    private Integer inStatus;

    @ApiModelProperty(value = "出库时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private LocalDateTime outTime;

    @ApiModelProperty(value = "期望出库时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private LocalDateTime expectOutTime;

    @ApiModelProperty(value = "期望入库时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private LocalDateTime expectTime;

    @ApiModelProperty(value = "调拨单状态")
    private Integer status;

    @ApiModelProperty(value = "到货时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private LocalDateTime inTime;

    @ApiModelProperty(value = "运输方式")
    private Integer transport;

    @ApiModelProperty(value = "物流单号")
    private String trackingNo;

    @ApiModelProperty(value = "添加时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private LocalDateTime addtime;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private LocalDateTime updatetime;


    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 调拨计划单号
     */
    private String planListNo;

    /**
     * 是否次日达：0、是  1、不是 （空表示次日达）
     */
    private Integer nextDayArrive;

    /**
     * 调拨计划id
     */
    private Long planListId;

    /**
     * 干线调度
     */
    private Integer trunkFlag;


    /**
     * 温区
     */
    private Integer storageLocation;

    /**
     * 租户
     */
    private Long tenantId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "步骤")
    private Integer process;

    @ApiModelProperty(value = "总货值")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "调拨类型:0每周调拨、1每两周调拨、2次日达")
    private Integer type;

    @ApiModelProperty(value = "销售参与：0.不参与1.参与")
    private Integer salePartake;

    @ApiModelProperty(value = "调拨周期:1每周、2每两周")
    private Integer cycleType;

    @ApiModelProperty(value = "销量日期")
    private LocalDate cycleDate;

    @ApiModelProperty(value = "调出仓销量预估天数")
    private Integer outDays;

    @ApiModelProperty(value = "调入仓销量预估天数")
    private Integer inDays;

    @ApiModelProperty(value = "调出仓历史销量天数")
    private Integer outHistoryDays;

    @ApiModelProperty(value = "调入仓历史销量天数")
    private Integer inHistoryDays;

    @ApiModelProperty(value = "配送时间：星期")
    private Integer logisticsTime;

    @ApiModelProperty(value = "物流信息配置id")
    private Integer configId;

    @ApiModelProperty(value = "销量预估天数")
    private Integer intervalDays;

    /**
     * 采购协作0.不参与1.参与
     */
    private Integer purchasePartake;

    /**
     * 需求销量开始时间
     */
    private LocalDateTime saleQuantityStart;

    /**
     * 需求销量结束时间
     */
    private LocalDateTime saleQuantityEnd;

    /**
     * 0:人工 1:调拨计划
     */
    private Integer originType;

}
