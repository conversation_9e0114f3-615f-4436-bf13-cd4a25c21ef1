package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class PurchasesBackDetail {

    @NotNull(groups = {Update.class})
    private Integer id;

    @NotNull(groups = {Update.class})
    private String purchasesBackNo;

    private String batch;

    private String sku;

    private Integer areaNo;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate qualityDate;

    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    private LocalDate productionDate;

    private BigDecimal cost;

    /**
     * 出库数量
     */
    @NotNull(groups = {Add.class, Update.class})
    @Min(value = 1, groups = {Add.class, Update.class})
    private Integer outQuantity;

    private BigDecimal totalCost;

    private Integer actualOutQuantity;

    private String glNo;

    /**
     * 0 未到货退货 1 已入库退货
     */
    private Integer type;

    /**
     * 供应商id
     */
    private Integer supplierId;
}
