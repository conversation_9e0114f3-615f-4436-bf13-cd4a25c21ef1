package net.summerfarm.wms.domain.initconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/3
 */
@Data
@Slf4j
@Configuration
@NacosConfigurationProperties(prefix = "produce.batch.switch", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class ProduceBatchSwitchConfig {

    private List<Integer> warehouseNos;

    /**
     * 是否已切换
     * @param warehouseNo
     * @return
     */
    public Boolean isSwitched(Integer warehouseNo) {
        if (CollectionUtils.isEmpty(warehouseNos)) {
            return false;
        }
        return warehouseNos.contains(warehouseNo);
    }

}
