package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description
 * @Date 2023/11/2 13:34
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskPurchasesBackDetailQuery {

    /**
     * 退货单号
     */
    private String purchasesBackNo;

    /**
     * 批次
     */
    private String batch;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 0 未到货退订 1 已入库退货
     */
    private Integer type;
}
