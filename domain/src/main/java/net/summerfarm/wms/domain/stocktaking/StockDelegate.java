package net.summerfarm.wms.domain.stocktaking;

public interface StockDelegate {
    /**
     * 获取生产批次id
     * @param itemId 盘点实例id
     * @param produceAt 生产日期
     * @param shelfLife 保质期
     * @param sku sku
     * @return
     */
    Long getProduceBatch(Long itemId, Long produceAt, Long shelfLife, String sku);

    /**
     * 获取库存库存id
     * @param itemId
     * @param produceAt
     * @param shelfLife
     * @param sku
     * @param cabinetCode
     * @return
     */
    Long getCabinetInventory(Long itemId, Long produceAt, Long shelfLife, String sku, String cabinetCode);
}
