package net.summerfarm.wms.domain.stockalert.domainobject.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryShelfLifeWarning {

    /**
     * 货品编码
     */
    private Long pdId;

    /**
     * SaaS skuId
     */
    private Long saasSkuId;

    /**
     * 仓库号
     */
    private Integer warehouseNo;

    /**
     * 保质期状态，1：正常、2：临保、3：过期
     */
    private Integer status;

    /**
     * sku的租户id
     */
    private Long skuTenantId;

    /**
     * 预警数据所属日期
     */
    private Integer dayTag;

    /**
     * 类目id集合
     */
    private List<Long> categoryIdList;

    /**
     * 货品启用状态，0：停用、1：启用
     */
    private Integer useFlag;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;
}
