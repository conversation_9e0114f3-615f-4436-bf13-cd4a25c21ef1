package net.summerfarm.wms.domain.stockarrage;

import net.summerfarm.wms.domain.stockarrage.domainobject.StockArrange;
import net.summerfarm.wms.domain.stockarrage.domainobject.StockArrangeItem;
import net.summerfarm.wms.domain.stockarrage.domainobject.StockArrangeItemDetail;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/12/14  10:58
 */
@Deprecated
public interface StockArrangeRepository {


    StockArrange selectStockArrange(Integer stockTaskId);

    List<StockArrangeItem> selectStockArrangeItem(Integer stockTaskId);


    List<StockArrangeItemDetail> selectStockArrangeItemdetail(List<Integer> stockTaskItemIdList);


}
