package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskOrderSku;

import java.util.List;

/**
 * @Description
 * @Date 2023/4/11 13:32
 * @<AUTHOR>
 */
public interface StockTaskOrderSkuRepository {

    /**
     * 插入出库任务订单明细
     *
     * <AUTHOR>
     * @date 2023/2/6 16:34
     * @param orderSku 任务订单明细
     */
    void insertStockTaskOrderSku(StockTaskOrderSku orderSku);

    /**
     * 批量插入出库任务订单明细
     *
     * @param orderSkuList 任务订单明细列表
     */
    void batchInsertStockTaskOrderSku(List<StockTaskOrderSku> orderSkuList);

    /**
     * 根据外部订单号查询明细信息
     *
     * <AUTHOR>
     * @date 2023/11/27 11:01
     * @param outOrderNo 外部单据
     */
    List<StockTaskOrderSku> selectListByOutOrderNo(String outOrderNo);

    /**
     * 根据外部订单号和sku查询明细信息
     * @param outOrderNo
     * @param sku
     * @return
     */
    List<StockTaskOrderSku> selectListByOutOrderNoAndSku(String outOrderNo, String sku);
}
