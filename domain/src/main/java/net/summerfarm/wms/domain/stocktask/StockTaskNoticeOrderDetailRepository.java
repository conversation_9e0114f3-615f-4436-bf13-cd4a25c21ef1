package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderDetail;

import java.util.List;

/**
 * @Description
 * @Date 2023/4/10 15:46
 * @<AUTHOR>
 */
public interface StockTaskNoticeOrderDetailRepository {

    /**
     * 创建通知单明细
     *
     * <AUTHOR>
     * @date 2023/4/21 15:15
     * @param noticeOrderDetail 通知单明细
     * @return java.lang.Long
     */
    Long createNoticeOrderDetail(StockTaskNoticeOrderDetail noticeOrderDetail);

    /**
     * 批量创建通知单明细
     *
     * <AUTHOR>
     * @date 2023/4/21 15:15
     * @param noticeOrderDetailList
     */
    void batchCreateNoticeOrderDetail(List<StockTaskNoticeOrderDetail> noticeOrderDetailList);

    /**
     * 批量查询通知单列表详情
     *
     * <AUTHOR>
     * @date 2023/4/21 15:15
     * @param noticeOrderIdList 通知单编码列表
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderDetail>
     */
    List<StockTaskNoticeOrderDetail> batchQueryNoticeOrderDetail(List<Long> noticeOrderIdList);

    /**
     * 通过通知单查询明细
     * @param noticeOrderId
     * @return
     */
    List<StockTaskNoticeOrderDetail> listByNoticeOrderId(Long noticeOrderId);

    /**
     * 查询未完成出库的通知单明细
     *
     * @param noticeOrderIdList 出库通知单id
     * @param sku               sku编码
     * @return 返回出库通知单明细内容
     */
    List<StockTaskNoticeOrderDetail> queryOrderDetailByOrderIdAndSku(List<Long> noticeOrderIdList, String sku);

    int deleteByNoticeId(Long noticeId);

    int updateCustomerSkuCode(String customerSkuCode, Long id);

    List<StockTaskNoticeOrderDetail> findByNoticeOrderIdListAndSku(List<Long> noticeOrderIdList, String sku);
}
