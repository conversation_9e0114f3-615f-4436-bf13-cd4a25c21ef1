package net.summerfarm.wms.domain.pickuptask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 投线任务entity
 * <AUTHOR>
 * @date 2023/02/18
 */
@Data
public class PickUpTask implements Serializable {

    /**
     * 库存仓编码
     */
    private Integer warehouseNo;

    /**
     * 城配仓编码
     */
    private Integer storeNo;

    /**
     * 客户名称
     */
    private String adminName;

    /**
     * 客户id
     */
    private Integer adminId;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * SKU单位
     */
    private String skuUnit;

    /**
     * SKU重量
     */
    private BigDecimal skuWeight;

    /**
     * SKU规格描述
     */
    private String skuSpecDesc;

    /**
     * SKU温区
     */
    private Integer storageLocation;

    /**
     * SKU类型 0自营 1代仓
     */
    private Integer skuType;

    /**
     * 配送时间
     */
    private Date deliveryTime;

    /**
     * 配送类型
     */
    private Boolean deliveryType;

    /**
     * 投线类型 1城配仓 2客户清单
     */
    private Integer pickUpType;

    /**
     * 进口/国产
     */
    private Integer isDomestic;

    /**
     * 包装
     */
    private String packing;
}
