package net.summerfarm.wms.domain.stocktask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderQuery;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * @Date 2023/4/10 12:01
 * @<AUTHOR>
 */
public interface StockTaskNoticeOrderRepository {

    /**
     * 创建出库通知单
     *
     * @param stockTaskNoticeOrder 通知单
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2023/4/21 15:16
     */
    Long createStockTaskNoticeOrder(StockTaskNoticeOrder stockTaskNoticeOrder);

    /**
     * 取消出库通知单
     *
     * @param stockTaskNoticeOrder s
     */
    void cancelStockTaskNoticeOrder(StockTaskNoticeOrder stockTaskNoticeOrder);

    /**
     * 预取消出库通知单
     *
     * @param stockTaskNoticeOrder s
     */
    void preCancelStockTaskNoticeOrder(StockTaskNoticeOrder stockTaskNoticeOrder);

    /**
     * 批量查询出库通知单
     *
     * @param noticeOrderIdList 通知单编码
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder>
     * <AUTHOR>
     * @date 2023/4/21 15:16
     */
    List<StockTaskNoticeOrder> batchQueryNoticeOrder(List<Long> noticeOrderIdList);

    /**
     * findById
     *
     * @param id
     * @return
     */
    StockTaskNoticeOrder findById(Long id);

    /**
     * 根据货品供应单查询通知单
     *
     * @param goodsSupplyNo 货品供应单
     * @return net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder
     * <AUTHOR>
     * @date 2023/4/21 15:16
     */
    StockTaskNoticeOrder findByGoodsSupplyNo(String goodsSupplyNo);

    /**
     * 根据货品供应单列表查询出库通知单信息
     *
     * @param goodsSupplyNoList 货品供应单号列表
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrder>
     * <AUTHOR>
     * @date 2023/5/26 15:48
     */
    List<StockTaskNoticeOrder> findByGoodsSupplyNoList(List<String> goodsSupplyNoList);

    /**
     * pageQuery
     *
     * @param stockTaskNoticeOrderQuery
     * @return
     */
    PageInfo<StockTaskNoticeOrder> pageQuery(StockTaskNoticeOrderQuery stockTaskNoticeOrderQuery);

    /**
     * 完成出库通知单
     *
     * @param orderIdList 通知单ID
     * @param stockTaskId 任务编码
     * <AUTHOR>
     * @date 2023/4/21 15:17
     */
    void finishNoticeOrder(List<Long> orderIdList, Long stockTaskId);

    /**
     * 查询未生成出库任务的通知单
     *
     * @param warehouseNo      仓库编号
     * @param storeNo          城配仓
     * @param outOrderTypeList 通知单类型列表
     * @param supplyMode       货品供应单模式
     * @param exceptTime       期望入库时间
     * @param noticeSkuFlag    通知单sku标记
     * @param pushMode         推单模式
     * @param leCloseTime      小于等于截单时间
     */
    List<StockTaskNoticeOrder> findUnGenOutTaskNoticeOrder(Integer warehouseNo,
                                                           Integer storeNo,
                                                           List<Integer> outOrderTypeList,
                                                           Integer supplyMode,
                                                           LocalDateTime exceptTime,
                                                           Integer noticeSkuFlag,
                                                           Integer pushMode,
                                                           LocalDateTime leCloseTime);

    /**
     * 查询未生成过出库任务的数量
     *
     * @param warehouseNo 仓库号
     * @param exceptTime  预计出库时间
     * @param skus        sku列表
     * @return map<sku, num></>
     */
    Map<String, Integer> mapUnUseNoticeNum(Long warehouseNo, LocalDate exceptTime, List<String> skus,
                                           List<Integer> supplyModes);

    /**
     * 查询未生成过出库任务的数量 - 大于等于指定的预计出库时间
     *
     * @param warehouseNo 仓库号
     * @param exceptTime  预计出库时间
     * @param skus        sku列表
     * @return map<sku, num>
     */
    Map<String, Integer> mapUnUseNoticeNumAfterSomeDay(Long warehouseNo, LocalDate exceptTime, List<String> skus);

    /**
     * 批量更新出库通知单状态信息
     *
     * @param noticeOrderIdList 出库通知单id列表
     * @param status            出库通知单状态信息
     * @return 返回出库通知单更新条数
     */
    int updateStatusByIdList(List<Long> noticeOrderIdList, Integer status);

    /**
     * 完成生成出库任务
     *
     * @param orderIdList 通知单ID
     * @param stockTaskId 任务编码
     */
    void finishGenOutTaskNoticeOrder(List<Long> orderIdList, Long stockTaskId);

    /**
     * 查询出库通知单信息
     *
     * @param warehouseNo 仓库编号
     * @param storeNo     城配仓编号
     * @param stockTaskId 任务id
     * @return 返回查询到出库通知单信息
     */
    List<StockTaskNoticeOrder> findByStockTaskId(Integer warehouseNo, Integer storeNo, Integer stockTaskId);

    /**
     * 查询出库通知单信息
     *
     * @param warehouseNo 仓库编号
     * @param storeNo     城配仓编号
     * @param exceptTime  预计出库时间
     * @return 返回查询到出库通知单信息
     */
    List<StockTaskNoticeOrder> findByExceptTime(Integer warehouseNo, Integer storeNo, LocalDateTime exceptTime);

    /**
     * 查询出库通知单根据出库任务id
     *
     * @param stockTaskId 出库任务id
     * @return
     */
    List<StockTaskNoticeOrder> findByStockTaskId(Integer stockTaskId);

    /**
     * 货品供应供应单号
     *
     * @param goodsSupplyNoList 货品供应单号列表
     * @return 返回查询到出库通知单信息
     */
    List<StockTaskNoticeOrder> findAllNoticeByGoodsSupplyNoList(List<String> goodsSupplyNoList);

    /**
     * 按照任务id查询出库通知单列表
     *
     * @param idList id列表
     * @param status 出库通知单状态
     * @return 返回出库通知单
     */
    List<StockTaskNoticeOrder> findByIdListAndStatus(List<Long> idList, Integer status);

    /**
     * 批量更新外部通知状态
     * @param noticeOrderIdList
     * @param externalStatus
     * @return
     */
    int updateExternalStatusByIdList(List<Long> noticeOrderIdList, Integer externalStatus);

    /**
     * 更新出库单状态为创建出库中
     *
     * @param noticeOrderIdList 出库通知单id列表
     * @return 返回出库通知单更新条数
     */
    int updateInitToCreatingStatusByIdList(List<Long> noticeOrderIdList);

    /**
     * 查询货品供应单信息
     *
     * @param stockTaskId 出库任务id
     * @param outOrderNo  外部单号
     * @return 返回货品供应单信息
     */
    List<StockTaskNoticeOrder> findByStockTaskIdAndOrderNo(Integer stockTaskId, String outOrderNo);

    /**
     * 根据出库任务id列表查询出库通知单
     * @param stockTaskIdList 出库任务id列表
     * @return 返回出库通知单
     */
    List<StockTaskNoticeOrder> findByStockTaskIdList(List<Long> stockTaskIdList);

    /**
     * 根据仓库号和出库时间查询待出城配仓编号
     * @param popWarehouseStorageNoList 仓库号列表
     * @param deliveryTime 出库时间
     * @return 返回仓库号和待出城配仓编号
     */
    Map<Integer, List<Integer>> findWaitStateStoreNoByWarehouseNosAndDeliveryTime(List<Integer> popWarehouseStorageNoList, LocalDate deliveryTime);

    /**
     * 查询未生成店铺出库任务的通知单
     *  @param exceptTime       期望入库时间
     * @param shopIdList       店铺id列表
     * @param tenantId         租户id
     */
    List<StockTaskNoticeOrder> findUnGenOutShopTaskNoticeOrder(LocalDateTime exceptTime, List<Long> shopIdList, Long tenantId);
}
