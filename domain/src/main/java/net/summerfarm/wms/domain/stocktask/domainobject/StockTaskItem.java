package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ct
 * create at:  2022/12/13  16:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskItem {

    private Integer id;

    private Integer stockTaskId;

    private String sku;

    private Integer quantity;

    private Integer actualQuantity;

    private String glNo;

    private Integer oldQuantity;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 外部自有编码
     */
    private String customerSkuCode;
}
