package net.summerfarm.wms.domain.stockalert.param.query;

import lombok.Data;

import java.util.List;

@Data
public class StoreAlertCountByStatusQueryParam {
    /**
     * sku租户id
     */
    private Long skuTenantId;
    /**
     * 数据日期
     */
    private Integer dayTag;
    /**
     * 货品启用状态，0：停用、1：启用
     */
    private Integer useFlag;
    /**
     * 状态列表
     */
    private List<Integer> statusList;
}
