package net.summerfarm.wms.domain.processingtask.domainService;

import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigUpdateAggregate;

public interface ProcessingConfigDomainService {

    /**
     * 新增
     *
     * @param createAggregate
     * @return
     */
    Long create(ProcessingConfigCreateAggregate createAggregate);

    /**
     * 更新
     *
     * @param updateAggregate
     * @return
     */
    Long update(ProcessingConfigUpdateAggregate updateAggregate);

    /**
     * 作废
     *
     * @param id            加工规则配置id
     * @param operatorName
     * @return 操作结果
     */
    Long invalid(Long id, String operatorName);
}
