package net.summerfarm.wms.domain.transaction;

import net.summerfarm.wms.domain.transaction.domainobject.AreaStoreInventoryTransaction;

import java.util.List;

public interface AreaStoreInventoryTransactionRepository {

    /**
     * 保存areastore事务
     *
     * @param transaction t
     */
    void saveAreaStoreInventoryTransaction(AreaStoreInventoryTransaction transaction);

    void batchSaveAreaStoreInventoryTransaction(List<AreaStoreInventoryTransaction> transaction);

    /**
     * 更改areastore事务状态
     *
     * @param transaction
     */
    void changeAreaStoreInventoryTransaction(AreaStoreInventoryTransaction transaction);

    /**
     * 变更事务消息状态
     * @param ids
     * @param status
     */
    void changeAreaStoreInventoryTransactionStatus(List<Long> ids, Integer status);

    /**
     * 查找araestore事务
     *
     * @param warehouseNo   仓库号
     * @param bizId         业务id
     * @param bizType       业务类型
     * @param inventoryType 库存类型
     * @return
     */
    AreaStoreInventoryTransaction findAreaStoreInventoryNormalTransaction(Long warehouseNo, String bizId, String bizType,
                                                                    String inventoryType, String sku);

    /**
     * 查询事务记录
     * @param warehouseNo
     * @param bizId
     * @param bizType
     * @param inventoryType
     * @return
     */
    List<AreaStoreInventoryTransaction> findAreaStoreInventoryNormalTransaction(Long warehouseNo, String bizId, String bizType, String inventoryType);
}
