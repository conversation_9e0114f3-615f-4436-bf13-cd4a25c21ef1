package net.summerfarm.wms.domain.crosswarehouse.repository;

import net.summerfarm.wms.domain.crosswarehouse.domainobject.CrossWarehouseSortInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @author: dongcheng
 * @date: 2023/10/7
 */
public interface CrossWarehouseSortInfoRepository {

    /**
     * 查询没有分配完成的越库分拣明细
     *
     * @param warehouseNo 仓库编号
     * @param psoNo       批次号
     * @param sku         商品sku
     */
    List<CrossWarehouseSortInfo> findNoAllocationComplete(Long warehouseNo, String psoNo, String sku);

    /**
     * 查询没有分配完成的越库分拣明细
     *
     * @param warehouseNo 仓库编号
     * @param psoNo       批次号
     * @param skuList     商品sku
     */
    List<CrossWarehouseSortInfo> findNoAllocationComplete(Long warehouseNo, String psoNo, List<String> skuList);

    /**
     * 查询投线分配完成可以分配上架的明细
     *
     * @param warehouseNo 仓库编号
     * @param psoNo       批次号
     * @param skuList     商品sku
     */
    List<CrossWarehouseSortInfo> findCanAllocationShelveInfo(Long warehouseNo, String psoNo, List<String> skuList);

    /**
     * 查询没有分配完成的越库分拣明细
     *
     * @param warehouseNo 仓库编号
     * @param psoNo       批次号
     * @param skuList     商品sku
     * @return key sku value 明细列表
     */
    Map<String, List<CrossWarehouseSortInfo>> findNoAllocationCompleteMap(Long warehouseNo, String psoNo, List<String> skuList);

    /**
     * 更新分配数量信息
     *
     * @param id                  数据id值
     * @param newGenerateQuantity 新的生成数量
     * @param oldGenerateQuantity 旧的生成数量
     * @return 返回更新是否成功行数
     */
    int updateAllocationQuantityById(Long id, Integer oldGenerateQuantity, Integer newGenerateQuantity);

    /**
     * 更新上架分配数量信息
     *
     * @param id                 数据id值
     * @param allocationQuantity 分配数量
     * @return 返回更新是否成功行数
     */
    int updateAllocationShelveQuantityById(Long id, Integer allocationQuantity);

    /**
     * 查询越库分拣明细简单信息
     *
     * @param psoNoList         ofc的批次号
     * @param saleOrderNoList   销售单号
     * @param skuCodeList       sku编码信息
     * @param fulfillmentNoList 履约单号
     */
    List<CrossWarehouseSortInfo> findSimpleByUnique(List<String> psoNoList, List<String> saleOrderNoList,
                                                    List<String> skuCodeList, List<String> fulfillmentNoList);

    /**
     * 批量保存
     *
     * @param sortInfoList 待批量保存列表
     */
    void batchSave(List<CrossWarehouseSortInfo> sortInfoList);

    /**
     * 通过主键id查询分拣明细
     *
     * @param id 主键信息
     * @return 返回分拣明细
     */
    CrossWarehouseSortInfo findById(Long id);

    /**
     * 通过采购供应单号查询越库分拣明细
     *
     * @param psoNo 采购供应单号
     * @return 返回分拣明细列表
     */
    List<CrossWarehouseSortInfo> findByPsoNo(String psoNo);

    /**
     * 查询越库分拣明细
     *
     * @param fulfillmentNo 履约单号 必填
     * @param psoNo         采购批次号 必填
     * @param skuCode       sku编码 必填
     * @return 返回分拣明细
     */
    List<CrossWarehouseSortInfo> findByFulfillmentNoAndPsoAndSku(String fulfillmentNo, String psoNo, String skuCode);

    /**
     * 执行退单修改总数量
     *
     * @param id                分拣明细id
     * @param oldTotalQuantity  旧的可分配总数量
     * @param afterSaleQuantity 售后退单数量
     */
    void updateTotalQuantityById(Long id, Integer oldTotalQuantity, Integer afterSaleQuantity);

    /**
     * 查询分拣明细
     *
     * @param psoNo   采购供应单号 必填
     * @param skuList sku列表 必填
     */
    List<CrossWarehouseSortInfo> findByPsoNoAndSkuList(String psoNo, List<String> skuList);

    /**
     * 执行退单修改总数量
     *
     * @param id                  分拣明细id
     * @param addGenerateQuantity 新增分配的数量
     * @param addShelveQuantity   新增上架的数量
     */
    void updateQuantityById(Long id, Integer addGenerateQuantity, Integer addShelveQuantity);

    /**
     * 查询分拣明细
     *
     * @param fulfillmentNo 履约单号 必填
     * @param skuCodeList   sku列表 必填
     */
    List<CrossWarehouseSortInfo> findByFulfillmentNoAndSkuCodeList(String fulfillmentNo, List<String> skuCodeList);

    /**
     * 更新备注信息
     * @param id 主键id
     * @param remark 备注信息
     */
    void updateRemarkById(Long id, String remark);

    /**
     * 更新城配仓编号
     * @param id 主键id
     * @param newStoreNo 新的城配仓编号
     */
    void updateStoreNoById(Long id, Integer newStoreNo);

    /**
     * 查询创建时间在指定时间范围内的非空备注数据
     * @param startCreateTime 开始时间
     * @param endCreateTime 结束时间
     * @return 返回非空备注数据
     */
    List<CrossWarehouseSortInfo> findNotNullRemarkDataByCreateTimeBetween(LocalDateTime startCreateTime, LocalDateTime endCreateTime);
}
