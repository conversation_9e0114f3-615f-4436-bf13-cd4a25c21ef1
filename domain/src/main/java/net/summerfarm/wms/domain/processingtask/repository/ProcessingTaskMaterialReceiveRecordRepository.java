package net.summerfarm.wms.domain.processingtask.repository;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ProcessingTaskMaterialReceiveRecordRepository {
    void batchCreate(List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList);

    ProcessingTaskMaterialReceiveRecord queryById(Long materialReceiveId);

    void updateWasteLossWeight(Long id, BigDecimal wasteLossWeight, String currentUserName);

    void addRestoreQuantity(Long materialReceiveId, Integer materialSkuRestoreQuantity, BigDecimal materialSkuRestoreWeight, String currentUserName);

    List<ProcessingTaskMaterialReceiveRecord> queryByMaterialIdList(List<Long> materialIdList);

    List<ProcessingTaskMaterialReceiveRecord> queryByProcessingTaskProductId(Long processingTaskProductId);

    void updateSpecLoseWeight(Long id, BigDecimal specLossWeight, String currentUserName);

    /**
     * 根据加工任务成品id查询原料领用明细
     * @param processingTaskProductIdList 加工任务成品id
     * @return 原料领用明细列表
     */
    Map<Long, List<ProcessingTaskMaterialReceiveRecord>> mapByProductIdsGroupMaterialId(List<Long> processingTaskProductIdList);

    Long countByProcessingTaskCode(String processingTaskCode);

    Long countByProcessingTaskProductId(Long processingTaskProductId);
}
