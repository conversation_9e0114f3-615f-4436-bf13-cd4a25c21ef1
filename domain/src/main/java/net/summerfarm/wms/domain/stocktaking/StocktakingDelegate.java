package net.summerfarm.wms.domain.stocktaking;

import net.summerfarm.wms.domain.stocktaking.domainobject.StockTakingItem;
import net.summerfarm.wms.domain.stocktaking.domainobject.Stocktaking;
import net.summerfarm.wms.domain.stocktaking.domainobject.StocktakingItemBatch;

import java.util.List;
import java.util.Map;

/**
 * 盘点代理类
 *
 * <AUTHOR>
 */
public interface StocktakingDelegate {

    /**
     * 盘点审核
     */
    void auditStocktaking(Stocktaking stocktaking);

    /**
     * 审核前sku库存数据扣减处理
     *
     * @param stocktaking 盘点对象
     */
    void dealSkuStockIfNeedAudit(Stocktaking stocktaking);

    /**
     * 审核前sku库存、库位库存冻结
     *
     * @param stocktaking
     */
    void dealSkuStockIfNeedAuditForCabinet(Stocktaking stocktaking);

    /**
     * 审核成功后的sku库存数据扣减处理
     *
     * @param stocktaking
     */
    void dealSkuStockIfAuditedSuccess(Stocktaking stocktaking, Boolean isAudit);

    /**
     * 审核失败后的sku库存数据扣减处理
     *
     * @param stocktaking
     */
    void dealSkuStockIfAuditedFailed(Stocktaking stocktaking);

    /**
     * 审核失败后的sku库存、库位库存释放
     *
     * @param stocktaking
     */
    void dealCabinetInventoryIfAuditedFailed(Stocktaking stocktaking);

    /**
     * 审核成功后批次库存数据扣减处理
     *
     * @param stocktaking 盘点对象
     */
    void dealBatchStockIfAuditedSuccess(Stocktaking stocktaking);

    void dealBatchStockIfAuditedSuccessWhenOpenCabinet(Stocktaking stocktaking, Map<String/*sku+batch+produceDate+qualityDate*/, List<Integer>> map);

    /**
     * 批次盘点库位库存处理
     *
     * @param stocktaking
     */
    void dealCabinetInventoryForBatchStocktaking(Stocktaking stocktaking);

    /**
     * 审核成功库位库存处理
     *
     * @param stocktaking
     */
    Map<String/*sku+batch+produceDate+qualityDate*/, List<Integer>> dealCabinetInventoryIfAuditedSuccess(Stocktaking stocktaking);

    /**
     * 盘点审核失败
     */
    void auditStocktakingFailed(Stocktaking stocktaking);

    /**
     * 完成盘点任务
     *
     * @param stocktaking dx
     */
    void finishStocktaking(Stocktaking stocktaking);

    /**
     * 更新实例详情
     *
     * @param stocktakingItemBatches 实例详情
     */
    void updateItemBatches(List<StocktakingItemBatch> stocktakingItemBatches);

    /**
     * 更新盘点主任务
     *
     * @param stocktaking
     */
    void updateStocktaking(Stocktaking stocktaking);

    /**
     * 获取盘点实例信息
     *
     * @param stocktakingId 任务id
     * @return 实例信息
     */
    List<StockTakingItem> listStocktakingItems(Long stocktakingId);

    /**
     * 获取盘点领域对象
     *
     * @param stocktakingId 任务id
     * @return 盘点对象
     */
    Stocktaking getStocktaking(Long stocktakingId);

    /**
     * 校验是否存在库位库存占用记录
     *
     * @param stocktaking
     */
    void checkCabinetInventoryOccupied(Stocktaking stocktaking);

    /**
     * 重新组装扣减数据（补偿审批过程中批次库存变动场景）
     *
     * @param stocktaking
     */
    Map<String, Integer> reassemblyReduceSkuMap(Stocktaking stocktaking);

    /**
     * 取消盘点任务
     * @param stocktaking 任务详情
     */
    void cancelStocktaking(Stocktaking stocktaking);
}
