package net.summerfarm.wms.domain.stockdamage.param;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/30
 */
@Data
public class StockDamageCreateItemValueObject implements Serializable {

    private static final long serialVersionUID = 4760714258931429682L;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate productDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 货损数量
     */
    private Integer quantity;

}
