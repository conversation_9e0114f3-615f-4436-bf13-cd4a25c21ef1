package net.summerfarm.wms.domain.processingtask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProcessingTaskProductOrderRecord implements Serializable {

    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 来源订单
     */
    private String sourceId;
    /**
     * 加工任务成品id
     */
    private Long processingTaskProductId;
    /**
     * 成品sku编号
     */
    private String productSkuCode;
    /**
     * 成品sku名称
     */
    private String productSkuName;
    /**
     * 成品sku预计加工数
     */
    private Integer productSkuNeedQuantity;
    /**
     * 成品sku规格重量
     */
    private BigDecimal productSkuSpecWeight;
    /**
     * 成品sku规格单位
     */
    private String productSkuSpecUnit;
    /**
     * 成品sku规格预计加工数
     */
    private Integer productSkuSpecNeedQuantity;
    /**
     * 成品sku规格实际加工数量
     */
    private Integer productSkuSpecSubmitQuantity;
    /**
     * 成品sku加工重量
     */
    private BigDecimal productSkuSpecSubmitWeight;
    /**
     * 成品打印次数
     */
    private Integer productSkuSpecPrintNumber;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;

}
