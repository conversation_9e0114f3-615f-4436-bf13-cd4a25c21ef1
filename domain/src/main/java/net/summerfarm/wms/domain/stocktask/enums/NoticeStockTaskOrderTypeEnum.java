package net.summerfarm.wms.domain.stocktask.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Description
 * @Date 2023/4/11 17:04
 * @<AUTHOR>
 */
@Getter
public enum NoticeStockTaskOrderTypeEnum {

    NORMAL(0, 51, "销售出库"),
    NORMAL_CROSS(0, 63, "越库出库"),
    REISSUE(1, 57, "补发出库"),
    SELF_PICK(2, 58, "自提出库"),
    DEMO_OUT(3, 52, "出样出库"),
    SAMPLE_SELF_PICK(4, 62, "样品自提出库"),
    ;

    private Integer outOrderType;

    private Integer stockTaskType;

    private String desc;

    NoticeStockTaskOrderTypeEnum(Integer outOrderType, Integer stockTaskType, String desc) {
        this.outOrderType = outOrderType;
        this.stockTaskType = stockTaskType;
        this.desc = desc;
    }

    public static Integer getStockTaskTypeByOrderType(Integer outOrderType) {
        for (NoticeStockTaskOrderTypeEnum typeEnum : NoticeStockTaskOrderTypeEnum.values()) {
            if (Objects.equals(outOrderType, typeEnum.outOrderType)) {
                return typeEnum.getStockTaskType();
            }
        }
        return null;
    }

    public static Integer getOrderTypeByStockTaskType(Integer stockTaskType) {
        for (NoticeStockTaskOrderTypeEnum typeEnum : NoticeStockTaskOrderTypeEnum.values()) {
            if (Objects.equals(stockTaskType, typeEnum.stockTaskType)) {
                return typeEnum.getOutOrderType();
            }
        }
        return null;
    }

}
