package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.stocktask.enums.NoticeOrderStatusEnum;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.math.NumberUtils;

import java.time.LocalDateTime;

/**
 * @Description
 * @Date 2023/4/10 12:16
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskNoticeOrder {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 货品供应单号
     */
    private String goodsSupplyNo;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * 仓租户id
     */
    private Long warehouseTenantId;

    private String warehouseName;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 订单类型
     */
    private Integer outOrderType;

    /**
     * 预计送达时间
     */
    private LocalDateTime exceptTime;

    /**
     * 通知单状态
     * @see NoticeOrderStatusEnum
     */
    private Integer status;

    /**
     * 货品供应单方式 0-入仓 1-不入仓
     */
    private Integer supplyMode;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否软删
     */
    private Byte isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 出库任务生成时间
     */
    private LocalDateTime stockTaskCreateTime;

    private Long stockTaskId;

    /**
     * 通知单的品标记 1-非代销不入仓品 2-代销不入仓品 3-混合品
     * @see net.summerfarm.wms.domain.stocktask.enums.NoticeSkuFlagEnums
     */
    private Integer noticeSkuFlag;

    /**
     * 外部标识（1:内部，2:外部）
     */
    private Integer externalOption;

    /**
     * 外部状态（1:初始，2:已下发）
     */
    private Integer externalStatus;

    /**
     * 推单模式（0手动推单，1实时推单，2全品类推单，3POP，4POPT+2）
     */
    private Integer pushMode;

    /**
     * 截单时间
     */
    private LocalDateTime closeTime;

    /**
     * 取消出库通知单
     */
    public void cancel(){
        if (NoticeOrderStatusEnum.CANT_CANCEL.contains(this.status)){
            throw new BizException(ErrorCodeNew.NOTICE_CANT_CANCEL);
        }
        this.status  = NoticeOrderStatusEnum.CANCEL.getStatus();
        this.isDeleted = NumberUtils.BYTE_ONE;
    }

    /**
     * 预取消出库通知单
     */
    public void preCancel(){
        // 靠状态码区分抛错类型
        if (NoticeOrderStatusEnum.CANT_PRE_CANCEL.contains(this.status)){
            // 出库通知单取消的场景需要给ofc抛错
            if (NoticeOrderStatusEnum.CANT_PRE_CANCEL_THROW_EX.contains(this.status)){
                throw new BizException(ErrorCodeNew.NOTICE_CANCEL);
            }
            // 出库任务生成中场景需要给ofc返回false
            throw new BizException("预取消出库通知单失败,请检查状态");
        }
        this.status  = NoticeOrderStatusEnum.CANCEL_ING.getStatus();
    }

    /**
     * 生成出库任务失败
     */
    public void createOutTaskFail(){
        if (!NoticeOrderStatusEnum.CREATING.getStatus().equals(status) ||
        !NoticeOrderStatusEnum.INIT.getStatus().equals(status)){
            throw new BizException("更改出库任务生成失败状态异常");
        }
        this.status  = NoticeOrderStatusEnum.CREATE_FAIL.getStatus();
    }
}
