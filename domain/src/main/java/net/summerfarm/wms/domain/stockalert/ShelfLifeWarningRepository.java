package net.summerfarm.wms.domain.stockalert;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.stockalert.domainobject.ShelfLifeWarning;
import net.summerfarm.wms.domain.stockalert.domainobject.query.QueryShelfLifeWarning;
import net.summerfarm.wms.domain.stockalert.param.query.StoreAlertCountByStatusQueryParam;
import net.summerfarm.wms.domain.stockalert.valueobject.StoreAlertCountByStatusValueObject;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-01 12:01:20
 */
public interface ShelfLifeWarningRepository {

    PageInfo<ShelfLifeWarning> pageQueryShelfLifeWarning(QueryShelfLifeWarning query);

    List<StoreAlertCountByStatusValueObject> countByStatus(StoreAlertCountByStatusQueryParam queryParam);

}