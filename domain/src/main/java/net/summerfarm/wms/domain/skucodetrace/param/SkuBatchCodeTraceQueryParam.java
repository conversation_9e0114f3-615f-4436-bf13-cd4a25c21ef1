package net.summerfarm.wms.domain.skucodetrace.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: 唯一溯源码创建参数<br/>
 * date: 2024/8/9 11:13<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuBatchCodeTraceQueryParam extends BasePageInput {

    /**
     * 分页条目
     */
    private Integer pageIndex;

    /**
     * 分页数量
     */
    private Integer pageSize;

    /**
     * 打印编码查询起始值
     */
    private Integer beginStockTaskStorageSeq;

    /**
     * 打印编码查询结束值
     */
    private Integer endStockTaskStorageSeq;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 货品Id
     */
    private Long pdId;

    /**
     * 批次唯一码
     */
    private String skuBatchOnlyCode;

    /**
     * 入库任务id
     */
    private Integer stockTaskStorageId;

    /**
     * 批次日期
     */
    private LocalDate batchDate;

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku数量
     */
    private Integer skuQuantity;

    /**
     * OFC采购供应单编号
     */
    private String psoNo;

    /**
     * 订单单号
     */
    private String orderNo;
    /**
     * 履约单号
     */
    private Long fulfillmentNo;
    /**
     * 城配仓编号
     */
    private Integer storeNo;
    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 门店名称模糊
     */
    private String merchantNameLike;


    /**
     * 买手名称模糊
     */
    private String buyerNameLike;

    /**
     * 状态
     10：订单待分配
     20：路线待分配
     30：待称重
     40：已称重
     */
    private Integer state;

    /**
     * 状态列表
     10：订单待分配
     20：路线待分配
     30：待称重
     40：已称重
     */
    private List<Integer> stateList;


    /**
     * 生成日期开始时间
     */
    private LocalDate createDateStart;
    /**
     * 生成日期结束时间
     */
    private LocalDate createDateEnd;
    /**
     * 标签类型0POP，1POPT2
     */
    private Integer labelType;
}
