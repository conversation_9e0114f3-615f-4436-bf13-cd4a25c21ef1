package net.summerfarm.wms.domain.stockTransfer.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTransferItemOpDetailEntity implements Serializable {

    private static final long serialVersionUID = 7389890602383431455L;

    /**
     * 主键id
     */
    Long id;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 关联表id
     */
    Long stockTransferItemOpId;

    /**
     * 转出数量
     */
    Long transferOutNum;

    /**
     * 转出生产日期
     */
    Long produceAt;

    /**
     * 转出保质期
     */
    Long shelfLife;

    /**
     * 转出批次
     */
    String transferOutBatch;

    /**
     * 转出库位
     */
    String transferOutCabinet;

    /**
     * 新生成的转入批次
     */
    String transferInBatch;

    /**
     * 转入库位
     */
    String transferInCabinet;

    /**
     * 外部回告转入数量
     */
    Integer externalTransferInNum;
}
