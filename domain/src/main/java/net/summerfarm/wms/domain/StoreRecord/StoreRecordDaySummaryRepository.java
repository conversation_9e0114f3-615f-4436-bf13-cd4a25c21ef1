package net.summerfarm.wms.domain.StoreRecord;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.StoreRecord.domainobject.StoreRecordDaySummary;
import net.summerfarm.wms.domain.StoreRecord.domainobject.query.QueryStoreRecordDaySummary;

/**
 * <AUTHOR>
 * @Date 2023-08-02
 **/
public interface StoreRecordDaySummaryRepository {

	/**
	 * 查询出入库汇总信息
	 *
	 * @param queryStoreRecordDaySummary
	 * @return
	 */
	PageInfo<StoreRecordDaySummary> query(QueryStoreRecordDaySummary queryStoreRecordDaySummary);
}
