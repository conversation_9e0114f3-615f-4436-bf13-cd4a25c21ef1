package net.summerfarm.wms.domain.processingtask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProcessingTaskMaterialRestoreRecord implements Serializable {

    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 加工任务成品id
     */
    private Long processingTaskProductId;
    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 原料sku
     */
    private String materialSkuCode;
    /**
     * 原料sku编号
     */
    private String materialSkuName;
    /**
     * 原料sku重量
     */
    private BigDecimal materialSkuWeight;
    /**
     * 原料sku单位
     */
    private String materialSkuUnit;
    /**
     * 原料sku采购批次
     */
    private String materialSkuPurchaseBatch;
    /**
     * 原料sku归还数量
     */
    private Integer materialSkuRestoreQuantity;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;

    /**
     * 物料SKU生产日期
     */
    private Date materialSkuProductionDate;

    /**
     * 物料SKU保质期
     */
    private Date materialSkuQualityDate;

    /**
     * 加工任务领料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 物料SKU库位
     */
    private String materialSkuCabinetCode;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
}
