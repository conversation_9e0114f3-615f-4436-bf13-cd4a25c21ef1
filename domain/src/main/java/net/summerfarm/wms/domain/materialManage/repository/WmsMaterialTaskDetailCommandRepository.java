package net.summerfarm.wms.domain.materialManage.repository;


import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskDetailCommandParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:27
* @version 1.0
*
*/
public interface WmsMaterialTaskDetailCommandRepository {

    WmsMaterialTaskDetailEntity insertSelective(WmsMaterialTaskDetailCommandParam param);

    void batchInsertSelective(List<WmsMaterialTaskDetailCommandParam> param);

    int updateSelectiveById(WmsMaterialTaskDetailCommandParam param);

    int remove(Long id);

}