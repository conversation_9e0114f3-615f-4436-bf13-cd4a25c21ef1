package net.summerfarm.wms.domain.stockTransfer.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransferQueryParam implements Serializable {

    private static final long serialVersionUID = 8540978846500978926L;

    /**
     * 主键id
     */
    Long id;

    /**
     * 主键id列表
     */
    List<Long> idList;

    /**
     * 库存仓编号
     */
    Long warehouseNo;

    /**
     * 转换维度
     */
    Integer transferDimension;

    /**
     * 状态
     */
    Integer state;

    /**
     * 操作人
     */
    String operator;

    /**
     * 备注
     */
    String remark;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 创建时间起始
     */
    LocalDateTime createdAtBegin;

    /**
     * 创建时间结束
     */
    LocalDateTime createdAtEnd;
}
