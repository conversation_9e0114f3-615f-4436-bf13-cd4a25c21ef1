package net.summerfarm.wms.domain.processingtask.repository;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductOrderRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ProcessingTaskProductOrderRecordRepository {

    /**
     * 批量创建
     *
     * @param processingTaskProductOrderRecordList
     */
    void batchCreate(List<ProcessingTaskProductOrderRecord> processingTaskProductOrderRecordList);

    /**
     * 获取产品的订单规格列表
     * @param processingTaskProductId
     * @return
     */
    List<ProcessingTaskProductOrderRecord> listByProductId(Long processingTaskProductId);

    void submitQuantity(Long processingTaskProductOrderSpecId, Integer submitQuantity, BigDecimal submitWeight, String currentUserName);

    List<ProcessingTaskProductOrderRecord> listBySourceIdList(List<String> orderNoList);

    Map<String, List<ProcessingTaskProductOrderRecord>> mapBySourceIdList(List<String> orderNoList);

}
