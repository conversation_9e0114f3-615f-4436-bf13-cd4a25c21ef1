package net.summerfarm.wms.domain.stockTransfer;

import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemOpDetailEntity;

import java.util.List;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
public interface StockTransferItemOpDetailQueryRepository {

    /**
     * 根据op表主键列表查询
     *
     * @param opIdList op主表id
     * @return 返回查询到的数列表
     */
    List<StockTransferItemOpDetailEntity> listByOpIdList(List<Long> opIdList);

    /**
     * 通过转换批次查询实例
     *
     * @param itemOpId         转化批次id
     * @param transferOutBatch 转出的批次
     * @return 返回转化实例详情
     */
    StockTransferItemOpDetailEntity selectByBatch(Long itemOpId, String transferOutBatch);
}
