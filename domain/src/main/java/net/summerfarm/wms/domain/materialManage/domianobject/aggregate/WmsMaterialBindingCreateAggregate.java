package net.summerfarm.wms.domain.materialManage.domianobject.aggregate;

import lombok.Data;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingCommandParam;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingDetailCommandParam;

import java.util.List;

@Data
public class WmsMaterialBindingCreateAggregate {

    /**
     * 物料绑定
     */
    private WmsMaterialBindingCommandParam wmsMaterialBindingCommandParam;

    /**
     * 物料绑定明细
     */
    private List<WmsMaterialBindingDetailCommandParam> wmsMaterialBindingDetailCommandParamList;
}
