package net.summerfarm.wms.domain.stockTransfer;

import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemOpEntity;

import java.util.List;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
public interface StockTransferItemOpQueryRepository {

    /**
     * 根据id查询
     * @param id
     * @return
     */
    StockTransferItemOpEntity findById(Long id);

    /**
     * 根据itemId查询
     */
    List<StockTransferItemOpEntity> listByItemIdList(List<Long> itemIds);
}
