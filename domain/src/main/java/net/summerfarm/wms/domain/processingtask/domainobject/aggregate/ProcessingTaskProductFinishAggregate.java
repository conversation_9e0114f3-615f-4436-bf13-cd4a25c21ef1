package net.summerfarm.wms.domain.processingtask.domainobject.aggregate;

import lombok.Data;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.MaterialReceiveSpecLoseUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProcessingTaskProductFinishAggregate implements Serializable {

    /**
     * 加工成品ID
     */
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 规格损耗
     */
    private BigDecimal specLossWeightTotal;

    /**
     * 结束备注
     */
    private String finishRemark;

    /**
     * 领料规格损耗列表
     */
    private List<MaterialReceiveSpecLoseUpdate> receiveSpecLoseUpdateList;

}
