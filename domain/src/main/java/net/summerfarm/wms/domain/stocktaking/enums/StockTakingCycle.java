package net.summerfarm.wms.domain.stocktaking.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * 盘点周期枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum StockTakingCycle {
    /**
     * 周期,日周月,单次
     */
    DAY(0, "日盘"),
    WEEK(1, "周盘"),
    MOUTH(2, "月盘"),
    ONE_WAY(3, "循环盘点"),
    ON_SALE(4, "动销盘点"),

    UNKNOWN(999, "未知周期"),
    ;

    public static StockTakingCycle convert(Integer param) {
        return Arrays.stream(StockTakingCycle.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElse(StockTakingCycle.UNKNOWN);
    }

    Integer code;
    String desc;
}
