package net.summerfarm.wms.domain.initconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 出库任务可直接关闭配置
 * @date 2025/5/6
 */
@Data
@Slf4j
@Configuration
@NacosConfigurationProperties(prefix = "stockout.close.option", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class StockOutCloseOptionConfig {

    /**
     * 直接开启可关闭任务仓库列表
     */
    private List<Integer> warehouseNos;

    /**
     * 强制完成任务校验维度，1-校验实物，2-校验可用
     */
    private Integer checkBlockFinish;

    /**
     * 判断当前仓库是否是可直接关闭任务
     * @param warehouseNo
     * @return
     */
    public Boolean isOpen(Integer warehouseNo) {
        if (CollectionUtils.isEmpty(warehouseNos)) {
            return false;
        }
        return warehouseNos.contains(warehouseNo);
    }

    /**
     * 强制完成出库是否校验实物库存
     * @return
     */
    public Boolean isCheckBlockFinishInventory() {
        if (null == checkBlockFinish) {
            return false;
        }
        return checkBlockFinish == 1;
    }

}
