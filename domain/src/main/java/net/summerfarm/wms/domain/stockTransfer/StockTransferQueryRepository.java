package net.summerfarm.wms.domain.stockTransfer;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.stockTransfer.domainobject.StockTransferItemOp;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferEntity;
import net.summerfarm.wms.domain.stockTransfer.param.StockTransferQueryParam;

import java.time.LocalDate;
import java.util.List;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
public interface StockTransferQueryRepository {

    /**
     * 按照条件分页查询转换任务信息
     *
     * @param transferQueryParam 转换任务查询条件
     * @param pageIndex          分页参数 页号
     * @param pageSize           分页大小
     * @return 返回查询到的转换任务信息
     */
    PageInfo<StockTransferEntity> pageByParams(StockTransferQueryParam transferQueryParam, Integer pageIndex, Integer pageSize);

    /**
     * 查询转换任务id
     *
     * @param id 转换任务id
     * @return 返回查询到的转换任务信息
     */
    StockTransferEntity selectById(Long id);

    /**
     * 通过id和仓库编号查询转化任务列表
     *
     * @param stockTransferIds 转换任务id列表
     * @param warehouseNo      仓库编号
     * @param states           状态列表
     * @return 返回转化任务列表
     */
    List<StockTransferEntity> selectByIdAndWarehouseNo(List<Long> stockTransferIds, Long warehouseNo, List<Integer> states);

    /**
     * 查询转化任务列表
     *
     * @param createAt 创建时间
     * @param remark   备注
     * @return 返回转化任务列表
     */
    List<StockTransferEntity> selectByCreatedAt(LocalDate createAt, String remark);

    /**
     * 查询转化任务列表
     *
     * @param warehouseNo 仓库编号
     * @param remark      备注
     * @return 返回转化任务列表
     */
    List<StockTransferEntity> selectByRemarkAndWarehouseNo(Long warehouseNo, String remark);

    /**
     * 统计转换任务数量
     *
     * @param stockTransferQueryParam 转换任务查询条件
     * @return 返回转换任务数量
     */
    int countStockTransfer(StockTransferQueryParam stockTransferQueryParam);

    /**
     * 查询转换操作实例
     *
     * @param opId id
     * @return r
     */
    StockTransferItemOp findTransferItemOp(Long opId);

    /**
     * 查询转化任务列表
     *
     * @param createAt 创建时间
     * @param taskSource 任务来源
     * @param status 状态
     * @return 返回转化任务列表
     */
    List<StockTransferEntity> selectByCreatedAtAndSource(LocalDate createAt, Integer taskSource, Integer status);


}
