package net.summerfarm.wms.domain.processingtask.domainService.impl;

import net.summerfarm.wms.common.enums.GeneratorDocTypeEnum;
import net.summerfarm.wms.common.util.SerialNumberGenerator;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingConfigDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigUpdateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingConfigTypeEnum;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingConfigRepository;
import net.summerfarm.wms.domain.processingtask.repository.SkuSpecRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingMaterialConfigCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ProcessingConfigDomainServiceImpl implements ProcessingConfigDomainService {

    @Autowired
    private ProcessingConfigRepository processingConfigRepository;
    @Autowired
    private SkuSpecRepository skuSpecRepository;
    @Autowired
    private ProcessingMaterialConfigCommandRepository materialConfigCommandRepository;
    @Autowired
    private SerialNumberGenerator serialNumberGenerator;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long create(ProcessingConfigCreateAggregate createAggregate) {
        // 生成规则单号
        ProcessingConfig processingConfig = createAggregate.getProcessingConfig();
        String processingConfigCode = "";
        // 商品加工
        if (ProcessingConfigTypeEnum.GOODS_PROCESSING.getValue() == processingConfig.getType()) {
            processingConfigCode = serialNumberGenerator
                    .createConfigCode(GeneratorDocTypeEnum.SHANG_PIN_JIA_GONG);
        }
        // 商品组装
        if (ProcessingConfigTypeEnum.ASSEMBLY_PROCESSING.getValue() == processingConfig.getType()){
            processingConfigCode = serialNumberGenerator
                    .createConfigCode(GeneratorDocTypeEnum.SHANG_PIN_ZU_ZHUANG);
        }
        // 订单加工
        if (ProcessingConfigTypeEnum.ORDER_PROCESSING.getValue() == processingConfig.getType()) {
            processingConfigCode = serialNumberGenerator
                    .createConfigCode(GeneratorDocTypeEnum.DING_DAN_JIA_GONG);
        }
        createAggregate.addProcessingConfigCode(processingConfigCode);

        // 创建规则
        Long configId = processingConfigRepository.insert(createAggregate.getProcessingConfig());

        // 追加id参数
        createAggregate.addProcessingConfigId(configId);

        // 批量创建SKU规格
        skuSpecRepository.insertBatch(createAggregate.getSkuSpecList());

        // 批量创建原料
        materialConfigCommandRepository.batchInsert(createAggregate.getProcessingMaterialConfigEntityList());
        return configId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long update(ProcessingConfigUpdateAggregate updateAggregate) {
        // 更新配置
        processingConfigRepository.update(updateAggregate.getProcessingConfig());
        Long configId = updateAggregate.getProcessingConfig().getId();

        updateAggregate.addProcessingConfigId(configId);

        // 删除历史SKU规格
        skuSpecRepository.updateDeletedByProcessingConfigId(configId);

        // 批量创建SKU规格
        skuSpecRepository.insertBatch(updateAggregate.getSkuSpecList());

        // 删除删除历史原料
        materialConfigCommandRepository.updateDeletedByProcessingConfigId(configId);

        // 批量创建原料
        materialConfigCommandRepository.batchInsert(updateAggregate.getProcessingMaterialConfigEntityList());

        return configId;
    }

    /**
     * 作废
     *
     * @param id            加工规则配置id
     * @param operatorName
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long invalid(Long id, String operatorName) {
        // 更新配置
        Long configId = processingConfigRepository.updateInvalidByProcessingConfigId(id, operatorName);
        // 作废SKU规格
//        skuSpecRepository.updateInvalidByProcessingConfigId(id);
        // 作废原料配置
//        materialConfigCommandRepository.updateDeletedByProcessingConfigId(id);
        return configId;
    }

}
