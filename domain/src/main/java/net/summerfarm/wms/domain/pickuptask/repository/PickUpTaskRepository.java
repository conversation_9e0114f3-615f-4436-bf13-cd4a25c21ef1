package net.summerfarm.wms.domain.pickuptask.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.pickuptask.domainobject.entity.PickUpTask;
import net.summerfarm.wms.domain.pickuptask.domainobject.entity.PickUpTaskOrderInfo;

import java.util.List;

/**
 * 投线任务
 * <AUTHOR>
 * @date 2023/02/18
 */
public interface PickUpTaskRepository {

    /**
     * 投线任务分页查询
     * @param query 查询请求对象
     * @param pageIndex 当前页
     * @param pageSize 页大小
     * @return 分页列表
     */
    PageInfo<PickUpTask> queryAllByLimit(PickUpTask query, Integer pageIndex, Integer pageSize);

    /**
     * 查询所有投线任务的订单号
     * @param query 查询对象
     * @return 订单号列表
     */
    List<PickUpTaskOrderInfo> queryOrderNoList(PickUpTask query);

    /**
     * 投线任务导出查询
     * @param query 查询请求对象
     * @return 数据列表
     */
    List<PickUpTask> queryAllByLimit(PickUpTask query);
}
