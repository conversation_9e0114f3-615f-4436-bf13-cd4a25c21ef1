package net.summerfarm.wms.domain.materialManage.service;


import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialBindingCreateAggregate;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity;
import net.summerfarm.wms.domain.materialManage.enums.MaterialBindingStatusEnum;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingCommandParam;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingDetailCommandParam;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialBindingCommandRepository;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialBindingDetailCommandRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 *
 * @Title: 物料绑定领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Service
public class WmsMaterialBindingCommandDomainService {


    @Autowired
    private WmsMaterialBindingCommandRepository wmsMaterialBindingCommandRepository;

    @Autowired
    private WmsMaterialBindingDetailCommandRepository detailCommandRepository;

    @Transactional(rollbackFor = Exception.class)
    public WmsMaterialBindingEntity insert(WmsMaterialBindingCreateAggregate param) {
        WmsMaterialBindingEntity entity = wmsMaterialBindingCommandRepository.insertSelective(
                param.getWmsMaterialBindingCommandParam());

        for (WmsMaterialBindingDetailCommandParam detailCommandParam : param.getWmsMaterialBindingDetailCommandParamList()) {
            detailCommandParam.setMaterialBingingId(entity.getId());
        }

        detailCommandRepository.insertBatch(param.getWmsMaterialBindingDetailCommandParamList());

        return entity;
    }


    public int update(WmsMaterialBindingCommandParam param) {
        return wmsMaterialBindingCommandRepository.updateSelectiveById(param);
    }


    @Transactional(rollbackFor = Exception.class)
    public int delete(WmsMaterialBindingCommandParam param) {
        if (param == null || param.getId() == null){
            throw new BizException("参数为空");
        }

        // 更新状态
        param.setUpdateTime(param.getUpdateTime());
        param.setUpdater(param.getUpdater());
        param.setStatus(MaterialBindingStatusEnum.NOT_VALID.getCode());
        int count = wmsMaterialBindingCommandRepository.updateSelectiveById(param);
        detailCommandRepository.updateDeletedByBindingId(param.getId(), param.getUpdater());
        return count;
    }
}
