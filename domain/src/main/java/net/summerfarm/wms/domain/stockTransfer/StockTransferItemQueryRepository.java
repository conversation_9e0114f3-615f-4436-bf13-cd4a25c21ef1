package net.summerfarm.wms.domain.stockTransfer;

import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemEntity;

import java.util.List;

/**
 * 转换任务明细查询
 *
 * @author: xdc
 * @date: 2024/2/21
 **/
public interface StockTransferItemQueryRepository {

    /**
     * 根据skus查询实例id列表
     */
    List<Long> listBySku(List<String> skus);

    /**
     * 查询转化任务实例列表
     *
     * @param stockTransferId 转换任务id
     * @return 返回转换任务实例列表
     */
    List<StockTransferItemEntity> listByStockTransferId(Long stockTransferId);

    /**
     * 查询转化任务实例列表
     *
     * @param stockTransferIdList 转换任务id
     * @return 返回转换任务实例列表
     */
    List<StockTransferItemEntity> listByStockTransferIds(List<Long> stockTransferIdList);

    /**
     * 查询转化任务实例列表
     *
     * @param idList 实例id列表
     * @return 返回转换任务实例列表
     */
    List<StockTransferItemEntity> listByIdList(List<Long> idList);

    /**
     * 查询转化任务实例列表
     *
     * @param id 实例id
     * @return 返回转换任务实例列表
     */
    StockTransferItemEntity selectById(Long id);
}
