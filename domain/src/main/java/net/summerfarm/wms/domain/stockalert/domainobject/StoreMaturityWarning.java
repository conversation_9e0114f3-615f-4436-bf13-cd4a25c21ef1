package net.summerfarm.wms.domain.stockalert.domainobject;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-01 12:01:21
 */
@Data
public class StoreMaturityWarning {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库服务商
     */
    private String warehouseProvider;

    /**
     * 货品编码
     */
    private Long pdId;

    /**
     * sku编码
     */
    private String sku;

    /**
     * saas skuId
     */
    private Long saasSkuId;

    /**
     * 类目id
     */
    private Integer categoryId;

    /**
     * sku租户id
     */
    private Long skuTenantId;

    /**
     * 仓库租户id
     */
    private Long warehouseTenantId;

    /**
     * 批次
     */
    private String batch;

    /**
     * 库存数量
     */
    private Integer quantity;

    /**
     * 可储存天数
     */
    private Integer storageDays;

    /**
     * 入库日期
     */
    private LocalDate inStoreDate;


    /**
     * 储存到期日期
     */
    private LocalDate storageExpirationDate;


    /**
     * 储存剩余天数
     */
    private Integer storageRemainingDays;

    /**
     * 临期百分比（保存百分数）
     */
    private Double approachingMaturityPercentage;

    /**
     * 储存状态，1：正常、2：临期、3：已滞库
     */
    private Integer status;

    /**
     * 数据所属日期:格式yyyyMMdd
     */
    private Integer dayTag;

    /**
     * 货品启用状态，0：停用、1：启用
     */
    private Integer useFlag;

}