package net.summerfarm.wms.domain.stocktaking.domainobject;

import com.google.common.collect.Maps;
import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.base.ValueObject;
import net.summerfarm.wms.domain.mission.enums.MissionCarrierTypeEnum;
import net.summerfarm.wms.domain.stocktaking.StockDelegate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 盘点实例批次对象
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StocktakingItemBatch implements ValueObject {

    /**
     * detailId
     */
    Long id;

    Long itemId;

    String sku;

    /**
     * 采购批次
     */
    String batch;

    /**
     * 成本
     */
    BigDecimal cost;

    /**
     * 生产批次
     */
    Long produceBatch;

    /**
     * 保质期
     */
    Long shelfLife;

    /**
     * 生产日期
     */
    Long produceAt;

    /**
     * 批次库存(快照)
     */
    Integer batchNum;

    /**
     * 库存差值
     */
    Integer diffStock;

    /**
     * 盘点库存
     */
    Integer stockTakingNum;

    /**
     * 删除时间
     */
    Long deletedAt;

    /**
     * 原因
     */
    Integer reason;

    /**
     * 备注
     */
    String remark;

    /**
     * 库位库存id
     */
    Long cabinetInventoryId;

    /**
     * 库位编码
     */
    String cabinetCode;

    /**
     * 货主编码
     */
    String ownerCode;

    /**
     * 货主名称
     */
    String ownerName;

    // 代理
    StockDelegate stockDelegate;

    /**
     * 租户 1-鲜沐
     */
    Long tenantId;

    /**
     * 业务标志位
     * 10-pda批次修改
     */
    Integer bizOption;

    /**
     * 来源明细id
     * 该条明细源自哪条明细
     */
    Long sourceDetailId;

    public String skuAndCabinetNo() {
        return sku + cabinetCode;
    }

    public String duplicateKey() {
        // 库位五要素,非库位四要素
        return sku + batch + getProduceDate() + getQualityDate() + cabinetCode;
    }

    public String duplicateKeyForPda() {
        // 盘点pda不走批次
        return sku + null + getProduceDate() + getQualityDate() + cabinetCode;
    }

    public LocalDate getProduceDate() {
        return DateUtil.toLocalDate(produceAt);
    }

    public LocalDate getQualityDate() {
        return DateUtil.toLocalDate(shelfLife);
    }

    public Long getProduceBatch() {
        if (Objects.nonNull(produceBatch)) {
            return produceBatch;
        }
        if (null == stockDelegate) {
            return null;
        }
        return stockDelegate.getProduceBatch(itemId, produceAt, shelfLife, sku);
    }

    public Long getCabinetInventoryId() {
        if (null != cabinetInventoryId) {
            return cabinetInventoryId;
        }
        if (null == stockDelegate) {
            return null;
        }
        return stockDelegate.getCabinetInventory(itemId, produceAt, shelfLife, sku, cabinetCode);
    }

    public Map<Long, String/*采购单号*/> productBatchMap() {
        HashMap<Long, String> map = Maps.newHashMap();
        map.put(getProduceBatch(), batch);
        return map;
    }

    public Map<Long, String/*采购单号*/> cabinetInventoryBatchMap() {
        HashMap<Long, String> map = Maps.newHashMap();
        map.put(getCabinetInventoryId(), batch);
        return map;
    }

    public Map<Long, Integer> existDiffBatch() {
        HashMap<Long, Integer> map = Maps.newHashMap();
        if (Objects.nonNull(diffStock) && diffStock != 0) {
            map.put(getProduceBatch(), diffStock);
        }
        return map;
    }

    public Integer queryStocktakingNum(){
        if (stockTakingNum == null){
            return 0;
        }
        return stockTakingNum;
    }

    public Integer queryDiffStock() {
        if (Objects.isNull(diffStock)) {
            return 0;
        }
        return diffStock;
    }

    public Map<Long, Integer> existDiffBatchForCabinet() {
        HashMap<Long, Integer> map = Maps.newHashMap();
        if (Objects.nonNull(diffStock) && diffStock != 0) {
            map.put(getCabinetInventoryId(), diffStock);
        }
        return map;
    }
}
