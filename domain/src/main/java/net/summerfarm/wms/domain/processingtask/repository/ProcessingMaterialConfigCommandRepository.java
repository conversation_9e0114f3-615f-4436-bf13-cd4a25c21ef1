package net.summerfarm.wms.domain.processingtask.repository;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity;
import net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingMaterialConfigCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-01-10 18:59:23
* @version 1.0
*
*/
public interface ProcessingMaterialConfigCommandRepository {

    ProcessingMaterialConfigEntity insertSelective(WmsProcessingMaterialConfigCommandParam param);

    int updateSelectiveById(WmsProcessingMaterialConfigCommandParam param);

    int remove(Long id);

    int batchInsert(List<WmsProcessingMaterialConfigCommandParam> processingMaterialConfigEntityList);

    void updateDeletedByProcessingConfigId(Long configId);
}