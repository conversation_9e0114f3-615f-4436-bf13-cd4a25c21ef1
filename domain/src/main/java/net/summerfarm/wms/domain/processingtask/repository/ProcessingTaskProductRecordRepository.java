package net.summerfarm.wms.domain.processingtask.repository;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ProcessingTaskProductRecordRepository {

    /**
     * 批量创建
     *
     * @param processingTaskProductList
     */
    void batchCreate(List<ProcessingTaskProductRecord> processingTaskProductList);

    /**
     * 根据id查询
     * @param processingTaskProductSpecId id
     * @return 查询结果
     */
    ProcessingTaskProductRecord queryById(Long processingTaskProductSpecId);

    /**
     * 批量查询
     * @param specIdList
     * @return
     */
    List<ProcessingTaskProductRecord> listByIdList(List<Long> specIdList);

    /**
     * 批量查询
     * @param specIdList
     * @return
     */
    Map<Long, ProcessingTaskProductRecord> mapByIdList(List<Long> specIdList);

    void submitQuantity(Long processingTaskProductSpecId, Integer submitQuantity,
                        BigDecimal submitWeight, String currentUserName);

    /**
     * 通过加工任务成品id查询加工任务成品明细
     * @param processingTaskProductId 加工任务成品id
     * @return 加工任务成品明细
     */
    List<ProcessingTaskProductRecord> queryByProcessingTaskProductId(Long processingTaskProductId);

    /**
     * 根据加工任务成品id list查询
     * @param processingTaskProductIdList 加工任务成品id list
     * @return 查询结果list
     */
    Map<Long, List<ProcessingTaskProductRecord>> mapByProcessingTaskProductIdList(List<Long> processingTaskProductIdList);

    /**
     * 通过id更新打印次数
     * @param processingTaskProductRecordId 加工任务成品明细id
     * @param canPrintNumber 打印次数
     */
    void updatePrintNumberById(Long processingTaskProductRecordId, Integer canPrintNumber);
}
