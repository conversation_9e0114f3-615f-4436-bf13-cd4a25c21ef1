package net.summerfarm.wms.domain.processingtask.delegate.converter;

import net.summerfarm.wms.domain.inventory.handler.entity.CabinetBatchInventoryReduceDistributeResult;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryReduceDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CabinetBatchInventoryReduceConverter {

    CabinetBatchInventoryReduceConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryReduceConverter.class);

    List<CabinetBatchInventoryReduceDetail> convertList(List<CabinetBatchInventoryReduceDistributeResult> results);

    CabinetBatchInventoryReduceDetail convert(CabinetBatchInventoryReduceDistributeResult result);
}
