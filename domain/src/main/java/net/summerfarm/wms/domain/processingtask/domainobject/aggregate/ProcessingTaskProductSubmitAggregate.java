package net.summerfarm.wms.domain.processingtask.domainobject.aggregate;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.domain.prove.domainobject.Prove;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductOrderRecordUpdate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductRecordUpdate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductSubmitRecord;
import net.summerfarm.wms.domain.skucode.domainobject.SkuBatchCode;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Slf4j
public class ProcessingTaskProductSubmitAggregate {

    /**
     * 加工成品ID
     */
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 成品平均成本
     */
    private BigDecimal productAvlBatchCost;

    /**
     * 加工数量
     */
    private Integer submitQuantity;

    /**
     * 加工重量
     */
    private BigDecimal submitWeight;

    /**
     * 加工提交明细
     */
    private List<ProcessingTaskProductSubmitRecord> productSubmitRecordList;

    /**
     * 加工规格记录
     */
    private List<ProcessingTaskProductRecordUpdate> productRecordUpdateList;

    /**
     * 加工订单记录
     */
    private List<ProcessingTaskProductOrderRecordUpdate> productOrderRecordUpdateList;

    /**
     * 原料四证
     */
    private Prove materialProve;

    /**
     * 追加批次信息
     * @param skuBatchCodes
     */
    private void addPurchaseBatchInfo(List<SkuBatchCode> skuBatchCodes){
        if (CollectionUtils.isEmpty(skuBatchCodes)){
            return;
        }

        Map<String, SkuBatchCode> skuBatchCodeMap = skuBatchCodes.stream()
                .collect(Collectors.toMap(skuBatchCode -> skuBatchCode.getSku() + "_" +
                                DateUtil.formatYmdDate(skuBatchCode.getProductionDate()),
                        Function.identity(),
                        (a, b) -> a));

        for (ProcessingTaskProductSubmitRecord productSubmitRecord : this.getProductSubmitRecordList()) {
            SkuBatchCode skuBatchCode = skuBatchCodeMap.get(
                    productSubmitRecord.getProductSkuCode() + "_"+
                            DateUtil.formatYmdDate(productSubmitRecord.getProductSkuProductionDate()));
            if (skuBatchCode == null){
                String errorMsg = String.format("采购批次不存在: %s",
                        productSubmitRecord.getProductSkuCode() + "_" +
                                DateUtil.formatYmdDate(productSubmitRecord.getProductSkuProductionDate()));
                log.error(errorMsg + " : {}", JSONObject.toJSONString(this));
                throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), errorMsg);
            }

            productSubmitRecord.setProductSkuPurchaseBatch(skuBatchCode.getPurchaseNo());
            productSubmitRecord.setProductSkuQualityDate(DateUtil.toDate(skuBatchCode.getQualityDate()));
        }
    }
}
