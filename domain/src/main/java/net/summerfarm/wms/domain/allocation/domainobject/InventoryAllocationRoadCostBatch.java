package net.summerfarm.wms.domain.allocation.domainobject;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-08-08 16:51:15
 */
@Data
public class InventoryAllocationRoadCostBatch {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 调拨单号
     */
    private String listNo;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 在途数量
     */
    private Integer roadQuantity;

    /**
     * 成本
     */
    private BigDecimal cost;


}