package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderAbnormalDetail;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description
 * @Date 2023/4/10 15:46
 * @<AUTHOR>
 */
public interface OutNoticeAbnormalDetailQueryRepository {

    /**
     * 查询通知单异常明细
     * @param goodsSupplyNo 货品供应单
     * @param sku sku
     * @return 异常明细
     */
    List<StockTaskNoticeOrderAbnormalDetail> queryNoticeOrderAbnormal(String goodsSupplyNo, List<String> sku);

    /**
     * 批量查询异常出库通知单明细
     * @param goodsSupplyOrderNoList 出库通知单列表
     * @return 返回异常出库通知单列表
     */
    List<StockTaskNoticeOrderAbnormalDetail> findByGoodsSupplyNoList(List<String> goodsSupplyOrderNoList);

    /**
     * 批量查询异常出库通知单明细
     * @param abnormalIds 出库通知单列表
     * @return 返回异常出库通知单列表
     */
    List<StockTaskNoticeOrderAbnormalDetail> findByIds(List<Long> abnormalIds);

    /**
     * 根据库存仓、城配仓、配送时间、类型、订单查询异常明细
     * @param warehouseNo
     * @param storeNo
     * @param expectTime
     * @param type
     * @param supplyMode
     * @param orderNos
     * @return
     */
    List<StockTaskNoticeOrderAbnormalDetail> findByExceptTimeAndWarehouseStore(Integer warehouseNo, Integer storeNo, LocalDate expectTime, Integer type, Integer supplyMode, List<String> orderNos);

    /**
     * 根据出库任务id查询异常明细
     * @param stockTaskId
     * @return
     */
    List<StockTaskNoticeOrderAbnormalDetail> findByStockTaskId(Long stockTaskId);
}
