package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Builder
@Data
@AllArgsConstructor
public class StockTaskProcess {

    private Long id;

    private Integer stockTaskId;

    private Long inBoundOrderId;

    private LocalDateTime addtime;

    private String recorder;

    private Integer isInspect;

    /**
     * 出库单状态（1：初始化，2：库存处理中，3：库存处理完成，4:库存处理失败）
     */
    private Integer processStatus;

    /**
     * 仓库
     */
    private Integer warehouseNo;

    /**
     * 出库任务类型
     */
    private Integer stockTaskType;

    /**
     * 更新时间
     */
    private Integer updateTime;

    public StockTaskProcess(){
    }

    public StockTaskProcess(Integer stockTaskId, LocalDateTime addtime, String recorder) {
        this.stockTaskId = stockTaskId;
        this.addtime = addtime;
        this.recorder = recorder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStockTaskId() {
        return stockTaskId;
    }

    public void setStockTaskId(Integer stockTaskId) {
        this.stockTaskId = stockTaskId;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public String getRecorder() {
        return recorder;
    }

    public void setRecorder(String recorder) {
        this.recorder = recorder;
    }


    public Integer getIsInspect() {
        return isInspect;
    }

    public void setIsInspect(Integer isInspect) {
        this.isInspect = isInspect;
    }
}
