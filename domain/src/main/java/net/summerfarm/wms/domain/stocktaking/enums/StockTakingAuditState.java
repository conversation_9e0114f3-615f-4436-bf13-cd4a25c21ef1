package net.summerfarm.wms.domain.stocktaking.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * 盘点审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum StockTakingAuditState {
    /**
     * 盘点审核状态
     */
    INIT(0, "待审核"),
    AUDITING(1, "审核中"),
    SUCCESS(2, "审核成功"),
    FAILED(3, "审核失败"),
    UNSUBMIT(4, "待提交"),

    UNKNOWN(999,"未知"),
    ;

    public static StockTakingAuditState convert(Integer param) {
        return Arrays.stream(StockTakingAuditState.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElse(StockTakingAuditState.UNKNOWN);
    }

    Integer code;
    String desc;
}
