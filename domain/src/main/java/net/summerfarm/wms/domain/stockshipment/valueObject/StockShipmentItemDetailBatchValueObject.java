package net.summerfarm.wms.domain.stockshipment.valueObject;

import lombok.Data;

import java.time.LocalDate;

@Data
public class StockShipmentItemDetailBatchValueObject {
    /**
     * 仓库号
     */
    private Integer warehouseNo;
    /**
     * sku
     */
    private String sku;
    /**
     * 调拨单号
     */
    private String listNo;
    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 实际调出数量
     */
    private Integer actualOutQuantity;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    private LocalDate productionDate;
}
