package net.summerfarm.wms.domain.processingtask.delegate.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryChangeTypeEnum;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinetBatchInventoryReduce implements Serializable {

    private static final long serialVersionUID = 4176685253972561266L;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private Integer warehouseNo;

    /**
     * 业务单号
     */
    @ApiModelProperty(value = "业务单号")
    private String orderNo;

    /**
     * 变动类型
     *
     * @see CabinetInventoryChangeTypeEnum
     */
    @ApiModelProperty(value = "变动类型")
    private String orderTypeName;


    /**
     * 内部流转单号
     */
    @ApiModelProperty(value = "内部流转单号")
    private String internalNo;

    /**
     * 操作类别
     *
     * @see net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryOperationType
     */
    @ApiModelProperty(value = "操作类别")
    private Integer operatorType;

    /**
     * 容器编码
     */
    @ApiModelProperty(value = "容器编码")
    private String containerCode;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 增加库存
     */
    private List<CabinetBatchInventoryReduceDetail> reduceDetailReqList;
}
