package net.summerfarm.wms.domain.materialManage.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Data
public class WmsMaterialBindingEntity {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * sku编码
	 */
	private String sku;

	/**
	 * skuSaasId
	 */
	private Long skuSaasId;

	/**
	 * sku名称
	 */
	private String skuName;

	/**
	 * sku比例
	 */
	private BigDecimal skuRatio;

	/**
	 * 状态，0：无效，1：有效
	 */
	private Integer status;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 是否删除标识，0：否，1：是
	 */
	private Integer deleteFlag;

	

	
}