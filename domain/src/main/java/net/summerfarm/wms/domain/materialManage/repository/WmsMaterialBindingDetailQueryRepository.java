package net.summerfarm.wms.domain.materialManage.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingDetailQueryParam;

import java.util.List;
import java.util.Map;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
public interface WmsMaterialBindingDetailQueryRepository {

    PageInfo<WmsMaterialBindingDetailEntity> getPage(WmsMaterialBindingDetailQueryParam param);

    WmsMaterialBindingDetailEntity selectById(Long id);

    List<WmsMaterialBindingDetailEntity> selectByCondition(WmsMaterialBindingDetailQueryParam param);

    List<WmsMaterialBindingDetailEntity> selectByBindingIdList(List<Long> bindingIdList);

    Map<Long, List<WmsMaterialBindingDetailEntity>> mapByBindingIdList(List<Long> bindingIdList);
}