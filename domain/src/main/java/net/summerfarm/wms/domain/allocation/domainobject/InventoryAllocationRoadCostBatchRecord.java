package net.summerfarm.wms.domain.allocation.domainobject;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-08-08 16:51:15
 */
@Data
public class InventoryAllocationRoadCostBatchRecord {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 调拨单号
     */
    private String listNo;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 原在途数量
     */
    private Integer oldRoadQuantity;

    /**
     * 新在途数量
     */
    private Integer newRoadQuantity;

    /**
     * 变更类型
     */
    private String recordType;


}