package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskProcessDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/30
 */
public interface StockTaskProcessDetailCommandRepository {

    /**
     * 新增
     * @param stockTaskProcessDetail
     * @return
     */
    Integer insert(StockTaskProcessDetail stockTaskProcessDetail);

    /**
     * 批量新增
     * @param stockTaskProcessDetails
     */
    void batchInsert(List<StockTaskProcessDetail> stockTaskProcessDetails);

}
