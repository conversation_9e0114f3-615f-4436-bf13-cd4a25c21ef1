package net.summerfarm.wms.domain.pickuptask.domainobject.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 投线任务类型枚举
 * <AUTHOR>
 * @date 2023/02/18
 */
@Getter
@AllArgsConstructor
public enum PickUpTypeEnum {

    /**
     * 1-城配仓
     */
    STORE(1, "城配仓"),

    /**
     * 2-客户清单
     */
    MERCHANT_LIST(2, "客户清单");

    /**
     * 值
     */
    private final int value;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据value返回description
     * @param value 值
     * @return 描述
     */
    public static String getDescriptionByValue(int value) {
        for (PickUpTypeEnum pickUpTypeEnum : PickUpTypeEnum.values()) {
            if (pickUpTypeEnum.getValue() == value) {
                return pickUpTypeEnum.getDescription();
            }
        }
        return "";
    }
}
