package net.summerfarm.wms.domain.processingtask.domainobject.aggregate;

import lombok.Data;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskProductStatusEnum;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ProcessingTaskCreateAggregate implements Serializable {

    /**
     * 加工任务
     */
    private ProcessingTask processingTask;

    /**
     * 加工任务成品
     */
    private List<ProcessingTaskProduct> processingTaskProductList;

    /**
     * 加工任务成品明细
     */
    private List<ProcessingTaskProductRecord> processingTaskProductRecordList;

    /**
     * 加工任务订单成品明细
     */
    private List<ProcessingTaskProductOrderRecord> processingTaskProductOrderRecordList;

    /**
     * 加工多原料
     */
    private List<ProcessingTaskMaterial> processingTaskMaterialList;

    public void addProcessingTaskProductIds(Map<String, ProcessingTaskProduct> processingTaskProductMap) {

        for (ProcessingTaskProductRecord productRecord : processingTaskProductRecordList) {
            ProcessingTaskProduct processingTaskProduct = processingTaskProductMap.get(productRecord.getProductSkuCode());
            if (processingTaskProduct == null){
                throw new BizException("创建加工任务成品不存在:" + productRecord.getProductSkuCode(), ErrorCodeNew.PARAM_ERROR);
            }

            productRecord.setProcessingTaskProductId(processingTaskProduct.getId());

        }

        for (ProcessingTaskProductOrderRecord orderRecord : processingTaskProductOrderRecordList) {
            ProcessingTaskProduct processingTaskProduct = processingTaskProductMap.get(orderRecord.getProductSkuCode());
            if (processingTaskProduct == null){
                throw new BizException("创建加工任务成品不存在:" + orderRecord.getProductSkuCode(), ErrorCodeNew.PARAM_ERROR);
            }

            orderRecord.setProcessingTaskProductId(processingTaskProduct.getId());
        }

        for (ProcessingTaskMaterial processingTaskMaterial : processingTaskMaterialList) {
            ProcessingTaskProduct processingTaskProduct = processingTaskProductMap.get(processingTaskMaterial.getProductSkuCode());
            if (processingTaskProduct == null){
                throw new BizException("创建加工任务成品不存在:" + processingTaskMaterial.getProductSkuCode(), ErrorCodeNew.PARAM_ERROR);
            }

            processingTaskMaterial.setProcessingTaskProductId(processingTaskProduct.getId());
        }
    }

    public void addTaskCode(String taskCode) {
        processingTask.setProcessingTaskCode(taskCode);

        for (ProcessingTaskProduct processingTaskProduct : processingTaskProductList) {
            processingTaskProduct.setProcessingTaskCode(taskCode);
            processingTaskProduct.setStatus(ProcessingTaskProductStatusEnum.NOT_PROCESSING.getValue());
        }

        for (ProcessingTaskProductRecord productRecord : processingTaskProductRecordList) {
            productRecord.setProcessingTaskCode(taskCode);
        }

        for (ProcessingTaskProductOrderRecord orderRecord : processingTaskProductOrderRecordList) {
            orderRecord.setProcessingTaskCode(taskCode);
        }

        for (ProcessingTaskMaterial processingTaskMaterial : processingTaskMaterialList) {
            processingTaskMaterial.setProcessingTaskCode(taskCode);
        }
    }
}
