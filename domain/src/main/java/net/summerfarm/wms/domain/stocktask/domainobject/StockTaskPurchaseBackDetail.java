package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description
 * @Date 2023/10/24 14:22
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskPurchaseBackDetail {

    /**
     * 主键
     */
    private Long id;

    /**
     * 出库任务编码
     */
    private Long stockTaskId;

    /**
     * 出库任务明细ID
     */
    private Long stockTaskItemId;

    /**
     * 退货单号
     */
    private String purchasesBackNo;

    /**
     * 批次
     */
    private String batch;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 0 未到货退订 1 已入库退货
     */
    private Integer type;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 单件成本
     */
    private BigDecimal cost;

    /**
     * 退货数量
     */
    private Integer outQuantity;

    /**
     * 退货总金额
     */
    private BigDecimal totalCost;

    /**
     * 实际退货数量
     */
    private Integer actualOutQuantity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

}
