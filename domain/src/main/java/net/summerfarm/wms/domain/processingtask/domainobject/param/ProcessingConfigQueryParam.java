package net.summerfarm.wms.domain.processingtask.domainobject.param;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ProcessingConfigQueryParam implements Serializable {

    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    private Integer type;
    /**
     * 原料sku
     */
    private String materialSkuCode;
    /**
     * 原料sku名称
     */
    private String materialSkuName;
    /**
     * 原料sku重量
     */
    private BigDecimal materialSkuWeight;
    /**
     * 原料sku单位
     */
    private String materialSkuUnit;
    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 成品sku名称
     */
    private String productSkuName;
    /**
     * 成品sku重量
     */
    private BigDecimal productSkuWeight;
    /**
     * 成品sku单位
     */
    private String productSkuUnit;
    /**
     * 是否作废，0：否，1：是
     */
    private Integer invalid;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;
    /**
     * 原料sku单位描述
     */
    private String materialSkuUnitDesc;
    /**
     * 成品sku单位描述
     */
    private String productSkuUnitDesc;
    /**
     * 加工规则编码
     */
    private String processingConfigCode;

    /**
     * 原料模式，0-单原料，1-多原料
     */
    private Integer materialModel;


    /**
     * 成品规则列表
     */
    private List<String> productSkuCodeList;

    /**
     * 类型列表
     */
    private List<Integer> typeList;

    /**
     * 成品sku比例数量
     */
    private Integer productSkuRatioNum;
}
