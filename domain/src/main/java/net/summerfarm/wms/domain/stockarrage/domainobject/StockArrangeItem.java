package net.summerfarm.wms.domain.stockarrage.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> ct
 * create at:  2022/12/14  10:41
 */
@Data
@Deprecated
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockArrangeItem {

    private Integer id;

    private Integer stockArrangeId;

    private String sku;

    private String pdName;

    private String weight;

    private Integer type;

    private String supplier;

    private Integer qualityTime;

    private String qualityTimeUnit;

    private Integer arrivalQuantity;

    private Integer actualQuantity;

    private Integer supplierId;
}
