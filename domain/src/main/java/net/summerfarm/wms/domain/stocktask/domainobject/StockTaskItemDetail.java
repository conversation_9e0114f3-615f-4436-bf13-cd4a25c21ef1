package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2022/12/13  16:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskItemDetail {

    private Integer id;

    private Integer stockTaskItemId;

    private String sku;

    private String listNo;

    /**
     * 库位编码
     */
    private String cabinetCode;

    private LocalDate qualityDate;
    /**
     * 转入数量
     */
    private Integer quantity;

    private String remark;
    /**
     * 货位编号
     */
    private String glNo;
    /**
     * 应出数量
     */
    private Integer shouldQuantity;

    private LocalDate productionDate;
    /**
     * 名称
     */
    private String name;

    /**
     * 是否操作了该条目是否操作出库 0未操作 1 操作
     */
    private Integer outStoreQuantity;

    /**
     * 是否为系统生成的销售出库单详情 0 是 1 不是
     */
    private Integer systemGeneration;
}
