package net.summerfarm.wms.domain.stockshipment.repository;

import net.summerfarm.wms.domain.stockshipment.valueObject.StockShipmentItemDetailBatchValueObject;

import java.math.BigDecimal;
import java.util.List;

public interface StockShipmentRepository {

    /**
     * 根据任务号和sku查询调拨出库批次详情
     *
     * @param taskNos
     * @param sku
     * @return
     */
    List<StockShipmentItemDetailBatchValueObject> selectByTaskNoAndSku(List<String> taskNos, String sku);

    BigDecimal countCapacity(List<Integer> stockTaskIdList);

}
