package net.summerfarm.wms.domain.skucodetrace.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2024/8/9 10:35<br/>
 *
 * <AUTHOR> />
 */
@Data
public class SkuBatchCodeTraceEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 货品毛重
     */
    private BigDecimal goodGrossWeight;

    /**
     * 货品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 状态
     10：订单待分配
     20：路线待分配
     30：待称重
     40：已称重
     */
    private Integer state;

    /**
     * sku子类型
     */
    private Integer skuSubType;

    /**
     * 批次唯一码
     */
    private String skuBatchOnlyCode;

    /**
     * 批次唯一溯源码
     */
    private String skuBatchTraceCode;

    /**
     * 入库任务id
     */
    private Long stockTaskStorageId;

    /**
     * 批次日期
     */
    private LocalDate batchDate;

    /**
     * 入库任务次序编号
     */
    private Integer stockTaskStorageSeq;

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * OFC采购供应单编号
     */
    private String psoNo;

    /**
     * 订单单号
     */
    private String orderNo;
    /**
     * 履约单号
     */
    private Long fulfillmentNo;
    /**
     * 门店名称
     */
    private String merchantName;

    /**
     * 线路编码
     */
    private String pathCode;

    /**
     * 点位在路线上的顺序
     */
    private Integer pathSequence;

    /**
     * SKU计划签收数量
     */
    private Integer skuPlanReceiptCount;

    /**
     * SKU当前商品次序
     */
    private Integer skuCurrentSeq;

    /**
     * 门店商品总数
     */
    private Integer merchantSkuTotalNum;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 称重人
     */
    private String weightPerson;

    /**
     * 称重时间
     */
    private LocalDateTime weightTime;


    /**
     * 货品Id
     */
    private Long pdId;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 供应商报价类型
     */
    private Integer quoteType;

    /**
     * 联系人ID
     */
    private String contactId;

    /**
     * 门店ID
     */
    private String merchantId;

    /**
     * 履约方式，0：干配，1：自提，2：干线，3：快递
     */
    private Integer fulfillmentWay;

    /**
     * 标签类型0POP，1POPT2
     */
    private Integer labelType;

    /**
     * 租户ID
     */
    private Long tenantId;
}
