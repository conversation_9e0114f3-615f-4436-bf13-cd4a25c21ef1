package net.summerfarm.wms.domain.stocktaking.domainobject.query;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QueryTakingId {
    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * sku
     */
    String sku;

    /**
     * 商品名称
     */
    String pdName;

    /**
     * 租户
     */
    Long tenantId;

    /**
     * 当前租户的仓库号列表
     */
    List<Integer> tenantWarehouseNoList;

}
