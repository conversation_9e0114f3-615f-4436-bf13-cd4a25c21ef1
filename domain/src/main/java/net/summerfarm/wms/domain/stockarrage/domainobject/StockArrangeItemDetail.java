package net.summerfarm.wms.domain.stockarrage.domainobject;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/12/14  10:41
 */
@Data
@Deprecated
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockArrangeItemDetail {

    private Integer id;

    @ApiModelProperty(value = "预约单条目id")
    private Integer stockArrangeItemId;

    @ApiModelProperty(value = "预约单id")
    private Integer stockArrangeId;

    @ApiModelProperty(value = "任务id")
    private Integer stockTaskId;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "预约数量")
    private Integer arrQuantity;

    @ApiModelProperty(value = "到货数量")
    private Integer quantity;

    @ApiModelProperty(value = "货位编号")
    private String glNo;

    @ApiModelProperty(value = "保质期")
    private LocalDate qualityDate;

    @ApiModelProperty(value = "生产日期")
    private LocalDate productionDate;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "发起时间")
    private LocalDateTime createTime;
}
