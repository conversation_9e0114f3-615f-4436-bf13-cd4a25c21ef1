package net.summerfarm.wms.domain.stocktaking.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.domain.base.ValueObject;

/**
 * 商品信息对象
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GoodsInfo implements ValueObject {
    String sku;

    /**
     * 商品名称
     */
    String pdName;

    /**
     * 规格
     */
    String specification;

    /**
     * 商品类目
     */
    String category;

    /**
     * 存储区域
     */
    String temperature;

    /**
     * 包装
     */
    String packaging;

    /**
     * 库存
     */
    Integer stockNum;
}
