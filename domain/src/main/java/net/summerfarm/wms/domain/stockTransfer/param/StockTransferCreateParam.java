package net.summerfarm.wms.domain.stockTransfer.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransferCreateParam implements Serializable {
    private static final long serialVersionUID = -2275812953318056642L;

    /**
     * 主键id
     */
    Long id;

    /**
     * 库存仓编号
     */
    Long warehouseNo;

    /**
     * 转换维度
     *
     */
    Integer transferDimension;

    /**
     * 状态
     *
     */
    Integer state;

    /**
     * 操作人
     */
    String operator;

    /**
     * 备注
     */
    String remark;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;


    /**
     * 任务来源（1:系统自动，2:手动创建）
     */
    private Integer taskSource;
}
