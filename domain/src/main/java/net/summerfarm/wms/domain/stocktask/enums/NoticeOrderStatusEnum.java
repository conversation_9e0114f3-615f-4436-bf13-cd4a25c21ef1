package net.summerfarm.wms.domain.stocktask.enums;

import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Set;

/**
 * @Description
 * @Date 2023/4/10 16:31
 * @<AUTHOR>
 */
@Getter
public enum NoticeOrderStatusEnum {

    /**
     * 出库任务未生成
     */
    INIT(1, "待处理"),
    /**
     * 出库任务生成成功
     */
    FINISHED(2, "已处理"),
    CREATING(10, "出库任务生成中"),
    CREATE_FAIL(20, "出库任务生成失败"),

    /**
     * 仓拦截状态
     */
    // 使用场景为履约仓拦截的时候，需要提前预占通知单，防止生成出库任务
    CANCEL_ING(30, "通知单冻结中"),
    CANCEL(40, "已取消"),
    ;

    public static Set<Integer> CANT_CANCEL = Sets.newHashSet(FINISHED.status, CREATING.status, CANCEL.status);

    public static Set<Integer> CANT_PRE_CANCEL = Sets.newHashSet(FINISHED.status, CREATING.status, CANCEL.status, CANCEL_ING.status);

    public static Set<Integer> CANT_PRE_CANCEL_THROW_EX = Sets.newHashSet(CANCEL.status, CANCEL_ING.status);

    public static Set<Integer> CANT_CHANGE = Sets.newHashSet(CANCEL.status, INIT.status, CREATE_FAIL.status);

    private Integer status;

    private String desc;

    NoticeOrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
