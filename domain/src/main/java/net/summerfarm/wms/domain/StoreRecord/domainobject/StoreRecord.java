package net.summerfarm.wms.domain.StoreRecord.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.util.DateUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  14:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreRecord {
    /**
     * 记录id
     */
    private Integer id;


    /**
     * 采购批次
     */
    private String batch;


    /**
     * sku编号
     */
    private String sku;


    /**
     * 10调拨入库、11采购入库、12退货入库、13拒收回库、14调拨未收入库（实收+拒收小于实发的情况、15盘盈入库、16转换入库、21越仓入库" +
     * "50调拨出库、51销售出库、52出样出库、53货损出库、54盘亏出库、55转换出库、60越仓出库
     */
    private Integer type;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 库存仓编号
     */
    private Integer areaNo;


    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 批次保质期库存
     */
    private Integer storeQuantity;

    /**
     * 货品单价
     */
    private BigDecimal cost;

    /**
     * 货位编码
     */
    private String glNo;

    /**
     * 批次类型：0、正常批次 1、降级批次
     */
    private Integer lotType;

    /**
     * 修改来源
     */
    private Integer insertType;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    public StoreRecord(String batch, String sku, Integer type, Integer quantity, String unit, String recorder, String remark, Date updateTime, Integer areaNo, LocalDate qualityDate, LocalDate productionDate, Integer storeQuantity, BigDecimal cost
            , Long tenantId) {
        this.batch = batch;
        this.sku = sku;
        this.type = type;
        this.quantity = quantity;
        this.unit = unit;
        this.recorder = recorder;
        this.remark = remark;
        this.updateTime = updateTime;
        this.areaNo = areaNo;
        this.qualityDate = qualityDate;
        this.productionDate = productionDate;
        this.storeQuantity = storeQuantity;
        this.cost = cost;
        this.tenantId = tenantId;
    }

    public Integer queryStoreQuantity() {
        if (Objects.isNull(storeQuantity)) {
            return 0;
        }
        return storeQuantity;
    }

    public String dupKey() {
        return areaNo + sku + DateUtil.toDate(productionDate) + DateUtil.toDate(qualityDate) + batch;
    }
}
