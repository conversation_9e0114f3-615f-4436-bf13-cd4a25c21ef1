package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskNoticeOrderQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 货品供应单号
     */
    private String goodsSupplyNo;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 订单类型
     */
    private Integer outOrderType;

    /**
     * 预计送达时间
     */
    private LocalDate exceptTime;

    /**
     * 通知单状态
     */
    private Integer status;

    /**
     * 收货人姓名
     */
    private String receiver;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否软删
     */
    private Byte isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 起始生成时间
     */
    private LocalDate startTime;

    /**
     * 结束生成时间
     */
    private LocalDate endTime;

    /**
     * 页数
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 货品名称
     */
    private String goodsName;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 仓库列表
     */
    private List<Integer> warehouseNoList;

}
