package net.summerfarm.wms.domain.stocktaking.domainobject.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryStocktakingBySaas implements Serializable {

    /**
     * 盘点开始时间
     */
    private LocalDate stockTakingStartTime;

    /**
     * 盘点结束时间
     */
    private LocalDate stockTakingEndTime;

    /**
     * 盘点任务id
     */
    private Long stockTakingId;

    /**
     * 任务id列表
     */
    private List<Long> stockTakingIdList;

    /**
     * 仓库
     */
    private Long warehouseNo;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 分页查询条件
     */
    private Integer pageNum;

    /**
     * 分页查询条件
     */
    private Integer pageSize;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 当前租户的仓库号列表
     */
    List<Integer> tenantWarehouseNoList;

}
