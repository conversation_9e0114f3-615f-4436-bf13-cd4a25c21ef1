package net.summerfarm.wms.domain.stockTransfer.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransferItemCreateParam implements Serializable {
    private static final long serialVersionUID = 5427882101540372428L;

    /**
     * 主键id
     */
    Long id;

    /**
     * 任务id
     */
    Long stockTransferId;

    /**
     * 转入sku
     */
    String transferInSku;

    /**
     * 预转入数量
     */
    Long preTransferInNum;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

}
