package net.summerfarm.wms.domain.StoreRecord.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2023-08-02
 **/
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreRecordDaySummary {
	Long id;

	Integer warehouseNo;

	String warehouseName;

	String warehouseProvider;

	
	Long pdId;

	String sku;

	Long saasSkuId;

	Integer categoryId;

	Long skuTenantId;

	Long warehouseTenantId;

	Integer openingQuantity;

	BigDecimal openingAmount;
	Integer endingQuantity;
	BigDecimal endingAmount;
	Integer allocationInQuantity;
	BigDecimal allocationInAmount;
	Integer purchaseInQuantity;
	BigDecimal purchaseInAmount;
	Integer afterSaleInQuantity;
	BigDecimal afterSaleInAmount;
	Integer stockTakingInQuantity;
	BigDecimal stockTakingInAmount;
	Integer transferInQuantity;
	BigDecimal transferInAmount;
	Integer allocationAbnormalInQuantity;
	BigDecimal allocationAbnormalInAmount;
	Integer otherInQuantity;
	BigDecimal otherInAmount;
	Integer inQuantity;
	BigDecimal inAmount;
	Integer allocationOutQuantity;
	BigDecimal allocationOutAmount;
	Integer saleOutQuantity;
	BigDecimal saleOutAmount;
	Integer damageOutQuantity;
	BigDecimal damageOutAmount;
	Integer stockTakingOutQuantity;
	BigDecimal stockTakingOutAmount;
	Integer transferOutQuantity;
	BigDecimal transferOutAmount;
	Integer purchaseBackOutQuantity;
	BigDecimal purchaseBackOutAmount;
	Integer supplyAgainOutQuantity;
	BigDecimal supplyAgainOutAmount;

	Integer ownSelfOutQuantity;

	BigDecimal ownSelfOutAmount;

	Integer otherOutQuantity;

	BigDecimal otherOutAmount;

	Integer outQuantity;

	BigDecimal outAmount;

	LocalDateTime createTime;

	LocalDateTime updateTime;

	/**
	 * 数据所属日期:格式yyyyMMdd
	 */
	Integer dayTag;

	/**
	 * 期初入库数量
	 */
	Integer initInQuantity;

	/**
	 * 期初入库金额
	 */
	BigDecimal initInAmount;

	/**
	 * 缺货入库数量
	 */
	Integer lackInQuantity;

	/**
	 * 缺货入库金额
	 */
	BigDecimal lackInAmount;

	/**
	 * 拦截入库数量
	 */
	Integer interceptInQuantity;

	/**
	 * 拦截入库金额
	 */
	BigDecimal interceptInAmount;

	/**
	 * 多出入库数量
	 */
	Integer outMoreInQuantity;

	/**
	 * 多出入库金额
	 */
	BigDecimal outMoreInAmount;

	/**
	 * 调拨货损数量
	 */
	Integer allocationDamageOutQuantity;

	/**
	 * 调拨货损金额
	 */
	Integer allocationDamageOutAmount;
	
}
