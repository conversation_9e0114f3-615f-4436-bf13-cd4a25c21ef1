package net.summerfarm.wms.domain.processingtask.domainobject.param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-01-21 10:43:37
 * @version 1.0
 *
 */
@Data
public class WmsProcessingTaskMaterialQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * 加工任务编号
	 */
	private String processingTaskCode;

	/**
	 * 加工任务成品id
	 */
	private Long processingTaskProductId;

	/**
	 * 成品sku
	 */
	private String productSkuCode;

	/**
	 * 原料sku编号
	 */
	private String materialSkuCode;

	/**
	 * 原料sku名称
	 */
	private String materialSkuName;

	/**
	 * 原料sku重量
	 */
	private BigDecimal materialSkuWeight;

	/**
	 * 原料sku单位
	 */
	private String materialSkuUnit;

	/**
	 * 原料sku使用数量
	 */
	private Integer materialSkuQuantity;

	/**
	 * 原料sku领料数量
	 */
	private Integer materialSkuReceiveQuantity;

	/**
	 * 原料sku领料重量
	 */
	private BigDecimal materialSkuReceiveWeight;

	/**
	 * 原料sku归还数量
	 */
	private Integer materialSkuRestoreQuantity;

	/**
	 * 废料损耗重量，人工填写
	 */
	private BigDecimal wasteLossWeight;

	/**
	 * 规格损耗重量，加工实重-加工数量*规格重量
	 */
	private BigDecimal specLossWeight;

	/**
	 * 原料SKU剩余重量，领料总重-加工实重-废料损耗
	 */
	private BigDecimal materialSkuRemainWeight;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 是否删除标识，0：否，1：是
	 */
	private Integer deleteFlag;

	/**
	 * 原料sku比例数量
	 */
	private Integer materialSkuRatioNum;

	/**
	 * 加工任务成品idList
	 */
	private List<Long> processingTaskProductIdList;

	
}