package net.summerfarm.wms.domain.materialManage.domianobject.aggregate;

import lombok.Data;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskCommandParam;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskDetailCommandParam;

import java.util.List;

@Data
public class WmsMaterialTaskRestoreAggregate {


    /**
     * 任务创建
     */
    private WmsMaterialTaskCommandParam taskCommandParam;

    /**
     * 任务明细创建
     */
    private List<WmsMaterialTaskDetailCommandParam> taskDetailCommandParam;
}
