package net.summerfarm.wms.domain.stockTransfer.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockTransferItemOpEntity implements Serializable {
    private static final long serialVersionUID = 6429357889971836350L;

    /**
     * 主键id
     */
    Long id;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 关联表id
     */
    Long stockTransferItemId;

    /**
     * 转换类型
     * 0-单一，1-混合
     */
    Integer type;

    /**
     * 转换比例
     */
    String transferRatio;

    /**
     * 转出sku
     */
    String transferOutSku;

    /**
     * 转入批次的生产日期
     */
    Long produceDate;

    /**
     * 转入批次的保质期
     */
    Long shelfLife;

    /**
     * 操作人
     */
    String operator;

}
