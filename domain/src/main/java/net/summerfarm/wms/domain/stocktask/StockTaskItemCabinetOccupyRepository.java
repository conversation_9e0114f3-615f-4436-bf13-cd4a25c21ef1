package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItemCabinetOccupy;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * @Date 2023/5/24 17:43
 * @<AUTHOR>
 */
public interface StockTaskItemCabinetOccupyRepository {

    /**
     * 查询出库任务库存明细占用信息
     *
     * <AUTHOR>
     * @date 2023/5/24 17:48
     * @param stockTaskId
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItemCabinetOccupy>
     */
    List<StockTaskItemCabinetOccupy> queryStockTaskCabinetOccupy(Integer stockTaskId);

    /**
     * 查询出库任务库存明细占用信息
     *
     * @date 2023/5/24 17:48
     * @param stockTaskIdList 任务id列表
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItemCabinetOccupy>
     */
    Map<Long, List<StockTaskItemCabinetOccupy>> queryStockTaskCabinetOccupyMulti(List<Long> stockTaskIdList);

    List<StockTaskItemCabinetOccupy> queryStockTaskCabinetOccupyListMulti(List<Long> stockTaskIdList);

    List<StockTaskItemCabinetOccupy> selectListByStockTaskId(Integer stockTaskId, List<String> skuCodeList);

    List<StockTaskItemCabinetOccupy> selectListByStockTaskIdExcludedJJAndCY(Integer stockTaskId, List<String> skuCodeList);

    List<StockTaskItemCabinetOccupy> selectListByStockTaskIdExcludedCY(Integer stockTaskId, List<String> skuCodeList);

    StockTaskItemCabinetOccupy selectListByStockTaskIdAndQualityExcludedJJAndCY(Integer stockTaskId, String skuCode, String cabinetCode, Date productionDate, Date qualityDate);

    List<StockTaskItemCabinetOccupy> selectListByStockTaskIdForJJ(Integer stockTaskId, List<String> skuCodeList);

    /**
     * 释放出库任务库位库存
     *
     * <AUTHOR>
     * @date 2023/5/30 14:50
     * @param stockTaskId 出库任务编码
     * @param sku sku
     * @param cabinetCode 库位编码
     */
    void releaseStockTaskCabinetInventory(Integer stockTaskId, String sku, String cabinetCode);

    /**
     * 释放变更
     * @param id
     * @param releaseChangeQuantity
     */
    void updateReleaseChange(Long id, Integer releaseChangeQuantity);

    /**
     * 更新数量新增
     * @param id
     * @param occupyQuantityAdd
     */
    void updateOccupyQuantityAdd(Long id, Integer occupyQuantityAdd);

    /**
     * 更新应拣数量
     * @param id
     * @param shouldPickQuantity
     */
    void updateShouldPickQuantity(Long id, Integer shouldPickQuantity);

    /**
     * 更新时间拣货数量
     * @param id
     * @param actualPickQuantity
     */
    void updateActualPickQuantity(Long id, Integer actualPickQuantity);

    void updateShouldAndActualPickQuantity(Long id, Integer shouldPickQuantity, Integer actualPickQuantity);

    void updateShouldAndAbnormalPickQuantity(Long id, Integer shouldPickQuantity, Integer abnormalQuantity);

    void updateShouldAndActualAndAbnormalPickQuantity(Long id, Integer shouldPickQuantity, Integer actualPickQuantity, Integer abnormalQuantity);

    /**
     * 更新缺拣数量
     * @param id
     * @param abnormalQuantity
     */
    void updateAbnormalQuantity(Long id, Integer abnormalQuantity);

    List<StockTaskItemCabinetOccupy> selectListByStockTaskIdOccupyed(Integer stockTaskId, List<String> skuCodeList);

}
