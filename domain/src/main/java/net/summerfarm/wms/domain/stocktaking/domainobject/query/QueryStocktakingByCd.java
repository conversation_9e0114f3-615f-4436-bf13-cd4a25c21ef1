package net.summerfarm.wms.domain.stocktaking.domainobject.query;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QueryStocktakingByCd {
    Long warehouseNo;
    /**
     * 页面列表人为选择的id
     */
    Long id;
    /**
     * 根据条件筛选出来的任务id
     */
    List<Long> ids;
    Integer auditState;
    Integer state;
    Integer dimension;
    Integer cycle;
    Long startAt;
    Long endAt;
    Integer pageNum;
    Integer pageSize;
    /**
     * 租户
     */
    Long tenantId;

    /**
     * 盘点方式
     * 10-明盘，20-盲盘
     */
    Integer method;

    /**
     * 当前租户的仓库号列表
     */
    List<Integer> tenantWarehouseNoList;
}
