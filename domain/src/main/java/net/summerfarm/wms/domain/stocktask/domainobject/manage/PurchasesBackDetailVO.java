package net.summerfarm.wms.domain.stocktask.domainobject.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.dto.SaasSku;
import net.summerfarm.wms.common.enums.manage.SkuTypeEnum;
import net.summerfarm.wms.domain.stocktask.domainobject.PurchasesBackDetail;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItemCabinetOccupyDO;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskItemCabinetPickDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @author: dongcheng
 * @date: 2023/7/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PurchasesBackDetailVO extends PurchasesBackDetail implements SaasSku {

    private String pdName;

    private String weight;

    @ApiModelProperty(value = "剩余批次库存")
    private Integer storeQuantity;

    private Integer stockTaskId;

    /**
     *退货单审核状态
     */
    private Integer status;

    private String supplier;

    @ApiModelProperty(value = "储存区域 ")
    private String storageArea;

    @ApiModelProperty(value = "包装方式")
    private String packing;

    private  Integer supplierId;

    @ApiModelProperty(value = "大客户名称备注")
    private String nameRemakes;

    @ApiModelProperty(value = "sku类型 0 自营 1 代仓")
    private Integer skuType;

    private Integer extType;

    /**
     * 采购单采购个数
     */
    private Integer quantity;

    /**
     * 采购单总成本
     */
    private BigDecimal price;

    /**
     * 采购单单个成本
     */
    private BigDecimal singleCost;

    @ApiModelProperty(value = "采购计划id")
    private Integer purchasePlanId;

    private String pic;
    private String unit;

    private Long saasSkuId;

    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    private Long skuId;

    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    @ApiModelProperty(value = "一级类目，鲜果、非鲜果")
    private String firstLevelCategory;

    @ApiModelProperty(value = "二级类目")
    private String secondLevelCategory;

    /**
     * 采购退货库位锁定明细
     */
    @ApiModelProperty(value = "采购退货库位锁定明细")
    private List<StockTaskItemCabinetOccupyDO> cabinetOccupyDOList;

    /**
     * 采购退货拣货明细
     */
    @ApiModelProperty(value = "采购退货拣货明细")
    private List<StockTaskItemCabinetPickDO> cabinetPickList;

    /**
     * 国产还是进口
     */
    private Integer isDomestic;

    /**
     * 类目
     */
    private Integer categoryType;

    @ApiModelProperty(value = "实际生成拣货数量")
    private Integer actualGenPickQuantity;

    /**
     * 是否系统自动处理
     */
    private Boolean systemAuto;

    /**
     * 其他属性
     */
    public String getOtherAttribute() {
        String skuTypeStr = "";
        // 自营还是代仓
        if (SkuTypeEnum.SELF_SUPPORT.getId().equals(skuType)) {
            skuTypeStr = SkuTypeEnum.SELF_SUPPORT.getStatus();
        } else if (SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getId().equals(skuType)) {
            skuTypeStr = SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getStatus();
        }
        // 包装方式 packing
        // 国产还是进口
        String origin = Objects.equals(NumberUtils.INTEGER_ZERO, isDomestic) ? "进口" : "国产";
        return StringUtils.joinWith("/", packing, skuTypeStr, origin);
    }

    public Integer getActualGenPickQuantity() {
        return actualGenPickQuantity == null ? 0 : actualGenPickQuantity;
    }
}
