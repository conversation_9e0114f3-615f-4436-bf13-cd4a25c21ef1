package net.summerfarm.wms.domain.materialManage.service;


import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskDetailCommandParam;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialTaskDetailCommandRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @Title: 物料任务明细领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-03-18 15:49:27
 * @version 1.0
 *
 */
@Service
public class WmsMaterialTaskDetailCommandDomainService {


    @Autowired
    private WmsMaterialTaskDetailCommandRepository wmsMaterialTaskDetailCommandRepository;

    public WmsMaterialTaskDetailEntity insert(WmsMaterialTaskDetailCommandParam param) {
        return wmsMaterialTaskDetailCommandRepository.insertSelective(param);
    }


    public int update(WmsMaterialTaskDetailCommandParam param) {
        return wmsMaterialTaskDetailCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wmsMaterialTaskDetailCommandRepository.remove(id);
    }
}
