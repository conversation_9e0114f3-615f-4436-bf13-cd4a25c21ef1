package net.summerfarm.wms.domain.StoreRecord.domainobject.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询出入库汇总请求参数
 *
 * <AUTHOR>
 * @Date 2023-08-02
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryStoreRecordDaySummary {

	/**
	 * 货品编码
	 */
	private Long pdId;

	/**
	 * SaaS skuId
	 */
	private Long saasSkuId;

	/**
	 * 仓库号
	 */
	private Integer warehouseNo;

	/**
	 * 类目id
	 */
	private Integer categoryId;

	/**
	 * 开始日期，格式为yyyyMMdd
	 */
	private Integer startDate;

	/**
	 * 结束日期，格式为yyyyMMdd
	 */
	private Integer endDate;

	/**
	 * 租户id
	 */
	private Long skuTenantId;

	private Integer pageNum;
	private Integer pageSize;

	/**
	 * 类目id集合
	 */
	private List<Long> categoryIdList;
}
