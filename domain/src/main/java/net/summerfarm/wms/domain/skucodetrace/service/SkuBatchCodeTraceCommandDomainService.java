package net.summerfarm.wms.domain.skucodetrace.service;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.FulfillmentWayEnum;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.enums.SkuBatchCodeTraceEnums;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceCreateCommandParam;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceQueryParam;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceCommandRepository;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceQueryRepository;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.ofc.PurchaseSupplyQueryFacade;
import net.summerfarm.wms.facade.ofc.dto.PurchaseSupplyDetailDTO;
import net.summerfarm.wms.facade.tms.TmsDistOrderQueryFacade;
import net.summerfarm.wms.facade.tms.dto.DeliverySiteItemDTO;
import net.summerfarm.wms.facade.tms.dto.TmsOrderPathDTO;
import net.summerfarm.wms.facade.tms.enums.DistOrderCityStatusEnum;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 唯一溯源码服务<br/>
 * date: 2024/8/9 10:50<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class SkuBatchCodeTraceCommandDomainService {

    @Resource
    private SkuBatchCodeTraceCommandRepository skuBatchCodeTraceCommandRepository;
    @Resource
    private SkuBatchCodeTraceDistributor skuBatchCodeTraceDistributor;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private PurchaseSupplyQueryFacade purchaseSupplyQueryFacade;
    @Resource
    private SkuBatchCodeTraceQueryRepository skuBatchCodeTraceQueryRepository;
    @Resource
    private TmsDistOrderQueryFacade tmsDistOrderQueryFacade;

    /**
     * 创建唯一溯源码
     * @param params 入参
     * @return 溯源码
     */
    @Transactional(rollbackFor = Exception.class)
    public List<SkuBatchCodeTraceEntity> skuBatchCodeTraceCreate(List<SkuBatchCodeTraceCreateCommandParam> params) {
        if(CollectionUtils.isEmpty(params)){
            return Collections.emptyList();
        }
        SkuBatchCodeTraceCreateCommandParam param = params.get(0);
        Integer warehouseNo = param.getWarehouseNo();
        Long stockTaskStorageId = param.getStockTaskStorageId();
        // 查询数据库是否已经存在此任务
        List<SkuBatchCodeTraceEntity> stockTaskStorageCodeTraceList = skuBatchCodeTraceQueryRepository.findByStockTaskStorageId(stockTaskStorageId);
        if(!CollectionUtils.isEmpty(stockTaskStorageCodeTraceList)){
            log.info("根据入库任务ID查询唯一码信息已存在，stockTaskStorageId:{}", stockTaskStorageId);
            return Collections.emptyList();
        }

        // 根据sku查询货品信息
        List<String> skuList = params.stream().map(SkuBatchCodeTraceCreateCommandParam::getSku).collect(Collectors.toList());
        Map<String, GoodsInfoDTO> skuGoodsMap = goodsReadFacade.mapGoodsInfoBySkus(warehouseNo.longValue(), skuList, null);

        // 批量构建唯一码信息参数
        List<SkuBatchCodeTraceEntity> createTraceEntityParamList = skuBatchCodeTraceDistributor.batchBuildCodeTraceList(params, skuGoodsMap);

        // 创建唯一码初始化信息
        return skuBatchCodeTraceCommandRepository.batchSave(createTraceEntityParamList);
    }

    /**
     * 更新溯源码订单信息
     * @param waitOrderAllocationSkuBatchCodeTraceList 订单待分配的唯一码信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCodeTraceOrderInfo(List<SkuBatchCodeTraceEntity> waitOrderAllocationSkuBatchCodeTraceList) {
        if(CollectionUtils.isEmpty(waitOrderAllocationSkuBatchCodeTraceList)){
            return;
        }
        List<PurchaseSupplyDetailDTO> purchaseSupplyDetailDTOS = this.queryOrderWaitAllocationDetailByPurNos(waitOrderAllocationSkuBatchCodeTraceList);
        if(CollectionUtils.isEmpty(purchaseSupplyDetailDTOS)){
            return;
        }

        // 订单分配到码上面
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntities = skuBatchCodeTraceDistributor.distributeOrder2SkuBatchCodeTrace(purchaseSupplyDetailDTOS, waitOrderAllocationSkuBatchCodeTraceList);

        // 存在自提的单据，将状态更新待称重
        skuBatchCodeTraceEntities.forEach(skuBatchCodeTraceEntity -> {
            if(Objects.equals(skuBatchCodeTraceEntity.getFulfillmentWay(),FulfillmentWayEnum.SELF_PICKUP.getValue())){
                skuBatchCodeTraceEntity.setState(SkuBatchCodeTraceEnums.State.WEIGHT_WAIT_ALLOCATION.getValue());
            }
        });

        // 更新订单信息
        skuBatchCodeTraceCommandRepository.batchUpdate(skuBatchCodeTraceEntities);
    }

    /**
     * 更新溯源码排线配送信息
     * @param deliveryTime 配送日期
     * @param storeNo 城配仓编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDeliveryPathInfo(LocalDate deliveryTime,Integer storeNo) {
        if(deliveryTime == null){
            return;
        }
        // 根据配送日期拉取溯源码-状态 路线待分配
        List<SkuBatchCodeTraceEntity> waitPathAllocationTraceList =  skuBatchCodeTraceQueryRepository.findList(SkuBatchCodeTraceQueryParam.builder()
                .deliveryTime(deliveryTime)
                .state(SkuBatchCodeTraceEnums.State.PATH_WAIT_ALLOCATION.getValue())
                .storeNo(storeNo)
                .build());
        if(CollectionUtils.isEmpty(waitPathAllocationTraceList)){
            return;
        }
        // 查询TMS配送数据
        List<String> orderNos = waitPathAllocationTraceList.stream().map(SkuBatchCodeTraceEntity::getOrderNo).distinct().collect(Collectors.toList());

        Map<String, TmsOrderPathDTO> orderPathMap = tmsDistOrderQueryFacade.queryOrderNoDeliveryInfoMap(orderNos, deliveryTime);

        for (SkuBatchCodeTraceEntity waitPathCodeTrace : waitPathAllocationTraceList) {
            TmsOrderPathDTO tmsOrderPathDTO = orderPathMap.get(waitPathCodeTrace.getOrderNo());
            if(tmsOrderPathDTO == null){
                continue;
            }
            // 配送状态不存在或者状态未完成排线，不需要处理
            if(tmsOrderPathDTO.getStatus() == null || tmsOrderPathDTO.getStatus() < DistOrderCityStatusEnum.TO_BE_PICKED.getCode()){
                continue;
            }
            waitPathCodeTrace.setPathCode(tmsOrderPathDTO.getPathCode());
            waitPathCodeTrace.setPathSequence(tmsOrderPathDTO.getPathSequence());
            waitPathCodeTrace.setState(SkuBatchCodeTraceEnums.State.WEIGHT_WAIT_ALLOCATION.getValue());
            waitPathCodeTrace.setMerchantSkuTotalNum(tmsOrderPathDTO.getMerchantSkuTotalNum());
            waitPathCodeTrace.setContactId(tmsOrderPathDTO.getOutContactId());
            waitPathCodeTrace.setMerchantId(tmsOrderPathDTO.getOutClientId());
            List<DeliverySiteItemDTO> deliverySiteItemDTOList = tmsOrderPathDTO.getDeliverySiteItemDTOList();
            if(!CollectionUtils.isEmpty(deliverySiteItemDTOList)){
                deliverySiteItemDTOList.forEach(item -> {
                    if(Objects.equals(waitPathCodeTrace.getSku(),item.getSku())){
                        waitPathCodeTrace.setSkuPlanReceiptCount(item.getSkuPlanReceiptCount());
                    }
                });
            }
        }
        // 按照联系人ID分组处理 分配门店的SKU次序
        Map<String, List<SkuBatchCodeTraceEntity>> contactIdToTraceListMap = waitPathAllocationTraceList.stream()
                .filter(trace -> !StringUtils.isEmpty(trace.getContactId()))
                .collect(Collectors.groupingBy(SkuBatchCodeTraceEntity::getContactId));
        // 地址ID维度 SKU次序分配
        contactIdToTraceListMap.forEach((contactId, traceList) -> {
            // 相同SKU次序分配
            Map<String, List<SkuBatchCodeTraceEntity>> skuToTraceListMap = traceList.stream().collect(Collectors.groupingBy(SkuBatchCodeTraceEntity::getSku));
            skuToTraceListMap.forEach((sku, sameSkuTraces)->{
                sameSkuTraces.sort(Comparator.comparing(SkuBatchCodeTraceEntity::getStockTaskStorageSeq));
                for (int i = 0; i < sameSkuTraces.size(); i++) {
                    sameSkuTraces.get(i).setSkuCurrentSeq(i + 1);
                }
            });
        });

        // 更新溯源码状态
        skuBatchCodeTraceCommandRepository.batchUpdate(waitPathAllocationTraceList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCodeTraceWeight(SkuBatchCodeTraceEntity skuBatchCodeTraceEntity,
                                      BigDecimal weight,
                                      String weightUserName,
                                      LocalDateTime weightDateTime) {
        // 更新重量
        skuBatchCodeTraceCommandRepository.updateWeight(skuBatchCodeTraceEntity, weight, weightUserName, weightDateTime);
    }

    /**
     * 设置溯源码城配仓信息
     * @param waitOrderAllocationSkuBatchCodeTraceList 溯源码信息
     */
    public void updateCodeTraceStoreNoInfo(List<SkuBatchCodeTraceEntity> waitOrderAllocationSkuBatchCodeTraceList) {
        if(CollectionUtils.isEmpty(waitOrderAllocationSkuBatchCodeTraceList)){
            log.info("更新溯源码城配仓信息，参数为空，处理结束。");
            return;
        }
        List<PurchaseSupplyDetailDTO> purchaseSupplyDetailDTOS = this.queryOrderWaitAllocationDetailByPurNos(waitOrderAllocationSkuBatchCodeTraceList);
        if(CollectionUtils.isEmpty(purchaseSupplyDetailDTOS)){
            return;
        }
        // 城配仓分配到码上面,状态【带称重】
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntities = skuBatchCodeTraceDistributor.distributeStoreInfo2SkuBatchCodeTrace(purchaseSupplyDetailDTOS, waitOrderAllocationSkuBatchCodeTraceList);

        // 更新信息
        skuBatchCodeTraceCommandRepository.batchUpdate(skuBatchCodeTraceEntities);
    }

    /**
     * 根据采购供应单查询订单信息
     * @param waitOrderAllocationSkuBatchCodeTraceList 溯源码信息
     * @return 采购供应单明细
     */
    public List<PurchaseSupplyDetailDTO> queryOrderWaitAllocationDetailByPurNos(List<SkuBatchCodeTraceEntity> waitOrderAllocationSkuBatchCodeTraceList){
        if(CollectionUtils.isEmpty(waitOrderAllocationSkuBatchCodeTraceList)){
            return Collections.emptyList();
        }
        // 溯源批次码状态是订单待分配的
        waitOrderAllocationSkuBatchCodeTraceList = waitOrderAllocationSkuBatchCodeTraceList.stream().filter(skuBatchCodeTraceEntity -> skuBatchCodeTraceEntity.getState()
                .equals(SkuBatchCodeTraceEnums.State.ORDER_WAIT_ALLOCATION.getValue())).collect(Collectors.toList());

        // 根据采购供应单查询订单信息
        List<String> psoNos = waitOrderAllocationSkuBatchCodeTraceList.stream().map(SkuBatchCodeTraceEntity::getPsoNo).distinct().collect(Collectors.toList());

        // 订单查询OFC
        List<PurchaseSupplyDetailDTO> purchaseSupplyDetailDTOS = purchaseSupplyQueryFacade.queryOrderDetailByPurchaseSupplyNos(psoNos);
        if(CollectionUtils.isEmpty(purchaseSupplyDetailDTOS)){
            BizException bizException = new BizException("货品供应单没有对应的订单信息psoNos:" + psoNos);
            log.error("货品供应单没有对应的订单信息psoNos:{}",psoNos,bizException);
            return Collections.emptyList();
        }

        return purchaseSupplyDetailDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSkuBatchCodeTraceFulfillmentOrderInfo(List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntities) {
        if(CollectionUtils.isEmpty(skuBatchCodeTraceEntities)){
            return;
        }
        skuBatchCodeTraceCommandRepository.batchUpdate(skuBatchCodeTraceEntities);
    }
}
