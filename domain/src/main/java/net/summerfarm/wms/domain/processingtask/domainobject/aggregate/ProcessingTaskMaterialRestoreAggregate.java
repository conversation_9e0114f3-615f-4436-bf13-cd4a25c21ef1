package net.summerfarm.wms.domain.processingtask.domainobject.aggregate;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialRestoreRecord;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Slf4j
public class ProcessingTaskMaterialRestoreAggregate implements Serializable {

    /**
     * 原料领料Id
     */
    private Long materialReceiveId;


    /**
     * 原料sku归还数量
     */
    private Integer materialSkuRestoreQuantity;

    /**
     * 原料sku归还重量
     */
    private BigDecimal materialSkuRestoreWeight;

    /**
     * 加工任务原料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 加工任务成品ID
     */
    private Long processingTaskProductId;


    /**
     * 归还记录
     */
    private ProcessingTaskMaterialRestoreRecord processingTaskMaterialRestoreRecord;

}
