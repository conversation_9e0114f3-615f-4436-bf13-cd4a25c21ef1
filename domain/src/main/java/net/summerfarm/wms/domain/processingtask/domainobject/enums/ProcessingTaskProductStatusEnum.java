package net.summerfarm.wms.domain.processingtask.domainobject.enums;

public enum ProcessingTaskProductStatusEnum {


    NOT_PROCESSING(0, "未加工"),

    PROCESSED(1, "加工完成"),

    ;

    private final int value;

    private final String description;

    ProcessingTaskProductStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public boolean equalsCode(Integer input){
        return Integer.valueOf(this.value).equals(input);
    }
}
