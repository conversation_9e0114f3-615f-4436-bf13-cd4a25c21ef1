package net.summerfarm.wms.domain.materialManage.repository;



import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
public interface WmsMaterialTaskQueryRepository {

    PageInfo<WmsMaterialTaskEntity> getPage(WmsMaterialTaskQueryParam param);

    WmsMaterialTaskEntity selectById(Long id);

    WmsMaterialTaskEntity selectByTaskCode(String taskCode);

    List<WmsMaterialTaskEntity> selectByCondition(WmsMaterialTaskQueryParam param);

}