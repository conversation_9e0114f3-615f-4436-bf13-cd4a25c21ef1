package net.summerfarm.wms.domain.StoreRecord.domainobject.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @author: dongcheng
 * @date: 2023/9/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryStoreRecordInProve {

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 批次编号
     */
    private String batch;

    /**
     * sku
     */
    private String sku;

    /**
     * 生产日期开始时间
     */
    private LocalDate productionDateStart;
    /**
     * 生产日期结束时间
     */
    private LocalDate productionDateEnd;

}
