package net.summerfarm.wms.domain.stocktask.factory;

import net.summerfarm.wms.common.enums.SaleOutTypeEnum;
import net.summerfarm.wms.common.enums.StockTaskStateEnum;
import net.summerfarm.wms.domain.instore.entity.WmsStockTaskStorageNoticeOrderEntity;
import net.summerfarm.wms.domain.instore.enums.NoticeStockTaskStorageOrderTypeEnum;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTask;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/20
 */
@Component
public class AfterSaleStockTaskFactory {

    public StockTask build(WmsStockTaskStorageNoticeOrderEntity wmsStockTaskStorageNoticeOrderEntity) {
        StockTask stockTask = new StockTask();
        stockTask.setTaskNo(wmsStockTaskStorageNoticeOrderEntity.getOutOrderNo());
        stockTask.setAreaNo(wmsStockTaskStorageNoticeOrderEntity.getWarehouseNo());
        stockTask.setAddtime(LocalDateTime.now());
        stockTask.setType(NoticeStockTaskStorageOrderTypeEnum.getStockTaskTypeByOrderType(wmsStockTaskStorageNoticeOrderEntity.getAferSaleOrderType()));
        stockTask.setExpectTime(LocalDate.now().plusDays(1).atTime(0, 0));
        stockTask.setState(StockTaskStateEnum.WAIT_IN_OUT.getId());
        stockTask.setOutStoreNo(wmsStockTaskStorageNoticeOrderEntity.getStoreNo());
        stockTask.setOutType(SaleOutTypeEnum.ACCROSS_STORE.ordinal());
        return stockTask;
    }

}
