package net.summerfarm.wms.domain.stocktaking.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * 盘点维度
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum StocktakingDimension {
    /**
     * sku盘点维度
     */
    SKU(0, "sku盘点"),
    BATCH(1, "批次盘点"),
    CABINET(2,"库位盘点"),

    UNKNOWN(99,"未知")
    ;

    public static StocktakingDimension convert(Integer param) {
        return Arrays.stream(StocktakingDimension.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElse(StocktakingDimension.UNKNOWN);
    }

    Integer code;
    String desc;
}
