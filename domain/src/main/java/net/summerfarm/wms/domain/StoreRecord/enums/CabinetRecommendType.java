package net.summerfarm.wms.domain.StoreRecord.enums;

import org.springframework.util.CollectionUtils;

import java.util.List;

public enum CabinetRecommendType {

    /**
     * 任务类型
     */
    INIT_IN(8, "期初入库"),
    STORE_ALLOCATION_IN(10, "调拨入库"),
    PURCHASE_IN(11, "采购入库"),
    AFTER_SALE_IN(12, "退货入库"),
    AFTER_SALE_IN_NEW(19, "退货入库"),

    SHELF_ON(82,"库内上架"),

    ;

    CabinetRecommendType(int id, String name) {
        this.id = id;
        this.name = name;
    }

    private final int id;

    private final String name;

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    /**
     * 匹配其中的一个
     * @param cabinetRecommendTypeList
     * @param cabinetRecommendTypeId
     * @return
     */
    public static boolean matchOne(List<CabinetRecommendType> cabinetRecommendTypeList,
                                   Integer cabinetRecommendTypeId){
        if (cabinetRecommendTypeId == null){
            return false;
        }
        if (CollectionUtils.isEmpty(cabinetRecommendTypeList)){
            return false;
        }

        for (CabinetRecommendType cabinetRecommendType : cabinetRecommendTypeList) {
            if (Integer.valueOf(cabinetRecommendType.getId()).compareTo(cabinetRecommendTypeId) == 0){
                return true;
            }
        }

        return false;
    }
}
