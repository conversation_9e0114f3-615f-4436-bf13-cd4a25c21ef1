package net.summerfarm.wms.domain.processingtask.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ProcessingTaskProductRepository {

    /**
     * 批量创建
     *
     * @param processingTaskProductList
     */
    void batchCreate(List<ProcessingTaskProduct> processingTaskProductList);

    /**
     * 根据任务编号查询
     *
     * @param processingTaskCode
     * @return
     */
    List<ProcessingTaskProduct> listByProcessingTaskCode(String processingTaskCode);

    /**
     * 根据任务编号查询
     *
     * @param processingTaskCode
     * @return
     */
    Map<String, ProcessingTaskProduct> mapSkuCodeByProcessingTaskCode(String processingTaskCode);

    /**
     * id查找
     *
     * @param id
     * @return
     */
    ProcessingTaskProduct queryById(Long id);

    /**
     * 增加原料
     *
     * @param processingTaskProductId
     * @param materialSkuReceiveQuantity
     * @param materialSkuReceiveWeight
     * @param currentUserName
     */
    void addMaterial(Long processingTaskProductId, Integer materialSkuReceiveQuantity,
                     BigDecimal materialSkuReceiveWeight, String currentUserName);

    /**
     * 归还原料
     *
     * @param processingTaskProductId
     * @param materialSkuRestoreQuantity
     * @param materialSkuRestoreWeight
     * @param currentUserName
     */
    void reduceMaterial(Long processingTaskProductId, Integer materialSkuRestoreQuantity,
                        BigDecimal materialSkuRestoreWeight, String currentUserName);

    /**
     * 原料损耗
     *
     * @param id
     * @param wasteLossWeight
     * @param currentUserName
     */
    void addWasteLossWeight(Long id, BigDecimal wasteLossWeight, String currentUserName);

    /**
     * 提交成品数量
     * @param processingTaskProductId
     * @param submitQuantity
     * @param submitWeight
     * @param currentUserName
     */
    void submitQuantity(Long processingTaskProductId, Integer submitQuantity, BigDecimal submitWeight, String currentUserName);

    /**
     * 根据加工任务编码查询加工任务成品明细
     * @param processingTaskCode 加工任务编码
     * @return 加工任务成品明细分页信息
     */
    List<ProcessingTaskProduct> queryByProcessingTaskCode(String processingTaskCode);

    PageInfo<ProcessingTaskProduct> pageByProcessingTaskCode(String processingTaskCode,
                                                             Integer pageIndex,
                                                             Integer pageSize);


    /**
     * 更新结束和规格损耗
     *
     * @param id
     * @param specLossWeightTotal
     * @param finishRemark
     * @param currentUserName
     */
    void updateFinishAndSpecLoseWeight(Long id, BigDecimal specLossWeightTotal, String finishRemark, String currentUserName);

    /**
     * 查询成品列表
     * @param taskCodeList
     * @return
     */
    List<ProcessingTaskProduct>  queryByProcessingTaskCodeList(List<String> taskCodeList);

    /**
     * 加工中的商品
     * @param warehouseNo
     * @param productSkuCode
     * @return
     */
    long countProcessingByWarehouseAndSkuCode(Integer warehouseNo, String productSkuCode);

    /**
     * 更新未加工的任务
     * @param productIdList
     * @param currentUserName
     */
    void updateFinishByNotProcessing(List<Long> productIdList, String currentUserName);

}
