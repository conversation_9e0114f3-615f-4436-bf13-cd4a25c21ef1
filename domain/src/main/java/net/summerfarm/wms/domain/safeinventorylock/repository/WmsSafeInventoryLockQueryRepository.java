package net.summerfarm.wms.domain.safeinventorylock.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.safeinventorylock.entity.WmsSafeInventoryLockEntity;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockQueryParam;

import java.util.List;

/**
 * 安全库存锁定表查询仓储接口
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface WmsSafeInventoryLockQueryRepository {

    /**
     * 分页查询
     * @param param 查询参数
     * @return 分页结果
     */
    PageInfo<WmsSafeInventoryLockEntity> getPage(WmsSafeInventoryLockQueryParam param);

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 实体
     */
    WmsSafeInventoryLockEntity selectById(Long id);

    /**
     * 根据条件查询列表
     * @param param 查询参数
     * @return 实体列表
     */
    List<WmsSafeInventoryLockEntity> selectByCondition(WmsSafeInventoryLockQueryParam param);

    /**
     * 根据锁定编号查询
     * @param lockNo 锁定编号
     * @return 实体
     */
    WmsSafeInventoryLockEntity selectByLockNo(String lockNo);

    /**
     * 根据仓库编码和SKU查询锁定记录
     * @param warehouseNo 仓库编码
     * @param sku SKU
     * @return 实体列表
     */
    List<WmsSafeInventoryLockEntity> selectByWarehouseNoAndSku(Integer warehouseNo, String sku);

    /**
     * 根据仓库编码、SKU和锁定状态查询锁定记录
     * @param warehouseNo 仓库编码
     * @param sku SKU
     * @param lockStatus 锁定状态
     * @return 实体列表
     */
    List<WmsSafeInventoryLockEntity> selectByWarehouseNoAndSkuAndLockStatus(Integer warehouseNo, String sku, Integer lockStatus);

    /**
     * 根据锁定状态查询锁定记录
     * @param lockStatus 锁定状态
     * @return 实体列表
     */
    List<WmsSafeInventoryLockEntity> selectByLockStatus(Integer lockStatus);

    List<WmsSafeInventoryLockEntity> selectByLockStatusAndWarehouseNoAndTenantId(Integer lockStatus, Integer warehouseNo, Long tenantId);

    List<Integer> selectWarehouseNoByLockStatusAndWarehouseTenantId(Integer lockStatus, Long warehouseTenantId);
}
