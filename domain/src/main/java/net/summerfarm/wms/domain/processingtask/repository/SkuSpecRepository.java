package net.summerfarm.wms.domain.processingtask.repository;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.SkuSpec;

import java.util.List;
import java.util.Map;

public interface SkuSpecRepository {

    /**
     * 批量新增数据
     *
     * @param skuSpecList
     * @return 影响行数
     */
    void insertBatch(List<SkuSpec> skuSpecList);

    /**
     * 删除历史规格
     *
     * @param processingConfigId
     */
    void updateDeletedByProcessingConfigId(Long processingConfigId);

    /**
     * 查询规格列表
     * @param warehouseNo 仓库编号
     * @param productSkuCodeList 成品sku集合
     * @param type 加工类型
     * @return 规格列表
     */
    List<SkuSpec> listByWarehouseAndProductSkuCodeListAndType(Integer warehouseNo, List<String> productSkuCodeList,
                                                              Integer type, List<Long> configIdList);

    /**
     * 查询规格map
     *
     * @param warehouseNo        仓库编号
     * @param productSkuCodeList 成品sku集合
     * @param type               加工类型
     * @param configIdList
     * @return 规格map
     */
    Map<String, List<SkuSpec>> mapByWarehouseAndProductSkuCodeListAndType(Integer warehouseNo, List<String> productSkuCodeList,
                                                                          Integer type, List<Long> configIdList);


    /**
     * 根据加工规则配置id查询成品SKU规格
     * @param processingConfigId 加工规则配置id
     * @return 成品SKU规格列表
     */
    List<SkuSpec> queryAllByProcessingConfigId(Long processingConfigId);

    /**
     * 根据加工规则配置id list查询成品SKU规格
     * @param idList 加工规则配置id
     * @return 成品SKU规格列表
     */
    List<SkuSpec> listByProcessingConfigIdList(List<Long> idList);

    /**
     * 根据加工规则配置id作废成品SKU规格
     * @param processingConfigId 加工规则配置id
     */
    void updateInvalidByProcessingConfigId(Long processingConfigId);
}
