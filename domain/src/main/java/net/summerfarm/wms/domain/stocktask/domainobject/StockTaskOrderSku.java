package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2023/4/11 13:33
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskOrderSku {

    /**
     * 出库任务ID
     */
    private Long stockTaskId;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * sku数量
     */
    private Integer quantity;

    /**
     * sku实出数量
     */
    private Integer actualQuantity;

    /**
     * 异常数量
     */
    private Integer abnormalQuantity;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long gmtCreated;

    /**
     * 更新时间
     */
    private Long gmtModified;
}
