package net.summerfarm.wms.domain.processingtask.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTask;
import net.summerfarm.wms.domain.processingtask.domainobject.param.ProcessingTaskQueryParam;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface ProcessingTaskRepository {

    /**
     * 创建
     *
     * @param processingTaskDO
     * @return
     */
    Long create(ProcessingTask processingTaskDO);

    /**
     * 加工任务分页查询
     * @param queryParam 查询参数
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @return 对象列表
     */
    PageInfo<ProcessingTask> queryAllByLimit(ProcessingTaskQueryParam queryParam, Integer pageIndex, Integer pageSize);

    /**
     * 根据加工任务编号查询加工任务
     *
     * @param processingTaskCode 加工任务编号
     * @return 加工任务实体
     */
    ProcessingTask queryByProcessingTaskCode(String processingTaskCode);

    /**
     * 查询加工任务列表
     *
     * @param taskCodeList
     * @return
     */
    List<ProcessingTask> queryByProcessingTaskCodeList(List<String> taskCodeList);

    /**
     * 完结加工
     *
     * @param processingTaskCode
     * @param remark
     * @param currentUserName
     */
    void finishTask(String processingTaskCode, String remark, String currentUserName);

    /**
     * 进行中加工
     * @param processingTaskCode
     * @param currentUserName
     */
    void processingTask(String processingTaskCode, String currentUserName);

    /**
     * 未完成加工任务数量
     * @param warehouseNo
     * @return
     */
    Long countUnfinishedProcessingTask(Integer warehouseNo);

    /**
     * 查询加工任务的任务面板 - 数量
     * @param warehouseNo 仓库编号
     * @param statusList 任务状态
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd 数据截止日期
     * @return 返回结果
     */
    TaskPanelQuantityDTO queryTaskPanelQuantity(Integer warehouseNo,
                                                LocalDateTime createTimeStart,
                                                LocalDateTime createTimeEnd,
                                                List<Integer> statusList);

    PageInfo<String> queryTaskPanelWindowPageData(Integer panelQueryTypeEnum,
                                                  Integer pageNum,
                                                  Integer pageSize,
                                                  Integer warehouseNo,
                                                  LocalDateTime createTimeStart,
                                                  LocalDateTime createTimeEnd,
                                                  List<Integer> statusList);
}
