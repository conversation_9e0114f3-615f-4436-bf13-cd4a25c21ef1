package net.summerfarm.wms.domain.stockTransfer.aggregate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.domain.stockTransfer.domainobject.TransferOutBatchInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 转换操作批次详情返回聚合根
 *
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTransferItemOpDetailAggregate implements Serializable {
    private static final long serialVersionUID = -4813666074009204691L;

    /**
     * 实例id
     */
    Long stockTransferItemId;

    /**
     * 操作id
     */
    Long stockTransferItemOpId;

    /**
     * 转出名称
     */
    String transferOutGoodsName;

    /**
     * 转出sku
     */
    String transferOutSku;

    /**
     * 转入批次
     */
    String transferInBatch;

    /**
     * 转入库位
     */
    String transferInCabinet;

    /**
     * 转入批次的生产日期
     */
    Long produceTime;

    /**
     * 转入批次的保质期
     */
    Long shelfLife;

    /**
     * 规格
     */
    String specification;

    /**
     * 转出总数
     */
    Long transferOutTotal;

    /**
     * 转入总数
     */
    Long transferInTotal;

    /**
     * 转换比例
     */
    String transferRatio;

    /**
     * 操作人
     */
    String operator;

    /**
     * 操作时间
     */
    Date operateTime;

    /**
     * 打印个数
     */
    Integer printNum;

    /**
     * 商品类目
     */
    String goodsCategory;

    /**
     * 转出信息详情
     */
    List<TransferOutBatchInfo> outBatchInfos;


    /**
     * 是否可打印
     */
    Boolean canPrint;

}
