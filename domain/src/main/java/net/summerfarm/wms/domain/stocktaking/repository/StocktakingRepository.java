package net.summerfarm.wms.domain.stocktaking.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;
import net.summerfarm.wms.domain.stocktaking.domainobject.QueryItemDetail;
import net.summerfarm.wms.domain.stocktaking.domainobject.Stocktaking;
import net.summerfarm.wms.domain.stocktaking.domainobject.StocktakingItemBatch;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 盘点仓储层
 *
 * <AUTHOR>
 */
public interface StocktakingRepository {
    /**
     * 存储盘点任务
     *
     * @param stockTaking 盘点对象
     * @return id
     */
    long saveStocktaking(Stocktaking stockTaking);

    /**
     * 生成盘点单-精细化仓
     * @param stocktaking
     * @return
     */
    long saveStocktakingForCabinet(Stocktaking stocktaking);

    /**
     * 更新盘点主任务
     * @param stockTaking 盘点对象
     */
    void updateStocktaking(Stocktaking stockTaking);

    /**
     * 盘点实例详情数量
     *
     * @param stockTaking 盘点对象
     * @return 数量
     */
    int countStocktakingItemDetail(Stocktaking stockTaking);

    /**
     * 更新实例详情
     *
     * @param batchInfos 实例详情对象
     */
    void updateItemDetail(List<StocktakingItemBatch> batchInfos);

    void updateItemDetailStocktakingNumAndDiff(List<StocktakingItemBatch> batchInfos);

    /**
     * 添加盘点批次
     *
     * @param batch
     */
    Long appendItemDetail(StocktakingItemBatch batch);

    /**
     * 删除盘点批次
     *
     * @param batch
     */
    void deleteItemDetail(StocktakingItemBatch batch);

    /**
     * 获取盘点主任务
     *
     * @param stocktakingId 盘点id
     * @return 主任务
     */
    Stocktaking getStocktaking(Long stocktakingId);

    /**
     * 完成盘点
     *
     * @param stockTaking renwu
     */
    void finishStocktaking(Stocktaking stockTaking);

    /**
     * 盘点审核
     */
    void auditStocktaking(Stocktaking stocktaking);

    /**
     * 盘点审核失败
     */
    void auditStocktakingFailed(Stocktaking stocktaking);

    /**
     * 更新盘点单审核状态为待提交
     * @param id
     */
    void updateStockTakingAuditStateUnsubmit(Long id);

    boolean existItemDetail(QueryItemDetail queryItemDetail);

    /**
     * 未完成盘点单数量
     * @param warehouseNo
     * @return
     */
    Long countUnfinishedTask(Integer warehouseNo);

    /**
     * 取消盘点任务
     * @param stocktaking 任务详情
     */
    void cancelStocktaking(Stocktaking stocktaking);

    /**
     * 查询未完成盘点单sku
     * @param skus
     * @param warehouseNo
     * @return
     */
    List<String> queryUnFinishSku(List<String> skus, Long warehouseNo);

    Integer countEmptyQualityBySkuAndCabinet(Long stocktakingItemId, String sku, String cabinetCode);

    Integer deleteEmptyQualityBySkuAndCabinet(Long stocktakingItemId, String sku, String cabinetCode);

    Long selectItemIdByTakingIdAndSku(Long stocktakingId, String sku);

    /**
     * 查询任务面板 - 盘点单数量
     *
     * @param warehouseNo     仓库编号
     * @param statusList      任务状态数组
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd   数据截止日期
     * @return 返回结果
     */
    TaskPanelQuantityDTO queryStockTakingTaskPanelQuantity(Integer warehouseNo,
                                                           LocalDateTime createTimeStart,
                                                           LocalDateTime createTimeEnd,
                                                           List<Integer> statusList,
                                                           Integer cycle);

    /**
     * 查询任务面板 - 盘点单数量
     *
     * @param warehouseNo     仓库编号
     * @param statusList      任务状态数组
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd   数据截止日期
     * @return 返回结果
     */
    PageInfo<String> queryStockTakingTaskPanelQuantityWindowPageData(Integer pageNum,
                                                                     Integer pageSize,
                                                                     Integer warehouseNo,
                                                                     LocalDateTime createTimeStart,
                                                                     LocalDateTime createTimeEnd,
                                                                     List<Integer> statusList);

    /**
     * 查询任务面板 - 盘点单数量
     *
     * @param warehouseNo     仓库编号
     * @param statusList      任务状态数组
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd   数据截止日期
     * @return 返回结果
     */
    PageInfo<String> queryStockTakingTaskPanelQuantityWindowPageDataNotInTime(Integer pageNum,
                                                                              Integer pageSize,
                                                                              Integer warehouseNo,
                                                                              LocalDateTime createTimeStart,
                                                                              LocalDateTime createTimeEnd,
                                                                              List<Integer> statusList,
                                                                              Integer cycle);
}
