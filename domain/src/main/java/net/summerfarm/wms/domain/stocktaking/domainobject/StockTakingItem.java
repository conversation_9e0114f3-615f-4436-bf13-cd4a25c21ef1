package net.summerfarm.wms.domain.stocktaking.domainobject;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.base.ValueObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 盘点实例对象
 * 注意！！！现产品形态为值对象，后续优化可能会存在生命周期
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTakingItem implements ValueObject {
    Long id;

    /**
     * 盘点任务id
     */
    Long stocktakingId;

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 商品信息
     */
    GoodsInfo goodsInfo;

    /**
     * 盘点批次信息
     */
    List<StocktakingItemBatch> batchInfos;

    /**
     * 租户id
     */
    Long tenantId;

    /**
     * 计算盘盈盘亏总金额
     *
     * @return 总金额
     */
    public long diffAmount() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return 0;
        }
        return batchInfos.stream()
                .filter(item -> Objects.nonNull(item.queryDiffStock()))
                .mapToLong(item -> {
                    BigDecimal cost = item.getCost();
                    Integer num = item.queryDiffStock();
                    if (num == null) {
                        return 0L;
                    }
                    return cost.multiply(BigDecimal.valueOf(num)).longValue();
                }).sum();
    }

    /**
     * 计算盘盈总金额
     *
     * @return amount
     */
    public long excessAmount() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return 0;
        }
        return batchInfos.stream()
                .filter(item -> Objects.nonNull(item.queryDiffStock()))
                .mapToLong(item -> {
                    BigDecimal cost = item.getCost();
                    int num = item.queryDiffStock();
                    return cost.multiply(BigDecimal.valueOf(num)).longValue();
                })
                .filter(o -> o > 0)
                .sum();
    }

    public boolean isExcess() {
        return excessAmount() > 0;
    }

    /**
     * 计算盘亏总金额
     *
     * @return amount
     */
    public long reduceAmount() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return 0;
        }
        return batchInfos.stream()
                .filter(item -> Objects.nonNull(item.queryDiffStock()))
                .mapToLong(item -> {
                    BigDecimal cost = item.getCost();
                    int num = item.queryDiffStock();
                    return cost.multiply(BigDecimal.valueOf(num)).longValue();
                })
                .filter(o -> o < 0)
                .sum();
    }

    /**
     * 盘盈个数
     *
     * @return 个数
     */
    public Long overNum() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return 0L;
        }
        return batchInfos.stream()
                .filter(item-> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> item.queryDiffStock() > 0)
                .mapToLong(StocktakingItemBatch::queryDiffStock)
                .sum();
    }

    /**
     * 盘亏个数
     *
     * @return 个数
     */
    public long reduceNum() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return 0;
        }
        return batchInfos.stream()
                .filter(item-> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> item.queryDiffStock() < 0)
                .mapToLong(StocktakingItemBatch::queryDiffStock)
                .sum();
    }

    /**
     * 盘亏map,key:sku,value:num
     *
     * @return
     */
    public Map<String/*sku*/, Integer> reduceSkuMap() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<String, Integer> result = Maps.newHashMap();
        int sum = batchInfos.stream()
                .filter(item -> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> item.queryDiffStock() < 0)
                .mapToInt(StocktakingItemBatch::queryDiffStock)
                .sum();
        if (Objects.equals(sum, 0)) {
            return Maps.newHashMap();
        }
        result.put(goodsInfo.getSku(), sum);
        return result;
    }

    /**
     * 盘亏占用map,key:sku_cabinetCode_quality,value:num
     *
     * @return
     */
    public Map<String/*sku_cabinetCode_quality*/, Integer> reduceSkuMapForCabinet() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<String, Integer> result = Maps.newHashMap();
        Map<String, List<StocktakingItemBatch>> skuItemBatchMap = batchInfos.stream()
                .filter(item-> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> item.queryDiffStock() < 0)
                .collect(Collectors.groupingBy(item -> item.getSku() + "_" + item.getCabinetCode() + "_" + item.getShelfLife()));
        skuItemBatchMap.forEach((k, v) -> result.put(k, Math.abs(v.stream().mapToInt(StocktakingItemBatch::queryDiffStock).sum())));
        return result;
    }

    /**
     * 盘亏释放占用map,key:sku_cabinetCode_produce,value:num
     *
     * @return
     */
    public Map<String/*sku_cabinetCode_produce*/, Integer> releaseSkuMapForCabinet() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<String, Integer> result = Maps.newHashMap();
        Map<String, List<StocktakingItemBatch>> skuItemBatchMap = batchInfos.stream()
                .filter(item-> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> item.queryDiffStock() < 0)
                .collect(Collectors.groupingBy(item -> item.getSku() + "_" + item.getCabinetCode() + "_" + item.getProduceAt()));
        skuItemBatchMap.forEach((k, v) -> result.put(k, Math.abs(v.stream().mapToInt(StocktakingItemBatch::queryDiffStock).sum())));
        return result;
    }

    /**
     * 盘赢map,key:sku,value:num
     *
     * @return
     */
    public Map<String/*sku*/, Integer> excessSkuMap() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<String, Integer> result = Maps.newHashMap();
        int sum = batchInfos.stream()
                .filter(item-> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> item.queryDiffStock() > 0)
                .mapToInt(StocktakingItemBatch::queryDiffStock)
                .sum();
        if (Objects.equals(sum, 0)) {
            return Maps.newHashMap();
        }
        result.put(goodsInfo.getSku(), sum);
        return result;
    }

    public Map<Long/*produceBatch*/, Integer> existDiffBatch() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<Long, Integer> result = Maps.newHashMap();
        batchInfos.forEach(batch -> {
            Integer diffNum = result.get(batch.getProduceBatch());
            if (null != diffNum) {
                Map<Long, Integer> existDiffBatch = batch.existDiffBatch();
                Integer produceBatchQuantity = existDiffBatch.get(batch.getProduceBatch());
                if (null == produceBatchQuantity) {
                    produceBatchQuantity = 0;
                }
                existDiffBatch.put(batch.getProduceBatch(), produceBatchQuantity + diffNum);
                result.putAll(existDiffBatch);
            } else {
                result.putAll(batch.existDiffBatch());
            }
        });
        return result;
    }

    public Map<Long/*produceBatch*/, List<Integer>> normalDiffBatch() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<Long, List<Integer>> result = Maps.newHashMap();
        batchInfos.stream()
                .filter(item -> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> Objects.isNull(item.getBizOption()))
                .forEach(batch -> {
                    List<Integer> diff = result.get(batch.getProduceBatch());
                    diff = CollectionUtils.isEmpty(diff) ? Lists.newArrayList() : diff;
                    diff.add(batch.queryDiffStock());
                    result.put(batch.getProduceBatch(), diff);
                });
        return result;
    }

    public Map<Long/*cabinetInventoryId*/, Integer> existDiffBatchForCabinet() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<Long, Integer> result = Maps.newHashMap();
        batchInfos.forEach(batch -> result.putAll(batch.existDiffBatchForCabinet()));
        return result;
    }

    public Map<Long/*cabinetInventoryId*/, Integer> normalDiffBatchForCabinet() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<Long, Integer> result = Maps.newHashMap();
        batchInfos.stream().filter(item -> Objects.isNull(item.getBizOption()))
                .forEach(batch -> result.putAll(batch.existDiffBatchForCabinet()));
        return result;
    }

    public Map<Long, String/*采购单号*/> productBatchMap() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<Long, String> result = Maps.newHashMap();
        batchInfos.forEach(batch -> result.putAll(batch.productBatchMap()));
        return result;
    }

    public Map<Long, String/*采购单号*/> cabinetInventoryBatchMap() {
        if (CollectionUtils.isEmpty(batchInfos)) {
            return Maps.newHashMap();
        }
        HashMap<Long, String> result = Maps.newHashMap();
        batchInfos.forEach(batch -> result.putAll(batch.cabinetInventoryBatchMap()));
        return result;
    }

    /**
     * 盘盈理由
     *
     * @return 理由
     */
    public String excessAmountReason() {
        if (overNum() == 0) {
            return "";
        }
        if (CollectionUtils.isEmpty(batchInfos)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("sku:").append(goodsInfo.getSku()).append(goodsInfo.getPdName()).append("盘盈")
                .append(overNum()).append("件,盘盈金额总计").append(excessAmount()).append("元。").append("\n");
        batchInfos.stream()
                .filter(item -> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> {
                    BigDecimal cost = item.getCost();
                    int num = item.queryDiffStock();
                    long amount = cost.multiply(BigDecimal.valueOf(num)).longValue();
                    return amount > 0;
                })
                .forEach(o -> {
                    String productDateStr = DateUtil.formatYmdDateWithOblique(o.getProduceDate());
                    String qualityDateStr = DateUtil.formatYmdDateWithOblique(o.getQualityDate());
                    productDateStr = StringUtils.isEmpty(productDateStr) ? "" : productDateStr;
                    qualityDateStr = StringUtils.isEmpty(qualityDateStr) ? "" : qualityDateStr;
                    stringBuilder
                            .append("批次:").append(o.getBatch())
                            .append(" 效期:").append(productDateStr).append(Global.CROSS_BAR).append(qualityDateStr)
                            .append(" 原因:").append(o.getRemark()).append(";").append("\n");
                });
        return stringBuilder.toString();
    }

    /**
     * 盘亏理由
     *
     * @return 理由
     */
    public String reduceAmountReason() {
        if (reduceNum() == 0) {
            return "";
        }
        if (CollectionUtils.isEmpty(batchInfos)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("sku:").append(goodsInfo.getSku()).append(goodsInfo.getPdName()).append("盘亏")
                .append(reduceNum()).append("件,盘亏金额总计").append(reduceAmount()).append("元。").append("\n");
        batchInfos.stream()
                .filter(item -> Objects.nonNull(item.queryDiffStock()))
                .filter(item -> {
                    BigDecimal cost = item.getCost();
                    int num = item.queryDiffStock();
                    long amount = cost.multiply(BigDecimal.valueOf(num)).longValue();
                    return amount < 0;
                })
                .forEach(o -> {
                    String productDateStr = DateUtil.formatYmdDateWithOblique(o.getProduceDate());
                    String qualityDateStr = DateUtil.formatYmdDateWithOblique(o.getQualityDate());
                    productDateStr = StringUtils.isEmpty(productDateStr) ? "" : productDateStr;
                    qualityDateStr = StringUtils.isEmpty(qualityDateStr) ? "" : qualityDateStr;
                    stringBuilder.append("批次:").append(o.getBatch())
                            .append(" 效期:").append(productDateStr).append(Global.CROSS_BAR).append(qualityDateStr)
                            .append(" 原因:").append(o.getRemark()).append(";").append("\n");
                });
        return stringBuilder.toString();
    }

    /**
     * 校验库存数量是否一致
     *
     * @return 布尔
     */
    public boolean checkStockNum() {
        Integer stockNum = goodsInfo.getStockNum();
        // 老数据兼容
        if (Objects.isNull(stockNum)) {
            return true;
        }
        int sum = batchInfos.stream()
                .mapToInt(StocktakingItemBatch::queryStocktakingNum).sum();
        if (stockNum == sum) {
            // 总数相同，明细不同场景
            for (StocktakingItemBatch stocktakingItemBatch : batchInfos) {
                if (!stocktakingItemBatch.getBatchNum().equals(stocktakingItemBatch.queryStocktakingNum())) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 校验批次库存和仓库库存
     *
     * @return 布尔
     */
    public boolean checkStocktakingNum() {
        int stockNum = goodsInfo.getStockNum();
        int sum = batchInfos.stream().mapToInt(StocktakingItemBatch::queryStocktakingNum).sum();
        if (stockNum >= sum) {
            return true;
        }
        return false;
    }
}
