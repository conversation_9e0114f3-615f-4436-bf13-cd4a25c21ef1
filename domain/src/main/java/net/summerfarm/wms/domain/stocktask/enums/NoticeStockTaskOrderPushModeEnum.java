package net.summerfarm.wms.domain.stocktask.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: <br/>
 * date: 2025/2/20 15:59<br/>
 *
 * <AUTHOR> />
 */
@Getter
@AllArgsConstructor
public enum NoticeStockTaskOrderPushModeEnum {
    //推单模式（0手动推单，1实时推单，2全品类推单，3POP，4POPT+2）
    MANUAL(0, "手动推单"),
    REAL_TIME(1, "实时推单"),

    SALE_TO_PURCHASE(2, "销转采推单"),
    POP(3, "鲜果POP推单"),
    POPT2(4, "鲜果POPT+2推单"),

    ;
    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

}
