package net.summerfarm.wms.domain.stockalert.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-03-05 15:03:04
 * @version 1.0
 *
 */
@Data
public class SafeStockWarningQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 仓库号
	 */
	private Integer warehouseNo;

	/**
	 * 仓库名称
	 */
	private String warehouseName;

	/**
	 * 仓库服务商
	 */
	private String warehouseProvider;

	/**
	 * 货品编码
	 */
	private Long pdId;

	/**
	 * sku编码
	 */
	private String sku;

	/**
	 * saas skuId
	 */
	private Long saasSkuId;

	/**
	 * 类目id
	 */
	private Long categoryId;

	/**
	 * 类目id列表
	 */
	private List<Long> categoryIdList;

	/**
	 * sku租户id
	 */
	private Long skuTenantId;

	/**
	 * 仓库租户id
	 */
	private Long warehouseTenantId;

	/**
	 * 库存数量
	 */
	private Integer quantity;

	/**
	 * 安全库存下限
	 */
	private Integer stockLevelMinimum;

	/**
	 * 安全库存上限
	 */
	private Integer stockLevelMaximum;

	/**
	 * 库存状态，1：正常、2：低库存、3：高库存
	 */
	private Integer status;

	/**
	 * 数据所属日期:格式yyyyMMdd
	 */
	private Integer dayTag;

	/**
	 * 货品启用状态，0：停用、1：启用
	 */
	private Integer useFlag;

}