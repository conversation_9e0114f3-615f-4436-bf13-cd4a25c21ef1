package net.summerfarm.wms.domain.processingtask.domainService.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.common.domainobject.Cabinet;
import net.summerfarm.wms.domain.common.repository.CabinetRepository;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.processingtask.delegate.CabinetBatchInventoryDelegate;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAdd;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAddDetail;
import net.summerfarm.wms.domain.processingtask.delegate.factory.CabinetBatchInventoryReduceAddFactory;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.StoreRecord.enums.OtherStockChangeType;
import net.summerfarm.wms.domain.batch.domainService.PurchaseBatchDomainService;
import net.summerfarm.wms.domain.batch.domainobject.ProduceBatch;
import net.summerfarm.wms.domain.batch.enums.LotTypeEnum;
import net.summerfarm.wms.domain.batch.proxy.ProduceBatchInnerServiceProxy;
import net.summerfarm.wms.domain.batch.repository.ProduceBatchRepository;
import net.summerfarm.wms.domain.inventory.InventoryRepository;
import net.summerfarm.wms.domain.inventory.domainService.WmsInventoryDomainService;
import net.summerfarm.wms.domain.inventory.domainobject.Inventory;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingTaskProductDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductFinishAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductSubmitAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.MaterialReceiveSpecLoseUpdate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductOrderRecordUpdate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductRecordUpdate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductSubmitRecord;
import net.summerfarm.wms.domain.processingtask.repository.*;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProcessingTaskProductDomainServiceImpl implements ProcessingTaskProductDomainService {

    @Autowired
    private ProcessingTaskProductSubmitRecordRepository submitRecordRepository;
    @Autowired
    private ProcessingTaskProductRepository productRepository;
    @Autowired
    private ProcessingTaskProductRecordRepository productRecordRepository;
    @Autowired
    private ProcessingTaskProductOrderRecordRepository productOrderRecordRepository;
    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProcessingTaskProductSubmitRecordRepository productSubmitRecordRepository;

    @Autowired
    private ProcessingTaskMaterialReceiveRecordRepository materialReceiveRecordRepository;
    @Autowired
    private ProcessingTaskMaterialRepository materialRepository;

    @Autowired
    private PurchaseBatchDomainService purchaseBatchDomainService;
    @Autowired
    private InventoryRepository skuRepository;
    @Autowired
    private ProductRepository spuRepository;
    @Autowired
    private WmsInventoryDomainService wmsInventoryDomainService;
    @Autowired
    private ProduceBatchRepository produceBatchRepository;
    @Autowired
    private ProduceBatchInnerServiceProxy produceBatchInnerServiceProxy;
    @Autowired
    private CabinetBatchInventoryReduceAddFactory cabinetBatchInventoryReduceAddFactory;
    @Autowired
    private CabinetRepository cabinetRepository;
    @Autowired
    private CabinetBatchInventoryDelegate cabinetBatchInventoryDelegate;
    @Autowired
    private WarehouseConfigRepository warehouseConfigRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void productSubmit(ProcessingTaskProductSubmitAggregate submitAggregate) {

        // region 处理保质期、批次

        Map<String, String> existPurchaseNo = new HashMap<>();
        Map<String, Date> existQualityDate = new HashMap<>();
        // 查询或生成生产日期的批次信息
        for (ProcessingTaskProductSubmitRecord productSubmitRecord : submitAggregate.getProductSubmitRecordList()) {

            // 生产日期
            LocalDate productionDate = DateUtil.toLocalDate(productSubmitRecord.getProductSkuProductionDate());
            Long produceAt = DateUtil.toMill(productionDate);

            if (existPurchaseNo.get(productSubmitRecord.getProductSkuCode() + produceAt) != null) {
                productSubmitRecord.setProductSkuPurchaseBatch(
                        existPurchaseNo.get(productSubmitRecord.getProductSkuCode() + produceAt)
                );
                productSubmitRecord.setProductSkuQualityDate(
                        existQualityDate.get(productSubmitRecord.getProductSkuCode() + produceAt)
                );

                continue;
            }

            // 保质期单位
            Inventory inventory = skuRepository.selectSku(productSubmitRecord.getProductSkuCode());
            Product product = spuRepository.queryProductById(inventory.getPdId());
            LocalDate shelfLifeDate;
            Long shelfLife;
            if ("month".equals(product.getTimeUnit())) {
                shelfLifeDate = productionDate.plusDays(product.getEffectiveTime() * 30);
            } else {
                shelfLifeDate = productionDate.plusDays(product.getEffectiveTime());
            }
            shelfLife = DateUtil.toMill(shelfLifeDate);

            // 采购批次单号
            String purchaseNo = purchaseBatchDomainService.createNewPurchasesNo();

            purchaseBatchDomainService.getOrCreateBatch(
                    productSubmitRecord.getProductSkuCode(), productSubmitRecord.getWarehouseNo(),
                    shelfLife, produceAt,
                    LoginInfoThreadLocal.getCurrentUserName(),
                    OperationType.PROCESSING_TASK_PRODUCT_INCREASE.getId(),
                    purchaseNo,
                    LotTypeEnum.PROCESSING_INSERT.getType(),
                    submitAggregate.getProductAvlBatchCost(),
                    submitAggregate.getMaterialProve(),
                    submitAggregate.getProcessingTaskCode(),
                    submitAggregate.getTenantId());

            productSubmitRecord.setProductSkuPurchaseBatch(purchaseNo);
            productSubmitRecord.setProductSkuQualityDate(DateUtil.toDate(shelfLifeDate));

            existPurchaseNo.put(productSubmitRecord.getProductSkuCode() + produceAt,
                    productSubmitRecord.getProductSkuPurchaseBatch());
            existQualityDate.put(productSubmitRecord.getProductSkuCode() + produceAt,
                    productSubmitRecord.getProductSkuQualityDate());
        }
        // endregion

        // 开启库位管理配置
        Boolean openCabinetManagement = warehouseConfigRepository.openCabinetManagement(submitAggregate.getWarehouseNo());
        // 开启库存管理成品提交记录追加加工暂存库位
        if (Boolean.TRUE.equals(openCabinetManagement)) {
            submitAggregate.getProductSubmitRecordList().forEach(item -> item.setProductSkuCabinetCode(DefaultCabinetCodeEnum.JG.getCode()));
        }
        // 处理加工任务成品提交明细
        submitRecordRepository.batchCreate(submitAggregate.getProductSubmitRecordList());

        // 处理加工任务成品规格提交
        for (ProcessingTaskProductRecordUpdate productSpecSubmit : submitAggregate.getProductRecordUpdateList()) {
            productRecordRepository.submitQuantity(
                    productSpecSubmit.getProcessingTaskProductSpecId(),
                    productSpecSubmit.getSubmitSpecQuantity(),
                    productSpecSubmit.getSubmitWeight(),
                    LoginInfoThreadLocal.getCurrentUserName()
            );
        }

        // 处理加工任务成品订单规格提交
        for (ProcessingTaskProductOrderRecordUpdate productSpecOrderSubmit : submitAggregate.getProductOrderRecordUpdateList()) {
            productOrderRecordRepository.submitQuantity(
                    productSpecOrderSubmit.getProcessingTaskProductOrderSpecId(),
                    productSpecOrderSubmit.getSubmitQuantity(),
                    productSpecOrderSubmit.getSubmitWeight(),
                    LoginInfoThreadLocal.getCurrentUserName()
            );
        }

        // 处理加工任务成品提交
        productRepository.submitQuantity(
                submitAggregate.getProcessingTaskProductId(),
                submitAggregate.getSubmitQuantity(),
                submitAggregate.getSubmitWeight(),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // 处理加工任务状态
        processingTaskRepository.processingTask(submitAggregate.getProcessingTaskCode(),
                LoginInfoThreadLocal.getCurrentUserName());

        // 库位批次库存操作实体
        CabinetBatchInventoryAdd cabinetBatchInventoryAdd = cabinetBatchInventoryReduceAddFactory.buildCabinetBatchInventoryAddReqForProcessingProductAdd(submitAggregate.getWarehouseNo(), submitAggregate.getProcessingTaskCode(), submitAggregate.getTenantId(), LoginInfoThreadLocal.getCurrentUserName());

        // region 增加成品库存
        for (ProcessingTaskProductSubmitRecord productSubmitRecord : submitAggregate.getProductSubmitRecordList()) {
            Integer submitQuantity = BigDecimal.valueOf(productSubmitRecord.getProductSkuSpecSubmitQuantity())
                    .multiply(productSubmitRecord.getProductSkuSpecWeight())
                    .divide(productSubmitRecord.getProductSkuWeight()).intValue();

            wmsInventoryDomainService.increaseInventory(
                    submitQuantity,
                    productSubmitRecord.getProductSkuCode(),
                    Long.valueOf(productSubmitRecord.getWarehouseNo()),
                    OtherStockChangeType.PROCESSING_TASK_PRODUCT_INCREASE,
                    "" + productSubmitRecord.getId(),
                    LoginInfoThreadLocal.getCurrentUserName()
            );

            // 获取相应的采购批次
            ProduceBatch produceBatch = produceBatchRepository.queryProduceBatchByWnoAndSkuCodeAndDate(
                    productSubmitRecord.getWarehouseNo(),
                    productSubmitRecord.getProductSkuCode(),
                    productSubmitRecord.getProductSkuProductionDate(),
                    productSubmitRecord.getProductSkuQualityDate()
            );
            if (produceBatch == null) {
                log.error("领料创建批次库存时，查询采购批次不存在：" + JSONObject.toJSONString(productSubmitRecord));
                throw new BizException("创建批次库存时，查询采购批次不存在");
            }

            // 增加批次库存
            produceBatchInnerServiceProxy.updateProduceBatch(
                    OperationType.PROCESSING_TASK_PRODUCT_INCREASE.getId(),
                    LoginInfoThreadLocal.getCurrentUserName(),
                    productSubmitRecord.getProductSkuPurchaseBatch(),
                    produceBatch.getId(),
                    submitQuantity,
                    submitAggregate.getProductAvlBatchCost(),
                    productSubmitRecord.getId(),
                    LoginInfoThreadLocal.getTenantId()
            );

            // 库位批次库存操作明细
            CabinetBatchInventoryAddDetail cabinetBatchInventoryAddDetail = cabinetBatchInventoryReduceAddFactory.buildCabinetBatchInventoryAddDetail(productSubmitRecord.getProductSkuCode(), DefaultCabinetCodeEnum.JG.getCode(), productSubmitRecord.getProductSkuProductionDate(), productSubmitRecord.getProductSkuQualityDate(), productSubmitRecord.getProductSkuPurchaseBatch(), submitQuantity);
            cabinetBatchInventoryAdd.getAddDetailReqList().add(cabinetBatchInventoryAddDetail);

        }
        // endregion

        // 开启库位管理
        if (Boolean.TRUE.equals(openCabinetManagement)) {
            Cabinet cabinet = cabinetRepository.defaultJGCabinet(submitAggregate.getWarehouseNo());
            if (null == cabinet) {
                throw new BizException("未查询到加工暂存库位");
            }
            log.info("加工成品调用增加库位批次库存，任务号：{}", submitAggregate.getProcessingTaskCode());
            cabinetBatchInventoryDelegate.addCabinetBatchInventory(cabinetBatchInventoryAdd);
            log.info("加工成品完成增加库位批次库存，任务号：{}", submitAggregate.getProcessingTaskCode());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void productFinish(ProcessingTaskProductFinishAggregate finishAggregate) {

        // 更新原料领料规格损耗
        for (MaterialReceiveSpecLoseUpdate receiveSpecLoseUpdate : finishAggregate.getReceiveSpecLoseUpdateList()) {
            materialReceiveRecordRepository.updateSpecLoseWeight(
                    receiveSpecLoseUpdate.getMaterialReceiveRecordId(),
                    receiveSpecLoseUpdate.getSpecLossWeight(),
                    LoginInfoThreadLocal.getCurrentUserName()
            );
        }

        // 更新原料规格损耗
        Map<Long, MaterialReceiveSpecLoseUpdate> materialIdMap = finishAggregate.getReceiveSpecLoseUpdateList()
                .stream()
                .collect(Collectors.toMap(MaterialReceiveSpecLoseUpdate::getProcessingTaskMaterialId,
                        Function.identity(),
                        (a, b) -> {
                            a.setSpecLossWeight(a.getSpecLossWeight().add(b.getSpecLossWeight()));
                            return a;
                        }));
        for (MaterialReceiveSpecLoseUpdate value : materialIdMap.values()) {
            materialRepository.updateSpecLoseWeight(
                    value.getProcessingTaskMaterialId(),
                    value.getSpecLossWeight(),
                    LoginInfoThreadLocal.getCurrentUserName());
        }

        // 更新成品状态和规格损耗
        productRepository.updateFinishAndSpecLoseWeight(
                finishAggregate.getProcessingTaskProductId(),
                finishAggregate.getSpecLossWeightTotal(),
                finishAggregate.getFinishRemark(),
                LoginInfoThreadLocal.getCurrentUserName());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePrintNumber(Long processingTaskProductSubmitRecordId, Long processingTaskProductRecordId, Integer canPrintNumber) {
        // 更新加工任务成品明细打印次数
        productRecordRepository.updatePrintNumberById(processingTaskProductRecordId, canPrintNumber);
        // 更新加工任务成品提交明细打印次数
        productSubmitRecordRepository.updatePrintNumberById(processingTaskProductSubmitRecordId, canPrintNumber);
    }
}
