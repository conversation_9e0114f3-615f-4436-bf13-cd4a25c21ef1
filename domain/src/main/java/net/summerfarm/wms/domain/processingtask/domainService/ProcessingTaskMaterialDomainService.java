package net.summerfarm.wms.domain.processingtask.domainService;

import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.MaterialWasteLossWeightUpdateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialReceiveAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialRestoreAggregate;

public interface ProcessingTaskMaterialDomainService {


    /**
     * 物料领用
     *
     * @param receiveAggregate
     * @return
     */
    Long materialReceive(ProcessingTaskMaterialReceiveAggregate receiveAggregate);

    /**
     * 领料物料损耗
     *
     * @param updateAggregate
     */
    void materialWasteLossWeightUpdate(MaterialWasteLossWeightUpdateAggregate updateAggregate);

    /**
     * 物料归还
     *
     * @param materialRestoreAggregate
     * @return
     */
    Long materialRestore(ProcessingTaskMaterialRestoreAggregate materialRestoreAggregate);

}
