package net.summerfarm.wms.domain.processingtask.domainobject.enums;

/**
 * 加工类型枚举
 * <AUTHOR>
 * @date 2023/02/13
 */
public enum ProcessingConfigTypeEnum {

    ORDER_PROCESSING(1, "订单加工"),

    GOODS_PROCESSING(2, "商品加工"),

    ASSEMBLY_PROCESSING(3, "商品组装"),

    ;

    private final int value;

    private final String description;

    ProcessingConfigTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public boolean equalsCode(Integer input){
        return Integer.valueOf(this.value).equals(input);
    }
}
