package net.summerfarm.wms.domain.stockTransfer.aggregate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransferItemDetailAggregate implements Serializable {
    private static final long serialVersionUID = 6411543550195998541L;

    /**
     * 转换任务实例id
     */
    private Long stockTransferItemId;

    /**
     * 转入商品id
     */
    private Long transferInGoodsId;

    /**
     * 商品类目
     */
    private String goodsCategory;

    /**
     * 转入商品名称
     */
    private String transferInGoodsName;

    /**
     * 转入商品sku
     */
    private String transferInGoodsSku;

    /**
     * 转入商品规格
     */
    private String transferInGoodsSpec;

    /**
     * 存储区域
     */
    private String storageArea;

    /**
     * 包装
     */
    private String packaging;

    /**
     * 应转入数量
     */
    private Long preTransferInNum;

    /**
     * 实际转入数量
     */
    private Long actualTransferInNum;

    /**
     * 进口/国产
     */
    private Integer isDomestic;

    /**
     * 商品归属 自营/代仓
     */
    private Integer skuType;
}
