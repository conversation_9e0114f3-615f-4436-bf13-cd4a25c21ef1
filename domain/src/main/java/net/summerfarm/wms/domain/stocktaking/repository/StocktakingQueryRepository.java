package net.summerfarm.wms.domain.stocktaking.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.stocktaking.domainobject.StockTakingItem;
import net.summerfarm.wms.domain.stocktaking.domainobject.Stocktaking;
import net.summerfarm.wms.domain.stocktaking.domainobject.StocktakingItemBatch;
import net.summerfarm.wms.domain.stocktaking.domainobject.query.*;

import java.util.List;

/**
 * 盘点查询仓储
 */
public interface StocktakingQueryRepository {
    /**
     * 根据仓库号，sku，pdName查询盘点id
     *
     * @param queryTakingIdByCd
     * @return
     */
    List<Long> findAllStocktakingBySkuAndPdName(QueryTakingId queryTakingIdByCd);

    /**
     * 条件查询任务详情
     * @param queryStocktakingByCd 条件对象
     * @return 详情
     */
    PageInfo<Stocktaking> findAllStocktakingByCd(QueryStocktakingByCd queryStocktakingByCd);

    /**
     * sass条件查询任务详情
     * @param query 条件对象
     * @return 详情
     */
    PageInfo<Stocktaking> findAllStocktakingBySass(QueryStocktakingBySaas query);

    /**
     * 根据任务id查询主任务
     * tps：不带item的信息
     *
     * @param stocktakingId 任务id
     * @return 详情
     */
    Stocktaking findStocktakingById(Long stocktakingId);

    StockTakingItem findItemByItemId(Long itemId);

    PageInfo<StockTakingItem> findStocktakingItemsByTaskId(PageFindItem pageFindItem);

    List<StocktakingItemBatch> findItemBatchByItemIds(QueryItemBatch queryItemBatch);

    PageInfo<StocktakingItemBatch> pageItemBatchByItemIds(QueryItemBatch queryItemBatch);

    /**
     * 查询盘点明细
     *
     * <AUTHOR>
     * @date 2023/7/6 16:36
     * @param taskId 盘点任务编码
     * @return java.util.List<net.summerfarm.wms.domain.stocktaking.domainobject.StockTakingItem>
     */
    List<StockTakingItem> listStocktakingItemListByTaskId(Long taskId);


    /**
     * 根据盘点单号查询主任务
     * tps：不带item的信息
     *
     * @param stockTakingNo 盘点单号
     * @param warehouseNo   仓库编号
     * @param tenantId      租户ID
     * @return 盘点主任务
     */
    Stocktaking findByTakingNoAndWarehouseNoAndTenant(String stockTakingNo, Integer warehouseNo, Long tenantId);

}
