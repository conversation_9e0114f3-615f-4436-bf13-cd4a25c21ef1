package net.summerfarm.wms.domain.processingtask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MaterialReceiveSpecLoseUpdate implements Serializable {

    /**
     * 领料批次Id
     */
    private Long materialReceiveRecordId;

    /**
     * 领料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 成品ID
     */
    private Long processingTaskProductId;

    /**
     * 规格损耗
     */
    private BigDecimal specLossWeight;
}
