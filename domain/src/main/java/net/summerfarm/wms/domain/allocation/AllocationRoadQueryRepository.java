package net.summerfarm.wms.domain.allocation;

import net.summerfarm.wms.domain.allocation.domainobject.AggregateAllocationRoadData;
import net.summerfarm.wms.domain.allocation.domainobject.InventoryAllocationRoadCostBatch;
import net.summerfarm.wms.domain.allocation.domainobject.query.InventoryAllocationRoadCostBatchQuery;
import net.summerfarm.wms.domain.allocation.valueobject.AllocationRoadQuantityValueObject;

import java.util.List;

public interface AllocationRoadQueryRepository {

    List<InventoryAllocationRoadCostBatch> selectByCondition(InventoryAllocationRoadCostBatchQuery query);

    InventoryAllocationRoadCostBatch selectById(Long id);

    List<InventoryAllocationRoadCostBatch> selectByIdList(List<Long> idList);

    List<AggregateAllocationRoadData> queryAggregateAllocationRoadData(List<Integer> warehouseNoList, List<String> skuList);


    /**
     * 根据库存仓查询批次调拨在途库存
     *
     * @param warehouseNo
     * @return
     */
    List<AllocationRoadQuantityValueObject> queryAllocationRoadQuantity(Integer warehouseNo);

}
