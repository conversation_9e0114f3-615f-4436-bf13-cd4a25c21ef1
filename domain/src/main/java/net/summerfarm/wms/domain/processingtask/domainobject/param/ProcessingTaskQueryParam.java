package net.summerfarm.wms.domain.processingtask.domainobject.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description:加工任务查询参数
 * date: 2024/3/5 18:40
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingTaskQueryParam {

    /**
     * 库存仓编码
     */
    private Integer warehouseNo;

    /**
     * 加工任务类型
     */
    private Integer type;

    /**
     * 加工任务类型列表
     */
    private List<Integer> typeList;

    /**
     * 加工任务状态
     */
    private Integer status;

    /**
     * 成品SKU名称
     */
    private String productSkuName;

    /**
     * 成品SKU
     */
    private String productSkuCode;

    /**
     * 原料sku名称
     */
    private String materialSkuName;

    /**
     * 原料sku编号
     */
    private String materialSkuCode;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 加工任务编码集合
     */
    private List<String> processingTaskCodeList;

}
