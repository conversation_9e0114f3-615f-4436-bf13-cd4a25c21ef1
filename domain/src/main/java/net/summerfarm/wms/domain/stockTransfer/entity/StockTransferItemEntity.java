package net.summerfarm.wms.domain.stockTransfer.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTransferItemEntity implements Serializable {
    private static final long serialVersionUID = -5216430040043389892L;

    /**
     * 主键id
     */
    Long id;

    /**
     * 任务id
     */
    Long stockTransferId;

    /**
     * 转入sku
     */
    String transferInSku;

    /**
     * 预转入数量
     */
    Long preTransferInNum;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;
}
