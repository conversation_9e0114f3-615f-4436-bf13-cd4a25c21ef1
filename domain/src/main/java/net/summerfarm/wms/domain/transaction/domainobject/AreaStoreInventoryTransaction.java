package net.summerfarm.wms.domain.transaction.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * wms_area_store_inventory_transaction
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AreaStoreInventoryTransaction {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 仓库号
     */
    private Long warehouseNo;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 库存类型
     */
    private String inventoryType;

    /**
     * 请求参数
     */
    private String param;

    /**
     * 分布式事务状态
     */
    private Integer transactionState;

    /**
     * sku
     */
    private String sku;

}