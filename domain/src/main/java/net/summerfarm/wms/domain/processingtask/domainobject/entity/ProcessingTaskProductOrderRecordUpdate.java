package net.summerfarm.wms.domain.processingtask.domainobject.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessingTaskProductOrderRecordUpdate implements Serializable {



    /**
     * 加工成品订单规格ID
     */
    private Long processingTaskProductOrderSpecId;

    /**
     * 加工成品规格ID
     */
    private Long processingTaskProductSpecId;

    /**
     * 加工数量
     */
    private Integer submitQuantity;

    /**
     * 加工重量
     */
    private BigDecimal submitWeight;
}
