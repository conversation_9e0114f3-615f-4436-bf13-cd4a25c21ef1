package net.summerfarm.wms.domain.processingtask.domainobject.enums;

import net.xianmu.common.exception.BizException;

import java.util.Arrays;
import java.util.Objects;

/**
 * 加工任务类型枚举
 * <AUTHOR>
 * @date 2023/02/13
 */
public enum ProcessingTaskTypeEnum {

    ORDER_PROCESSING(1, "订单加工"),

    SKU_PROCESSING(2, "商品加工"),

    SKU_ASSEMBLY(3, "商品组装"),

    ;

    private final int value;

    private final String description;

    ProcessingTaskTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static ProcessingTaskTypeEnum getTypeByValue(Integer value) {
        return Arrays.stream(ProcessingTaskTypeEnum.values()).filter(processingTaskTypeEnum -> Objects.equals(value, processingTaskTypeEnum.getValue()))
                .findFirst().orElseThrow(() -> new BizException("不支持的加工类型"));
    }

    public static String getDescriptionByValue(Integer value) {
        return getTypeByValue(value).getDescription();
    }

    public boolean equalsCode(Integer input){
        return Integer.valueOf(this.value).equals(input);
    }
}


