package net.summerfarm.wms.domain.initconfig;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * @Description
 * @Date 2025/8/7 15:35
 * @<AUTHOR>
 */
@NacosConfigurationProperties(prefix = "batch.fix", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@Configuration
@Data
public class BatchOriginFixAsyncConfig {

    /**
     * 异步(binlog触发)
     */
    private boolean async = false;

}
