package net.summerfarm.wms.domain.skucodetrace.service;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.enums.GeneratorDocTypeEnum;
import net.summerfarm.wms.common.util.SerialNumberGenerator;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.enums.SkuBatchCodeTraceEnums;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceCreateCommandParam;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.facade.ofc.dto.PurchaseSupplyDetailDTO;
import net.xianmu.common.exception.BizException;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SkuBatchCodeTraceDistributor {

    /**
     * 批量构建唯一码信息
     * @param params 构建码信息
     * @param skuGoodsMap 货品信息
     * @return 唯一溯源码信息
     */
    /**
     * 批量构建唯一码信息
     * @param params 构建码信息
     * @param skuGoodsMap 货品信息
     * @return 唯一溯源码信息
     */
    @NotNull
    public List<SkuBatchCodeTraceEntity> batchBuildCodeTraceList(List<SkuBatchCodeTraceCreateCommandParam> params, Map<String, GoodsInfoDTO> skuGoodsMap) {
        List<SkuBatchCodeTraceEntity> createTraceEntityList = new ArrayList<>();

        AtomicInteger stockTaskStorageSeq = new AtomicInteger(1);
        // 唯一码创建
        params.forEach(param ->{
            Integer skuQuantity = param.getSkuQuantity();
            GoodsInfoDTO goodsInfoDTO = skuGoodsMap.get(param.getSku());
            if(goodsInfoDTO == null){
                log.error("根据sku查询货品信息失败，sku:{}\\n", param.getSku(),new BizException("根据sku查询货品信息失败sku:" + param.getSku()));
                goodsInfoDTO = new GoodsInfoDTO();
            }
            // 按照sku的数量生成每一个唯一码
            for (int i = 1; i <= skuQuantity; i++) {
                if(skuQuantity > 9999){
                    BizException bizException = new BizException("SKU数量大于9999，溯源码暂不支持这么大数量\\n");
                    log.error(bizException.getMessage(),bizException);
                    throw bizException;
                }
                SkuBatchCodeTraceEntity skuBatchCodeTraceEntity = new SkuBatchCodeTraceEntity();

                skuBatchCodeTraceEntity.setSku(param.getSku());
                skuBatchCodeTraceEntity.setGoodGrossWeight(goodsInfoDTO.getWeight());
                skuBatchCodeTraceEntity.setPdName(goodsInfoDTO.getTitle());
                skuBatchCodeTraceEntity.setSpecification(goodsInfoDTO.getSpecification());
                skuBatchCodeTraceEntity.setSkuSubType(goodsInfoDTO.getSubAgentType());
                skuBatchCodeTraceEntity.setPdId(goodsInfoDTO.getXmPdId());
                skuBatchCodeTraceEntity.setQuoteType(goodsInfoDTO.getQuoteType());

                skuBatchCodeTraceEntity.setState(SkuBatchCodeTraceEnums.State.ORDER_WAIT_ALLOCATION.getValue());
                skuBatchCodeTraceEntity.setSkuBatchOnlyCode(param.getSkuBatchOnlyCode());
                skuBatchCodeTraceEntity.setSkuBatchTraceCode(param.getSkuBatchOnlyCode() + String.format("%04d", i));
                skuBatchCodeTraceEntity.setStockTaskStorageId(param.getStockTaskStorageId());
                skuBatchCodeTraceEntity.setBatchDate(param.getBatchDate());
                skuBatchCodeTraceEntity.setStockTaskStorageSeq(stockTaskStorageSeq.get());
                skuBatchCodeTraceEntity.setPurchaseNo(param.getPurchaseNo());
                skuBatchCodeTraceEntity.setWarehouseNo(param.getWarehouseNo());
                skuBatchCodeTraceEntity.setPsoNo(param.getPsoNo());

                stockTaskStorageSeq.getAndIncrement();

                createTraceEntityList.add(skuBatchCodeTraceEntity);
            }
        });
        return createTraceEntityList;
    }

    /**
     * 订单分配到码上面
     * @param purchaseSupplyDetailDTOS 采购供应单信息
     * @param waitOrderAllocationSkuBatchCodeTraceList  待分配订单的溯源码信息
     * @return 被分配订单的溯源码
     */
    /**
     * 订单分配到码上面
     * @param purchaseSupplyDetailDTOS 采购供应单信息
     * @param waitOrderAllocationSkuBatchCodeTraceList  待分配订单的溯源码信息
     * @return 被分配订单的溯源码
     */
    public List<SkuBatchCodeTraceEntity> distributeOrder2SkuBatchCodeTrace(List<PurchaseSupplyDetailDTO> purchaseSupplyDetailDTOS,
                                                                           List<SkuBatchCodeTraceEntity> waitOrderAllocationSkuBatchCodeTraceList) {
        // 采购供应单编号 + sku 对应的订单信息
        Map<String, List<PurchaseSupplyDetailDTO>> psoNoSku2OrderMap = purchaseSupplyDetailDTOS.stream().collect(Collectors.groupingBy(pur -> pur.getPsoNo() + "#" + pur.getSkuCode()));
        // 采购供应单编号 + sku 对应的码信息
        Map<String, List<SkuBatchCodeTraceEntity>> psoNoSku2CodeTraceMap = waitOrderAllocationSkuBatchCodeTraceList.stream().collect(Collectors.groupingBy(codeTrace -> codeTrace.getPsoNo() + "#" + codeTrace.getSku()));

        // 被分配订单的溯源码
        List<SkuBatchCodeTraceEntity> haveDistributeCodeTraceList = new ArrayList<>();

        psoNoSku2CodeTraceMap.forEach((psoNoSku, codeTraceList) -> {
            List<PurchaseSupplyDetailDTO> psoSkuOrderDetails = psoNoSku2OrderMap.get(psoNoSku);
            if(CollectionUtils.isEmpty(psoSkuOrderDetails)){
                log.error("订单信息为空，psoNoSku:{}\\n",psoNoSku);
                return;
            }
            // 判断数量是否一致，不一致则告警
            if(psoSkuOrderDetails.stream().mapToInt(PurchaseSupplyDetailDTO::getQuantity).sum() != codeTraceList.size()){
                log.error("订单SKU数量与码数量不一致，psoNoSku:{}\\n",psoNoSku);
            }
            // 逐个分配
            codeTraceList.forEach(codeTrace -> {
                for (PurchaseSupplyDetailDTO psoSkuOrderDetailDTO : psoSkuOrderDetails) {
                    Integer skuQuantity = psoSkuOrderDetailDTO.getQuantity();
                    if(skuQuantity == 0){
                        continue;
                    }
                    codeTrace.setOrderNo(psoSkuOrderDetailDTO.getOrderNo());
                    codeTrace.setFulfillmentNo(psoSkuOrderDetailDTO.getFulfillmentNo());
                    codeTrace.setMerchantName(psoSkuOrderDetailDTO.getMerchantName());
                    codeTrace.setMerchantId(psoSkuOrderDetailDTO.getMerchantId());
                    codeTrace.setStoreNo(psoSkuOrderDetailDTO.getStoreNo());
                    codeTrace.setBuyerName(psoSkuOrderDetailDTO.getBuyerName());
                    codeTrace.setState(SkuBatchCodeTraceEnums.State.PATH_WAIT_ALLOCATION.getValue());
                    codeTrace.setDeliveryTime(psoSkuOrderDetailDTO.getFulfillmentTime());
                    codeTrace.setFulfillmentWay(psoSkuOrderDetailDTO.getFulfillmentWay());
                    codeTrace.setLabelType(codeTrace.getLabelType());
                    codeTrace.setTenantId(codeTrace.getTenantId());

                    psoSkuOrderDetailDTO.setQuantity(skuQuantity - 1);

                    haveDistributeCodeTraceList.add(codeTrace);
                    // 跳出循环，继续分配下一个溯源码
                    break;
                }
            });
        });

        return haveDistributeCodeTraceList;
    }

    /**
     * 城配仓分配到码上面
     * @param purchaseSupplyDetailDTOS 采购供应单信息
     * @param waitOrderAllocationSkuBatchCodeTraceList  待分配订单的溯源码信息
     * @return 被分配城配仓的溯源码
     */
    public List<SkuBatchCodeTraceEntity> distributeStoreInfo2SkuBatchCodeTrace(List<PurchaseSupplyDetailDTO> purchaseSupplyDetailDTOS,
                                                                           List<SkuBatchCodeTraceEntity> waitOrderAllocationSkuBatchCodeTraceList) {
        // 采购供应单编号 + sku 对应的订单信息
        Map<String, List<PurchaseSupplyDetailDTO>> psoNoSku2OrderMap = purchaseSupplyDetailDTOS.stream().collect(Collectors.groupingBy(pur -> pur.getPsoNo() + "#" + pur.getSkuCode()));
        // 采购供应单编号 + sku 对应的码信息
        Map<String, List<SkuBatchCodeTraceEntity>> psoNoSku2CodeTraceMap = waitOrderAllocationSkuBatchCodeTraceList.stream().collect(Collectors.groupingBy(codeTrace -> codeTrace.getPsoNo() + "#" + codeTrace.getSku()));

        // 被分配的溯源码
        List<SkuBatchCodeTraceEntity> haveDistributeCodeTraceList = new ArrayList<>();

        psoNoSku2CodeTraceMap.forEach((psoNoSku, codeTraceList) -> {
            List<PurchaseSupplyDetailDTO> psoSkuOrderDetails = psoNoSku2OrderMap.get(psoNoSku);
            if(CollectionUtils.isEmpty(psoSkuOrderDetails)){
                log.error("订单信息为空，psoNoSku:{}\\n",psoNoSku);
                return;
            }
            // 判断数量是否一致，不一致则告警
            if(psoSkuOrderDetails.stream().mapToInt(PurchaseSupplyDetailDTO::getQuantity).sum() != codeTraceList.size()){
                log.error("订单SKU数量与码数量不一致，psoNoSku:{}\\n",psoNoSku);
            }
            // 逐个分配
            codeTraceList.forEach(codeTrace -> {
                for (PurchaseSupplyDetailDTO psoSkuOrderDetailDTO : psoSkuOrderDetails) {
                    Integer skuQuantity = psoSkuOrderDetailDTO.getQuantity();
                    if(skuQuantity == 0){
                        continue;
                    }
                    codeTrace.setStoreNo(psoSkuOrderDetailDTO.getStoreNo());
                    codeTrace.setState(SkuBatchCodeTraceEnums.State.WEIGHT_WAIT_ALLOCATION.getValue());
                    codeTrace.setDeliveryTime(psoSkuOrderDetailDTO.getFulfillmentTime());
                    codeTrace.setFulfillmentWay(psoSkuOrderDetailDTO.getFulfillmentWay());
                    codeTrace.setLabelType(codeTrace.getLabelType());
                    codeTrace.setTenantId(codeTrace.getTenantId());
                    codeTrace.setBuyerName(psoSkuOrderDetailDTO.getBuyerName());

                    psoSkuOrderDetailDTO.setQuantity(skuQuantity - 1);

                    haveDistributeCodeTraceList.add(codeTrace);
                    // 跳出循环，继续分配下一个溯源码
                    break;
                }
            });
        });

        return haveDistributeCodeTraceList;
    }
}
