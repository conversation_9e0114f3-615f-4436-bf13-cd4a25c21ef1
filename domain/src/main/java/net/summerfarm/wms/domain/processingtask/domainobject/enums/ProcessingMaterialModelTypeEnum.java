package net.summerfarm.wms.domain.processingtask.domainobject.enums;

public enum ProcessingMaterialModelTypeEnum {


    SINGLE_MATERIAL(0, "单原料"),

    MULTI_MATERIAL(1, "多原料"),

    ;

    private final int value;

    private final String description;

    ProcessingMaterialModelTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public boolean equalsCode(Integer input){
        return Integer.valueOf(this.value).equals(input);
    }
}
