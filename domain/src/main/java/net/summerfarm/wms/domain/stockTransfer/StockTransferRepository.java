package net.summerfarm.wms.domain.stockTransfer;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.common.dto.TaskPanelQuantityDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/23
 */
public interface StockTransferRepository {

    /**
     * 未完成转换任务数量
     * @param warehouseNo
     * @return
     */
    Long countUnfinishedTask(Integer warehouseNo);

    /**
     * 查询转换任务的任务面板 - 数量
     * @param warehouseNo 仓库编号
     * @param statusList 任务状态数组
     * @param taskSource 来源
     * @param createTimeStart 数据起始日期
     * @param createTimeEnd 数据截止日期
     * @return 返回结果
     */
    TaskPanelQuantityDTO queryTaskPanelQuantity(Integer warehouseNo,
                                                LocalDateTime createTimeStart,
                                                LocalDateTime createTimeEnd,
                                                List<Integer> statusList,
                                                String remark,
                                                Integer taskSource
                                                );

    PageInfo<String> queryPanelWindowPageData(Integer panelQueryTypeEnum,
                                              Integer pageNum,
                                              Integer pageSize,
                                              Integer warehouseNo,
                                              LocalDateTime createTimeStart,
                                              LocalDateTime createTimeEnd,
                                              List<Integer> statusList,
                                              String remark,
                                              Integer taskSource);
}
