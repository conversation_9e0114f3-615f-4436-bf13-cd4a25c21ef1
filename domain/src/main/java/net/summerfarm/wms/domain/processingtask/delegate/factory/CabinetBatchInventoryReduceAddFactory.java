package net.summerfarm.wms.domain.processingtask.delegate.factory;

import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventory;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryChangeTypeEnum;
import net.summerfarm.wms.domain.inventory.domainobject.enums.CabinetInventoryOperationType;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAdd;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAddDetail;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryReduce;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryReduceDetail;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/31
 */
@Component
public class CabinetBatchInventoryReduceAddFactory {

    public CabinetBatchInventoryReduceDetail buildCabinetBatchInventoryReduceDetail(CabinetBatchInventory cabinetBatchInventory, Integer reduceQuantity) {
        CabinetBatchInventoryReduceDetail cabinetBatchInventoryReduceDetailReq = new CabinetBatchInventoryReduceDetail();
        cabinetBatchInventoryReduceDetailReq.setCabinetCode(cabinetBatchInventory.getCabinetCode());
        cabinetBatchInventoryReduceDetailReq.setSkuCode(cabinetBatchInventory.getSku());
        cabinetBatchInventoryReduceDetailReq.setProduceDate(cabinetBatchInventory.getProduceDate());
        cabinetBatchInventoryReduceDetailReq.setQualityDate(cabinetBatchInventory.getQualityDate());
        cabinetBatchInventoryReduceDetailReq.setSkuBatchCode(cabinetBatchInventory.getBatchNo());
        cabinetBatchInventoryReduceDetailReq.setReduceQuantity(reduceQuantity);
        return cabinetBatchInventoryReduceDetailReq;
    }

    public CabinetBatchInventoryAddDetail buildCabinetBatchInventoryAddDetail(CabinetBatchInventory cabinetBatchInventory, Integer addQuantity) {
        CabinetBatchInventoryAddDetail cabinetBatchInventoryAddDetailReq = new CabinetBatchInventoryAddDetail();
        cabinetBatchInventoryAddDetailReq.setCabinetCode(cabinetBatchInventory.getCabinetCode());
        cabinetBatchInventoryAddDetailReq.setSkuCode(cabinetBatchInventory.getSku());
        cabinetBatchInventoryAddDetailReq.setProduceDate(cabinetBatchInventory.getProduceDate());
        cabinetBatchInventoryAddDetailReq.setQualityDate(cabinetBatchInventory.getQualityDate());
        cabinetBatchInventoryAddDetailReq.setSkuBatchCode(cabinetBatchInventory.getBatchNo());
        cabinetBatchInventoryAddDetailReq.setAddQuantity(addQuantity);
        return cabinetBatchInventoryAddDetailReq;
    }

    public CabinetBatchInventoryAddDetail buildCabinetBatchInventoryAddDetail(String sku, String cabinetCode, Date produceDate, Date qualityDate, String batchNo, Integer addQuantity) {
        CabinetBatchInventoryAddDetail cabinetBatchInventoryAddDetailReq = new CabinetBatchInventoryAddDetail();
        cabinetBatchInventoryAddDetailReq.setCabinetCode(cabinetCode);
        cabinetBatchInventoryAddDetailReq.setSkuCode(sku);
        cabinetBatchInventoryAddDetailReq.setProduceDate(produceDate);
        cabinetBatchInventoryAddDetailReq.setQualityDate(qualityDate);
        cabinetBatchInventoryAddDetailReq.setSkuBatchCode(batchNo);
        cabinetBatchInventoryAddDetailReq.setAddQuantity(addQuantity);
        return cabinetBatchInventoryAddDetailReq;
    }

    public CabinetBatchInventoryReduce buildCabinetBatchInventoryReduceReqForProcessingMaterialReduce(Integer warehouseNo, String orderNo, Long tenantId, String operatorName) {
        CabinetBatchInventoryReduce cabinetBatchInventoryReduceReq = CabinetBatchInventoryReduce.builder()
                .warehouseNo(warehouseNo)
                .orderTypeName(CabinetInventoryChangeTypeEnum.PROCESSING_TASK.getTypeName())
                .operatorType(CabinetInventoryOperationType.PROCESSING_TASK_MATERIAL_REDUCE.getId())
                .orderNo(orderNo)
//                .internalNo(orderNo)
                .reduceDetailReqList(new ArrayList<>())
                .tenantId(tenantId)
                .operatorName(operatorName)
                .build();
        return cabinetBatchInventoryReduceReq;
    }

    public CabinetBatchInventoryAdd buildCabinetBatchInventoryAddReqForProcessingMaterialAdd(Integer warehouseNo, String orderNo, Long tenantId, String operatorName) {
        CabinetBatchInventoryAdd cabinetBatchInventoryAddReq = CabinetBatchInventoryAdd.builder()
                .warehouseNo(warehouseNo)
                .orderTypeName(CabinetInventoryChangeTypeEnum.PROCESSING_TASK.getTypeName())
                .operatorType(CabinetInventoryOperationType.PROCESSING_TASK_MATERIAL_INCREASE.getId())
                .orderNo(orderNo)
//                .internalNo(orderNo)
                .addDetailReqList(new ArrayList<>())
                .tenantId(tenantId)
                .operatorName(operatorName)
                .build();
        return cabinetBatchInventoryAddReq;
    }

    public CabinetBatchInventoryAdd buildCabinetBatchInventoryAddReqForProcessingProductAdd(Integer warehouseNo, String orderNo, Long tenantId, String operatorName) {
        CabinetBatchInventoryAdd cabinetBatchInventoryAddReq = CabinetBatchInventoryAdd.builder()
                .warehouseNo(warehouseNo)
                .orderTypeName(CabinetInventoryChangeTypeEnum.PROCESSING_TASK.getTypeName())
                .operatorType(CabinetInventoryOperationType.PROCESSING_TASK_PRODUCT_INCREASE.getId())
                .orderNo(orderNo)
//                .internalNo(orderNo)
                .addDetailReqList(new ArrayList<>())
                .tenantId(tenantId)
                .operatorName(operatorName)
                .build();
        return cabinetBatchInventoryAddReq;
    }

}
