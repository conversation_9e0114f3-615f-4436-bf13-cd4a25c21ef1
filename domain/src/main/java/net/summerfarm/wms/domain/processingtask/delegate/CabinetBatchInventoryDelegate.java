package net.summerfarm.wms.domain.processingtask.delegate;

import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAdd;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryReduce;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/6
 */
public interface CabinetBatchInventoryDelegate {

    /**
     * 库位批次库存扣减
     * @param cabinetBatchInventoryReduce
     * @return
     */
    String reduceCabinetBatchInventory(CabinetBatchInventoryReduce cabinetBatchInventoryReduce);

    /**
     * 库位批次库存增加
     * @param cabinetBatchInventoryAdd
     * @return
     */
    String addCabinetBatchInventory(CabinetBatchInventoryAdd cabinetBatchInventoryAdd);
}
