package net.summerfarm.wms.domain.stockTransfer;

import net.summerfarm.wms.domain.stockTransfer.param.StockTransferItemOpDetailParam;

import java.util.List;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
public interface StockTransferItemOpDetailCommandRepository {

    /**
     * 保存转换批次信息
     *
     * @param itemOpDetailParam 转换批次信息
     */
    int insert(StockTransferItemOpDetailParam itemOpDetailParam);

    /**
     * 批量保存转换批次信息
     *
     * @param stockTransferItemOpDetailParams 转换批次信息
     */
    void batchInsert(List<StockTransferItemOpDetailParam> stockTransferItemOpDetailParams);

    void updateExternalTransferInNum(Long opDetailId, Integer externalTransferInNum);
}
