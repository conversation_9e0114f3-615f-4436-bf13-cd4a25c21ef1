package net.summerfarm.wms.domain.processingtask.domainobject.aggregate;

import lombok.Data;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class ProcessingTaskMaterialReceiveAggregate implements Serializable {


    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 原料领用数量
     */
    private Integer materialSkuReceiveQuantity;

    /**
     * 物料
     */
    private ProcessingTaskMaterial processingTaskMaterial;

    /**
     * 物料领用记录列表
     */
    private List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList;

    /**
     * 追加原料ID
     *
     * @param materialId
     */
    public void addMaterialId(Long materialId) {
        for (ProcessingTaskMaterialReceiveRecord receiveRecord : materialReceiveRecordList) {
            receiveRecord.setProcessingTaskMaterialId(materialId);
        }
    }
}
