package net.summerfarm.wms.domain.processingtask.repository;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialRestoreRecord;

public interface ProcessingTaskMaterialRestoreRecordRepository {

    Long create(ProcessingTaskMaterialRestoreRecord processingTaskMaterialRestoreRecord);

}
