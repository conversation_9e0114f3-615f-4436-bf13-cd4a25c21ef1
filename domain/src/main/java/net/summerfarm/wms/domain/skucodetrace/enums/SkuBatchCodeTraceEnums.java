package net.summerfarm.wms.domain.skucodetrace.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 批次唯一溯源码枚举<br/>
 * date: 2024/8/9 14:40<br/>
 *
 * <AUTHOR> />
 */
public interface SkuBatchCodeTraceEnums {

    @Getter
    @AllArgsConstructor
    enum State {
        /**
         * 订单待分配
         */
        ORDER_WAIT_ALLOCATION(10, "订单待分配"),
        /**
         * 路线待分配
         */
        PATH_WAIT_ALLOCATION(20, "路线待分配"),
        /**
         * 待称重
         */
        WEIGHT_WAIT_ALLOCATION(30, "待称重"),
        /**
         * 已称重
         */
        HAVE_WEIGHT(40, "已称重"),
        ;
        private final Integer value;
        private final String content;

    }

    //标签类型
    @Getter
    @AllArgsConstructor
    enum LabelType{
        POP(0, "POP"),
        POP_T2(1, "POP T+2")
        ;
        private final Integer value;
        private final String content;
    }
}
