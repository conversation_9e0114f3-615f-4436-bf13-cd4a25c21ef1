package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.domain.stocktask.enums.NoticeAbnormalDetailStateEnum;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 出库通知单异常明细表
 * @author: dongcheng
 * @date: 2024/1/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTaskNoticeOrderAbnormalDetail {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 通知单编码
     */
    private Long noticeOrderId;

    /**
     * 货品供应单号
     */
    private String goodsSupplyNo;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 仓库租户id
     */
    private Long warehouseTenantId;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 状态
     * @see NoticeAbnormalDetailStateEnum
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否软删
     */
    private Long isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 外部自有编码
     */
    private String customerSkuCode;
}
