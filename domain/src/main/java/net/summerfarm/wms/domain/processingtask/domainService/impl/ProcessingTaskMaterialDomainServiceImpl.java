package net.summerfarm.wms.domain.processingtask.domainService.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.common.domainobject.Cabinet;
import net.summerfarm.wms.domain.common.repository.CabinetRepository;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.inventory.domainobject.CabinetBatchInventory;
import net.summerfarm.wms.domain.inventory.handler.distributor.CabinetBatchInventoryDistributor;
import net.summerfarm.wms.domain.inventory.handler.entity.CabinetBatchInventoryReduceDistributeResult;
import net.summerfarm.wms.domain.inventory.repository.CabinetBatchInventoryRepository;
import net.summerfarm.wms.domain.processingtask.delegate.CabinetBatchInventoryDelegate;
import net.summerfarm.wms.domain.processingtask.delegate.converter.CabinetBatchInventoryReduceConverter;
import net.summerfarm.wms.domain.processingtask.delegate.converter.ProcessingTaskMaterialReceiveRecordConverter;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAdd;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAddDetail;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryReduce;
import net.summerfarm.wms.domain.processingtask.delegate.factory.CabinetBatchInventoryReduceAddFactory;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.StoreRecord.enums.OtherStockChangeType;
import net.summerfarm.wms.domain.batch.domainobject.ProduceBatch;
import net.summerfarm.wms.domain.batch.proxy.ProduceBatchInnerServiceProxy;
import net.summerfarm.wms.domain.batch.repository.ProduceBatchRepository;
import net.summerfarm.wms.domain.inventory.domainService.WmsInventoryDomainService;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingTaskMaterialDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.MaterialWasteLossWeightUpdateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialReceiveAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialRestoreAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialRestoreRecord;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskMaterialReceiveRecordRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskMaterialRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskMaterialRestoreRecordRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskProductRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ProcessingTaskMaterialDomainServiceImpl implements ProcessingTaskMaterialDomainService {

    @Autowired
    private ProcessingTaskMaterialRepository materialRepository;
    @Autowired
    private ProcessingTaskMaterialReceiveRecordRepository materialReceiveRecordRepository;
    @Autowired
    private ProcessingTaskMaterialRestoreRecordRepository materialRestoreRecordRepository;
    @Autowired
    private ProcessingTaskProductRepository processingTaskProductRepository;

    @Autowired
    private WmsInventoryDomainService wmsInventoryDomainService;
    @Autowired
    private ProduceBatchInnerServiceProxy produceBatchInnerServiceProxy;
    @Autowired
    private ProduceBatchRepository produceBatchRepository;
    @Autowired
    private WarehouseConfigRepository warehouseConfigRepository;
    @Autowired
    private CabinetBatchInventoryRepository cabinetBatchInventoryRepository;
    @Autowired
    private CabinetBatchInventoryReduceAddFactory cabinetBatchInventoryReduceAddFactory;
    @Autowired
    private CabinetBatchInventoryDelegate cabinetBatchInventoryDelegate;
    @Autowired
    private CabinetRepository cabinetRepository;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long materialReceive(ProcessingTaskMaterialReceiveAggregate receiveAggregate) {
        if (receiveAggregate == null) {
            throw new BizException(ErrorCodeNew.PARAM_ERROR);
        }

        // 开启库位管理配置
        Boolean openCabinetManagement = warehouseConfigRepository.openCabinetManagement(receiveAggregate.getProcessingTaskMaterial().getWarehouseNo());

        // 库位批次库存扣减请求实体
        CabinetBatchInventoryReduce cabinetBatchInventoryReduce = cabinetBatchInventoryReduceAddFactory.buildCabinetBatchInventoryReduceReqForProcessingMaterialReduce(
                receiveAggregate.getProcessingTaskMaterial().getWarehouseNo(), receiveAggregate.getProcessingTaskMaterial().getProcessingTaskCode(),
                receiveAggregate.getTenantId(), LoginInfoThreadLocal.getCurrentUserName());

        // 开启库位管理补充批次，组装库位批次库存请求明细
        if (Boolean.TRUE.equals(openCabinetManagement)) {
            // 重新分配领用记录
            List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordListForCabinet = new ArrayList<>();
            for (ProcessingTaskMaterialReceiveRecord receiveRecord : receiveAggregate.getMaterialReceiveRecordList()) {
                // 查询可用库位批次库存
                List<CabinetBatchInventory> cabinetBatchInventoryList = cabinetBatchInventoryRepository.listByWnoAndSkuAndCabinetAndAvailableQualityGtZero(receiveAggregate.getProcessingTaskMaterial().getWarehouseNo(), receiveRecord.getMaterialSkuCode(), receiveRecord.getMaterialSkuCabinetCode(), receiveRecord.getMaterialSkuProductionDate(), receiveRecord.getMaterialSkuQualityDate());
                if (CollectionUtils.isEmpty(cabinetBatchInventoryList)) {
                    throw new BizException("无可用库存批次库存");
                }
                // 需扣减数量分配
                List<CabinetBatchInventoryReduceDistributeResult> distributeReduceResults = CabinetBatchInventoryDistributor.distributeReduce(cabinetBatchInventoryList, receiveRecord.getMaterialSkuQuantity());
                distributeReduceResults.forEach(item -> cabinetBatchInventoryReduce.getReduceDetailReqList().add(CabinetBatchInventoryReduceConverter.INSTANCE.convert(item)));
                distributeReduceResults.forEach(reduceDetail -> {
                    ProcessingTaskMaterialReceiveRecord processingTaskMaterialReceiveRecord = ProcessingTaskMaterialReceiveRecordConverter.INSTANCE.convert(receiveRecord);
                    processingTaskMaterialReceiveRecord.setMaterialSkuPurchaseBatch(reduceDetail.getSkuBatchCode());
                    processingTaskMaterialReceiveRecord.setMaterialSkuQuantity(reduceDetail.getReduceQuantity());
                    processingTaskMaterialReceiveRecord.setMaterialSkuReceiveQuantity(reduceDetail.getReduceQuantity());
                    materialReceiveRecordListForCabinet.add(processingTaskMaterialReceiveRecord);
                });
            }
            log.info("加工任务：{}，传入明细：{}", receiveAggregate.getProcessingTaskMaterial().getProcessingTaskCode(), JSON.toJSONString(receiveAggregate.getMaterialReceiveRecordList()));
            log.info("加工任务：{}，领料重新分配明细{}", receiveAggregate.getProcessingTaskMaterial().getProcessingTaskCode(), JSON.toJSONString(materialReceiveRecordListForCabinet));
            if (CollectionUtils.isEmpty(materialReceiveRecordListForCabinet)) {
                throw new BizException("领料重新分配明细异常");
            }
            receiveAggregate.setMaterialReceiveRecordList(materialReceiveRecordListForCabinet);
        }

        Long materialId = receiveAggregate.getProcessingTaskMaterial().getId();

        // 追加id
        receiveAggregate.addMaterialId(materialId);

        materialReceiveRecordRepository.batchCreate(receiveAggregate.getMaterialReceiveRecordList());

        // 追加原料领料
        materialRepository.addMaterial(
                materialId,
                receiveAggregate.getMaterialSkuReceiveQuantity(),
                receiveAggregate.getProcessingTaskMaterial().getMaterialSkuWeight()
                        .multiply(BigDecimal.valueOf(receiveAggregate.getMaterialSkuReceiveQuantity())),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // 追加成品领料
        processingTaskProductRepository.addMaterial(
                receiveAggregate.getProcessingTaskMaterial().getProcessingTaskProductId(),
                receiveAggregate.getMaterialSkuReceiveQuantity(),
                receiveAggregate.getProcessingTaskMaterial().getMaterialSkuWeight()
                        .multiply(BigDecimal.valueOf(receiveAggregate.getMaterialSkuReceiveQuantity())),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // region 库存操作
        for (ProcessingTaskMaterialReceiveRecord receiveRecord : receiveAggregate.getMaterialReceiveRecordList()) {
            // 扣减原料库存
            wmsInventoryDomainService.occupyAndReduceInventory(
                    receiveRecord.getMaterialSkuQuantity(),
                    receiveRecord.getMaterialSkuCode(),
                    Long.valueOf(receiveRecord.getWarehouseNo()),
                    OtherStockChangeType.PROCESSING_TASK_MATERIAL_REDUCE,
                    "" + receiveRecord.getId(),
                    LoginInfoThreadLocal.getCurrentUserName());

            // 获取相应的采购批次
            ProduceBatch produceBatch = produceBatchRepository.queryProduceBatchByWnoAndSkuCodeAndDate(
                    receiveRecord.getWarehouseNo(),
                    receiveRecord.getMaterialSkuCode(),
                    receiveRecord.getMaterialSkuProductionDate(),
                    receiveRecord.getMaterialSkuQualityDate()
            );
            if (produceBatch == null) {
                log.error("领料创建批次库存时，查询采购批次不存在：" + JSONObject.toJSONString(receiveRecord));
                throw new BizException("创建批次库存时，查询采购批次不存在");
            }

            // 扣减批次库存
            produceBatchInnerServiceProxy.updateProduceBatch(
                    OperationType.PROCESSING_TASK_MATERIAL_REDUCE.getId(),
                    LoginInfoThreadLocal.getCurrentUserName(),
                    receiveRecord.getMaterialSkuPurchaseBatch(),
                    produceBatch.getId(),
                    -receiveRecord.getMaterialSkuQuantity(),
                    null,
                    receiveRecord.getId(),
                    LoginInfoThreadLocal.getTenantId()
            );
        }
        // endregion
        // 扣减库位批次库存
        if (!CollectionUtils.isEmpty(cabinetBatchInventoryReduce.getReduceDetailReqList())) {
            log.info("加工领料调用扣减库位批次库存，任务号：{}", receiveAggregate.getProcessingTaskMaterial().getProcessingTaskCode());
            cabinetBatchInventoryDelegate.reduceCabinetBatchInventory(cabinetBatchInventoryReduce);
            log.info("加工领料完成扣减库位批次库存，任务号：{}", receiveAggregate.getProcessingTaskMaterial().getProcessingTaskCode());
        }


        return materialId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void materialWasteLossWeightUpdate(MaterialWasteLossWeightUpdateAggregate updateAggregate) {
        ProcessingTaskMaterialReceiveRecord receiveRecord = materialReceiveRecordRepository.queryById(
                updateAggregate.getMaterialReceiveId());
        if (receiveRecord == null) {
            throw new BizException(ErrorCodeNew.PARAM_ERROR);
        }

        ProcessingTaskMaterial processingTaskMaterial = materialRepository.queryById(
                updateAggregate.getProcessingTaskMaterialId());
        if (processingTaskMaterial == null) {
            throw new BizException(ErrorCodeNew.PARAM_ERROR);
        }

        ProcessingTaskProduct processingTaskProduct = processingTaskProductRepository.queryById(
                updateAggregate.getProcessingTaskProductId());
        if (processingTaskProduct == null) {
            throw new BizException(ErrorCodeNew.PARAM_ERROR);
        }

        // 更新物料领用废料损耗
        materialReceiveRecordRepository.updateWasteLossWeight(
                receiveRecord.getId(),
                updateAggregate.getWasteLossWeight(),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // 更新物料废料损耗
        materialRepository.addWasteLossWeight(
                processingTaskMaterial.getId(),
                updateAggregate.getWasteLossWeight().subtract(
                        receiveRecord.getWasteLossWeight() == null ? BigDecimal.ZERO :
                                receiveRecord.getWasteLossWeight()
                ),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // 更新成品废料损耗
        processingTaskProductRepository.addWasteLossWeight(
                processingTaskProduct.getId(),
                updateAggregate.getWasteLossWeight().subtract(
                        receiveRecord.getWasteLossWeight() == null ? BigDecimal.ZERO :
                                receiveRecord.getWasteLossWeight()
                ),
                LoginInfoThreadLocal.getCurrentUserName()
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long materialRestore(ProcessingTaskMaterialRestoreAggregate materialRestoreAggregate) {

        ProcessingTaskMaterialRestoreRecord restoreRecord =
                materialRestoreAggregate.getProcessingTaskMaterialRestoreRecord();

        // 创建归还明细
        Long id = materialRestoreRecordRepository.create(
                restoreRecord
        );

        // 修改原料领用归还数量
        materialReceiveRecordRepository.addRestoreQuantity(
                materialRestoreAggregate.getMaterialReceiveId(),
                materialRestoreAggregate.getMaterialSkuRestoreQuantity(),
                materialRestoreAggregate.getMaterialSkuRestoreWeight(),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // 修改原料归还数量
        materialRepository.addRestoreQuantity(
                materialRestoreAggregate.getProcessingTaskMaterialId(),
                materialRestoreAggregate.getMaterialSkuRestoreQuantity(),
                materialRestoreAggregate.getMaterialSkuRestoreWeight(),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // 修改成品原料归还数量
        processingTaskProductRepository.reduceMaterial(
                materialRestoreAggregate.getProcessingTaskProductId(),
                materialRestoreAggregate.getMaterialSkuRestoreQuantity(),
                restoreRecord.getMaterialSkuWeight()
                        .multiply(BigDecimal.valueOf(materialRestoreAggregate.getMaterialSkuRestoreQuantity())),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // region 操作归还库存
        wmsInventoryDomainService.increaseInventory(
                restoreRecord.getMaterialSkuRestoreQuantity(),
                restoreRecord.getMaterialSkuCode(),
                Long.valueOf(restoreRecord.getWarehouseNo()),
                OtherStockChangeType.PROCESSING_TASK_MATERIAL_INCREASE,
                "" + restoreRecord.getId(),
                LoginInfoThreadLocal.getCurrentUserName()
        );

        // 获取相应的采购批次
        ProduceBatch produceBatch = produceBatchRepository.queryProduceBatchByWnoAndSkuCodeAndDate(
                restoreRecord.getWarehouseNo(),
                restoreRecord.getMaterialSkuCode(),
                restoreRecord.getMaterialSkuProductionDate(),
                restoreRecord.getMaterialSkuQualityDate()
        );
        if (produceBatch == null) {
            log.error("领料创建批次库存时，查询采购批次不存在：" + JSONObject.toJSONString(restoreRecord));
            throw new BizException("创建批次库存时，查询采购批次不存在");
        }

        // 增加批次库存
        produceBatchInnerServiceProxy.updateProduceBatch(
                OperationType.PROCESSING_TASK_MATERIAL_INCREASE.getId(),
                LoginInfoThreadLocal.getCurrentUserName(),
                restoreRecord.getMaterialSkuPurchaseBatch(),
                produceBatch.getId(),
                restoreRecord.getMaterialSkuRestoreQuantity(),
                null,
                restoreRecord.getId(),
                LoginInfoThreadLocal.getTenantId()
        );
        // endregion

        // 开启库位管理配置
        Boolean openCabinetManagement = warehouseConfigRepository.openCabinetManagement(restoreRecord.getWarehouseNo());
        // 开启库位管理
        if (Boolean.TRUE.equals(openCabinetManagement)) {
            // 查询加工暂存位
            Cabinet cabinet = cabinetRepository.defaultJGCabinet(restoreRecord.getWarehouseNo());
            if (null == cabinet) {
                throw new BizException("未查询到加工暂存库位");
            }
            // 查询库位批次库存记录
//            CabinetBatchInventory cabinetBatchInventory = cabinetBatchInventoryRepository.findOneByWnoAndSkuAndCabinetAndQuality(restoreRecord.getWarehouseNo(), restoreRecord.getMaterialSkuCode(), restoreRecord.getMaterialSkuCabinetCode(), restoreRecord.getMaterialSkuProductionDate(), restoreRecord.getMaterialSkuQualityDate());
//            if (null == cabinetBatchInventory) {
//                throw new BizException("查询库位批次库存记录为空");
//            }
            // 库位库存操作实体
            CabinetBatchInventoryAdd cabinetBatchInventoryAdd = cabinetBatchInventoryReduceAddFactory.buildCabinetBatchInventoryAddReqForProcessingMaterialAdd(restoreRecord.getWarehouseNo(), restoreRecord.getProcessingTaskCode(), restoreRecord.getTenantId(), LoginInfoThreadLocal.getCurrentUserName());
            CabinetBatchInventoryAddDetail cabinetBatchInventoryAddDetail = cabinetBatchInventoryReduceAddFactory.buildCabinetBatchInventoryAddDetail(restoreRecord.getMaterialSkuCode(), DefaultCabinetCodeEnum.JG.getCode(), restoreRecord.getMaterialSkuProductionDate(), restoreRecord.getMaterialSkuQualityDate(), restoreRecord.getMaterialSkuPurchaseBatch(), restoreRecord.getMaterialSkuRestoreQuantity());
            cabinetBatchInventoryAdd.getAddDetailReqList().add(cabinetBatchInventoryAddDetail);
            log.info("加工原料归还调用增加库位批次库存，任务号：{}", restoreRecord.getProcessingTaskCode());
            cabinetBatchInventoryDelegate.addCabinetBatchInventory(cabinetBatchInventoryAdd);
            log.info("加工原料归还完成增加库位批次库存，任务号：{}", restoreRecord.getProcessingTaskCode());
        }

        return id;
    }

}
