package net.summerfarm.wms.domain.stocktask.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Description
 * @Date 2023/4/21 15:44
 * @<AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum StorageLocationEnum {

    PEER_MOVE(0, "未分类"),
    FREEZING(1, "冷冻"),
    REFRIGERATION(2, "冷藏"),
    CONSTANT_TEMPERATURE(3, "常温"),
    TOP(4, "顶汇大流通"),
    ;

    public static String convert(Integer code) {
        return Arrays.stream(StorageLocationEnum.values())
                .filter(o -> o.getCode().equals(code))
                .map(StorageLocationEnum::getDesc)
                .findFirst().orElse("");
    }

    Integer code;
    String desc;

    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (StorageLocationEnum storageLocationEnum : StorageLocationEnum.values()) {
            if (Objects.equals(storageLocationEnum.getCode(), code)) {
                return storageLocationEnum.getDesc();
            }
        }
        return "";
    }
}
