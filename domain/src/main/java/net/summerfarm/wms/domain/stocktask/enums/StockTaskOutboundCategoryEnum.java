package net.summerfarm.wms.domain.stocktask.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * Description: 出库分类<br/>
 * date: 2024/6/21 16:00<br/>
 *
 * <AUTHOR> />
 */
@Getter
public enum StockTaskOutboundCategoryEnum {

    DEFAULT(0, "默认"),
    POP(1, "POP出库"),
    POPT2(2, "POPT+2出库"),
    SLD(3, "顺鹿达出库"),
    ;

    private final Integer code;
    private final String desc;

    StockTaskOutboundCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
