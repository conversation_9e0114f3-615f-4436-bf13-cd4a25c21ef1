package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskPurchaseBackDetail;
import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskPurchasesBackDetailQuery;

import java.util.List;

/**
 * @Description
 * @Date 2023/10/24 14:19
 * @<AUTHOR>
 */
public interface StockTaskPurchasesBackDetailRepository {

    /**
     * 批量创建
     *
     * <AUTHOR>
     * @date 2023/11/6 15:01
     */
    void batchCreate(List<StockTaskPurchaseBackDetail> purchaseBackDetailList);

    /**
     * 根据采退单号 sku查询详情
     *
     * <AUTHOR>
     * @date 2023/11/6 15:01
     * @return java.util.List<net.summerfarm.wms.domain.stocktask.domainobject.StockTaskPurchaseBackDetail>
     */
    List<StockTaskPurchaseBackDetail> queryByTaskNoAndSku(String taskNo, String sku, List<String> querySkus);

    /**
     * 统计采退出库数量
     *
     * <AUTHOR>
     * @date 2023/11/6 15:02
     */
    Integer sumOutQuantity(String taskNo, String sku);

    /**
     * 根据采退单号查询详情
     *
     * <AUTHOR>
     * @date 2023/11/6 15:02
     */
    List<StockTaskPurchaseBackDetail> queryByPurchaseBackNo(String purchaseBackNo);

    /**
     * 根据采退单号&sku查询详情
     * @param purchaseBackNo
     * @param skuList
     * @return
     */
    List<StockTaskPurchaseBackDetail> selectListByTaskNoAndSkus(String purchaseBackNo, List<String> skuList);

    /**
     * 根据采退单号查询详情
     *
     * <AUTHOR>
     * @date 2023/11/6 15:03
     */
    List<StockTaskPurchaseBackDetail> queryByPurchaseBackNoList(List<String> purchaseBackNoList);

    /**
     * 更新
     *
     * <AUTHOR>
     * @date 2023/11/6 15:03
     */
    void update(StockTaskPurchaseBackDetail purchaseBackDetail);

    /**
     * 根据ID查询详情
     *
     * <AUTHOR>
     * @date 2023/11/6 15:03
     */
    StockTaskPurchaseBackDetail queryById(Long detailId);

    /**
     * 统计采退冻结数量
     *
     * <AUTHOR>
     * @date 2023/11/6 15:03
     */
    Integer statisticsLockQuantity(StockTaskPurchasesBackDetailQuery detailQuery);

    /**
     * 更新采退单号 取消场景
     *
     * <AUTHOR>
     * @date 2023/11/15 10:40
     */
    void updatePurchaseBackNo(Long stockTaskId, String taskNo, String cancelTaskNo);
}
