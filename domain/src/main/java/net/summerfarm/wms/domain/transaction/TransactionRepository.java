package net.summerfarm.wms.domain.transaction;

import net.summerfarm.wms.domain.transaction.domainobject.TransactionLog;

import java.util.List;

/**
 * 事务仓储
 *
 * <AUTHOR>
 */
public interface TransactionRepository {
    /**
     * 保存事务日志
     *
     * @param transactionLog t
     */
    void saveTransactionLog(TransactionLog transactionLog);

    void saveTransactionLogs(List<TransactionLog> transactionLog);

    /**
     * 修改事务状态
     *
     * @param transactionLog t
     */
    void changeTransactionLogState(TransactionLog transactionLog);

    /**
     * 查询本地事务
     *
     * @param warehouseNo 仓库号
     * @param bizId       业务id
     * @param bizType     业务类型
     * @return 事务
     */
    TransactionLog findTransactionLog(Long warehouseNo, String bizId, String bizType);
}
