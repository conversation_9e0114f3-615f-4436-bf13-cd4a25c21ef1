package net.summerfarm.wms.domain.processingtask.repository;

import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductSubmitRecord;

import java.util.List;

public interface ProcessingTaskProductSubmitRecordRepository {

    /**
     * 批量创建
     *
     * @param productSubmitRecordList
     */
    void batchCreate(List<ProcessingTaskProductSubmitRecord> productSubmitRecordList);

    /**
     * 根据加工任务成品id查询提交记录
     *
     * @param processingTaskProductId 加工任务成品id
     * @return 对象列表
     */
    List<ProcessingTaskProductSubmitRecord> queryAllByProcessingTaskProductId(Long processingTaskProductId);

    /**
     * 查询单条提交记录
     *
     * @param recordId id
     * @return
     */
    ProcessingTaskProductSubmitRecord queryById(Long recordId);

    /**
     * 通过id更新加工任务成品提交记录打印次数
     *
     * @param processingTaskProductSubmitRecordId 加工任务成品提交记录id
     * @param canPrintNumber                      打印次数
     */
    void updatePrintNumberById(Long processingTaskProductSubmitRecordId, Integer canPrintNumber);
}
