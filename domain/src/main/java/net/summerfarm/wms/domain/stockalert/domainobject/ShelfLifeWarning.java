package net.summerfarm.wms.domain.stockalert.domainobject;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-01 12:01:20
 */
@Data
public class ShelfLifeWarning {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 仓库号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库服务商
     */
    private String warehouseProvider;

    /**
     * 货品编码
     */
    private Long pdId;

    /**
     * sku编码
     */
    private String sku;

    /**
     * saas skuId
     */
    private Long saasSkuId;

    /**
     * 类目id
     */
    private Integer categoryId;

    /**
     * sku租户id
     */
    private Long skuTenantId;

    /**
     * 仓库租户id
     */
    private Long warehouseTenantId;

    /**
     * 批次
     */
    private String batch;

    /**
     * 生产日期
     */
    private LocalDate productionDate;


    /**
     * 保质期
     */
    private LocalDate qualityDate;


    /**
     * 库存数量
     */
    private Integer quantity;

    /**
     * 保质期天数
     */
    private Integer shelfLifeDays;

    /**
     * 保质期剩余天数
     */
    private Integer remainingDays;

    /**
     * 临保百分比（保存百分数）
     */
    private Double approachingShelfLifePercentage;

    /**
     * 保质期状态，1：正常、2：临保、3：过期
     */
    private Integer status;

    /**
     * 数据所属日期:格式yyyyMMdd
     */
    private Integer dayTag;

    /**
     * 货品启用状态，0：停用、1：启用
     */
    private Integer useFlag;

}