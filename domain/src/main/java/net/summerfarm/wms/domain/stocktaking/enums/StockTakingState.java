package net.summerfarm.wms.domain.stocktaking.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * 盘点状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum StockTakingState {
    /**
     * 盘点状态
     */
    STOCKTAKING(0, "盘点中"),
    INIT(1, "待盘点"),
    SUCCESS(2, "盘点成功"),

    UNKNOWN(3,"未知"),
    CANCEL(4, "盘点取消")
    ;

    Integer code;
    String desc;

    public static StockTakingState convert(Integer param) {
        return Arrays.stream(StockTakingState.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElse(StockTakingState.UNKNOWN);
    }

    public static boolean noChange(Integer code) {
        return StockTakingState.SUCCESS.getCode().equals(code) || StockTakingState.CANCEL.getCode().equals(code) || StockTakingState.STOCKTAKING.getCode().equals(code);
    }

}
