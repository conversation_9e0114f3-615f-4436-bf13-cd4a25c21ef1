package net.summerfarm.wms.domain.stocktaking.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 盘点任务创建请求对象
 *
 * <AUTHOR>
 * @date 2022/09/14
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockTakingDomainCreateCommand {

    /**
     * 仓库号
     */
    Long warehouseNo;

    /**
     * 盘点维度
     */
    Integer dimension;

    /**
     * sku盘点
     */
    List<String> skus;

    /**
     * 类目盘点条件
     *
     */
    String condition;

    /**
     * 操作人
     */
    String operator;


}
