package net.summerfarm.wms.domain.allocation;

import net.summerfarm.wms.domain.allocation.domainobject.StockAllocationList;
import net.summerfarm.wms.domain.allocation.valueobject.AllocationRoadQuantityValueObject;

import java.util.List;

public interface StockAllocationRepository {
    StockAllocationList findAllocation(String listNo);

    /**
     * 根据sku和仓库号获取处理中的调拨单，包括调入和调出
     *
     * @param sku
     * @param warehouseNo
     * @return
     */
    List<StockAllocationList> findProcessingAllocationBySkuAndWarehouseNo(String sku, Integer warehouseNo);

    /**
     * 根据调入仓库号和sku获取调拨单
     *
     * @param inStore       调入仓库号，必传
     * @param sku           调拨sku，必传
     * @param nextDayArrive 是否次日达：（0、是  1、不是），非必传
     * @param status        调拨单状态，非必传
     * @return
     */
    List<StockAllocationList> findAllocationByInStoreAndSku(Integer inStore, String sku, Integer nextDayArrive, Integer status);

    /**
     * 根据库存仓查询调拨在途库存
     *
     * @param warehouseNo
     * @return
     */
    List<AllocationRoadQuantityValueObject> queryAllocationRoadQuantity(Integer warehouseNo);
}
