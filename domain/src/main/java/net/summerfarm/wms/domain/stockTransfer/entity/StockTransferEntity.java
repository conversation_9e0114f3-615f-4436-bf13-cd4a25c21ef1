package net.summerfarm.wms.domain.stockTransfer.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTransferEntity implements Serializable {
    private static final long serialVersionUID = -4396331476177728949L;

    /**
     * 主键id
     */
    Long id;

    /**
     * 库存仓编号
     */
    Long warehouseNo;

    /**
     * 仓库名称
     */
    String warehouseName;

    /**
     * 转换维度
     * PEER_MOVE(0, "库存转换(相同sku不同批次)"),
     * DOWN_GRADE(1, "降级转换(不同spu)"),
     *
     * @see TransferDimensionEnum
     */
    Integer transferDimension;

    /**
     * 状态
     *
     * @see StockTransferStateEnum
     */
    Integer state;

    /**
     * 操作人
     */
    String operator;

    /**
     * 发起人
     */
    String founder;

    /**
     * 备注
     */
    String remark;

    /**
     * 创建时间
     */
    Date createdAt;

    /**
     * 更新时间
     */
    Date updatedAt;

    /**
     * 任务来源（1:系统自动，2:手动创建）
     */
    private Integer taskSource;

}
