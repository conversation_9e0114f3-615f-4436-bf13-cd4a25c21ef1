package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @author: dongcheng
 * @date: 2024/1/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskProcessOrderSku implements Serializable {

    private static final long serialVersionUID = 7937946610707559511L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 出库单ID
     */
    private Long stockTaskProcessId;

    /**
     * 出库任务ID
     */
    private Long stockTaskId;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * 货品供应单
     */
    private String goodsSupplyNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * sku数量
     */
    private Integer quantity;

    /**
     * 缺出数量
     */
    private Integer abnormalQuantity;

    /**
     * 采购批次
     */
    private String purchaseBatch;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long gmtCreated;

    /**
     * 更新时间
     */
    private Long gmtModified;

    /**
     * 是否软删
     */
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;
}
