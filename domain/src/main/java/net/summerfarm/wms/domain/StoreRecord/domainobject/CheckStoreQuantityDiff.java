package net.summerfarm.wms.domain.StoreRecord.domainobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CheckStoreQuantityDiff implements Serializable {

    /**
     * 仓库编码
     */
    private Integer warehouseNo;
    /**
     * sku
     */
    private String sku;
    /**
     * 生产日期
     */
    private Date produceAt;
    /**
     * 保质期
     */
    private Date shelfLife;
    /**
     * 采购单号
     */
    private String purchaseNo;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 批次流水数量
     */
    private Integer batchFlowQuantity;
    /**
     * 生产批次数量
     */
    private Integer produceBatchQuantity;
    /**
     * 成本成本数量
     */
    private Integer costBatchQuantity;
}
