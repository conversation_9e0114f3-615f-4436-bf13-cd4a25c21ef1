package net.summerfarm.wms.domain.stocktask.domainobject;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.util.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: dongcheng
 * @date: 2023/7/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskItemCabinetOccupyDO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 出库任务编码
     */
    private Long stockTaskId;

    /**
     * 出库任务明细编码
     */
    private Long stockTaskItemId;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 库区编码
     */
    private String zoneCode;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    @JSONField(format = DateUtil.YYYY_MM_DD)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD, timezone = "GMT+8")
    private Date productionDate;

    /**
     * 保质期
     */
    @DateTimeFormat(pattern = DateUtil.YYYY_MM_DD)
    @JSONField(format = DateUtil.YYYY_MM_DD)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD, timezone = "GMT+8")
    private Date qualityDate;

    /**
     * 占用数量
     */
    private Integer occupyQuantity;

    /**
     * 拣货数量
     */
    private Integer pickQuantity;

    /**
     * 释放数量
     */
    private Integer releaseQuantity;

    /**
     * 实际拣货数量
     */
    private Integer actualPickQuantity;

    /**
     * 应出
     */
    private Integer shouldPickQuantity;

    /**
     * create time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD, timezone = "GMT+8")
    private Date createTime;

    /**
     * update time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private Date updateTime;

    /**
     * 是否软删 0-正常 1-软删
     */
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 异常数量
     */
    private Integer abnormalQuantity;

    private Integer shouldQuantity;

    public String dupKey() {
        return sku + DateUtil.toLocalDate(productionDate) + DateUtil.toLocalDate(qualityDate) + cabinetCode;
    }
}
