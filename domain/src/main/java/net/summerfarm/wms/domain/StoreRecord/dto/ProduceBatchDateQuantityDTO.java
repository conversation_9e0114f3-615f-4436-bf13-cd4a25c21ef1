package net.summerfarm.wms.domain.StoreRecord.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/24
 */
@Data
public class ProduceBatchDateQuantityDTO implements Serializable {

    private Long id;

    private Integer warehouseNo;

    private String sku;

    private Integer quantity;

    private LocalDate produceAt;

    private LocalDate shelfLife;

}
