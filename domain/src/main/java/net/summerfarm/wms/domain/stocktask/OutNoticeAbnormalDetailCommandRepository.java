package net.summerfarm.wms.domain.stocktask;

import net.summerfarm.wms.domain.stocktask.domainobject.StockTaskNoticeOrderAbnormalDetail;

import java.util.List;

/**
 * @Description
 * @Date 2023/4/10 15:46
 * @<AUTHOR>
 */
public interface OutNoticeAbnormalDetailCommandRepository {

    /**
     * 生成异常出库单明细
     *
     * @param abnormalDetails 异常明细
     */
    List<StockTaskNoticeOrderAbnormalDetail> createNoticeOrderAbnormalDetail(List<StockTaskNoticeOrderAbnormalDetail> abnormalDetails);

    /**
     * 完成出库异常通知单
     * @param abnormalNoticeIds ids
     */
    void finishAbnormalDetail(List<Long> abnormalNoticeIds);
}
