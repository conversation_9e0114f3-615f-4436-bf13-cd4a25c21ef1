package net.summerfarm.wms.domain.processingtask.domainService.impl;

import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.common.enums.GeneratorDocTypeEnum;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.common.util.SerialNumberGenerator;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingTaskDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskProductStatusEnum;
import net.summerfarm.wms.domain.processingtask.repository.*;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProcessingTaskDomainServiceImpl implements ProcessingTaskDomainService {

    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProcessingTaskProductRepository processingTaskProductRepository;
    @Autowired
    private ProcessingTaskProductRecordRepository processingTaskProductRecordRepository;
    @Autowired
    private ProcessingTaskProductOrderRecordRepository processingTaskProductOrderRecordRepository;

    @Autowired
    private ProcessingTaskMaterialRepository materialRepository;
    @Autowired
    private ProcessingTaskMaterialReceiveRecordRepository materialReceiveRecordRepository;

    @Autowired
    private SerialNumberGenerator serialNumberGenerator;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createProcessingTask(ProcessingTaskCreateAggregate processingTaskCreateAggregate){

        // 处理单号
        String taskCode = serialNumberGenerator.createDocCode(GeneratorDocTypeEnum.JIA_GONG_REN_WU);
        processingTaskCreateAggregate.addTaskCode(taskCode);

        // 创建任务
        Long processingTaskId = processingTaskRepository.create(
                processingTaskCreateAggregate.getProcessingTask());

        // 创建成品
        processingTaskProductRepository.batchCreate(
                processingTaskCreateAggregate.getProcessingTaskProductList()
        );

        // 绑定成品ID
        Map<String, ProcessingTaskProduct> processingTaskProductMap = processingTaskProductRepository.mapSkuCodeByProcessingTaskCode(
                processingTaskCreateAggregate.getProcessingTask().getProcessingTaskCode());;
        processingTaskCreateAggregate.addProcessingTaskProductIds(processingTaskProductMap);

        // 创建成品规格
        processingTaskProductRecordRepository.batchCreate(
                processingTaskCreateAggregate.getProcessingTaskProductRecordList()
        );

        //创建成品订单规格
        processingTaskProductOrderRecordRepository.batchCreate(
                processingTaskCreateAggregate.getProcessingTaskProductOrderRecordList()
        );

        // 创建原料
        materialRepository.batchCreate(
                processingTaskCreateAggregate.getProcessingTaskMaterialList()
        );

        return processingTaskId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Long> finishTask(String processingTaskCode,
                                         List<ProcessingTaskProduct> processingTaskProductList,
                                         String remark) {
        if (StringUtils.isEmpty(processingTaskCode)){
            throw new BizException(ErrorCodeNew.PARAM_ERROR);
        }

        List<ProcessingTaskProduct> unfinishedProductList = processingTaskProductList.stream()
                .filter(processingTaskProduct ->
                        !ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unfinishedProductList)) {
            List<Long> productIdList = unfinishedProductList.stream()
                    .map(ProcessingTaskProduct::getId)
                    .distinct()
                    .collect(Collectors.toList());
            // 更新未领料成品任务状态
            processingTaskProductRepository.updateFinishByNotProcessing(productIdList, LoginInfoThreadLocal.getCurrentUserName());
        }

        // 更新加工任务状态
        processingTaskRepository.finishTask(processingTaskCode, remark, LoginInfoThreadLocal.getCurrentUserName());
        return CommonResult.ok(1L);
    }
}
