package net.summerfarm.wms.domain.processingtask.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity;
import net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingMaterialConfigQueryParam;

import java.util.List;
import java.util.Map;


/**
*
* <AUTHOR>
* @date 2025-01-10 18:59:23
* @version 1.0
*
*/
public interface ProcessingMaterialConfigQueryRepository {

    PageInfo<ProcessingMaterialConfigEntity> getPage(WmsProcessingMaterialConfigQueryParam param);

    ProcessingMaterialConfigEntity selectById(Long id);

    List<ProcessingMaterialConfigEntity> selectByCondition(WmsProcessingMaterialConfigQueryParam param);

    Map<Long, List<ProcessingMaterialConfigEntity>> mapByConfigIdList(List<Long> configIdList);

    Map<String, ProcessingMaterialConfigEntity> mapByConfigIdListGroupByMSku(List<Long> configIdList);

    Map<String, ProcessingMaterialConfigEntity> mapByConfigIdGroupByMSku(Long configId);
}