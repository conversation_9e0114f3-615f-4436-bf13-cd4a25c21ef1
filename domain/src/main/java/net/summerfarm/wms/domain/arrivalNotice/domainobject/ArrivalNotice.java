package net.summerfarm.wms.domain.arrivalNotice.domainobject;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ArrivalNotice {
    private Integer id;

    private Integer mId;

    private String mname;

    private LocalDateTime addTime;

    private String sku;

    private String pdName;

    private String weight;

    private Integer status;

    private Integer storeNo;

    private int quantity;

    private String openid;

    private Integer pdId;

    /**
     * 0默认到货提醒 1上新通知
     */
    private Integer type;

    /**
     * 订阅数量
     */
    private Integer num;

    private String storeName;
}
