package net.summerfarm.wms.domain.processingtask.domainobject.enums;

public enum ProcessingTaskStatusEnum {


    NOT_PROCESSING(0, "未加工"),

    PROCESSED(1, "已加工"),

    PART_PROCESSING(2, "部分加工"),

    ;

    private final int value;

    private final String description;

    ProcessingTaskStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据value返回description
     * @param value 值
     * @return 描述
     */
    public static String getDescriptionByValue(int value) {
        for (ProcessingTaskStatusEnum processingTaskStatusEnum : ProcessingTaskStatusEnum.values()) {
            if (processingTaskStatusEnum.getValue() == value) {
                return processingTaskStatusEnum.getDescription();
            }
        }
        return "";
    }
}
