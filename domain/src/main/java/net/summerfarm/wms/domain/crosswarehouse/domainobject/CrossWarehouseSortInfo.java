package net.summerfarm.wms.domain.crosswarehouse.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 越库分拣明细内容
 *
 * @author: dongcheng
 * @date: 2023/10/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrossWarehouseSortInfo {

    private Long id;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * ofc传输过程中的编号
     */
    private String psoNo;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 总需分拣的数量
     */
    private Integer totalQuantity;

    /**
     * 生成的分拣数量
     */
    private Integer generateQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 供应商
     */
    private Long supplierId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 履约单号
     */
    private String fulfillmentNo;

    /**
     * 初始数量
     */
    private Integer initQuantity;

    /**
     * 上架数量
     */
    private Integer shelveQuantity;

    /**
     * 获取剩余还需要分配的数量
     *
     * @return 返回剩余可分配的数量
     */
    public Integer getRemainingAllocationQuantity() {
        return totalQuantity - generateQuantity;
    }

    /**
     * 获取剩余还需要分配的上架数量
     */
    public Integer getRemainingAllocationShelveQuantity() {
        return initQuantity - generateQuantity - shelveQuantity;
    }
}
