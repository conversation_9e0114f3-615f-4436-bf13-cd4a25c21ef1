package net.summerfarm.wms.domain.allocation.domainobject;

import lombok.Data;

import java.math.BigDecimal;


/**
 * 聚合的调拨在途数据
 *
 * <AUTHOR>
 */
@Data
public class AggregateAllocationRoadData {

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 调拨在途数量
     */
    private Integer roadQuantity;

    /**
     * 调拨在途货值
     */
    private BigDecimal goodsValue;

}