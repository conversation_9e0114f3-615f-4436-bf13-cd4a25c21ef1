package net.summerfarm.wms.domain.processingtask.domainService;

import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductFinishAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductSubmitAggregate;

public interface ProcessingTaskProductDomainService {

    void productSubmit(ProcessingTaskProductSubmitAggregate submitAggregate);

    void productFinish(ProcessingTaskProductFinishAggregate finishAggregate);

    void updatePrintNumber(Long processingTaskProductSubmitRecordId, Long processingTaskProductRecordId, Integer canPrintNumber);
}
