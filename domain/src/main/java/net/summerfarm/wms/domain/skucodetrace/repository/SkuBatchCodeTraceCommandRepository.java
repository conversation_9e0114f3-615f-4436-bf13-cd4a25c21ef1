package net.summerfarm.wms.domain.skucodetrace.repository;

import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: 溯源码操作类<br/>
 * date: 2024/8/9 10:36<br/>
 *
 * <AUTHOR> />
 */
public interface SkuBatchCodeTraceCommandRepository {
    /**
     * 批量保存
     * @param createTraceEntitys 保存的实体
     * @return
     */
    List<SkuBatchCodeTraceEntity> batchSave(List<SkuBatchCodeTraceEntity> createTraceEntitys);

    /**
     * 批量更新
     * @param skuBatchCodeTraceEntities sku唯一溯源码
     */
    void batchUpdate(List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntities);

    void updateWeight(SkuBatchCodeTraceEntity id, BigDecimal weight, String weightUserName, LocalDateTime weightDateTime);
}
