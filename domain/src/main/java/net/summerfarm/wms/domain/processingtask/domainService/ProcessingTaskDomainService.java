package net.summerfarm.wms.domain.processingtask.domainService;

import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.xianmu.common.result.CommonResult;

import java.util.List;

public interface ProcessingTaskDomainService {

    /**
     * 创建加工任务
     *
     * @param processingTaskCreateAggregate
     * @return
     */
    Long createProcessingTask(ProcessingTaskCreateAggregate processingTaskCreateAggregate);

    /**
     * 结束任务
     *
     * @param processingTaskCode
     * @param processingTaskProductList
     * @param remark
     * @return
     */
    CommonResult<Long> finishTask(String processingTaskCode, List<ProcessingTaskProduct> processingTaskProductList, String remark);
}
