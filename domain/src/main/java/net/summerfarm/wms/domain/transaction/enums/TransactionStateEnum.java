package net.summerfarm.wms.domain.transaction.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.wms.common.exceptions.BizException;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum TransactionStateEnum {
    DOING(10, "执行中"),
    FINISH(20, "执行成功"),
    ERROR(30, "执行失败"),
    ;

    public static TransactionStateEnum convert(Integer param) {
        return Arrays.stream(TransactionStateEnum.values())
                .filter(o -> o.getCode().equals(param))
                .findFirst().orElseThrow(BizException::new);
    }

    Integer code;
    String desc;
}
