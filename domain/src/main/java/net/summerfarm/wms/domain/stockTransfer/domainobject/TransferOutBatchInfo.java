package net.summerfarm.wms.domain.stockTransfer.domainobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 转出信息详情
 *
 * @author: xdc
 * @date: 2024/2/21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TransferOutBatchInfo implements Serializable {

    private static final long serialVersionUID = -3574981803460165556L;

    /**
     * 转出批次
     */
    String transferOutBatch;

    /**
     * 转出库位
     */
    String transferOutCabinetNo;

    /**
     * 转出数量
     */
    Long transferOutNum;

    /**
     * 转出批次的保质期
     */
    Long shelfLife;

    /**
     * 外部回告转入数量
     */
    Integer externalTransferInNum;
}
