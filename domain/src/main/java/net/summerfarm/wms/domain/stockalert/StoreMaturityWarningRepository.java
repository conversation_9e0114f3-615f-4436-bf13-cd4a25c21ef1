package net.summerfarm.wms.domain.stockalert;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.stockalert.domainobject.StoreMaturityWarning;
import net.summerfarm.wms.domain.stockalert.domainobject.query.QueryStoreMaturityWarning;
import net.summerfarm.wms.domain.stockalert.param.query.StoreAlertCountByStatusQueryParam;
import net.summerfarm.wms.domain.stockalert.valueobject.StoreAlertCountByStatusValueObject;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023-11-01 12:01:21
 */
public interface StoreMaturityWarningRepository {

    PageInfo<StoreMaturityWarning> pageQueryStoreMaturityWarning(QueryStoreMaturityWarning query);

    List<StoreAlertCountByStatusValueObject> countByStatus(StoreAlertCountByStatusQueryParam queryParam);

}