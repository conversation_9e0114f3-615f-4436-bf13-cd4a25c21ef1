package net.summerfarm.wms.domain.processingtask.repository;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ProcessingTaskMaterialRepository {

    /**
     * 创建物料
     *
     * @param processingTaskMaterial
     * @return
     */
    Long create(ProcessingTaskMaterial processingTaskMaterial);

    Long batchCreate(List<ProcessingTaskMaterial> processingTaskMaterialList);

    /**
     * 查询id
     *
     * @param processingTaskMaterialId
     * @return
     */
    ProcessingTaskMaterial queryById(Long processingTaskMaterialId);

    /**
     * 增加废料损耗
     *
     * @param id
     * @param wasteLossWeight
     * @param currentUserName
     */
    void addWasteLossWeight(Long id, BigDecimal wasteLossWeight, String currentUserName);

    /**
     * 增加归还数量
     *
     * @param processingTaskMaterialId
     * @param materialSkuRestoreQuantity
     * @param materialSkuRestoreWeight
     * @param currentUserName
     */
    void addRestoreQuantity(Long processingTaskMaterialId, Integer materialSkuRestoreQuantity, BigDecimal materialSkuRestoreWeight, String currentUserName);

    /**
     * 查询列表
     *
     * @param processingTaskCode
     * @param processingTaskProductId
     */
    ProcessingTaskMaterial queryByProductIdAndMaterailSkuCode(String processingTaskCode, Long processingTaskProductId,
                                                    String processingMaterialSkuCode);


    PageInfo<ProcessingTaskMaterial> pageByProductId(String processingTaskCode, Long processingTaskProductId,
                                                     String processingMaterialSkuCode, Integer pageIndex, Integer pageSize);

    List<ProcessingTaskMaterial> listByProductId(String processingTaskCode, Long processingTaskProductId);

    Map<Long, List<ProcessingTaskMaterial>> mapByProductIdList(List<Long> processingTaskProductIdList);

    /**
     * 更新原料规格损耗
     * @param id
     * @param specLossWeight
     * @param currentUserName
     */
    void updateSpecLoseWeight(Long id, BigDecimal specLossWeight, String currentUserName);

    void addMaterial(Long materialId, Integer materialSkuReceiveQuantity, BigDecimal materialSkuReceiveWeight, String currentUserName);

}
