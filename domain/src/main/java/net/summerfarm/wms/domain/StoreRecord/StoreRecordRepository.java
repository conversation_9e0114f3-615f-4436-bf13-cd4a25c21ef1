package net.summerfarm.wms.domain.StoreRecord;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.domain.StoreRecord.domainobject.CheckStoreQuantityDiff;
import net.summerfarm.wms.domain.StoreRecord.domainobject.StoreRecord;
import net.summerfarm.wms.domain.StoreRecord.domainobject.query.QueryStoreRecordInProve;
import net.summerfarm.wms.domain.StoreRecord.dto.StoreRecordDifferentBatchDTO;
import net.summerfarm.wms.domain.StoreRecord.param.query.BatchFirstInStoreInfoQueryParam;
import net.summerfarm.wms.domain.areaStore.domainobject.StoreRecordCountBySkuCode;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 库存变动记录仓储层
 * <p>
 * 暂时用一下
 */
public interface StoreRecordRepository {

    List<String> listPurchaseNo(Long warehouseNo, String sku, Long produceAt, Long shelfLife);

    String selectOnePurchaseNo(Long warehouseNo, String sku, Long produceAt, Long shelfLife);

    String findLastPurchaseNoByShelfLife(Long warehouseNo, String sku, Long produceAt, Long shelfLife);

    String findLastPurchaseNoBySku(Long warehouseNo, String sku);

    /**
     * 记录
     *
     * @param sku
     * @param warehouseNo
     * @param batch
     * @param shelfLife
     * @return
     */
    StoreRecord selectOne(@Param("sku") String sku, @Param("warehouseNo") Long warehouseNo, @Param("batch") String batch,
                          @Param("shelfLife") LocalDate shelfLife);

    /**
     * 根据五要素查询最近一条
     *
     * @param sku
     * @param warehouseNo
     * @param batch
     * @param shelfLife
     * @param productionDate
     * @return
     */
    StoreRecord selectLastOne(String sku, Long warehouseNo, String batch, LocalDate shelfLife, LocalDate productionDate);

    /**
     * 当前批次库存
     * @param sku
     * @param warehouseNo
     * @param batch
     * @param shelfLife
     * @param productionDate
     * @return
     */
    Integer sumStoreQuantityGroupByQualityDate(String sku, Long warehouseNo, String batch, LocalDate shelfLife, LocalDate productionDate);

    /**
     * @param record
     * @return
     */
    int insert(StoreRecord record);

    /**
     * 查询差异批次库存记录
     *
     * @param areaNo
     * @param skuList
     * @return
     */
    List<StoreRecordDifferentBatchDTO> queryDifferentBatchStoreRecord(Integer areaNo, List<String> skuList);

    /**
     * 查询最新一条批次
     *
     * @param warehouseNo
     * @param sku
     * @param produceAt
     * @param shelfLife
     * @return
     */
    String queryLastBatchByWarehouseAndSkuAndDate(Integer warehouseNo, String sku, LocalDate produceAt, LocalDate shelfLife);

    /**
     * 查询仓-sku-date-batch最新一条记录
     *
     * @param areaNo
     * @param skuList
     * @return
     */
    List<StoreRecord> queryLastStoreRecordByWarehouseAndSku(Integer areaNo, List<String> skuList);

    List<StoreRecord> findListByUnique(Integer warehouseNo,
                                       List<String> skuCodeList,
                                       List<String> purchaseBatchList,
                                       List<Date> productionDateList,
                                       List<Date> qualityDateList);

    List<StoreRecord> findGt0ListByUnique(Integer warehouseNo,
                                       List<String> skuCodeList,
                                       List<String> purchaseBatchList,
                                       List<Date> productionDateList,
                                       List<Date> qualityDateList);

    StoreRecord selectLastQualityDate(Integer warehouseNo, String sku);

    Long updatePurchaseCost100(Integer warehouseNo, String skuCode, String purchaseNo, BigDecimal singleCost);

    List<StoreRecordCountBySkuCode> countBySkuCodeList(List<String> skuCodeList);

    StoreRecord selectLastRecord(Integer warehouseNo, String sku, String batch, LocalDate qualityDate, LocalDate productionDate);

    List<CheckStoreQuantityDiff> checkQuantityRecordDiff(Long warehouseNo, String sku);

    List<CheckStoreQuantityDiff> checkQuantityProduceDiff(Long warehouseNo, String sku);

    List<CheckStoreQuantityDiff> checkQuantityCostDiff(Long warehouseNo, String sku);

    /**
     * 获取sku列表
     *
     * @param warehouseNo 仓库编号
     * @return 返回sku列表
     */
    List<String> listSkuList(Integer warehouseNo);

    /**
     * 获取sku列表
     *
     * @param warehouseNo 仓库编号
     * @return 返回sku列表
     */
    int listSkuListCount(Integer warehouseNo);

    /**
     * 查询证明列表
     *
     * @param query 查询条件
     * @return 返回查询到的证明条件内容
     */
    List<StoreRecord> listInProve(QueryStoreRecordInProve query);

    /**
     * 查询证明列表
     *
     * @param query 查询条件
     * @return 返回查询到的证明条件内容
     */
    PageInfo<StoreRecord> listInProve(Integer pageIndex, Integer pageSize, QueryStoreRecordInProve query);

    /**
     * 获取批次库存数据
     *
     * <AUTHOR>
     * @date 2023/10/19 17:17
     */
    List<StoreRecord> selectListByBatch(@Param("warehouseNo") Integer warehouseNo, @Param("skuList") List<String> skuList, @Param("batch") String batch);

    /**
     * 分页查询库存记录中的sku
     *
     * @param warehouseNo 仓库编号
     * @param skuList     sku列表
     * @return 返回sku列表
     */
    PageInfo<String> pageSkuByAreaNoAndSku(Integer warehouseNo, List<String> skuList, Integer pageIndex, Integer pageSize);

    /**
     * 查询批次库存
     *
     * @param warehouseNo      仓库号
     * @param skus             sku列表
     * @param storeQuantityMin 库存最小值
     * @param productionDateMin 最早的生产日期
     * @return 记录
     */
    List<StoreRecord> listBatchBySkuAndWarehouseNoAndInventory(Long warehouseNo, List<String> skus, Integer storeQuantityMin, LocalDate productionDateMin);

    /**
     * 查询仓中sku最近的一个批次
     * @param warehouseNo 仓库号
     * @param sku sku列表
     * @return 记录
     */
    List<StoreRecord> selectRecordNear(Long warehouseNo, List<String> sku);


    /**
     * 查询仓库库存大于0的库存数据
     *
     * @param warehouseNo
     * @param sku
     * @return
     */
    List<StoreRecord> selectStoreQuantityGtZeroByAreaNoAndSku(Integer warehouseNo, String sku);

    /**
     * 查询批次首次入库信息
     *
     * @param queryParam
     * @return
     */
    List<StoreRecord> selectBatchFirstInStoreInfo(BatchFirstInStoreInfoQueryParam queryParam);

    /**
     * 根据sku和仓库查询最近一次采购成本价
     *
     * <AUTHOR>
     * @date 2024/2/23 14:03
     * @param sku sku
     * @param warehouseNo 库存仓
     * @return java.math.BigDecimal
     */
    BigDecimal queryLastedPurchaseCost(String sku, Integer warehouseNo);
}
