package net.summerfarm.wms.domain.stocktask.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description
 * @Date 2023/4/10 15:48
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockTaskNoticeOrderDetail {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 通知单ID
     */
    private Long noticeOrderId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 订单号
     */
    private String outOrderNo;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    /**
     * 仓库租户id
     */
    private Long warehouseTenantId;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 类型
     */
    private Integer outOrderType;

    /**
     * sku编码
     */
    private String sku;
    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否软删
     */
    private Byte isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;

    /**
     * 外部自有编码
     */
    private String customerSkuCode;
}
