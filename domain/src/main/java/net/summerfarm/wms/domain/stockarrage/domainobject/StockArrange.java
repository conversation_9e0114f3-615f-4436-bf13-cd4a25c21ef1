package net.summerfarm.wms.domain.stockarrage.domainobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/12/14  10:39
 */
@Data
@Deprecated
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StockArrange implements Serializable {

    private Integer id;

    private String purchaseNo;

    private Integer stockTaskId;

    private Integer state;

    private LocalDate arrangeTime;

    private String adminName;

    private Integer adminId;

    private String arrangeRemark;

    private Integer isClose;

    private Integer multiSupplier;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer supplierId;
}