<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.offline.storealert.ShelfLifeWarningDAO">
    <!-- 结果集映射 -->
    <resultMap id="shelfLifeWarningDayResultMap" type="net.summerfarm.wms.infrastructure.offline.storealert.dataobject.ShelfLifeWarningDO">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
		<result column="warehouse_provider" property="warehouseProvider" jdbcType="VARCHAR"/>
		<result column="pd_id" property="pdId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="saas_sku_id" property="saasSkuId" jdbcType="NUMERIC"/>
		<result column="category_id" property="categoryId" jdbcType="INTEGER"/>
		<result column="sku_tenant_id" property="skuTenantId" jdbcType="NUMERIC"/>
		<result column="warehouse_tenant_id" property="warehouseTenantId" jdbcType="NUMERIC"/>
		<result column="batch" property="batch" jdbcType="VARCHAR"/>
		<result column="production_date" property="productionDate" jdbcType="DATE"/>
		<result column="quality_date" property="qualityDate" jdbcType="DATE"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="shelf_life_days" property="shelfLifeDays" jdbcType="INTEGER"/>
		<result column="remaining_days" property="remainingDays" jdbcType="INTEGER"/>
		<result column="approaching_shelf_life_percentage" property="approachingShelfLifePercentage" jdbcType="DOUBLE"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="day_tag" property="dayTag" jdbcType="INTEGER"/>
		<result column="use_flag" property="useFlag" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="shelfLifeWarningDayColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.warehouse_no,
          t.warehouse_name,
          t.warehouse_provider,
          t.pd_id,
          t.sku,
          t.saas_sku_id,
          t.category_id,
          t.sku_tenant_id,
          t.warehouse_tenant_id,
          t.batch,
          t.production_date,
          t.quality_date,
          t.quantity,
          t.shelf_life_days,
          t.remaining_days,
          t.approaching_shelf_life_percentage,
          t.status,
          t.day_tag,
          t.use_flag
    </sql>

    <!-- 根据条件查询对象 -->
    <select id="selectShelfLifeWarning" parameterType="net.summerfarm.wms.domain.stockalert.domainobject.query.QueryShelfLifeWarning" resultMap="shelfLifeWarningDayResultMap" >
        SELECT <include refid="shelfLifeWarningDayColumns" />
        FROM shelf_life_warning_day t
        <where>
            <if test="categoryIdList != null and categoryIdList.size() > 0">
                and t.category_id in
                <foreach collection="categoryIdList" item="category" open="(" close=")" separator=",">
                    #{category}
                </foreach>
            </if>
            <if test="pdId != null">
                and t.pd_id = #{pdId}
            </if>
            <if test="saasSkuId != null">
                and t.saas_sku_id = #{saasSkuId}
            </if>
            <if test="warehouseNo != null">
                and t.warehouse_no = #{warehouseNo}
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="skuTenantId != null">
                and t.sku_tenant_id = #{skuTenantId}
            </if>
            <if test="dayTag != null">
                and t.day_tag = #{dayTag}
            </if>
            <if test="useFlag != null">
                and t.use_flag = #{useFlag}
            </if>
        </where>
        order by t.id asc
    </select>

    <!-- 根据状态查询记录条数 -->
    <select id="countByStatus" parameterType="net.summerfarm.wms.domain.stockalert.param.query.StoreAlertCountByStatusQueryParam"
            resultType="net.summerfarm.wms.domain.stockalert.valueobject.StoreAlertCountByStatusValueObject">
        SELECT t.status, COUNT(1) AS totalCount
        FROM shelf_life_warning_day t
        <where>
            t.sku_tenant_id = #{skuTenantId} AND t.day_tag = #{dayTag}
            <if test="statusList != null and statusList.size() > 0">
                AND t.status IN
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="useFlag != null">
                AND t.use_flag = #{useFlag}
            </if>
        </where>
        GROUP BY t.status
    </select>

</mapper>