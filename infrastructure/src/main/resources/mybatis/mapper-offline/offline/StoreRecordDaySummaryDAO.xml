<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.offline.storerecord.StoreRecordDaySummaryDAO">
    <resultMap id="baseResultMap"
               type="net.summerfarm.wms.infrastructure.offline.storerecord.daoobject.StoreRecordDaySummaryDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="warehouse_provider" property="warehouseProvider"/>
        <result column="pd_id" property="pdId"/>
        <result column="sku" property="sku"/>
        <result column="saas_sku_id" property="saasSkuId"/>
        <result column="category_id" property="categoryId"/>
        <result column="sku_tenant_id" property="skuTenantId"/>
        <result column="warehouse_tenant_id" property="warehouseTenantId"/>
        <result column="opening_quantity" property="openingQuantity"/>
        <result column="opening_amount" property="openingAmount"/>
        <result column="ending_quantity" property="endingQuantity"/>
        <result column="ending_amount" property="endingAmount"/>
        <result column="allocation_in_quantity" property="allocationInQuantity"/>
        <result column="allocation_in_amount" property="allocationInAmount"/>
        <result column="purchase_in_quantity" property="purchaseInQuantity"/>
        <result column="purchase_in_amount" property="purchaseInAmount"/>
        <result column="after_sale_in_quantity" property="afterSaleInQuantity"/>
        <result column="after_sale_in_amount" property="afterSaleInAmount"/>
        <result column="stock_taking_in_quantity" property="stockTakingInQuantity"/>
        <result column="stock_taking_in_amount" property="stockTakingInAmount"/>
        <result column="transfer_in_quantity" property="transferInQuantity"/>
        <result column="transfer_in_amount" property="transferInAmount"/>
        <result column="allocation_abnormal_in_quantity" property="allocationAbnormalInQuantity"/>
        <result column="allocation_abnormal_in_amount" property="allocationAbnormalInAmount"/>
        <result column="other_in_quantity" property="otherInQuantity"/>
        <result column="other_in_amount" property="otherInAmount"/>
        <result column="in_quantity" property="inQuantity"/>
        <result column="in_amount" property="inAmount"/>
        <result column="allocation_out_quantity" property="allocationOutQuantity"/>
        <result column="allocation_out_amount" property="allocationOutAmount"/>
        <result column="sale_out_quantity" property="saleOutQuantity"/>

        <result column="sale_out_amount" property="saleOutAmount"/>
        <result column="damage_out_quantity" property="damageOutQuantity"/>
        <result column="damage_out_amount" property="damageOutAmount"/>
        <result column="stock_taking_out_quantity" property="stockTakingOutQuantity"/>
        <result column="stock_taking_out_amount" property="stockTakingOutAmount"/>
        <result column="transfer_out_quantity" property="transferOutQuantity"/>
        <result column="transfer_out_amount" property="transferOutAmount"/>
        <result column="purchase_back_out_quantity" property="purchaseBackOutQuantity"/>
        <result column="purchase_back_out_amount" property="purchaseBackOutAmount"/>
        <result column="supply_again_out_quantity" property="supplyAgainOutQuantity"/>
        <result column="supply_again_out_amount" property="supplyAgainOutAmount"/>
        <result column="own_self_out_quantity" property="ownSelfOutQuantity"/>
        <result column="own_self_out_amount" property="ownSelfOutAmount"/>
        <result column="other_out_quantity" property="otherOutQuantity"/>
        <result column="other_out_amount" property="otherOutAmount"/>

        <result column="out_quantity" property="outQuantity"/>
        <result column="out_amount" property="outAmount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="day_tag" property="dayTag"/>
        <result column="init_in_quantity" property="initInQuantity"/>
        <result column="init_in_amount" property="initInAmount"/>
        <result column="lack_in_quantity" property="lackInQuantity"/>
        <result column="lack_in_amount" property="lackInAmount"/>
        <result column="intercept_in_quantity" property="interceptInQuantity"/>
        <result column="intercept_in_amount" property="interceptInAmount"/>
        <result column="out_more_in_quantity" property="outMoreInQuantity"/>
        <result column="out_more_in_amount" property="outMoreInAmount"/>
        <result column="allocation_damage_out_quantity" property="allocationDamageOutQuantity"/>
        <result column="allocation_damage_out_amount" property="allocationDamageOutAmount"/>
    </resultMap>
    <sql id="BASE_COLUMN">
        id,create_time,update_time,warehouse_no,warehouse_name,warehouse_provider,pd_id,
sku,saas_sku_id,category_id,sku_tenant_id,warehouse_tenant_id,opening_quantity,opening_amount,
ending_quantity,ending_amount,allocation_in_quantity,allocation_in_amount,purchase_in_quantity,
purchase_in_amount,after_sale_in_quantity,after_sale_in_amount,stock_taking_in_quantity,stock_taking_in_amount,
transfer_in_quantity,transfer_in_amount,allocation_abnormal_in_quantity,allocation_abnormal_in_amount,
other_in_quantity,other_in_amount,in_quantity,in_amount,allocation_out_quantity,allocation_out_amount,
sale_out_quantity,sale_out_amount,damage_out_quantity,damage_out_amount,stock_taking_out_quantity,
stock_taking_out_amount,transfer_out_quantity,transfer_out_amount,purchase_back_out_quantity,
purchase_back_out_amount,supply_again_out_quantity,supply_again_out_amount,own_self_out_quantity,
own_self_out_amount,other_out_quantity,other_out_amount,out_quantity,out_amount,day_tag,
init_in_quantity,init_in_amount,lack_in_quantity,lack_in_amount,intercept_in_quantity,intercept_in_amount,out_more_in_quantity,out_more_in_amount,allocation_damage_out_quantity,allocation_damage_out_amount
    </sql>
    <select id="query"
            resultMap="baseResultMap">
        select
        t.pd_id,
        t.sku,
        t.saas_sku_id,
        t.warehouse_no,
        t.warehouse_name,
        t.warehouse_tenant_id,
        t.warehouse_provider,
        MAX(
        CASE WHEN t.day_tag = #{startDate} THEN
        t.opening_quantity
        END
        ) opening_quantity,
        MAX(
        CASE WHEN t.day_tag = #{startDate} THEN
        t.opening_amount
        END
        ) opening_amount,
        MAX(
        CASE WHEN t.day_tag = #{endDate} THEN
        t.ending_amount
        END
        ) ending_amount,
        MAX(
        CASE WHEN t.day_tag = #{endDate} THEN
        t.ending_quantity
        END
        ) ending_quantity,
        sum(t.allocation_in_quantity) as allocation_in_quantity,
        sum(t.allocation_in_amount) as allocation_in_amount,
        sum(t.purchase_in_quantity) as purchase_in_quantity,
        sum(t.purchase_in_amount) as purchase_in_amount,
        sum(t.after_sale_in_quantity) as after_sale_in_quantity,
        sum(t.after_sale_in_amount) as after_sale_in_amount,
        sum(t.stock_taking_in_quantity) as stock_taking_in_quantity,
        sum(t.stock_taking_in_amount) as stock_taking_in_amount,
        sum(t.transfer_in_quantity) as transfer_in_quantity,
        sum(t.transfer_in_amount) as transfer_in_amount,
        sum(t.allocation_abnormal_in_quantity) as allocation_abnormal_in_quantity,
        sum(t.allocation_abnormal_in_amount) as allocation_abnormal_in_amount,
        sum(t.other_in_quantity) as other_in_quantity,
        sum(t.other_in_amount) as other_in_amount,
        sum(t.in_quantity) as in_quantity,
        sum(t.in_amount) as in_amount,
        sum(t.allocation_out_quantity) as allocation_out_quantity,
        sum(t.allocation_out_amount) as allocation_out_amount,
        sum(t.sale_out_quantity) as sale_out_quantity,
        sum(t.sale_out_amount) as sale_out_amount,
        sum(t.damage_out_quantity) as damage_out_quantity,
        sum(t.damage_out_amount) as damage_out_amount,
        sum(t.stock_taking_out_quantity) as stock_taking_out_quantity,
        sum(t.stock_taking_out_amount) as stock_taking_out_amount,
        sum(t.transfer_out_quantity) as transfer_out_quantity,
        sum(t.transfer_out_amount) as transfer_out_amount,
        sum(t.purchase_back_out_quantity) as purchase_back_out_quantity,
        sum(t.purchase_back_out_amount) as purchase_back_out_amount,
        sum(t.supply_again_out_quantity) as supply_again_out_quantity,
        sum(t.supply_again_out_amount) as supply_again_out_amount,
        sum(t.own_self_out_quantity) as own_self_out_quantity,
        sum(t.own_self_out_amount) as own_self_out_amount,
        sum(t.other_out_quantity) as other_out_quantity,
        sum(t.other_out_amount) as other_out_amount,
        sum(t.out_quantity) as out_quantity,
        sum(t.out_amount) as out_amount,
        sum(t.init_in_quantity) as init_in_quantity,
        sum(t.init_in_amount) as init_in_amount,
        sum(t.lack_in_quantity) as lack_in_quantity,
        sum(t.lack_in_amount) as lack_in_amount,
        sum(t.intercept_in_quantity) as intercept_in_quantity,
        sum(t.intercept_in_amount) as intercept_in_amount,
        sum(t.out_more_in_quantity) as out_more_in_quantity,
        sum(t.out_more_in_amount) as out_more_in_amount,
        sum(t.allocation_damage_out_quantity) as allocation_damage_out_quantity,
        sum(t.allocation_damage_out_amount) as allocation_damage_out_amount
        from store_record_day_summary t
        <where>
            <if test="categoryIdList != null and categoryIdList.size() > 0">
                and t.category_id in
                <foreach collection="categoryIdList" item="category" open="(" close=")" separator=",">
                    #{category}
                </foreach>
            </if>
            <if test="pdId != null">
                and t.pd_id = #{pdId}
            </if>
            <if test="saasSkuId != null">
                and t.saas_sku_id = #{saasSkuId}
            </if>
            <if test="warehouseNo != null">
                and t.warehouse_no = #{warehouseNo}
            </if>
            <if test="startDate != null">
                and t.day_tag >= #{startDate}
            </if>
            <if test="endDate != null">
                and t.day_tag <![CDATA[<]]>= #{endDate}
            </if>
            <if test="skuTenantId != null">
                and t.sku_tenant_id = #{skuTenantId}
            </if>
        </where>
        group by t.pd_id,t.sku,t.warehouse_no
        order by t.pd_id,t.sku,t.warehouse_no
    </select>
</mapper>
