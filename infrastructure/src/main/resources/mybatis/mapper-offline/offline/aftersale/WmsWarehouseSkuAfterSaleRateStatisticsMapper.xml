<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.offline.mapper.aftersale.WmsWarehouseSkuAfterSaleRateStatisticsMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsWarehouseSkuAfterSaleRateStatisticsResultMap" type="net.summerfarm.wms.infrastructure.offline.storealert.dataobject.WmsWarehouseSkuAfterSaleRateStatistics">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="after_sale_rate_type" property="afterSaleRateType" jdbcType="TINYINT"/>
		<result column="after_sale_rate" property="afterSaleRate" jdbcType="DOUBLE"/>
		<result column="change_time" property="changeTime" jdbcType="TIMESTAMP"/>
		<result column="pt" property="pt" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsWarehouseSkuAfterSaleRateStatisticsColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.warehouse_no,
          t.sku,
          t.after_sale_rate_type,
          t.after_sale_rate,
          t.change_time,
          t.pt
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="afterSaleRateType != null">
                AND t.after_sale_rate_type = #{afterSaleRateType}
            </if>
			<if test="afterSaleRate != null">
                AND t.after_sale_rate = #{afterSaleRate}
            </if>
			<if test="changeTime != null">
                AND t.change_time = #{changeTime}
            </if>
			<if test="pt != null and pt !=''">
                AND t.pt = #{pt}
            </if>
        </trim>
    </sql>

    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.aftersale.param.query.WmsWarehouseSkuAfterSaleRateStatisticsQueryParam" resultMap="wmsWarehouseSkuAfterSaleRateStatisticsResultMap" >
        SELECT <include refid="wmsWarehouseSkuAfterSaleRateStatisticsColumns" />
        FROM wms_warehouse_sku_after_sale_rate_statistics t
        <include refid="whereColumnBySelect"></include>
    </select>

</mapper>