<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.materialManage.WmsMaterialTaskDetailMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsMaterialTaskDetailResultMap" type="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTaskDetail">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="type" property="type" jdbcType="TINYINT"/>
		<result column="material_task_code" property="materialTaskCode" jdbcType="VARCHAR"/>
		<result column="material_sku" property="materialSku" jdbcType="VARCHAR"/>
		<result column="material_sku_saas_id" property="materialSkuSaasId" jdbcType="NUMERIC"/>
		<result column="material_sku_name" property="materialSkuName" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsMaterialTaskDetailColumns">
          t.id,
          t.tenant_id,
          t.warehouse_no,
          t.type,
          t.material_task_code,
          t.material_sku,
          t.material_sku_saas_id,
          t.material_sku_name,
          t.quantity,
          t.creator,
          t.create_time,
          t.updater,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="materialTaskCode != null and materialTaskCode !=''">
                AND t.material_task_code = #{materialTaskCode}
            </if>
			<if test="materialSku != null and materialSku !=''">
                AND t.material_sku = #{materialSku}
            </if>
			<if test="materialSkuSaasId != null">
                AND t.material_sku_saas_id = #{materialSkuSaasId}
            </if>
			<if test="materialSkuName != null and materialSkuName !=''">
                AND t.material_sku_name = #{materialSkuName}
            </if>
			<if test="quantity != null">
                AND t.quantity = #{quantity}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="materialTaskCode != null">
                    t.material_task_code = #{materialTaskCode},
                </if>
                <if test="materialSku != null">
                    t.material_sku = #{materialSku},
                </if>
                <if test="materialSkuSaasId != null">
                    t.material_sku_saas_id = #{materialSkuSaasId},
                </if>
                <if test="materialSkuName != null">
                    t.material_sku_name = #{materialSkuName},
                </if>
                <if test="quantity != null">
                    t.quantity = #{quantity},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsMaterialTaskDetailResultMap" >
        SELECT <include refid="wmsMaterialTaskDetailColumns" />
        FROM wms_material_task_detail t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskDetailQueryParam" resultType="net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskDetailEntity" >
        SELECT
            t.id id,
            t.tenant_id tenantId,
            t.warehouse_no warehouseNo,
            t.type type,
            t.material_task_code materialTaskCode,
            t.material_sku materialSku,
            t.material_sku_saas_id materialSkuSaasId,
            t.material_sku_name materialSkuName,
            t.quantity quantity,
            t.creator creator,
            t.create_time createTime,
            t.updater updater,
            t.update_time updateTime
        FROM wms_material_task_detail t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskDetailQueryParam" resultMap="wmsMaterialTaskDetailResultMap" >
        /*FORCE_MASTER*/
        SELECT <include refid="wmsMaterialTaskDetailColumns" />
        FROM wms_material_task_detail t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTaskDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_material_task_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="materialTaskCode != null">
				  material_task_code,
              </if>
              <if test="materialSku != null">
				  material_sku,
              </if>
              <if test="materialSkuSaasId != null">
				  material_sku_saas_id,
              </if>
              <if test="materialSkuName != null">
				  material_sku_name,
              </if>
              <if test="quantity != null">
				  quantity,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updater != null">
				  updater,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="type != null">
				#{type,jdbcType=TINYINT},
              </if>
              <if test="materialTaskCode != null">
				#{materialTaskCode,jdbcType=VARCHAR},
              </if>
              <if test="materialSku != null">
				#{materialSku,jdbcType=VARCHAR},
              </if>
              <if test="materialSkuSaasId != null">
				#{materialSkuSaasId,jdbcType=NUMERIC},
              </if>
              <if test="materialSkuName != null">
				#{materialSkuName,jdbcType=VARCHAR},
              </if>
              <if test="quantity != null">
				#{quantity,jdbcType=INTEGER},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updater != null">
				#{updater,jdbcType=VARCHAR},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

    <insert id="batchInsertSelective" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_material_task_detail (
            tenant_id, warehouse_no, type, material_task_code,
            material_sku, material_sku_saas_id, material_sku_name,
            quantity, creator, create_time, updater, update_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tenantId,jdbcType=NUMERIC},
                #{item.warehouseNo,jdbcType=INTEGER},
                #{item.type,jdbcType=TINYINT},
                #{item.materialTaskCode,jdbcType=VARCHAR},
                #{item.materialSku,jdbcType=VARCHAR},
                #{item.materialSkuSaasId,jdbcType=NUMERIC},
                #{item.materialSkuName,jdbcType=VARCHAR},
                #{item.quantity,jdbcType=INTEGER},
                #{item.creator,jdbcType=VARCHAR},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updater,jdbcType=VARCHAR},
                #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTaskDetail" >
        UPDATE wms_material_task_detail t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTaskDetail" >
        DELETE FROM wms_material_task_detail t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>


</mapper>