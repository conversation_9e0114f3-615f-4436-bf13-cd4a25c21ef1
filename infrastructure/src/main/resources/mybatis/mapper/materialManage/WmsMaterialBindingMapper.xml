<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.materialManage.WmsMaterialBindingMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsMaterialBindingResultMap" type="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBinding">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="sku_saas_id" property="skuSaasId" jdbcType="NUMERIC"/>
		<result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
		<result column="sku_ratio" property="skuRatio"/>
		<result column="status" property="status" jdbcType="TINYINT"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsMaterialBindingColumns">
          t.id,
          t.tenant_id,
          t.warehouse_no,
          t.sku,
          t.sku_saas_id,
          t.sku_name,
          t.sku_ratio,
          t.status,
          t.creator,
          t.create_time,
          t.updater,
          t.update_time,
          t.delete_flag
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
            <if test="skuList !=null and skuList.size > 0">
                and t.sku in
                <foreach collection="skuList" open="(" close=")" item="sku1" separator=",">
                    #{sku1}
                </foreach>
            </if>
			<if test="skuSaasId != null">
                AND t.sku_saas_id = #{skuSaasId}
            </if>
			<if test="skuName != null and skuName !=''">
                AND t.sku_name = #{skuName}
            </if>
			<if test="skuRatio != null">
                AND t.sku_ratio = #{skuRatio}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="deleteFlag != null">
                AND t.delete_flag = #{deleteFlag}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="skuSaasId != null">
                    t.sku_saas_id = #{skuSaasId},
                </if>
                <if test="skuName != null">
                    t.sku_name = #{skuName},
                </if>
                <if test="skuRatio != null">
                    t.sku_ratio = #{skuRatio},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="deleteFlag != null">
                    t.delete_flag = #{deleteFlag},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsMaterialBindingResultMap" >
        SELECT <include refid="wmsMaterialBindingColumns" />
        FROM wms_material_binding t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingQueryParam"  resultType="net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity" >
        SELECT
        <if test="materialSku != null or materialSkuSaasId != null or materialSkuName!= null">
            distinct
        </if>
            t.id id,
            t.tenant_id tenantId,
            t.warehouse_no warehouseNo,
            t.sku sku,
            t.sku_saas_id skuSaasId,
            t.sku_name skuName,
            t.sku_ratio skuRatio,
            t.status status,
            t.creator creator,
            t.create_time createTime,
            t.updater updater,
            t.update_time updateTime,
            t.delete_flag deleteFlag
        FROM wms_material_binding t
        <if test="materialSku != null or materialSkuSaasId != null or materialSkuName!= null">
            inner join wms_material_binding_detail wmbd on t.id = wmbd.material_binging_id
            <if test="materialSku!= null">
                and wmbd.material_sku = #{materialSku}
            </if>
            <if test="materialSkuSaasId!= null">
                and wmbd.material_sku_saas_id = #{materialSkuSaasId}
            </if>
            <if test="materialSkuName!= null">
                and wmbd.material_sku_name = #{materialSkuName}
            </if>
        </if>
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingQueryParam" resultMap="wmsMaterialBindingResultMap" >
        SELECT <include refid="wmsMaterialBindingColumns" />
        FROM wms_material_binding t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBinding" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_material_binding
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="skuSaasId != null">
				  sku_saas_id,
              </if>
              <if test="skuName != null">
				  sku_name,
              </if>
              <if test="skuRatio != null">
				  sku_ratio,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updater != null">
				  updater,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="deleteFlag != null">
				  delete_flag,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="skuSaasId != null">
				#{skuSaasId,jdbcType=NUMERIC},
              </if>
              <if test="skuName != null">
				#{skuName,jdbcType=VARCHAR},
              </if>
              <if test="skuRatio != null">
				#{skuRatio},
              </if>
              <if test="status != null">
				#{status,jdbcType=TINYINT},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updater != null">
				#{updater,jdbcType=VARCHAR},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="deleteFlag != null">
				#{deleteFlag,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBinding" >
        UPDATE wms_material_binding t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>


	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBinding" >
        DELETE FROM wms_material_binding t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>


</mapper>