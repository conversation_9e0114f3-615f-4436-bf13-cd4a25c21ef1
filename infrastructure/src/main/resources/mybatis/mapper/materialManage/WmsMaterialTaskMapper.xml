<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.materialManage.WmsMaterialTaskMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsMaterialTaskResultMap" type="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTask">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="type" property="type" jdbcType="TINYINT"/>
		<result column="material_task_code" property="materialTaskCode" jdbcType="VARCHAR"/>
		<result column="destination" property="destination" jdbcType="VARCHAR"/>
		<result column="source_type" property="sourceType" jdbcType="TINYINT"/>
		<result column="source_id" property="sourceId" jdbcType="NUMERIC"/>
		<result column="source_code" property="sourceCode" jdbcType="VARCHAR"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsMaterialTaskColumns">
          t.id,
          t.tenant_id,
          t.warehouse_no,
          t.type,
          t.material_task_code,
          t.destination,
          t.source_type,
          t.source_id,
          t.source_code,
          t.creator,
          t.create_time,
          t.updater,
          t.update_time,
          t.remark
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="materialTaskCode != null and materialTaskCode !=''">
                AND t.material_task_code = #{materialTaskCode}
            </if>
			<if test="destination != null and destination !=''">
                AND t.destination = #{destination}
            </if>
			<if test="sourceType != null">
                AND t.source_type = #{sourceType}
            </if>
			<if test="sourceId != null">
                AND t.source_id = #{sourceId}
            </if>
			<if test="sourceCode != null and sourceCode !=''">
                AND t.source_code = #{sourceCode}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
            <if test="remark != null">
                and t.remark = #{remark}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="materialTaskCode != null">
                    t.material_task_code = #{materialTaskCode},
                </if>
                <if test="destination != null">
                    t.destination = #{destination},
                </if>
                <if test="sourceType != null">
                    t.source_type = #{sourceType},
                </if>
                <if test="sourceId != null">
                    t.source_id = #{sourceId},
                </if>
                <if test="sourceCode != null">
                    t.source_code = #{sourceCode},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsMaterialTaskResultMap" >
        /*FORCE_MASTER*/
        SELECT <include refid="wmsMaterialTaskColumns" />
        FROM wms_material_task t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskQueryParam"  resultType="net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity" >
        SELECT
            t.id id,
            t.tenant_id tenantId,
            t.warehouse_no warehouseNo,
            t.type type,
            t.material_task_code materialTaskCode,
            t.destination destination,
            t.source_type sourceType,
            t.source_id sourceId,
            t.source_code sourceCode,
            t.creator creator,
            t.create_time createTime,
            t.updater updater,
            t.update_time updateTime,
            t.remark remark
        FROM wms_material_task t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskQueryParam" resultMap="wmsMaterialTaskResultMap" >
        SELECT <include refid="wmsMaterialTaskColumns" />
        FROM wms_material_task t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTask" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_material_task
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="materialTaskCode != null">
				  material_task_code,
              </if>
              <if test="destination != null">
				  destination,
              </if>
              <if test="sourceType != null">
				  source_type,
              </if>
              <if test="sourceId != null">
				  source_id,
              </if>
              <if test="sourceCode != null">
				  source_code,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updater != null">
				  updater,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="remark!= null">
                  remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="type != null">
				#{type,jdbcType=TINYINT},
              </if>
              <if test="materialTaskCode != null">
				#{materialTaskCode,jdbcType=VARCHAR},
              </if>
              <if test="destination != null">
				#{destination,jdbcType=VARCHAR},
              </if>
              <if test="sourceType != null">
				#{sourceType,jdbcType=TINYINT},
              </if>
              <if test="sourceId != null">
				#{sourceId,jdbcType=NUMERIC},
              </if>
              <if test="sourceCode != null">
				#{sourceCode,jdbcType=VARCHAR},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updater != null">
				#{updater,jdbcType=VARCHAR},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="remark!= null">
                  #{remark,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTask" >
        UPDATE wms_material_task t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialTask" >
        DELETE FROM wms_material_task t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>

</mapper>