<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.materialManage.WmsMaterialBindingDetailMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsMaterialBindingDetailResultMap" type="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBindingDetail">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="sku_saas_id" property="skuSaasId" jdbcType="NUMERIC"/>
		<result column="material_sku" property="materialSku" jdbcType="VARCHAR"/>
		<result column="material_sku_saas_id" property="materialSkuSaasId" jdbcType="NUMERIC"/>
		<!-- 新增绑定ID字段 -->
		<result column="material_binging_id" property="materialBingingId" jdbcType="NUMERIC"/>
		<result column="material_sku_name" property="materialSkuName" jdbcType="VARCHAR"/>
		<result column="material_sku_ratio" property="materialSkuRatio"/>
		<result column="reuse" property="reuse" jdbcType="VARCHAR"/>
		<result column="auto_outbound" property="autoOutbound" jdbcType="VARCHAR"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>

    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsMaterialBindingDetailColumns">
          t.id,
          t.tenant_id,
          t.warehouse_no,
          t.sku,
          t.sku_saas_id,
          t.material_sku,
          t.material_sku_saas_id,
          t.material_binging_id,
          t.material_sku_name,
          t.material_sku_ratio,
          t.reuse,
          t.auto_outbound,
          t.creator,
          t.create_time,
          t.updater,
          t.update_time,
          t.delete_flag
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="skuSaasId != null">
                AND t.sku_saas_id = #{skuSaasId}
            </if>
			<if test="materialSku != null and materialSku !=''">
                AND t.material_sku = #{materialSku}
            </if>
			<if test="materialSkuSaasId != null">
                AND t.material_sku_saas_id = #{materialSkuSaasId}
            </if>
			<if test="materialSkuName != null and materialSkuName !=''">
                AND t.material_sku_name = #{materialSkuName}
            </if>
			<if test="materialSkuRatio != null">
                AND t.material_sku_ratio = #{materialSkuRatio}
            </if>
			<if test="reuse != null">
                AND t.reuse = #{reuse}
            </if>
			<if test="autoOutbound != null">
                AND t.auto_outbound = #{autoOutbound}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="deleteFlag != null">
                AND t.delete_flag = #{deleteFlag}
            </if>
            <if test="materialBingingId != null">
                AND t.material_binging_id = #{materialBingingId}
            </if>
            <if test="materialBingingIdList != null and materialBingingIdList.size() > 0">
                AND t.material_binging_id in
                <foreach collection="materialBingingIdList" item="materialBingingId1" open="(" close=")" separator=",">
                    #{materialBingingId1}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="skuSaasId != null">
                    t.sku_saas_id = #{skuSaasId},
                </if>
                <if test="materialSku != null">
                    t.material_sku = #{materialSku},
                </if>
                <if test="materialSkuSaasId != null">
                    t.material_sku_saas_id = #{materialSkuSaasId},
                </if>
                <if test="materialSkuName != null">
                    t.material_sku_name = #{materialSkuName},
                </if>
                <if test="materialSkuRatio != null">
                    t.material_sku_ratio = #{materialSkuRatio},
                </if>
                <if test="reuse != null">
                    t.reuse = #{reuse},
                </if>
                <if test="autoOutbound != null">
                    t.auto_outbound = #{autoOutbound},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="deleteFlag != null">
                    t.delete_flag = #{deleteFlag},
                </if>
                <if test="materialBingingId != null">
                    t.material_binging_id = #{materialBingingId},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsMaterialBindingDetailResultMap" >
        SELECT <include refid="wmsMaterialBindingDetailColumns" />
        FROM wms_material_binding_detail t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingDetailQueryParam"  resultType="net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity" >
        SELECT
            t.id id,
            t.tenant_id tenantId,
            t.warehouse_no warehouseNo,
            t.sku sku,
            t.sku_saas_id skuSaasId,
            t.material_sku materialSku,
            t.material_sku_saas_id materialSkuSaasId,
            t.material_sku_name materialSkuName,
            t.material_sku_ratio materialSkuRatio,
            t.reuse reuse,
            t.auto_outbound autoOutbound,
            t.creator creator,
            t.create_time createTime,
            t.updater updater,
            t.update_time updateTime,
            t.delete_flag deleteFlag,
            t.material_binging_id materialBingingId
        FROM wms_material_binding_detail t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingDetailQueryParam" resultMap="wmsMaterialBindingDetailResultMap" >
        SELECT <include refid="wmsMaterialBindingDetailColumns" />
        FROM wms_material_binding_detail t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBindingDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_material_binding_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="skuSaasId != null">
				  sku_saas_id,
              </if>
              <if test="materialSku != null">
				  material_sku,
              </if>
              <if test="materialSkuSaasId != null">
				  material_sku_saas_id,
              </if>
              <if test="materialSkuName != null">
				  material_sku_name,
              </if>
              <if test="materialSkuRatio != null">
				  material_sku_ratio,
              </if>
              <if test="reuse != null">
				  reuse,
              </if>
              <if test="autoOutbound != null">
				  auto_outbound,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updater != null">
				  updater,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="deleteFlag != null">
				  delete_flag,
              </if>
              <if test="materialBingingId != null">
                  material_binging_id,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="skuSaasId != null">
				#{skuSaasId,jdbcType=NUMERIC},
              </if>
              <if test="materialSku != null">
				#{materialSku,jdbcType=VARCHAR},
              </if>
              <if test="materialSkuSaasId != null">
				#{materialSkuSaasId,jdbcType=NUMERIC},
              </if>
              <if test="materialSkuName != null">
				#{materialSkuName,jdbcType=VARCHAR},
              </if>
              <if test="materialSkuRatio != null">
				#{materialSkuRatio},
              </if>
              <if test="reuse != null">
				#{reuse},
              </if>
              <if test="autoOutbound != null">
				#{autoOutbound},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updater != null">
				#{updater,jdbcType=VARCHAR},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="deleteFlag != null">
				#{deleteFlag,jdbcType=TINYINT},
              </if>
              <if test="materialBingingId != null">
                #{materialBingingId,jdbcType=NUMERIC},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBindingDetail" >
        UPDATE wms_material_binding_detail t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.materialManage.dataobject.WmsMaterialBindingDetail" >
        DELETE FROM wms_material_binding_detail t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>


    <!-- 新增批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO wms_material_binding_detail (
            tenant_id, warehouse_no, sku, sku_saas_id,
            material_sku, material_sku_saas_id, material_binging_id,
            material_sku_name, material_sku_ratio, reuse, auto_outbound,
            creator, create_time, updater, update_time, delete_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.tenantId,jdbcType=NUMERIC},
            #{item.warehouseNo,jdbcType=INTEGER},
            #{item.sku,jdbcType=VARCHAR},
            #{item.skuSaasId,jdbcType=NUMERIC},
            #{item.materialSku,jdbcType=VARCHAR},
            #{item.materialSkuSaasId,jdbcType=NUMERIC},
            #{item.materialBingingId,jdbcType=NUMERIC},
            #{item.materialSkuName,jdbcType=VARCHAR},
            #{item.materialSkuRatio},
            #{item.reuse},
            #{item.autoOutbound},
            #{item.creator,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updater,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=TINYINT}
        )
        </foreach>
    </insert>


    <update id="updateDeletedByBindingId">
        UPDATE wms_material_binding_detail t
        set update_time = now(),
            <if test="updater!= null">
                updater = #{updater},
            </if>
            delete_flag = 1
        where t.material_binging_id = #{bindingId}
    </update>

</mapper>