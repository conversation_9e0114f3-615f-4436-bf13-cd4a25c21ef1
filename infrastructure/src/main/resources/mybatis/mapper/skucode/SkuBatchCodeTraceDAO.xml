<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.skucode.SkuBatchCodeTraceDAO">


    <update id="updateWeight">
        update sku_batch_code_trace
        set weight = #{weight},
            state = #{state},
            weight_person = #{weightPerson},
            weight_time = #{weightTime}
        where id = #{id}
    </update>
    
     <select id="findDeliveryTimeRecentlyByMerchantId" resultType="net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity">
         select merchant_id merchantId,max(delivery_time) deliveryTime
         from sku_batch_code_trace
         where merchant_id in
         <foreach collection="merchantIds" item="merchantId" open="(" close=")" separator=",">
             #{merchantId}
         </foreach>
         and delivery_time <![CDATA[<]]> #{deliveryTime} group by merchant_id;
     </select>

    <select id="findDeliveryTimeFirstByMerchantId" resultType="net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity">
        select merchant_id merchantId,min(delivery_time) deliveryTime
        from sku_batch_code_trace
        where merchant_id in
        <foreach collection="merchantIds" item="merchantId" open="(" close=")" separator=",">
            #{merchantId}
        </foreach>
        group by merchant_id;
    </select>
    
</mapper>
