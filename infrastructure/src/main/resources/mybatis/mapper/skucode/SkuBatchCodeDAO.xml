<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.skucode.SkuBatchCodeDAO">

    <insert id="saveSkuBatchCode"
            parameterType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO" keyProperty="id"
            useGeneratedKeys="true">
        insert into sku_batch_code (create_time, update_time, sku, purchase_no, production_date, quality_date,
                                    sku_batch_only_code, biz_id, biz_type)
            value (now(),now(),#{sku}, #{purchaseNo}, #{productionDate}, #{qualityDate}, #{skuBatchOnlyCode},#{bizId},#{bizType})
    </insert>

    <insert id="saveSkuBatchCodeForSync"
            parameterType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO" keyProperty="id"
            useGeneratedKeys="true">
        insert into sku_batch_code (create_time, update_time, sku, purchase_no, production_date, quality_date,
                                    sku_batch_only_code, biz_id, biz_type, print_number)
            value (now(),now(),#{sku}, #{purchaseNo}, #{productionDate}, #{qualityDate}, #{skuBatchOnlyCode},#{bizId},#{bizType},
            #{printNumber})
    </insert>


    <update id="updateSkuBatchCode"
            parameterType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        update sku_batch_code
        <set>
            update_time = now()
            <if test="printNumber !=  null">
                ,print_number = #{printNumber}
            </if>
            <if test="skuBatchOnlyCode !=  null">
                ,sku_batch_only_code = #{skuBatchOnlyCode}
            </if>
            <if test="bizId !=  null">
                ,biz_id = #{bizId}
            </if>
            <if test="bizType !=  null">
                ,biz_type = #{bizType}
            </if>
            <if test="purchaseNo != null">
                ,purchase_no = #{purchaseNo}
            </if>
            <if test="productionDate != null">
                ,production_date = #{productionDate}
            </if>
            <if test="qualityDate != null">
                ,quality_date = #{qualityDate}
            </if>
        </set>
        where id =#{id}
    </update>

    <update id="updateSkuBatchCodeForSync"
            parameterType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        update sku_batch_code
        <set>
            update_time = now()
            <if test="printNumber !=  null">
                ,print_number = #{printNumber}
            </if>
            <if test="skuBatchOnlyCode !=  null">
                ,sku_batch_only_code = #{skuBatchOnlyCode}
            </if>
            <if test="bizId !=  null">
                ,biz_id = #{bizId}
            </if>
            <if test="bizType !=  null">
                ,biz_type = #{bizType}
            </if>
        </set>
        where biz_id = #{bizId} and biz_type = #{bizType} and sku = #{sku} and purchase_no = #{purchaseNo}
    </update>

    <select id="querySkuBatchCode"
            parameterType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select id,
        sku,
        purchase_no purchaseNo,
        production_date productionDate,
        quality_date qualityDate,
        sku_batch_only_code skuBatchOnlyCode,
        print_number printNumber,
        biz_id bizId,
        biz_type bizType
        from sku_batch_code
        <where>
            <if test="sku != null">
                sku = #{sku} and
                purchase_no = #{purchaseNo} and
                production_date = #{productionDate}
            </if>
            <if test="skuBatchOnlyCode != null">
                sku_batch_only_code = #{skuBatchOnlyCode}
            </if>
        </where>
        limit 1
    </select>

    <select id="querySkuBatchCodeForSync"
            parameterType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select id,
        sku,
        purchase_no purchaseNo,
        production_date productionDate,
        quality_date qualityDate,
        sku_batch_only_code skuBatchOnlyCode,
        print_number printNumber,
        biz_id bizId,
        biz_type bizType,
        create_time createTime
        from sku_batch_code
        <where>
            <if test="sku != null">
                sku = #{sku} and
                purchase_no = #{purchaseNo} and
                production_date = #{productionDate} and biz_id is null
            </if>
            <if test="skuBatchOnlyCode != null">
                sku_batch_only_code = #{skuBatchOnlyCode}
            </if>
        </where>
        limit 1
    </select>

    <select id="querySkuBatchCodeByCd"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        /*FORCE_MASTER*/
        select id,
        sku,
        purchase_no purchaseNo,
        production_date productionDate,
        quality_date qualityDate,
        sku_batch_only_code skuBatchOnlyCode,
        print_number printNumber,
        biz_id bizId,
        biz_type bizType,
        create_time createTime
        from sku_batch_code
        <where>
            <if test="bizId != null">
                biz_id = #{bizId}
            </if>
            <if test="bizType != null">
                and biz_Type = #{bizType}
            </if>
            <if test="sku != null">
                and sku = #{sku}
            </if>
            <if test="purchaseNo != null">
                and purchase_no = #{purchaseNo}
            </if>
            <if test="productionDate != null">
                and production_date = #{productionDate}
            </if>
            <if test="skuBatchOnlyCode != null">
                and sku_batch_only_code = #{skuBatchOnlyCode}
            </if>
        </where>
    </select>

    <select id="listBySkuAndBatches"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select id,
        purchase_no purchaseNo,
        print_number printNumber,
        biz_id bizId,
        biz_type bizType,
        batch_type batchType
        from sku_batch_code
        where
        sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
        and
        purchase_no in
        <foreach collection="batches" item="batch" open="(" close=")" separator=",">
            #{batch}
        </foreach>
    </select>

    <select id="listBySkus"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select max(id),
        sku sku,
        sku_batch_only_code skuBatchOnlyCode
        from sku_batch_code
        where
        sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
        group by sku,sku_batch_only_code
    </select>

    <select id="queryBatchSkuBatchCode"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select id,
        sku,
        purchase_no purchaseNo,
        production_date productionDate,
        quality_date qualityDate,
        sku_batch_only_code skuBatchOnlyCode,
        biz_id bizId,
        biz_type bizType
        from sku_batch_code
        where sku_batch_only_code in
        <foreach collection="batchCode" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="queryProduceBatchByWnoAndSkuCodeAndDate"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select id,
               sku,
               purchase_no         purchaseNo,
               production_date     productionDate,
               quality_date        qualityDate,
               sku_batch_only_code skuBatchOnlyCode,
               biz_id              bizId,
               biz_type            bizType
        from sku_batch_code
        where sku = #{sku}
          and purchase_no = #{purchaseNo}
          and production_date = #{productionDate}
          and quality_date = #{qualityDate}
    </select>

    <select id="batchQuerySkuBatchCodeForSync"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select id,
        sku,
        purchase_no purchaseNo,
        production_date productionDate,
        quality_date qualityDate,
        sku_batch_only_code skuBatchOnlyCode
        from sku_batch_code
        where sku_batch_only_code in
        <foreach collection="batchCodeList" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="queryByBizIdAndSkuList"
            resultType="net.summerfarm.wms.infrastructure.dao.skucode.dataobject.SkuBatchCodeDO">
        select id,
        sku,
        purchase_no purchaseNo,
        production_date productionDate,
        quality_date qualityDate,
        sku_batch_only_code skuBatchOnlyCode,
        print_number printNumber
        from sku_batch_code
        where biz_id = #{bizId} and biz_type = #{bizType}
        and sku in
        <foreach collection="skuList" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
    </select>

    <update id="updateSkuBatchType">
        update sku_batch_code
        <set>
            update_time = now()
            <if test="batchType !=  null">
                ,batch_type = #{batchType}
            </if>
        </set>
        where purchase_no in
        <foreach collection="batchs" item="batch" open="(" close=")" separator=",">
            #{batch}
        </foreach>
    </update>

    <update id="addPrintNumber">
        update sku_batch_code
        <set>
            update_time = now(),
            print_number = print_number + #{canPrintNumber}
        </set>
        where id = #{id}
    </update>
</mapper>