<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingConfigDao">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingConfigDO" id="WmsProcessingConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="materialSkuCode" column="material_sku_code" jdbcType="VARCHAR"/>
        <result property="materialSkuName" column="material_sku_name" jdbcType="VARCHAR"/>
        <result property="materialSkuWeight" column="material_sku_weight" jdbcType="NUMERIC"/>
        <result property="materialSkuUnit" column="material_sku_unit" jdbcType="VARCHAR"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="productSkuName" column="product_sku_name" jdbcType="VARCHAR"/>
        <result property="productSkuWeight" column="product_sku_weight" jdbcType="NUMERIC"/>
        <result property="productSkuUnit" column="product_sku_unit" jdbcType="VARCHAR"/>
        <result property="invalid" column="invalid" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="materialSkuUnitDesc" column="material_sku_unit_desc" jdbcType="VARCHAR"/>
        <result property="productSkuUnitDesc" column="product_sku_unit_desc" jdbcType="VARCHAR"/>
        <result property="processingConfigCode" column="processing_config_code" jdbcType="VARCHAR"/>
        <result property="materialModel" column="material_model" jdbcType="INTEGER"/>
        <result property="productSkuRatioNum" column="product_sku_ratio_num" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="WmsProcessingConfigMap">
        select
          id, warehouse_no, type, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, invalid, creator, create_time, updater, update_time, delete_flag, material_sku_unit_desc, product_sku_unit_desc, processing_config_code
            , material_model, product_sku_ratio_num
        from wms_processing_config
        where id = #{id}
    </select>

    <!--通过编码查询单个-->
    <select id="queryByProcessingConfigCode" resultMap="WmsProcessingConfigMap">
        select
          id, warehouse_no, type, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, invalid, creator, create_time, updater, update_time, delete_flag, material_sku_unit_desc, product_sku_unit_desc, processing_config_code
            , material_model, product_sku_ratio_num
        from wms_processing_config
        where processing_config_code = #{processingConfigCode}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultType="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingConfigDO">
        select
        <if test="materialSkuCode != null or materialSkuName != null">
            distinct
        </if>
            t.id id,
            t.warehouse_no warehouseNo,
            t.type type,
            t.material_sku_code materialSkuCode,
            t.material_sku_name materialSkuName,
            t.material_sku_weight materialSkuWeight,
            t.material_sku_unit materialSkuUnit,
            t.product_sku_code productSkuCode,
            t.product_sku_name productSkuName,
            t.product_sku_weight productSkuWeight,
            t.product_sku_unit productSkuUnit,
            t.invalid invalid,
            t.creator creator,
            t.create_time createTime,
            t.updater updater,
            t.update_time updateTime,
            t.delete_flag deleteFlag,
            t.material_sku_unit_desc materialSkuUnitDesc,
            t.product_sku_unit_desc productSkuUnitDesc,
            t.processing_config_code processingConfigCode,
            t.material_model materialModel,
            t.product_sku_ratio_num productSkuRatioNum
        from wms_processing_config t
        <if test="materialSkuCode != null or materialSkuName != null">
            inner join wms_processing_material_config mconfig on mconfig.processing_config_id = t.id
                                                                     and mconfig.delete_flag = 0
            <if test="materialSkuCode != null">
                and mconfig.material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null">
                and mconfig.material_sku_name = #{materialSkuName}
            </if>
        </if>
        <include refid="whereColumnBySelect"></include>
        order by t.id desc
    </select>

    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
            <if test="type != null">
                AND t.type = #{type}
            </if>
            <if test="typeList != null">
                and t.type in
                <foreach collection="typeList" item="type1" open="(" close=")" separator=",">
                    #{type1}
                </foreach>
            </if>
            <if test="productSkuCode != null and productSkuCode !=''">
                AND t.product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuCodeList != null">
                and t.product_sku_code in
                <foreach collection="productSkuCodeList" item="productSkuCode1" open="(" close=")" separator=",">
                    #{productSkuCode1}
                </foreach>
            </if>
            <if test="productSkuName != null and productSkuName !=''">
                AND t.product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuWeight != null">
                AND t.product_sku_weight = #{productSkuWeight}
            </if>
            <if test="productSkuUnit != null and productSkuUnit !=''">
                AND t.product_sku_unit = #{productSkuUnit}
            </if>
            <if test="invalid != null">
                AND t.invalid = #{invalid}
            </if>
            <if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
            <if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                AND t.delete_flag = #{deleteFlag}
            </if>
            <if test="productSkuUnitDesc != null and productSkuUnitDesc !=''">
                AND t.product_sku_unit_desc = #{productSkuUnitDesc}
            </if>
            <if test="processingConfigCode != null and processingConfigCode !=''">
                AND t.processing_config_code = #{processingConfigCode}
            </if>
            <if test="materialModel != null">
                AND t.material_model = #{materialModel}
            </if>
            <if test="productSkuRatioNum!= null">
                AND t.product_sku_ratio_num = #{productSkuRatioNum}
            </if>
        </trim>
    </sql>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_processing_config
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="materialSkuWeight != null">
                and material_sku_weight = #{materialSkuWeight}
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                and material_sku_unit = #{materialSkuUnit}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuWeight != null">
                and product_sku_weight = #{productSkuWeight}
            </if>
            <if test="productSkuUnit != null and productSkuUnit != ''">
                and product_sku_unit = #{productSkuUnit}
            </if>
            <if test="invalid != null">
                and invalid = #{invalid}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="deleteFlag == null">
                and delete_flag = 0
            </if>
            <if test="materialSkuUnitDesc != null and materialSkuUnitDesc != ''">
                and material_sku_unit_desc = #{materialSkuUnitDesc}
            </if>
            <if test="productSkuUnitDesc != null and productSkuUnitDesc != ''">
                and product_sku_unit_desc = #{productSkuUnitDesc}
            </if>
            <if test="processingConfigCode != null and processingConfigCode != ''">
                and processing_config_code = #{processingConfigCode}
            </if>
            <if test="materialModel != null">
                and material_model = #{materialModel}
            </if>
            <if test="productSkuRatioNum!= null">
                and product_sku_ratio_num = #{productSkuRatioNum}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_config(warehouse_no, type, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, invalid, creator, create_time, updater, update_time, delete_flag, material_sku_unit_desc, product_sku_unit_desc, processing_config_code
                                         , material_model, product_sku_ratio_num)
        values (#{warehouseNo}, #{type}, #{materialSkuCode}, #{materialSkuName}, #{materialSkuWeight}, #{materialSkuUnit}, #{productSkuCode}, #{productSkuName}, #{productSkuWeight}, #{productSkuUnit}, #{invalid}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag}, #{materialSkuUnitDesc}, #{productSkuUnitDesc}, #{processingConfigCode}
               , #{materialModel}, #{productSkuRatioNum})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_config(warehouse_no, type, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, invalid, creator, create_time, updater, update_time, delete_flag, material_sku_unit_desc, product_sku_unit_desc, processing_config_code
            , material_model, product_sku_ratio_num)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.warehouseNo}, #{entity.type}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuWeight}, #{entity.productSkuUnit}, #{entity.invalid}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.materialSkuUnitDesc}, #{entity.productSkuUnitDesc}, #{entity.processingConfigCode}
            , #{entity.materialModel}, #{entity.productSkuRatioNum})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_config(warehouse_no, type, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, invalid, creator, create_time, updater, update_time, delete_flag, material_sku_unit_desc, product_sku_unit_desc, processing_config_code
            , material_model, product_sku_ratio_num)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.warehouseNo}, #{entity.type}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuWeight}, #{entity.productSkuUnit}, #{entity.invalid}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.materialSkuUnitDesc}, #{entity.productSkuUnitDesc}, #{entity.processingConfigCode}
                , #{entity.materialModel}, #{entity.productSkuRatioNum})
        </foreach>
        on duplicate key update
        warehouse_no = values(warehouse_no),
        type = values(type),
        material_sku_code = values(material_sku_code),
        material_sku_name = values(material_sku_name),
        material_sku_weight = values(material_sku_weight),
        material_sku_unit = values(material_sku_unit),
        product_sku_code = values(product_sku_code),
        product_sku_name = values(product_sku_name),
        product_sku_weight = values(product_sku_weight),
        product_sku_unit = values(product_sku_unit),
        invalid = values(invalid),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag),
        material_sku_unit_desc = values(material_sku_unit_desc),
        product_sku_unit_desc = values(product_sku_unit_desc),
        material_model = values(material_model),
        product_sku_ratio_num = values(product_sku_ratio_num)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_processing_config
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                material_sku_code = #{materialSkuCode},
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                material_sku_name = #{materialSkuName},
            </if>
            <if test="materialSkuWeight != null">
                material_sku_weight = #{materialSkuWeight},
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                material_sku_unit = #{materialSkuUnit},
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                product_sku_code = #{productSkuCode},
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                product_sku_name = #{productSkuName},
            </if>
            <if test="productSkuWeight != null">
                product_sku_weight = #{productSkuWeight},
            </if>
            <if test="productSkuUnit != null and productSkuUnit != ''">
                product_sku_unit = #{productSkuUnit},
            </if>
            <if test="invalid != null">
                invalid = #{invalid},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="materialSkuUnitDesc != null and materialSkuUnitDesc != ''">
                material_sku_unit_desc = #{materialSkuUnitDesc},
            </if>
            <if test="productSkuUnitDesc != null and productSkuUnitDesc != ''">
                product_sku_unit_desc = #{productSkuUnitDesc},
            </if>
            <if test="materialModel != null">
                material_model = #{materialModel},
            </if>
            <if test="productSkuRatioNum!= null">
                product_sku_ratio_num = #{productSkuRatioNum},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--根据加工规则配置id作废-->
    <update id="updateInvalidByProcessingConfigId">
        update wms_processing_config
        set invalid = 1,
        update_time = now(),
        updater = #{updater}
        <where>
            id = #{processingConfigId}
            <if test="processingConfigId == null">
                and 1=2
            </if>
        </where>
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_processing_config where id = #{id}
    </delete>

</mapper>

