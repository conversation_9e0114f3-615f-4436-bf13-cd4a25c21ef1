<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsSkuSpecDao">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsSkuSpecDO" id="WmsSkuSpecMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="processingConfigId" column="processing_config_id" jdbcType="INTEGER"/>
        <result property="materialSkuCode" column="material_sku_code" jdbcType="VARCHAR"/>
        <result property="materialSkuName" column="material_sku_name" jdbcType="VARCHAR"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="productSkuName" column="product_sku_name" jdbcType="VARCHAR"/>
        <result property="productSkuWeight" column="product_sku_weight" />
        <result property="productSkuUnit" column="product_sku_unit" jdbcType="VARCHAR"/>
        <result property="productSkuSpecWeight" column="product_sku_spec_weight" />
        <result property="productSkuSpecUnit" column="product_sku_spec_unit" jdbcType="VARCHAR"/>
        <result property="invalid" column="invalid" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="productSkuSpecUnitDesc" column="product_sku_spec_unit_desc" jdbcType="VARCHAR"/>
        <result property="productSkuUnitDesc" column="product_sku_unit_desc" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="WmsSkuSpecMap">
        select
          id, warehouse_no, type, processing_config_id, material_sku_code, material_sku_name, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_spec_weight, product_sku_spec_unit, invalid, creator, create_time, updater, update_time, delete_flag, product_sku_spec_unit_desc, product_sku_unit_desc
        from wms_sku_spec
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="WmsSkuSpecMap">
        select
          id, warehouse_no, type, processing_config_id, material_sku_code, material_sku_name, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_spec_weight, product_sku_spec_unit, invalid, creator, create_time, updater, update_time, delete_flag, product_sku_spec_unit_desc, product_sku_unit_desc
        from wms_sku_spec
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="processingConfigId != null">
                and processing_config_id = #{processingConfigId}
            </if>
            <if test="processingConfigIdList != null">
                and processing_config_id in
                <foreach collection="processingConfigIdList" item="processingConfigId1" open="(" close=")" separator=",">
                    #{processingConfigId1}
                </foreach>
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuCodeList != null">
                and product_sku_code in
                <foreach collection="productSkuCodeList" item="productSkuCode1" open="(" close=")" separator=",">
                    #{productSkuCode1}
                </foreach>
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuWeight != null">
                and product_sku_weight = #{productSkuWeight}
            </if>
            <if test="productSkuUnit != null and productSkuUnit != ''">
                and product_sku_unit = #{productSkuUnit}
            </if>
            <if test="productSkuSpecWeight != null">
                and product_sku_spec_weight = #{productSkuSpecWeight}
            </if>
            <if test="productSkuSpecUnit != null and productSkuSpecUnit != ''">
                and product_sku_spec_unit = #{productSkuSpecUnit}
            </if>
            <if test="invalid != null">
                and invalid = #{invalid}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="deleteFlag == null">
                and delete_flag = 0
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_sku_spec
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="processingConfigId != null">
                and processing_config_id = #{processingConfigId}
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuWeight != null">
                and product_sku_weight = #{productSkuWeight}
            </if>
            <if test="productSkuUnit != null and productSkuUnit != ''">
                and product_sku_unit = #{productSkuUnit}
            </if>
            <if test="productSkuSpecWeight != null">
                and product_sku_spec_weight = #{productSkuSpecWeight}
            </if>
            <if test="productSkuSpecUnit != null and productSkuSpecUnit != ''">
                and product_sku_spec_unit = #{productSkuSpecUnit}
            </if>
            <if test="invalid != null">
                and invalid = #{invalid}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="deleteFlag == null">
                and delete_flag = 0
            </if>
        </where>
    </select>

    <!--通过加工规则配置id查询SKU规格-->
    <select id="queryAllByProcessingConfigId" resultMap="WmsSkuSpecMap">
        select product_sku_spec_weight, product_sku_spec_unit
        from wms_sku_spec
        <where>
            delete_flag = 0
            <if test="processingConfigId != null">
                and processing_config_id = #{processingConfigId}
            </if>
        </where>
    </select>

    <!--通过加工规则配置id list查询SKU规格-->
    <select id="listByProcessingConfigIdList" resultMap="WmsSkuSpecMap">
        select product_sku_spec_weight, product_sku_spec_unit
        from wms_sku_spec
        <where>
            delete_flag = 0
            <if test="idList != null and idList.size() != 0">
                and processing_config_id in
                <foreach collection="idList" item="id1" open="(" close=")" separator=",">
                    #{id1}
                </foreach>
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_sku_spec(warehouse_no, type, processing_config_id, material_sku_code, material_sku_name, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_spec_weight, product_sku_spec_unit, invalid, creator, create_time, updater, update_time, delete_flag, product_sku_spec_unit_desc, product_sku_unit_desc)
        values (#{warehouseNo}, #{type}, #{processingConfigId}, #{materialSkuCode}, #{materialSkuName}, #{productSkuCode}, #{productSkuName}, #{productSkuWeight}, #{productSkuUnit}, #{productSkuSpecWeight}, #{productSkuSpecUnit}, #{invalid}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag}, #{productSkuSpecUnitDesc}, #{productSkuUnitDesc})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_sku_spec(warehouse_no, type, processing_config_id, material_sku_code, material_sku_name, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_spec_weight, product_sku_spec_unit, invalid, creator, create_time, updater, update_time, delete_flag, product_sku_spec_unit_desc, product_sku_unit_desc)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.warehouseNo}, #{entity.type}, #{entity.processingConfigId}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuWeight}, #{entity.productSkuUnit}, #{entity.productSkuSpecWeight}, #{entity.productSkuSpecUnit}, #{entity.invalid}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.productSkuSpecUnitDesc}, #{entity.productSkuUnitDesc})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_sku_spec(warehouse_no, type, processing_config_id, material_sku_code, material_sku_name, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_spec_weight, product_sku_spec_unit, invalid, creator, create_time, updater, update_time, delete_flag, product_sku_spec_unit_desc, product_sku_unit_desc)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.warehouseNo}, #{entity.type}, #{entity.processingConfigId}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuWeight}, #{entity.productSkuUnit}, #{entity.productSkuSpecWeight}, #{entity.productSkuSpecUnit}, #{entity.invalid}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.productSkuSpecUnitDesc}, #{entity.productSkuUnitDesc})
        </foreach>
        on duplicate key update
        warehouse_no = values(warehouse_no),
        type = values(type),
        processing_config_id = values(processing_config_id),
        material_sku_code = values(material_sku_code),
        material_sku_name = values(material_sku_name),
        product_sku_code = values(product_sku_code),
        product_sku_name = values(product_sku_name),
        product_sku_weight = values(product_sku_weight),
        product_sku_unit = values(product_sku_unit),
        product_sku_spec_weight = values(product_sku_spec_weight),
        product_sku_spec_unit = values(product_sku_spec_unit),
        invalid = values(invalid),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag),
        product_sku_spec_unit_desc = values(product_sku_spec_unit_desc),
        product_sku_unit_desc = values(product_sku_unit_desc)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_sku_spec
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="processingConfigId != null">
                processing_config_id = #{processingConfigId},
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                material_sku_code = #{materialSkuCode},
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                material_sku_name = #{materialSkuName},
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                product_sku_code = #{productSkuCode},
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                product_sku_name = #{productSkuName},
            </if>
            <if test="productSkuWeight != null">
                product_sku_weight = #{productSkuWeight},
            </if>
            <if test="productSkuUnit != null and productSkuUnit != ''">
                product_sku_unit = #{productSkuUnit},
            </if>
            <if test="productSkuSpecWeight != null">
                product_sku_spec_weight = #{productSkuSpecWeight},
            </if>
            <if test="productSkuSpecUnit != null and productSkuSpecUnit != ''">
                product_sku_spec_unit = #{productSkuSpecUnit},
            </if>
            <if test="invalid != null">
                invalid = #{invalid},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="productSkuSpecUnitDesc != null and productSkuSpecUnitDesc != ''">
                product_sku_spec_unit_desc = #{productSkuSpecUnitDesc},
            </if>
            <if test="productSkuUnitDesc != null and productSkuUnitDesc != ''">
                product_sku_unit_desc = #{productSkuUnitDesc},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_sku_spec where id = #{id}
    </delete>

    <!--通过加工规则配置id删除-->
    <update id="updateDeletedByProcessingConfigId">
        update
            wms_sku_spec
        set delete_flag = 1
        <where>
            processing_config_id = #{processingConfigId}
            <if test="processingConfigId == null">
                1=2
            </if>
        </where>
    </update>

    <!--通过加工规则配置id作废-->
    <update id="updateInvalidByProcessingConfigId">
        update
            wms_sku_spec
        set invalid = 1
        <where>
            processing_config_id = #{processingConfigId}
            <if test="processingConfigId == null">
                1=2
            </if>
        </where>
    </update>
</mapper>

