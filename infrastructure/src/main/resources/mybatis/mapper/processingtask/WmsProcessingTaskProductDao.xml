<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingTaskProductDao">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskProductDO" id="WmsProcessingTaskProductMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="processingTaskCode" column="processing_task_code" jdbcType="VARCHAR"/>
        <result property="materialSkuCode" column="material_sku_code" jdbcType="VARCHAR"/>
        <result property="materialSkuName" column="material_sku_name" jdbcType="VARCHAR"/>
        <result property="materialSkuWeight" column="material_sku_weight" />
        <result property="materialSkuUnit" column="material_sku_unit" jdbcType="VARCHAR"/>
        <result property="materialSkuReceiveQuantity" column="material_sku_receive_quantity" jdbcType="INTEGER"/>
        <result property="materialSkuRestoreQuantity" column="material_sku_restore_quantity" jdbcType="INTEGER"/>
        <result property="materialSkuReceiveWeight" column="material_sku_receive_weight" />
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="productSkuName" column="product_sku_name" jdbcType="VARCHAR"/>
        <result property="productSkuWeight" column="product_sku_weight" />
        <result property="productSkuUnit" column="product_sku_unit" jdbcType="VARCHAR"/>
        <result property="productSkuUnitDesc" column="product_sku_unit_desc" jdbcType="VARCHAR"/>
        <result property="productSkuNeedQuantity" column="product_sku_need_quantity" jdbcType="INTEGER"/>
        <result property="productSkuFinishQuantity" column="product_sku_finish_quantity" jdbcType="INTEGER"/>
        <result property="productSkuSpecFinishWeight" column="product_sku_spec_finish_weight" />
        <result property="wasteLossWeight" column="waste_loss_weight" />
        <result property="specLossWeight" column="spec_loss_weight" />
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="finishRemark" column="finish_remark" jdbcType="VARCHAR"/>
        <result property="materialSkuUnitDesc" column="material_sku_unit_desc" jdbcType="VARCHAR"/>
        <result property="materialModel" column="material_model" jdbcType="INTEGER"/>
        <result property="materialConfigJson" column="material_config_json" jdbcType="VARCHAR"/>
        <result property="productSkuRatioNum" column="product_sku_ratio_num" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="WmsProcessingTaskProductMap">
        select
          id, warehouse_no, processing_task_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_receive_quantity, material_sku_restore_quantity, material_sku_receive_weight, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_unit_desc, product_sku_need_quantity, product_sku_finish_quantity, product_sku_spec_finish_weight, waste_loss_weight, spec_loss_weight, creator, create_time, updater, update_time, delete_flag, status, finish_remark, material_sku_unit_desc
            , material_model, material_config_json, product_sku_ratio_num
        from wms_processing_task_product
        where id = #{id}
    </select>

    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode !=''">
                AND t.processing_task_code = #{processingTaskCode}
            </if>
            <if test="materialSkuReceiveQuantity != null">
                AND t.material_sku_receive_quantity = #{materialSkuReceiveQuantity}
            </if>
            <if test="materialSkuRestoreQuantity != null">
                AND t.material_sku_restore_quantity = #{materialSkuRestoreQuantity}
            </if>
            <if test="materialSkuReceiveWeight != null">
                AND t.material_sku_receive_weight = #{materialSkuReceiveWeight}
            </if>
            <if test="productSkuCode != null and productSkuCode !=''">
                AND t.product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuName != null and productSkuName !=''">
                AND t.product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuWeight != null">
                AND t.product_sku_weight = #{productSkuWeight}
            </if>
            <if test="productSkuUnit != null and productSkuUnit !=''">
                AND t.product_sku_unit = #{productSkuUnit}
            </if>
            <if test="productSkuUnitDesc != null and productSkuUnitDesc !=''">
                AND t.product_sku_unit_desc = #{productSkuUnitDesc}
            </if>
            <if test="productSkuNeedQuantity != null">
                AND t.product_sku_need_quantity = #{productSkuNeedQuantity}
            </if>
            <if test="productSkuFinishQuantity != null">
                AND t.product_sku_finish_quantity = #{productSkuFinishQuantity}
            </if>
            <if test="productSkuSpecFinishWeight != null">
                AND t.product_sku_spec_finish_weight = #{productSkuSpecFinishWeight}
            </if>
            <if test="wasteLossWeight != null">
                AND t.waste_loss_weight = #{wasteLossWeight}
            </if>
            <if test="specLossWeight != null">
                AND t.spec_loss_weight = #{specLossWeight}
            </if>
            <if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
            <if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                AND t.delete_flag = #{deleteFlag}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="finishRemark != null and finishRemark !=''">
                AND t.finish_remark = #{finishRemark}
            </if>
            <if test="materialSkuUnitDesc != null and materialSkuUnitDesc !=''">
                AND t.material_sku_unit_desc = #{materialSkuUnitDesc}
            </if>
            <if test="materialModel != null">
                AND t.material_model = #{materialModel}
            </if>
            <if test="materialConfigJson != null and materialConfigJson !=''">
                AND t.material_config_json = #{materialConfigJson}
            </if>
            <if test="productSkuRatioNum!= null">
                AND t.product_sku_ratio_num = #{productSkuRatioNum}
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="WmsProcessingTaskProductMap">
        select
            t.id,
            t.warehouse_no,
            t.processing_task_code,
            t.material_sku_code,
            t.material_sku_name,
            t.material_sku_weight,
            t.material_sku_unit,
            t.material_sku_receive_quantity,
            t.material_sku_restore_quantity,
            t.material_sku_receive_weight,
            t.product_sku_code,
            t.product_sku_name,
            t.product_sku_weight,
            t.product_sku_unit,
            t.product_sku_unit_desc,
            t.product_sku_need_quantity,
            t.product_sku_finish_quantity,
            t.product_sku_spec_finish_weight,
            t.waste_loss_weight,
            t.spec_loss_weight,
            t.creator,
            t.create_time,
            t.updater,
            t.update_time,
            t.delete_flag,
            t.status,
            t.finish_remark,
            t.material_sku_unit_desc,
            t.material_model,
            t.material_config_json,
            t.product_sku_ratio_num
        from wms_processing_task_product t
        <if test="materialSkuCode != null or materialSkuName != null">
            inner join wms_processing_task_material m on m.processing_task_product_id = t.id
            <if test="materialSkuCode != null">
                and m.material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null">
                and m.material_sku_name = #{materialSkuName}
            </if>
        </if>
        <include refid="whereColumnBySelect" />
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_processing_task_product
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="materialSkuWeight != null">
                and material_sku_weight = #{materialSkuWeight}
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                and material_sku_unit = #{materialSkuUnit}
            </if>
            <if test="materialSkuReceiveQuantity != null">
                and material_sku_receive_quantity = #{materialSkuReceiveQuantity}
            </if>
            <if test="materialSkuRestoreQuantity != null">
                and material_sku_restore_quantity = #{materialSkuRestoreQuantity}
            </if>
            <if test="materialSkuReceiveWeight != null">
                and material_sku_receive_weight = #{materialSkuReceiveWeight}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuWeight != null">
                and product_sku_weight = #{productSkuWeight}
            </if>
            <if test="productSkuUnit != null and productSkuUnit != ''">
                and product_sku_unit = #{productSkuUnit}
            </if>
            <if test="productSkuUnitDesc != null and productSkuUnitDesc != ''">
                and product_sku_unit_desc = #{productSkuUnitDesc}
            </if>
            <if test="productSkuNeedQuantity != null">
                and product_sku_need_quantity = #{productSkuNeedQuantity}
            </if>
            <if test="productSkuFinishQuantity != null">
                and product_sku_finish_quantity = #{productSkuFinishQuantity}
            </if>
            <if test="productSkuSpecFinishWeight != null">
                and product_sku_spec_finish_weight = #{productSkuSpecFinishWeight}
            </if>
            <if test="wasteLossWeight != null">
                and waste_loss_weight = #{wasteLossWeight}
            </if>
            <if test="specLossWeight != null">
                and spec_loss_weight = #{specLossWeight}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="finishRemark != null">
                and finish_remark = #{finishRemark}
            </if>
            <if test="materialModel != null">
                and material_model = #{materialModel}
            </if>
            <if test="productSkuRatioNum!= null">
                and product_sku_ratio_num = #{productSkuRatioNum}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_product(warehouse_no, processing_task_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_receive_quantity, material_sku_restore_quantity, material_sku_receive_weight, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_unit_desc, product_sku_need_quantity, product_sku_finish_quantity, product_sku_spec_finish_weight, waste_loss_weight, spec_loss_weight, creator, create_time, updater, update_time, delete_flag, status, finish_remark, material_sku_unit_desc
                                               , material_model, material_config_json, product_sku_ratio_num)
        values (#{warehouseNo}, #{processingTaskCode}, #{materialSkuCode}, #{materialSkuName}, #{materialSkuWeight}, #{materialSkuUnit}, #{materialSkuReceiveQuantity}, #{materialSkuRestoreQuantity}, #{materialSkuReceiveWeight}, #{productSkuCode}, #{productSkuName}, #{productSkuWeight}, #{productSkuUnit}, #{productSkuUnitDesc}, #{productSkuNeedQuantity}, #{productSkuFinishQuantity}, #{productSkuSpecFinishWeight}, #{wasteLossWeight}, #{specLossWeight}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag}, #{status}, #{finishRemark}, #{materialSkuUnitDesc}
               , #{materialModel}, #{materialConfigJson}, #{productSkuRatioNum})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_product(warehouse_no, processing_task_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_receive_quantity, material_sku_restore_quantity, material_sku_receive_weight, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_unit_desc, product_sku_need_quantity, product_sku_finish_quantity, product_sku_spec_finish_weight, waste_loss_weight, spec_loss_weight, creator, create_time, updater, update_time, delete_flag, status, finish_remark, material_sku_unit_desc
                , material_model, material_config_json, product_sku_ratio_num)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.materialSkuReceiveQuantity}, #{entity.materialSkuRestoreQuantity}, #{entity.materialSkuReceiveWeight}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuWeight}, #{entity.productSkuUnit}, #{entity.productSkuUnitDesc}, #{entity.productSkuNeedQuantity}, #{entity.productSkuFinishQuantity}, #{entity.productSkuSpecFinishWeight}, #{entity.wasteLossWeight}, #{entity.specLossWeight}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.status}, #{entity.finishRemark}, #{entity.materialSkuUnitDesc}
            , #{entity.materialModel}, #{entity.materialConfigJson}, #{entity.productSkuRatioNum})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_product(warehouse_no, processing_task_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_receive_quantity, material_sku_restore_quantity, material_sku_receive_weight, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_unit_desc, product_sku_need_quantity, product_sku_finish_quantity, product_sku_spec_finish_weight, waste_loss_weight, spec_loss_weight, creator, create_time, updater, update_time, delete_flag, status, finish_remark, material_sku_unit_desc
                , material_model, material_config_json, product_sku_ratio_num)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.materialSkuReceiveQuantity}, #{entity.materialSkuRestoreQuantity}, #{entity.materialSkuReceiveWeight}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuWeight}, #{entity.productSkuUnit}, #{entity.productSkuUnitDesc}, #{entity.productSkuNeedQuantity}, #{entity.productSkuFinishQuantity}, #{entity.productSkuSpecFinishWeight}, #{entity.wasteLossWeight}, #{entity.specLossWeight}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.status}, #{entity.finishRemark}, #{entity.materialSkuUnitDesc}
                , #{entity.materialModel}, #{entity.materialConfigJson}, #{entity.productSkuRatioNum})
        </foreach>
        on duplicate key update
        warehouse_no = values(warehouse_no),
        processing_task_code = values(processing_task_code),
        material_sku_code = values(material_sku_code),
        material_sku_name = values(material_sku_name),
        material_sku_weight = values(material_sku_weight),
        material_sku_unit = values(material_sku_unit),
        material_sku_receive_quantity = values(material_sku_receive_quantity),
        material_sku_restore_quantity = values(material_sku_restore_quantity),
        material_sku_receive_weight = values(material_sku_receive_weight),
        product_sku_code = values(product_sku_code),
        product_sku_name = values(product_sku_name),
        product_sku_weight = values(product_sku_weight),
        product_sku_unit = values(product_sku_unit),
        product_sku_unit_desc = values(product_sku_unit_desc),
        product_sku_need_quantity = values(product_sku_need_quantity),
        product_sku_finish_quantity = values(product_sku_finish_quantity),
        product_sku_spec_finish_weight = values(product_sku_spec_finish_weight),
        waste_loss_weight = values(waste_loss_weight),
        spec_loss_weight = values(spec_loss_weight),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag),
        status = values(status),
        finish_remark = values(finish_remark),
        material_sku_unit_desc = values(material_sku_unit_desc),
        material_model = values(material_model),
        product_sku_ratio_num = values(product_sku_ratio_num)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_processing_task_product
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                processing_task_code = #{processingTaskCode},
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                material_sku_code = #{materialSkuCode},
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                material_sku_name = #{materialSkuName},
            </if>
            <if test="materialSkuWeight != null">
                material_sku_weight = #{materialSkuWeight},
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                material_sku_unit = #{materialSkuUnit},
            </if>
            <if test="materialSkuReceiveQuantity != null">
                material_sku_receive_quantity = #{materialSkuReceiveQuantity},
            </if>
            <if test="materialSkuRestoreQuantity != null">
                material_sku_restore_quantity = #{materialSkuRestoreQuantity},
            </if>
            <if test="materialSkuReceiveWeight != null">
                material_sku_receive_weight = #{materialSkuReceiveWeight},
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                product_sku_code = #{productSkuCode},
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                product_sku_name = #{productSkuName},
            </if>
            <if test="productSkuWeight != null">
                product_sku_weight = #{productSkuWeight},
            </if>
            <if test="productSkuUnit != null and productSkuUnit != ''">
                product_sku_unit = #{productSkuUnit},
            </if>
            <if test="productSkuUnitDesc != null and productSkuUnitDesc != ''">
                product_sku_unit_desc = #{productSkuUnitDesc},
            </if>
            <if test="productSkuNeedQuantity != null">
                product_sku_need_quantity = #{productSkuNeedQuantity},
            </if>
            <if test="productSkuFinishQuantity != null">
                product_sku_finish_quantity = #{productSkuFinishQuantity},
            </if>
            <if test="productSkuSpecFinishWeight != null">
                product_sku_spec_finish_weight = #{productSkuSpecFinishWeight},
            </if>
            <if test="wasteLossWeight != null">
                waste_loss_weight = #{wasteLossWeight},
            </if>
            <if test="specLossWeight != null">
                spec_loss_weight = #{specLossWeight},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="status != null">
                status = #{status}
            </if>
            <if test="finishRemark != null">
                finish_remark = #{finishRemark}
            </if>
            <if test="materialModel != null">
                material_model = #{materialModel},
            </if>
            <if test="materialConfigJson != null">
                material_config_json = #{materialConfigJson},
            </if>
            <if test="productSkuRatioNum!= null">
                product_sku_ratio_num = #{productSkuRatioNum}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_processing_task_product where id = #{id}
    </delete>

    <update id="addMaterial">
        update wms_processing_task_product
        <set>
            material_sku_receive_quantity = material_sku_receive_quantity + #{materialSkuReceiveQuantity},
            material_sku_receive_weight = material_sku_receive_weight + #{materialSkuReceiveWeight},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
        and status = 0
    </update>

    <update id="reduceMaterial">
        update wms_processing_task_product
        <set>
            material_sku_restore_quantity = material_sku_restore_quantity + #{materialSkuRestoreQuantity},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
        and material_sku_receive_weight >= #{materialSkuRestoreWeight}
        and status = 0
    </update>

    <update id="addWasteLossWeight">
        update wms_processing_task_product
        <set>
            waste_loss_weight = waste_loss_weight + #{wasteLossWeight},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
        and status = 0
    </update>

    <update id="submitQuantity">
        update wms_processing_task_product
        <set>
            product_sku_finish_quantity = product_sku_finish_quantity + #{submitQuantity},
            product_sku_spec_finish_weight = product_sku_spec_finish_weight + #{submitWeight},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
        and product_sku_need_quantity >= product_sku_finish_quantity + #{submitQuantity}
        and status = 0
    </update>

    <update id="updateFinishAndSpecLoseWeight">
        update wms_processing_task_product
        <set>
            status = 1,
            spec_loss_weight = spec_loss_weight + #{specLossWeightTotal},
            finish_remark = #{finishRemark},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
        and status = 0
    </update>

    <update id="updateFinishByNotProcessing">
        update wms_processing_task_product
        <set>
            status = 1,
            updater = #{updater},
            update_time = now()
        </set>
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and status = 0
        and material_sku_receive_quantity = 0
    </update>

    <select id="queryByTaskCodeList" resultMap="WmsProcessingTaskProductMap">
        select
        id, warehouse_no, processing_task_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_receive_quantity, material_sku_restore_quantity, material_sku_receive_weight, product_sku_code, product_sku_name, product_sku_weight, product_sku_unit, product_sku_unit_desc, product_sku_need_quantity, product_sku_finish_quantity, product_sku_spec_finish_weight, waste_loss_weight, spec_loss_weight, creator, create_time, updater, update_time, delete_flag, status, finish_remark, material_sku_unit_desc
            , material_model, material_config_json, product_sku_ratio_num
        from wms_processing_task_product
        <where>
            <if test="taskCodeList != null">
                <if test = "taskCodeList.size() > 0">
                    and processing_task_code in
                    <foreach collection="taskCodeList" item="taskCode1" open="(" close=")" separator=",">
                        #{taskCode1}
                    </foreach>
                </if>
                <if test = "taskCodeList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="taskCodeList == null">
                and 1 = 2
            </if>
        </where>
    </select>

    <select id="countProcessingByWarehouseAndSkuCode" resultType="java.lang.Long">
        select count(1)
        from wms_processing_task_product
        <where>
            warehouse_no = #{warehouseNo}
            and product_sku_code = #{productSkuCode}
            and status = 0
        </where>
    </select>
</mapper>

