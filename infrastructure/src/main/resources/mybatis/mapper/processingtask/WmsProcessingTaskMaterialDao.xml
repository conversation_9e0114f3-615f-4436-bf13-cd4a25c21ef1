<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingTaskMaterialDao">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskMaterialDO" id="WmsProcessingTaskMaterialMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="processingTaskCode" column="processing_task_code" jdbcType="VARCHAR"/>
        <result property="processingTaskProductId" column="processing_task_product_id" jdbcType="INTEGER"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="materialSkuCode" column="material_sku_code" jdbcType="VARCHAR"/>
        <result property="materialSkuName" column="material_sku_name" jdbcType="VARCHAR"/>
        <result property="materialSkuWeight" column="material_sku_weight"/>
        <result property="materialSkuUnit" column="material_sku_unit" jdbcType="VARCHAR"/>
        <result property="materialSkuQuantity" column="material_sku_quantity" jdbcType="INTEGER"/>
        <result property="materialSkuReceiveQuantity" column="material_sku_receive_quantity" jdbcType="INTEGER"/>
        <result property="materialSkuReceiveWeight" column="material_sku_receive_weight" />
        <result property="materialSkuRestoreQuantity" column="material_sku_restore_quantity" jdbcType="INTEGER"/>
        <result property="wasteLossWeight" column="waste_loss_weight" />
        <result property="specLossWeight" column="spec_loss_weight" />
        <result property="materialSkuRemainWeight" column="material_sku_remain_weight" />
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="materialSkuRatioNum" column="material_sku_ratio_num" jdbcType="INTEGER"/>
        <result property="materialSkuUnitDesc" column="material_sku_unit_desc" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="WmsProcessingTaskMaterialMap">
        select
          id, warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_quantity, material_sku_receive_quantity, material_sku_receive_weight, material_sku_restore_quantity, waste_loss_weight, spec_loss_weight, material_sku_remain_weight, creator, create_time, updater, update_time, delete_flag
            , material_sku_ratio_num, material_sku_unit_desc
        from wms_processing_task_material
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="WmsProcessingTaskMaterialMap"
                parameterType="net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingTaskMaterialQueryParam">
        select
          id, warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_quantity, material_sku_receive_quantity, material_sku_receive_weight, material_sku_restore_quantity, waste_loss_weight, spec_loss_weight, material_sku_remain_weight, creator, create_time, updater, update_time, delete_flag
            , material_sku_ratio_num, material_sku_unit_desc
        from wms_processing_task_material
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="processingTaskProductId != null">
                and processing_task_product_id = #{processingTaskProductId}
            </if>
            <if test="processingTaskProductIdList != null and processingTaskProductIdList.size() > 0">
                and processing_task_product_id in
                <foreach collection="processingTaskProductIdList" item="processingTaskProductId1" open="(" close=")" separator=",">
                    #{processingTaskProductId1}
                </foreach>
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="materialSkuWeight != null">
                and material_sku_weight = #{materialSkuWeight}
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                and material_sku_unit = #{materialSkuUnit}
            </if>
            <if test="materialSkuQuantity != null">
                and material_sku_quantity = #{materialSkuQuantity}
            </if>
            <if test="materialSkuReceiveQuantity != null">
                and material_sku_receive_quantity = #{materialSkuReceiveQuantity}
            </if>
            <if test="materialSkuReceiveWeight != null">
                and material_sku_receive_weight = #{materialSkuReceiveWeight}
            </if>
            <if test="materialSkuRestoreQuantity != null">
                and material_sku_restore_quantity = #{materialSkuRestoreQuantity}
            </if>
            <if test="wasteLossWeight != null">
                and waste_loss_weight = #{wasteLossWeight}
            </if>
            <if test="specLossWeight != null">
                and spec_loss_weight = #{specLossWeight}
            </if>
            <if test="materialSkuRemainWeight != null">
                and material_sku_remain_weight = #{materialSkuRemainWeight}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="materialSkuRatioNum != null">
                and material_sku_ratio_num = #{materialSkuRatioNum}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_processing_task_material
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="processingTaskProductId != null">
                and processing_task_product_id = #{processingTaskProductId}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="materialSkuWeight != null">
                and material_sku_weight = #{materialSkuWeight}
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                and material_sku_unit = #{materialSkuUnit}
            </if>
            <if test="materialSkuQuantity != null">
                and material_sku_quantity = #{materialSkuQuantity}
            </if>
            <if test="materialSkuReceiveQuantity != null">
                and material_sku_receive_quantity = #{materialSkuReceiveQuantity}
            </if>
            <if test="materialSkuReceiveWeight != null">
                and material_sku_receive_weight = #{materialSkuReceiveWeight}
            </if>
            <if test="materialSkuRestoreQuantity != null">
                and material_sku_restore_quantity = #{materialSkuRestoreQuantity}
            </if>
            <if test="wasteLossWeight != null">
                and waste_loss_weight = #{wasteLossWeight}
            </if>
            <if test="specLossWeight != null">
                and spec_loss_weight = #{specLossWeight}
            </if>
            <if test="materialSkuRemainWeight != null">
                and material_sku_remain_weight = #{materialSkuRemainWeight}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="materialSkuRatioNum != null">
                and material_sku_ratio_num = #{materialSkuRatioNum}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_material(warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_quantity, material_sku_receive_quantity, material_sku_receive_weight, material_sku_restore_quantity, waste_loss_weight, spec_loss_weight, material_sku_remain_weight, creator, create_time, updater, update_time, delete_flag
            , material_sku_ratio_num, material_sku_unit_desc)
        values (#{warehouseNo}, #{processingTaskCode}, #{processingTaskProductId}, #{productSkuCode}, #{materialSkuCode}, #{materialSkuName}, #{materialSkuWeight}, #{materialSkuUnit}, #{materialSkuQuantity}, #{materialSkuReceiveQuantity}, #{materialSkuReceiveWeight}, #{materialSkuRestoreQuantity}, #{wasteLossWeight}, #{specLossWeight}, #{materialSkuRemainWeight}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag}
            , #{materialSkuRatioNum}, #{materialSkuUnitDesc})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_material(warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_quantity, material_sku_receive_quantity, material_sku_receive_weight, material_sku_restore_quantity, waste_loss_weight, spec_loss_weight, material_sku_remain_weight, creator, create_time, updater, update_time, delete_flag
            , material_sku_ratio_num, material_sku_unit_desc)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.processingTaskProductId}, #{entity.productSkuCode}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.materialSkuQuantity}, #{entity.materialSkuReceiveQuantity}, #{entity.materialSkuReceiveWeight}, #{entity.materialSkuRestoreQuantity}, #{entity.wasteLossWeight}, #{entity.specLossWeight}, #{entity.materialSkuRemainWeight}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}
            , #{entity.materialSkuRatioNum}, #{entity.materialSkuUnitDesc})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_material(warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_quantity, material_sku_receive_quantity, material_sku_receive_weight, material_sku_restore_quantity, waste_loss_weight, spec_loss_weight, material_sku_remain_weight, creator, create_time, updater, update_time, delete_flag
            , material_sku_ratio_num, material_sku_unit_desc)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.processingTaskProductId}, #{entity.productSkuCode}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.materialSkuQuantity}, #{entity.materialSkuReceiveQuantity}, #{entity.materialSkuReceiveWeight}, #{entity.materialSkuRestoreQuantity}, #{entity.wasteLossWeight}, #{entity.specLossWeight}, #{entity.materialSkuRemainWeight}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}
            , #{entity.materialSkuRatioNum}, #{entity.materialSkuUnitDesc})
        </foreach>
        on duplicate key update
        warehouse_no = values(warehouse_no),
        processing_task_code = values(processing_task_code),
        processing_task_product_id = values(processing_task_product_id),
        product_sku_code = values(product_sku_code),
        material_sku_code = values(material_sku_code),
        material_sku_name = values(material_sku_name),
        material_sku_weight = values(material_sku_weight),
        material_sku_unit = values(material_sku_unit),
        material_sku_quantity = values(material_sku_quantity),
        material_sku_receive_quantity = values(material_sku_receive_quantity),
        material_sku_receive_weight = values(material_sku_receive_weight),
        material_sku_restore_quantity = values(material_sku_restore_quantity),
        waste_loss_weight = values(waste_loss_weight),
        spec_loss_weight = values(spec_loss_weight),
        material_sku_remain_weight = values(material_sku_remain_weight),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag),
        material_sku_ratio_num = values(material_sku_ratio_num),
        material_sku_unit_desc = values(material_sku_unit_desc)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_processing_task_material
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                processing_task_code = #{processingTaskCode},
            </if>
            <if test="processingTaskProductId != null">
                processing_task_product_id = #{processingTaskProductId},
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                product_sku_code = #{productSkuCode},
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                material_sku_code = #{materialSkuCode},
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                material_sku_name = #{materialSkuName},
            </if>
            <if test="materialSkuWeight != null">
                material_sku_weight = #{materialSkuWeight},
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                material_sku_unit = #{materialSkuUnit},
            </if>
            <if test="materialSkuQuantity != null">
                material_sku_quantity = #{materialSkuQuantity},
            </if>
            <if test="materialSkuReceiveQuantity != null">
                material_sku_receive_quantity = #{materialSkuReceiveQuantity},
            </if>
            <if test="materialSkuReceiveWeight != null">
                material_sku_receive_weight = #{materialSkuReceiveWeight},
            </if>
            <if test="materialSkuRestoreQuantity != null">
                material_sku_restore_quantity = #{materialSkuRestoreQuantity},
            </if>
            <if test="wasteLossWeight != null">
                waste_loss_weight = #{wasteLossWeight},
            </if>
            <if test="specLossWeight != null">
                spec_loss_weight = #{specLossWeight},
            </if>
            <if test="materialSkuRemainWeight != null">
                material_sku_remain_weight = #{materialSkuRemainWeight},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="materialSkuRatioNum != null">
                material_sku_ratio_num = #{materialSkuRatioNum},
            </if>
            <if test="materialSkuUnitDesc != null">
                material_sku_unit_desc = #{materialSkuUnitDesc},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_processing_task_material where id = #{id}
    </delete>

    <update id="addWasteLossWeight">
        update wms_processing_task_material
        <set>
            waste_loss_weight = waste_loss_weight + #{wasteLossWeight},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
    </update>

    <update id="addRestoreQuantity">
        update wms_processing_task_material
        <set>
            material_sku_restore_quantity = material_sku_restore_quantity + #{materialSkuRestoreQuantity},
            material_sku_quantity = material_sku_quantity - #{materialSkuRestoreQuantity},
            material_sku_remain_weight = material_sku_remain_weight + #{materialSkuRestoreWeight},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
        and material_sku_quantity >= #{materialSkuRestoreQuantity}
    </update>

    <update id="addMaterial">
        update wms_processing_task_material
        <set>
            material_sku_quantity = material_sku_quantity + #{materialSkuReceiveQuantity},
            material_sku_receive_quantity = material_sku_receive_quantity + #{materialSkuReceiveQuantity},
            material_sku_receive_weight = material_sku_receive_weight + #{materialSkuReceiveWeight},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
    </update>

</mapper>

