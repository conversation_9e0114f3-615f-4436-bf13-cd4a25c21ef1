<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingMaterialConfigMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsProcessingMaterialConfigResultMap" type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingMaterialConfig">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="processing_config_id" property="processingConfigId" jdbcType="NUMERIC"/>
		<result column="material_sku_code" property="materialSkuCode" jdbcType="VARCHAR"/>
		<result column="material_sku_name" property="materialSkuName" jdbcType="VARCHAR"/>
		<result column="material_sku_weight" property="materialSkuWeight" jdbcType="DOUBLE"/>
		<result column="material_sku_unit" property="materialSkuUnit" jdbcType="VARCHAR"/>
		<result column="material_sku_unit_desc" property="materialSkuUnitDesc" jdbcType="VARCHAR"/>
		<result column="material_sku_ratio_num" property="materialSkuRatioNum" jdbcType="INTEGER"/>
		<result column="product_sku_code" property="productSkuCode" jdbcType="VARCHAR"/>
		<result column="product_sku_name" property="productSkuName" jdbcType="VARCHAR"/>
		<result column="product_sku_weight" property="productSkuWeight" jdbcType="DOUBLE"/>
		<result column="product_sku_unit" property="productSkuUnit" jdbcType="VARCHAR"/>
		<result column="product_sku_unit_desc" property="productSkuUnitDesc" jdbcType="VARCHAR"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="updater" property="updater" jdbcType="VARCHAR"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsProcessingMaterialConfigColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.processing_config_id,
          t.material_sku_code,
          t.material_sku_name,
          t.material_sku_weight,
          t.material_sku_unit,
          t.material_sku_unit_desc,
          t.material_sku_ratio_num,
          t.product_sku_code,
          t.product_sku_name,
          t.product_sku_weight,
          t.product_sku_unit,
          t.product_sku_unit_desc,
          t.creator,
          t.updater,
          t.delete_flag
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="processingConfigId != null">
                AND t.processing_config_id = #{processingConfigId}
            </if>
            <if test="processingConfigIdList != null and processingConfigIdList.size() > 0">
                and t.processing_config_id in
                <foreach collection="processingConfigIdList" item="processingConfigId1" open="(" close=")" separator=",">
                    #{processingConfigId1}
                </foreach>
            </if>
			<if test="materialSkuCode != null and materialSkuCode !=''">
                AND t.material_sku_code = #{materialSkuCode}
            </if>
			<if test="materialSkuName != null and materialSkuName !=''">
                AND t.material_sku_name = #{materialSkuName}
            </if>
			<if test="materialSkuWeight != null">
                AND t.material_sku_weight = #{materialSkuWeight}
            </if>
			<if test="materialSkuUnit != null and materialSkuUnit !=''">
                AND t.material_sku_unit = #{materialSkuUnit}
            </if>
			<if test="materialSkuUnitDesc != null and materialSkuUnitDesc !=''">
                AND t.material_sku_unit_desc = #{materialSkuUnitDesc}
            </if>
			<if test="materialSkuRatioNum != null">
                AND t.material_sku_ratio_num = #{materialSkuRatioNum}
            </if>
			<if test="productSkuCode != null and productSkuCode !=''">
                AND t.product_sku_code = #{productSkuCode}
            </if>
			<if test="productSkuName != null and productSkuName !=''">
                AND t.product_sku_name = #{productSkuName}
            </if>
			<if test="productSkuWeight != null">
                AND t.product_sku_weight = #{productSkuWeight}
            </if>
			<if test="productSkuUnit != null and productSkuUnit !=''">
                AND t.product_sku_unit = #{productSkuUnit}
            </if>
			<if test="productSkuUnitDesc != null and productSkuUnitDesc !=''">
                AND t.product_sku_unit_desc = #{productSkuUnitDesc}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="updater != null and updater !=''">
                AND t.updater = #{updater}
            </if>
			<if test="deleteFlag != null">
                AND t.delete_flag = #{deleteFlag}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="processingConfigId != null">
                    t.processing_config_id = #{processingConfigId},
                </if>
                <if test="materialSkuCode != null">
                    t.material_sku_code = #{materialSkuCode},
                </if>
                <if test="materialSkuName != null">
                    t.material_sku_name = #{materialSkuName},
                </if>
                <if test="materialSkuWeight != null">
                    t.material_sku_weight = #{materialSkuWeight},
                </if>
                <if test="materialSkuUnit != null">
                    t.material_sku_unit = #{materialSkuUnit},
                </if>
                <if test="materialSkuUnitDesc != null">
                    t.material_sku_unit_desc = #{materialSkuUnitDesc},
                </if>
                <if test="materialSkuRatioNum != null">
                    t.material_sku_ratio_num = #{materialSkuRatioNum},
                </if>
                <if test="productSkuCode != null">
                    t.product_sku_code = #{productSkuCode},
                </if>
                <if test="productSkuName != null">
                    t.product_sku_name = #{productSkuName},
                </if>
                <if test="productSkuWeight != null">
                    t.product_sku_weight = #{productSkuWeight},
                </if>
                <if test="productSkuUnit != null">
                    t.product_sku_unit = #{productSkuUnit},
                </if>
                <if test="productSkuUnitDesc != null">
                    t.product_sku_unit_desc = #{productSkuUnitDesc},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="updater != null">
                    t.updater = #{updater},
                </if>
                <if test="deleteFlag != null">
                    t.delete_flag = #{deleteFlag},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsProcessingMaterialConfigResultMap" >
        SELECT <include refid="wmsProcessingMaterialConfigColumns" />
        FROM wms_processing_material_config t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingMaterialConfigQueryParam"  resultType="net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.processing_config_id processingConfigId,
            t.material_sku_code materialSkuCode,
            t.material_sku_name materialSkuName,
            t.material_sku_weight materialSkuWeight,
            t.material_sku_unit materialSkuUnit,
            t.material_sku_unit_desc materialSkuUnitDesc,
            t.material_sku_ratio_num materialSkuRatioNum,
            t.product_sku_code productSkuCode,
            t.product_sku_name productSkuName,
            t.product_sku_weight productSkuWeight,
            t.product_sku_unit productSkuUnit,
            t.product_sku_unit_desc productSkuUnitDesc,
            t.creator creator,
            t.updater updater,
            t.delete_flag deleteFlag
        FROM wms_processing_material_config t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.processingtask.domainobject.param.WmsProcessingMaterialConfigQueryParam" resultMap="wmsProcessingMaterialConfigResultMap" >
        SELECT <include refid="wmsProcessingMaterialConfigColumns" />
        FROM wms_processing_material_config t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingMaterialConfig" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_processing_material_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="processingConfigId != null">
				  processing_config_id,
              </if>
              <if test="materialSkuCode != null">
				  material_sku_code,
              </if>
              <if test="materialSkuName != null">
				  material_sku_name,
              </if>
              <if test="materialSkuWeight != null">
				  material_sku_weight,
              </if>
              <if test="materialSkuUnit != null">
				  material_sku_unit,
              </if>
              <if test="materialSkuUnitDesc != null">
				  material_sku_unit_desc,
              </if>
              <if test="materialSkuRatioNum != null">
				  material_sku_ratio_num,
              </if>
              <if test="productSkuCode != null">
				  product_sku_code,
              </if>
              <if test="productSkuName != null">
				  product_sku_name,
              </if>
              <if test="productSkuWeight != null">
				  product_sku_weight,
              </if>
              <if test="productSkuUnit != null">
				  product_sku_unit,
              </if>
              <if test="productSkuUnitDesc != null">
				  product_sku_unit_desc,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="updater != null">
				  updater,
              </if>
              <if test="deleteFlag != null">
				  delete_flag,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="processingConfigId != null">
				#{processingConfigId,jdbcType=NUMERIC},
              </if>
              <if test="materialSkuCode != null">
				#{materialSkuCode,jdbcType=VARCHAR},
              </if>
              <if test="materialSkuName != null">
				#{materialSkuName,jdbcType=VARCHAR},
              </if>
              <if test="materialSkuWeight != null">
				#{materialSkuWeight,jdbcType=DOUBLE},
              </if>
              <if test="materialSkuUnit != null">
				#{materialSkuUnit,jdbcType=VARCHAR},
              </if>
              <if test="materialSkuUnitDesc != null">
				#{materialSkuUnitDesc,jdbcType=VARCHAR},
              </if>
              <if test="materialSkuRatioNum != null">
				#{materialSkuRatioNum,jdbcType=INTEGER},
              </if>
              <if test="productSkuCode != null">
				#{productSkuCode,jdbcType=VARCHAR},
              </if>
              <if test="productSkuName != null">
				#{productSkuName,jdbcType=VARCHAR},
              </if>
              <if test="productSkuWeight != null">
				#{productSkuWeight,jdbcType=DOUBLE},
              </if>
              <if test="productSkuUnit != null">
				#{productSkuUnit,jdbcType=VARCHAR},
              </if>
              <if test="productSkuUnitDesc != null">
				#{productSkuUnitDesc,jdbcType=VARCHAR},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="updater != null">
				#{updater,jdbcType=VARCHAR},
              </if>
              <if test="deleteFlag != null">
				#{deleteFlag,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_material_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
                create_time,
                update_time,
                processing_config_id,
                material_sku_code,
                material_sku_name,
                material_sku_weight,
                material_sku_unit,
                material_sku_unit_desc,
                material_sku_ratio_num,
                product_sku_code,
                product_sku_name,
                product_sku_weight,
                product_sku_unit,
                product_sku_unit_desc,
                creator,
                updater,
                delete_flag,
        </trim>
        values
        <foreach collection="entities" item="entity" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                    #{entity.createTime,jdbcType=TIMESTAMP},
                    #{entity.updateTime,jdbcType=TIMESTAMP},
                    #{entity.processingConfigId,jdbcType=NUMERIC},
                    #{entity.materialSkuCode,jdbcType=VARCHAR},
                    #{entity.materialSkuName,jdbcType=VARCHAR},
                    #{entity.materialSkuWeight,jdbcType=DOUBLE},
                    #{entity.materialSkuUnit,jdbcType=VARCHAR},
                    #{entity.materialSkuUnitDesc,jdbcType=VARCHAR},
                    #{entity.materialSkuRatioNum,jdbcType=INTEGER},
                    #{entity.productSkuCode,jdbcType=VARCHAR},
                    #{entity.productSkuName,jdbcType=VARCHAR},
                    #{entity.productSkuWeight,jdbcType=DOUBLE},
                    #{entity.productSkuUnit,jdbcType=VARCHAR},
                    #{entity.productSkuUnitDesc,jdbcType=VARCHAR},
                    #{entity.creator,jdbcType=VARCHAR},
                    #{entity.updater,jdbcType=VARCHAR},
                    #{entity.deleteFlag,jdbcType=TINYINT},
            </trim>
        </foreach>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingMaterialConfig" >
        UPDATE wms_processing_material_config t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingMaterialConfig" >
        DELETE FROM wms_processing_material_config t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>

	<!-- 根据主键ID进行批量物理删除 -->
	<delete id="batchRemove" parameterType="java.util.List" >
        DELETE FROM wms_processing_material_config t
		WHERE t.id IN
        <foreach item="item" collection="list" index="index" open="("
                 separator="," close=")">
			#{item}
        </foreach>
    </delete>

    <!--通过加工规则配置id删除-->
    <update id="updateDeletedByProcessingConfigId">
        update
            wms_processing_material_config
        set delete_flag = 1
        <where>
            processing_config_id = #{processingConfigId}
            <if test="processingConfigId == null">
                1=2
            </if>
        </where>
    </update>

</mapper>