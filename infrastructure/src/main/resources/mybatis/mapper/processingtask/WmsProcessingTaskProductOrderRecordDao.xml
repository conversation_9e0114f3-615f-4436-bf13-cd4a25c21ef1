<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingTaskProductOrderRecordDao">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskProductOrderRecordDO" id="WmsProcessingTaskProductOrderRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="processingTaskCode" column="processing_task_code" jdbcType="VARCHAR"/>
        <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
        <result property="processingTaskProductId" column="processing_task_product_id" jdbcType="INTEGER"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="productSkuName" column="product_sku_name" jdbcType="VARCHAR"/>
        <result property="productSkuNeedQuantity" column="product_sku_need_quantity" jdbcType="INTEGER"/>
        <result property="productSkuSpecWeight" column="product_sku_spec_weight" />
        <result property="productSkuSpecUnit" column="product_sku_spec_unit" jdbcType="VARCHAR"/>
        <result property="productSkuSpecNeedQuantity" column="product_sku_spec_need_quantity" jdbcType="INTEGER"/>
        <result property="productSkuSpecSubmitQuantity" column="product_sku_spec_submit_quantity" jdbcType="INTEGER"/>
        <result property="productSkuSpecSubmitWeight" column="product_sku_spec_submit_weight" />
        <result property="productSkuSpecPrintNumber" column="product_sku_spec_print_number" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="WmsProcessingTaskProductOrderRecordMap">
        select
          id, warehouse_no, processing_task_code, source_id, processing_task_product_id, product_sku_code, product_sku_name, product_sku_need_quantity, product_sku_spec_weight, product_sku_spec_unit, product_sku_spec_need_quantity, product_sku_spec_submit_quantity, product_sku_spec_submit_weight, product_sku_spec_print_number, creator, create_time, updater, update_time, delete_flag
        from wms_processing_task_product_order_record
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="WmsProcessingTaskProductOrderRecordMap">
        select
          id, warehouse_no, processing_task_code, source_id, processing_task_product_id, product_sku_code, product_sku_name, product_sku_need_quantity, product_sku_spec_weight, product_sku_spec_unit, product_sku_spec_need_quantity, product_sku_spec_submit_quantity, product_sku_spec_submit_weight, product_sku_spec_print_number, creator, create_time, updater, update_time, delete_flag
        from wms_processing_task_product_order_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="sourceId != null and sourceId != ''">
                and source_id = #{sourceId}
            </if>
            <if test="processingTaskProductId != null">
                and processing_task_product_id = #{processingTaskProductId}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuSpecWeight != null and productSkuSpecWeight != ''">
                and product_sku_spec_weight = #{productSkuSpecWeight}
            </if>
            <if test="productSkuSpecUnit != null and productSkuSpecUnit != ''">
                and product_sku_spec_unit = #{productSkuSpecUnit}
            </if>
            <if test="productSkuSpecNeedQuantity != null">
                and product_sku_spec_need_quantity = #{productSkuSpecNeedQuantity}
            </if>
            <if test="productSkuSpecSubmitQuantity != null">
                and product_sku_spec_submit_quantity = #{productSkuSpecSubmitQuantity}
            </if>
            <if test="productSkuSpecSubmitWeight != null">
                and product_sku_spec_submit_weight = #{productSkuSpecSubmitWeight}
            </if>
            <if test="productSkuSpecPrintNumber != null">
                and product_sku_spec_print_number = #{productSkuSpecPrintNumber}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_processing_task_product_order_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="sourceId != null and sourceId != ''">
                and source_id = #{sourceId}
            </if>
            <if test="processingTaskProductId != null">
                and processing_task_product_id = #{processingTaskProductId}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                and product_sku_name = #{productSkuName}
            </if>
            <if test="productSkuSpecWeight != null and productSkuSpecWeight != ''">
                and product_sku_spec_weight = #{productSkuSpecWeight}
            </if>
            <if test="productSkuSpecUnit != null and productSkuSpecUnit != ''">
                and product_sku_spec_unit = #{productSkuSpecUnit}
            </if>
            <if test="productSkuSpecNeedQuantity != null">
                and product_sku_spec_need_quantity = #{productSkuSpecNeedQuantity}
            </if>
            <if test="productSkuSpecSubmitQuantity != null">
                and product_sku_spec_submit_quantity = #{productSkuSpecSubmitQuantity}
            </if>
            <if test="productSkuSpecSubmitWeight != null">
                and product_sku_spec_submit_weight = #{productSkuSpecSubmitWeight}
            </if>
            <if test="productSkuSpecPrintNumber != null">
                and product_sku_spec_print_number = #{productSkuSpecPrintNumber}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_product_order_record(warehouse_no, processing_task_code, source_id, processing_task_product_id, product_sku_code, product_sku_name, product_sku_need_quantity, product_sku_spec_weight, product_sku_spec_unit, product_sku_spec_need_quantity, product_sku_spec_submit_quantity, product_sku_spec_submit_weight, product_sku_spec_print_number, creator, create_time, updater, update_time, delete_flag)
        values (#{warehouseNo}, #{processingTaskCode}, #{sourceId}, #{processingTaskProductId}, #{productSkuCode}, #{productSkuName}, #{productSkuNeedQuantity}, #{productSkuSpecWeight}, #{productSkuSpecUnit}, #{productSkuSpecNeedQuantity}, #{productSkuSpecSubmitQuantity}, #{productSkuSpecSubmitWeight}, #{productSkuSpecPrintNumber}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_product_order_record(warehouse_no, processing_task_code, source_id, processing_task_product_id, product_sku_code, product_sku_name, product_sku_need_quantity, product_sku_spec_weight, product_sku_spec_unit, product_sku_spec_need_quantity, product_sku_spec_submit_quantity, product_sku_spec_submit_weight, product_sku_spec_print_number, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.sourceId}, #{entity.processingTaskProductId}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuNeedQuantity}, #{entity.productSkuSpecWeight}, #{entity.productSkuSpecUnit}, #{entity.productSkuSpecNeedQuantity}, #{entity.productSkuSpecSubmitQuantity}, #{entity.productSkuSpecSubmitWeight}, #{entity.productSkuSpecPrintNumber}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_product_order_record(warehouse_no, processing_task_code, source_id, processing_task_product_id, product_sku_code, product_sku_name, product_sku_need_quantity, product_sku_spec_weight, product_sku_spec_unit, product_sku_spec_need_quantity, product_sku_spec_submit_quantity, product_sku_spec_submit_weight, product_sku_spec_print_number, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.sourceId}, #{entity.processingTaskProductId}, #{entity.productSkuCode}, #{entity.productSkuName}, #{entity.productSkuNeedQuantity}, #{entity.productSkuSpecWeight}, #{entity.productSkuSpecUnit}, #{entity.productSkuSpecNeedQuantity}, #{entity.productSkuSpecSubmitQuantity}, #{entity.productSkuSpecSubmitWeight}, #{entity.productSkuSpecPrintNumber}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
        warehouse_no = values(warehouse_no),
        processing_task_code = values(processing_task_code),
        source_id = values(source_id),
        processing_task_product_id = values(processing_task_product_id),
        product_sku_code = values(product_sku_code),
        product_sku_name = values(product_sku_name),
        product_sku_need_quantity = values(product_sku_need_quantity),
        product_sku_spec_weight = values(product_sku_spec_weight),
        product_sku_spec_unit = values(product_sku_spec_unit),
        product_sku_spec_need_quantity = values(product_sku_spec_need_quantity),
        product_sku_spec_submit_quantity = values(product_sku_spec_submit_quantity),
        product_sku_spec_submit_weight = values(product_sku_spec_submit_weight),
        product_sku_spec_print_number = values(product_sku_spec_print_number),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_processing_task_product_order_record
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                processing_task_code = #{processingTaskCode},
            </if>
            <if test="sourceId != null and sourceId != ''">
                source_id = #{sourceId},
            </if>
            <if test="processingTaskProductId != null">
                processing_task_product_id = #{processingTaskProductId},
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                product_sku_code = #{productSkuCode},
            </if>
            <if test="productSkuName != null and productSkuName != ''">
                product_sku_name = #{productSkuName},
            </if>
            <if test="productSkuSpecWeight != null and productSkuSpecWeight != ''">
                product_sku_spec_weight = #{productSkuSpecWeight},
            </if>
            <if test="productSkuSpecUnit != null and productSkuSpecUnit != ''">
                product_sku_spec_unit = #{productSkuSpecUnit},
            </if>
            <if test="productSkuSpecNeedQuantity != null">
                product_sku_spec_need_quantity = #{productSkuSpecNeedQuantity},
            </if>
            <if test="productSkuSpecSubmitQuantity != null">
                product_sku_spec_submit_quantity = #{productSkuSpecSubmitQuantity},
            </if>
            <if test="productSkuSpecSubmitWeight != null">
                product_sku_spec_submit_weight = #{productSkuSpecSubmitWeight},
            </if>
            <if test="productSkuSpecPrintNumber != null">
                product_sku_spec_print_number = #{productSkuSpecPrintNumber},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_processing_task_product_order_record where id = #{id}
    </delete>

    <update id="submitQuantity">
        update wms_processing_task_product_order_record
        <set>
            product_sku_spec_submit_quantity = product_sku_spec_submit_quantity + #{submitQuantity},
            product_sku_spec_submit_weight = product_sku_spec_submit_weight + #{submitWeight},
            updater = #{updater},
            update_time = now()
        </set>
        where id = #{id}
        and product_sku_spec_need_quantity >= product_sku_spec_submit_quantity + #{submitQuantity}
    </update>

    <select id="querySourceIdList" resultMap="WmsProcessingTaskProductOrderRecordMap">
        select
            id, warehouse_no, processing_task_code, source_id, processing_task_product_id, product_sku_code, product_sku_name, product_sku_need_quantity, product_sku_spec_weight, product_sku_spec_unit, product_sku_spec_need_quantity, product_sku_spec_submit_quantity, product_sku_spec_submit_weight, product_sku_spec_print_number, creator, create_time, updater, update_time, delete_flag
        from wms_processing_task_product_order_record
        where delete_flag = 0 and source_id in
        <foreach collection="sourceIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

</mapper>

