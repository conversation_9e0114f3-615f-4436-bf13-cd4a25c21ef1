<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.processingtask.WmsProcessingTaskMaterialRestoreRecordDao">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.processingtask.dataobject.WmsProcessingTaskMaterialRestoreRecordDO" id="WmsProcessingTaskMaterialRestoreRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="processingTaskCode" column="processing_task_code" jdbcType="VARCHAR"/>
        <result property="processingTaskProductId" column="processing_task_product_id" jdbcType="INTEGER"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="materialSkuCode" column="material_sku_code" jdbcType="VARCHAR"/>
        <result property="materialSkuName" column="material_sku_name" jdbcType="VARCHAR"/>
        <result property="materialSkuWeight" column="material_sku_weight" />
        <result property="materialSkuUnit" column="material_sku_unit" jdbcType="VARCHAR"/>
        <result property="materialSkuPurchaseBatch" column="material_sku_purchase_batch" jdbcType="VARCHAR"/>
        <result property="materialSkuRestoreQuantity" column="material_sku_restore_quantity" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="materialSkuProductionDate" column="material_sku_production_date" />
        <result property="materialSkuQualityDate" column="material_sku_quality_date" />
        <result property="processingTaskMaterialId" column="processing_task_material_id" />
        <result property="materialSkuCabinetCode" column="material_sku_cabinet_code" />
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="WmsProcessingTaskMaterialRestoreRecordMap">
        select
          id, warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_purchase_batch, material_sku_restore_quantity, creator, create_time, updater, update_time, delete_flag, material_sku_production_date, material_sku_quality_date, processing_task_material_id, material_sku_cabinet_code
        from wms_processing_task_material_restore_record
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="WmsProcessingTaskMaterialRestoreRecordMap">
        select
          id, warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_purchase_batch, material_sku_restore_quantity, creator, create_time, updater, update_time, delete_flag, material_sku_production_date, material_sku_quality_date, processing_task_material_id, material_sku_cabinet_code
        from wms_processing_task_material_restore_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="processingTaskProductId != null">
                and processing_task_product_id = #{processingTaskProductId}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="materialSkuWeight != null">
                and material_sku_weight = #{materialSkuWeight}
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                and material_sku_unit = #{materialSkuUnit}
            </if>
            <if test="materialSkuPurchaseBatch != null and materialSkuPurchaseBatch != ''">
                and material_sku_purchase_batch = #{materialSkuPurchaseBatch}
            </if>
            <if test="materialSkuRestoreQuantity != null">
                and material_sku_restore_quantity = #{materialSkuRestoreQuantity}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="materialSkuProductionDate != null">
                and material_sku_production_date = #{materialSkuProductionDate}
            </if>
            <if test="materialSkuQualityDate != null">
                and material_sku_quality_date = #{materialSkuQualityDate}
            </if>
            <if test="processingTaskMaterialId != null">
                and processing_task_material_id = #{processingTaskMaterialId}
            </if>
            <if test="materialSkuCabinetCode != null">
                and material_sku_cabinet_code = #{materialSkuCabinetCode}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from wms_processing_task_material_restore_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                and processing_task_code = #{processingTaskCode}
            </if>
            <if test="processingTaskProductId != null">
                and processing_task_product_id = #{processingTaskProductId}
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                and product_sku_code = #{productSkuCode}
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                and material_sku_code = #{materialSkuCode}
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                and material_sku_name = #{materialSkuName}
            </if>
            <if test="materialSkuWeight != null">
                and material_sku_weight = #{materialSkuWeight}
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                and material_sku_unit = #{materialSkuUnit}
            </if>
            <if test="materialSkuPurchaseBatch != null and materialSkuPurchaseBatch != ''">
                and material_sku_purchase_batch = #{materialSkuPurchaseBatch}
            </if>
            <if test="materialSkuRestoreQuantity != null">
                and material_sku_restore_quantity = #{materialSkuRestoreQuantity}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
            <if test="materialSkuProductionDate != null">
                and material_sku_production_date = #{materialSkuProductionDate}
            </if>
            <if test="materialSkuQualityDate != null">
                and material_sku_quality_date = #{materialSkuQualityDate}
            </if>
            <if test="processingTaskMaterialId != null">
                and processing_task_material_id = #{processingTaskMaterialId}
            </if>
            <if test="materialSkuCabinetCode != null">
                and material_sku_cabinet_code = #{materialSkuCabinetCode}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_material_restore_record(warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_purchase_batch, material_sku_restore_quantity, creator, create_time, updater, update_time, delete_flag, material_sku_production_date, material_sku_quality_date, processing_task_material_id, material_sku_cabinet_code)
        values (#{warehouseNo}, #{processingTaskCode}, #{processingTaskProductId}, #{productSkuCode}, #{materialSkuCode}, #{materialSkuName}, #{materialSkuWeight}, #{materialSkuUnit}, #{materialSkuPurchaseBatch}, #{materialSkuRestoreQuantity}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag}, #{materialSkuProductionDate}, #{materialSkuQualityDate}, #{processingTaskMaterialId}, #{materialSkuCabinetCode})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_material_restore_record(warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_purchase_batch, material_sku_restore_quantity, creator, create_time, updater, update_time, delete_flag, material_sku_production_date, material_sku_quality_date, processing_task_material_id, material_sku_cabinet_code)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.processingTaskProductId}, #{entity.productSkuCode}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.materialSkuPurchaseBatch}, #{entity.materialSkuRestoreQuantity}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.materialSkuProductionDate}, #{entity.materialSkuQualityDate}, #{entity.processingTaskMaterialId}, #{entity.materialSkuCabinetCode})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into wms_processing_task_material_restore_record(warehouse_no, processing_task_code, processing_task_product_id, product_sku_code, material_sku_code, material_sku_name, material_sku_weight, material_sku_unit, material_sku_purchase_batch, material_sku_restore_quantity, creator, create_time, updater, update_time, delete_flag, material_sku_production_date, material_sku_quality_date, processing_task_material_id, material_sku_cabinet_code)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.warehouseNo}, #{entity.processingTaskCode}, #{entity.processingTaskProductId}, #{entity.productSkuCode}, #{entity.materialSkuCode}, #{entity.materialSkuName}, #{entity.materialSkuWeight}, #{entity.materialSkuUnit}, #{entity.materialSkuPurchaseBatch}, #{entity.materialSkuRestoreQuantity}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.materialSkuProductionDate}, #{entity.materialSkuQualityDate}, #{entity.processingTaskMaterialId}, #{entity.materialSkuCabinetCode})
        </foreach>
        on duplicate key update
        warehouse_no = values(warehouse_no),
        processing_task_code = values(processing_task_code),
        processing_task_product_id = values(processing_task_product_id),
        product_sku_code = values(product_sku_code),
        material_sku_code = values(material_sku_code),
        material_sku_name = values(material_sku_name),
        material_sku_weight = values(material_sku_weight),
        material_sku_unit = values(material_sku_unit),
        material_sku_purchase_batch = values(material_sku_purchase_batch),
        material_sku_restore_quantity = values(material_sku_restore_quantity),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag),
        material_sku_production_date = values(material_sku_production_date),
        material_sku_quality_date = values(material_sku_quality_date),
        processing_task_material_id = values(processing_task_material_id),
        material_sku_cabinet_code = values(material_sku_cabinet_code)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update wms_processing_task_material_restore_record
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="processingTaskCode != null and processingTaskCode != ''">
                processing_task_code = #{processingTaskCode},
            </if>
            <if test="processingTaskProductId != null">
                processing_task_product_id = #{processingTaskProductId},
            </if>
            <if test="productSkuCode != null and productSkuCode != ''">
                product_sku_code = #{productSkuCode},
            </if>
            <if test="materialSkuCode != null and materialSkuCode != ''">
                material_sku_code = #{materialSkuCode},
            </if>
            <if test="materialSkuName != null and materialSkuName != ''">
                material_sku_name = #{materialSkuName},
            </if>
            <if test="materialSkuWeight != null">
                material_sku_weight = #{materialSkuWeight},
            </if>
            <if test="materialSkuUnit != null and materialSkuUnit != ''">
                material_sku_unit = #{materialSkuUnit},
            </if>
            <if test="materialSkuPurchaseBatch != null and materialSkuPurchaseBatch != ''">
                material_sku_purchase_batch = #{materialSkuPurchaseBatch},
            </if>
            <if test="materialSkuRestoreQuantity != null">
                material_sku_restore_quantity = #{materialSkuRestoreQuantity},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="materialSkuProductionDate != null">
                material_sku_production_date = #{materialSkuProductionDate},
            </if>
            <if test="materialSkuQualityDate != null">
                material_sku_quality_date = #{materialSkuQualityDate},
            </if>
            <if test="processingTaskMaterialId != null">
                processing_task_material_id = #{processingTaskMaterialId},
            </if>
            <if test="materialSkuCabinetCode != null">
                and material_sku_cabinet_code = #{materialSkuCabinetCode}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from wms_processing_task_material_restore_record where id = #{id}
    </delete>

</mapper>

