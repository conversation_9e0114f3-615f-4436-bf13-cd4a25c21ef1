<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionSourcePropertyDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSourcePropertyDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="source_order_no" jdbcType="VARCHAR" property="sourceOrderNo"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="mission_type" jdbcType="INTEGER" property="missionType"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="store_no" jdbcType="BIGINT" property="storeNo"/>
        <result column="target_warehouse_no" jdbcType="BIGINT" property="targetWarehouseNo"/>
        <result column="expect_time" jdbcType="DATE" property="expectTime"/>
        <result column="business_tag" jdbcType="INTEGER" property="businessTag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, source_order_no, source_type, source_id, mission_no,
    mission_type, warehouse_no, store_no, expect_time, target_warehouse_no , business_tag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_source_property
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByMissionNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_source_property
        where mission_no = #{missionNo}
    </select>
    <select id="selectBySourceIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_source_property
        where source_id = #{sourceId} and source_type = #{sourceType} and mission_type = #{missionType}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_mission_source_property
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSourcePropertyDO"
            useGeneratedKeys="true">
        insert into wms_mission_source_property (create_time, update_time, source_order_no,
                                                 source_type, source_id, mission_no,
                                                 mission_type, warehouse_no, store_no, target_warehouse_no, business_tag)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{sourceOrderNo,jdbcType=VARCHAR},
                #{sourceType,jdbcType=INTEGER}, #{sourceId,jdbcType=VARCHAR}, #{missionNo,jdbcType=VARCHAR},
                #{missionType,jdbcType=INTEGER}, #{warehouseNo,jdbcType=BIGINT}, #{storeNo,jdbcType=BIGINT},
                #{targetWarehouseNo,jdbcType=BIGINT}, #{businessTag,jdbcType=INTEGER})
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSourcePropertyDO"
            useGeneratedKeys="true">
        insert into wms_mission_source_property (source_order_no,
        source_type, source_id, mission_no,
        mission_type, warehouse_no, store_no, expect_time,
        target_warehouse_no,business_tag
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.sourceOrderNo,jdbcType=VARCHAR},
            #{item.sourceType,jdbcType=INTEGER}, #{item.sourceId,jdbcType=VARCHAR}, #{item.missionNo,jdbcType=VARCHAR},
            #{item.missionType,jdbcType=INTEGER}, #{item.warehouseNo,jdbcType=BIGINT}, #{item.storeNo,jdbcType=BIGINT},
            #{item.expectTime,jdbcType=DATE},#{item.targetWarehouseNo,jdbcType=BIGINT},#{item.businessTag,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSourcePropertyDO"
            useGeneratedKeys="true">
        insert into wms_mission_source_property
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="sourceOrderNo != null">
                source_order_no,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="sourceId != null">
                source_id,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="missionType != null">
                mission_type,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="targetWarehouseNo != null">
                target_warehouse_no,
            </if>
            <if test="businessTag != null">
                business_tag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sourceOrderNo != null">
                #{sourceOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceId != null">
                #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                #{missionType,jdbcType=INTEGER},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=BIGINT},
            </if>
            <if test="expectTime != null">
                #{expectTime,jdbcType=DATE},
            </if>
            <if test="targetWarehouseNo != null">
                #{targetWarehouseNo,jdbcType=BIGINT},
            </if>
            <if test="businessTag != null">
                #{businessTag,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSourcePropertyDO">
        update wms_mission_source_property
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sourceOrderNo != null">
                source_order_no = #{sourceOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceId != null">
                source_id = #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=INTEGER},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=BIGINT},
            </if>
            <if test="expectTime != null">
                expect_time = #{expectTime,jdbcType=DATE},
            </if>
            <if test="businessTag != null">
                business_tag = #{businessTag,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSourcePropertyDO">
        update wms_mission_source_property
        set create_time     = #{createTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            source_order_no = #{sourceOrderNo,jdbcType=VARCHAR},
            source_type     = #{sourceType,jdbcType=INTEGER},
            source_id       = #{sourceId,jdbcType=VARCHAR},
            mission_no      = #{missionNo,jdbcType=VARCHAR},
            mission_type    = #{missionType,jdbcType=INTEGER},
            warehouse_no    = #{warehouseNo,jdbcType=BIGINT},
            store_no        = #{storeNo,jdbcType=BIGINT},
            expect_time     = #{expectTime,javaType=DATE},
            business_tag    = #{businessTag,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByMissionNoList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_source_property
        where mission_no in
        <foreach collection="missionNoList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>