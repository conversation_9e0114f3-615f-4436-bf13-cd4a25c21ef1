<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cancel_time" jdbcType="BIGINT" property="cancelTime"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="source_order_no" jdbcType="VARCHAR" property="sourceOrderNo"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="mission_type" jdbcType="INTEGER" property="missionType"/>
        <result column="p_mission_type" jdbcType="INTEGER" property="pMissionType"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="p_mission_no" jdbcType="VARCHAR" property="pMissionNo"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId"/>
        <result column="operator_id" jdbcType="VARCHAR" property="operatorId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, cancel_time, creator_name, `state`, source_order_no,
    source_type, source_id, mission_type, mission_no, warehouse_no, p_mission_no, operator_name, 
    creator_id, operator_id, p_mission_type, tenant_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_mission
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectByPMissionNo" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission
        where p_mission_no = #{pMissionNo} and p_mission_type = #{pMissionType}
    </select>

    <select id="selectByPMissionNoList" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission
        where p_mission_type = #{pMissionType}
        and p_mission_no in
        <foreach collection="pMissionNoList" item="pMissionNo" open="(" close=")" separator=",">
            #{pMissionNo}
        </foreach>
        and mission_type = #{missionType}
    </select>

    <select id="missionCountByState"
            resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionStateCountDO">
        /*FORCE_MASTER*/ select
        state, count(*) num
        from wms_mission
        <where>
            warehouse_no = #{warehouseNo}
            and mission_type = #{missionType}
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
        group by state
    </select>

    <select id="selectByMissionNo" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission
        where mission_no = #{missionNo}
    </select>

    <select id="selectByMissionNos" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission
        where mission_no in
        <foreach collection="missionNos" item="missionNo" open="(" close=")" separator=",">
            #{missionNo}
        </foreach>
        order by id desc
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO" useGeneratedKeys="true">
        insert into wms_mission (create_time, update_time, cancel_time,
                                 creator_name, `state`, source_order_no,
                                 source_type, source_id, mission_type,
                                 mission_no, warehouse_no, p_mission_no,
                                 operator_name, creator_id, operator_id, p_mission_type, tenant_id)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{cancelTime,jdbcType=BIGINT},
                #{creatorName,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, #{sourceOrderNo,jdbcType=VARCHAR},
                #{sourceType,jdbcType=INTEGER}, #{sourceId,jdbcType=VARCHAR}, #{missionType,jdbcType=INTEGER},
                #{missionNo,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=BIGINT}, #{pMissionNo,jdbcType=VARCHAR},
                #{operatorName,jdbcType=VARCHAR}, #{creatorId,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR},
                #{pMissionType,jdbcType=INTEGER}, #{tenantId})
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO" useGeneratedKeys="true">
        insert into wms_mission ( cancel_time,
        creator_name, `state`, source_order_no,
        source_type, source_id, mission_type,
        mission_no, warehouse_no, p_mission_no,
        operator_name, creator_id, operator_id, p_mission_type, tenant_id)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.cancelTime,jdbcType=BIGINT},
            #{item.creatorName,jdbcType=VARCHAR}, #{item.state,jdbcType=INTEGER},
            #{item.sourceOrderNo,jdbcType=VARCHAR},
            #{item.sourceType,jdbcType=INTEGER}, #{item.sourceId,jdbcType=VARCHAR},
            #{item.missionType,jdbcType=INTEGER},
            #{item.missionNo,jdbcType=VARCHAR}, #{item.warehouseNo,jdbcType=BIGINT},
            #{item.pMissionNo,jdbcType=VARCHAR},
            #{item.operatorName,jdbcType=VARCHAR}, #{item.creatorId,jdbcType=VARCHAR},
            #{item.operatorId,jdbcType=VARCHAR},
            #{item.pMissionType,jdbcType=INTEGER}, #{item.tenantId})
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO" useGeneratedKeys="true">
        insert into wms_mission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="cancelTime != null">
                cancel_time,
            </if>
            <if test="creatorName != null">
                creator_name,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="sourceOrderNo != null">
                source_order_no,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="sourceId != null">
                source_id,
            </if>
            <if test="missionType != null">
                mission_type,
            </if>
            <if test="pMissionType != null">
                p_mission_type,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="pMissionNo != null">
                p_mission_no,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTime != null">
                #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="sourceOrderNo != null">
                #{sourceOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceId != null">
                #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                #{missionType,jdbcType=INTEGER},
            </if>
            <if test="pMissionType != null">
                #{pMissionType,jdbcType=INTEGER},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="pMissionNo != null">
                #{pMissionNo,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO">
        update wms_mission
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="sourceOrderNo != null">
                source_order_no = #{sourceOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceId != null">
                source_id = #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=INTEGER},
            </if>
            <if test="pMissionType != null">
                p_mission_type = #{pMissionType,jdbcType=INTEGER},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="pMissionNo != null">
                p_mission_no = #{pMissionNo,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeySelectiveByMissionNo"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO">
        update wms_mission
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="sourceOrderNo != null">
                source_order_no = #{sourceOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceId != null">
                source_id = #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=INTEGER},
            </if>
            <if test="pMissionType != null">
                p_mission_type = #{pMissionType,jdbcType=INTEGER},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="pMissionNo != null">
                p_mission_no = #{pMissionNo,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
        </set>
        where mission_no = #{missionNo}
    </update>

    <update id="updateStateByMissionNo"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO">
        update wms_mission
        set `state` = #{state,jdbcType=INTEGER}
        where mission_no = #{missionNo}
    </update>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO">
        update wms_mission
        set create_time     = #{createTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            cancel_time     = #{cancelTime,jdbcType=BIGINT},
            creator_name    = #{creatorName,jdbcType=VARCHAR},
            `state`         = #{state,jdbcType=INTEGER},
            source_order_no = #{sourceOrderNo,jdbcType=VARCHAR},
            source_type     = #{sourceType,jdbcType=INTEGER},
            source_id       = #{sourceId,jdbcType=VARCHAR},
            mission_type    = #{missionType,jdbcType=INTEGER},
            p_mission_type  = #{pMissionType,jdbcType=INTEGER},
            mission_no      = #{missionNo,jdbcType=VARCHAR},
            warehouse_no    = #{warehouseNo,jdbcType=BIGINT},
            p_mission_no    = #{pMissionNo,jdbcType=VARCHAR},
            operator_name   = #{operatorName,jdbcType=VARCHAR},
            creator_id      = #{creatorId,jdbcType=VARCHAR},
            operator_id     = #{operatorId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="pcPage" resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.mission_no missionNo
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_execute_unit weu on wm.mission_no = weu.mission_no
        </if>
        <if test="storeNo != null or sourceOrderNo != null or bizType != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <if test="sku != null ">
            inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="storeNo != null">
                and wmsp.store_no = #{storeNo}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm.mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sku != null">
                and wmd.sku = #{sku}
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and weu.carrier_code = #{containerNo}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
        </where>
        group by wm.mission_no
        order by wm.mission_no desc
    </select>

    <select id="pcStocktakingPage" resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.mission_no missionNo
        from wms_mission wm
        <if test="sourceOrderNo != null and sourceOrderNo != '' or bizType != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <if test="sku != null and sku != ''">
            left join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null and operatorName != ''">
            left join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="sourceOrderNo != null and sourceOrderNo != ''">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null and missionNo != ''">
                and wm.mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sku != null and sku != ''">
                and wmd.deleted_at = 0
                and wmd.sku = #{sku}
            </if>
            <if test="operatorName != null and operatorName != ''">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
        </where>
        group by wm.mission_no
        order by wm.mission_no desc
    </select>

    <select id="pcPageByPick" resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.mission_no missionNo
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_submit_detail wsd on wm.mission_no = wsd.mission_no
        </if>
        <if test="storeNo != null or sourceOrderNo != null or bizType != null or sourceId != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <if test="sku != null ">
            inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="storeNo != null">
                and wmsp.store_no = #{storeNo}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceId != null">
                and wmsp.source_id = #{sourceId}
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm.mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sku != null">
                and wmd.deleted_at = 0
                and wmd.sku = #{sku}
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and wsd.target_carrier_type = 10
                and wsd.target_carrier_code = #{containerNo}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
        </where>
        group by wm.mission_no
        order by wm.mission_no desc
    </select>

    <select id="pcPageStateCount"
            resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionStateCountDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        tmp.state, count(tmp.mission_no) num
        from (
        select
        distinct wm.mission_no, wm.state
        from wms_mission wm
        left join wms_execute_unit weu on wm.mission_no = weu.mission_no
        <if test="sku != null ">
            left join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            left join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <if test="storeNo != null or sourceOrderNo != null or bizType != null or sourceId != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="storeNo != null">
                and wmsp.store_no = #{storeNo}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceId != null">
                and wmsp.source_id = #{sourceId}
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sku != null">
                and wmd.sku = #{sku}
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and weu.carrier_code = #{containerNo}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
        </where>
        group by wm.mission_no, wm.state
        ) as tmp group by tmp.state
    </select>

    <select id="pcPageStateCountV2"
            resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionStateCountDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.state, count(distinct wm.id) num
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_execute_unit weu on wm.mission_no = weu.mission_no
        </if>
        <if test="sku != null or skuList != null or batchNo != null">
            inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <if test="storeNo != null or sourceOrderNo != null or bizType != null or sourceId != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm.mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
            <if test="sku != null">
                and wmd.deleted_at = 0
                and wmd.sku = #{sku}
            </if>
            <if test="skuList != null and skuList.size() > 0">
                and wmd.deleted_at = 0
                AND wmd.`sku` in
                <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                    #{sku1}
                </foreach>
            </if>
            <if test="batchNo != null">
                and wmd.batch_no = #{batchNo}
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and weu.carrier_code = #{containerNo}
            </if>
            <if test="storeNo != null">
                and wmsp.store_no = #{storeNo}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceId != null">
                and wmsp.source_id = #{sourceId}
            </if>
        </where>
        group by wm.state
    </select>

    <select id="pcCrossWarehousePageStateCount"
            resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionStateCountDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.state, count(distinct wm.id) num
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_execute_unit weu on wm.mission_no = weu.mission_no
        </if>
        <if test="storeNo != null or sku != null ">
            inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <if test="sourceOrderNo != null or bizType != null or sourceId != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="storeNo != null">
                and wmd.split_info = #{storeNo}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceId != null">
                and wmsp.source_id = #{sourceId}
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sku != null">
                and wmd.deleted_at = 0
                and wmd.sku = #{sku}
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and weu.carrier_code = #{containerNo}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
        </where>
        group by wm.state
    </select>

    <select id="pcPickMissionPageStateCount"
            resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionStateCountDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.state, count(distinct wm.id) num
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_submit_detail wsd on wm.mission_no = wsd.mission_no
        </if>
        <if test="storeNo != null or sourceOrderNo != null or bizType != null or sourceId != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <if test="sku != null ">
            inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <where>
            wm.mission_type = 40
            <if test="warehouseNo != null">
                and wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="storeNo != null">
                and wmsp.store_no = #{storeNo}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceId != null">
                and wmsp.source_id = #{sourceId}
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm.mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sku != null">
                and wmd.deleted_at = 0
                and wmd.sku = #{sku}
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and wsd.target_carrier_type = 10
                and wsd.target_carrier_code = #{containerNo}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
        </where>
        group by wm.state
    </select>

    <select id="pdaPage" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        tem.id
        , tem.create_time, tem.update_time, tem.cancel_time, tem.creator_name, tem.`state`, tem.source_order_no,
        tem.source_type, tem.source_id, tem.mission_type, tem.mission_no, tem.warehouse_no, tem.p_mission_no,
        tem.operator_name,
        tem.creator_id, tem.operator_id, tem.p_mission_type, tem.tenant_id from (
        select
        wm.id
        , wm.create_time, wm.update_time, wm.cancel_time, wm.creator_name, wm.`state`, wmsp.source_order_no,
        wmsp.source_type, wmsp.source_id, wm.mission_type, wm.mission_no, wm.warehouse_no, wm.p_mission_no,
        wm.operator_name,
        wm.creator_id, wm.operator_id, wm.p_mission_type, wm.tenant_id
        from wms_mission wm
        left join wms_execute_unit weu on wm.mission_no = weu.mission_no
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        <if test="sku != null ">
            left join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            left join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        where
        wm.state in (20,30,40)
        and weu.state in (20)
        and wmsp.source_type not in (18,21)
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="operatorName != null">
            and wmo.operator_name = #{operatorName}
            and wmo.cancel_time = 0
        </if>
        <if test="containerNo != null">
            and weu.carrier_code = #{containerNo}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo} and wmsp.mission_no = wm.mission_no)
        </if>
        union all
        select
        wm.id
        , wm.create_time, wm.update_time, wm.cancel_time, wm.creator_name, wm.`state`, wmsp.source_order_no,
        wmsp.source_type, wmsp.source_id, wm.mission_type, wm.mission_no, wm.warehouse_no, wm.p_mission_no,
        wm.operator_name,
        wm.creator_id, wm.operator_id, wm.p_mission_type, wm.tenant_id
        from wms_mission wm
        left join wms_execute_unit weu on wm.mission_no = weu.mission_no
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        where wm.state = 10 and wm.warehouse_no = #{warehouseNo} and weu.state in (20)
        and wmsp.source_type not in (18,21)
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo} and wmsp.mission_no = wm.mission_no)
        </if>
        ) tem
        order by concat(tem.state,tem.id) desc
    </select>

    <select id="pdaStocktakingPage" resultType="java.lang.String">
        select wm.mission_no
        from wms_mission wm
        <if test="skus != null and skus.size()> 0 or (cabinetNo != null and cabinetNo != '')">
            inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        where wm.warehouse_no = #{warehouseNo}
        and wm.mission_type = 50
        and wm.state in (10,20,30,40)
        <if test="missionNo != null and missionNo != ''">
            and wm.mission_no = #{missionNo}
        </if>
        <if test="cabinetNo != null and cabinetNo != ''">
            and wmd.cabinet_no = #{cabinetNo}
        </if>
        <if test="skus != null and skus.size()> 0">
            and wmd.sku in
            <foreach collection="skus" item="sku" close=")" open="(" separator=",">
                #{sku}
            </foreach>
        </if>
        group by wm.mission_no, wm.state
        order by wm.id desc
    </select>

    <select id="pdaPickMissionPage" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.mission_no, wm.state, wmsp.expect_time
        from wms_mission wm
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        where wm.mission_type = #{missionType}
        and wm.warehouse_no = #{warehouseNo}
        and wm.state in
        <foreach collection="state" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="missionNo != null and missionNo.length > 0">
            and wm.mission_no = #{missionNo}
        </if>
        <if test="storeNo != null">
            and wmsp.store_no = #{storeNo}
        </if>
        <if test="storeNoList != null and storeNoList.size > 0">
            and wmsp.store_no in
            <foreach item="item" collection="storeNoList" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by wm.mission_no, wm.state, wmsp.expect_time
        order by wmsp.expect_time desc, wm.id desc
    </select>

    <select id="listMissionNo" resultType="java.lang.String">
        select mission_no
        from wms_mission
        where create_time >= #{day}
    </select>

    <select id="queryStoreNoList" resultType="java.lang.Long"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionStoreNoQueryDO">
        select
        distinct wmsp.store_no
        from
        wms_mission wm
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        where
        wm.warehouse_no = #{warehouseNo}
        and wm.mission_type = #{missionType}
        and wmsp.store_no is not null
        <if test="stateList != null and stateList.size > 0">
            and wm.state in
            <foreach collection="stateList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="selectByMission" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO">
        select *
        from wms_mission
        where
        warehouse_no = #{warehouseNo}
        <if test="missionNo != null and missionNo.length > 0">
            and mission_no = #{missionNo}
        </if>
        <if test="missionType != null">
            and mission_type = #{missionType}
        </if>
    </select>

    <select id="pcCrossWarehousePage" resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.mission_no missionNo
        from wms_mission wm
        left join wms_execute_unit weu on wm.mission_no = weu.mission_no
        <if test="storeNo != null or sourceOrderNo != null or bizType != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        <if test="sku != null or storeNo != null">
            left join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            left join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="storeNo != null">
                and wmd.split_info = #{storeNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm.mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sku != null">
                and wmd.sku = #{sku}
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and weu.carrier_code = #{containerNo}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
        </where>
        group by wm.mission_no
        order by wm.id desc
    </select>

    <select id="pdaCrossWarehousePage" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        tem.id
        , tem.create_time, tem.update_time, tem.cancel_time, tem.creator_name, tem.`state`, tem.source_order_no,
        tem.source_type, tem.source_id, tem.mission_type, tem.mission_no, tem.warehouse_no, tem.p_mission_no,
        tem.operator_name,
        tem.creator_id, tem.operator_id, tem.p_mission_type, tem.tenant_id from (
        select
        wm.id
        , wm.create_time, wm.update_time, wm.cancel_time, wm.creator_name, wm.`state`, wmsp.source_order_no,
        wmsp.source_type, wmsp.source_id, wm.mission_type, wm.mission_no, wm.warehouse_no, wm.p_mission_no,
        wm.operator_name,
        wm.creator_id, wm.operator_id, wm.p_mission_type, wm.tenant_id
        from wms_mission wm
        left join wms_execute_unit weu on wm.mission_no = weu.mission_no
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        <if test="sku != null ">
            left join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        </if>
        <if test="operatorName != null">
            left join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        where
        wm.state in (20,30,40)
        and weu.state in (20)
        and wmsp.source_type not in (18,21)
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="operatorName != null">
            and wmo.operator_name = #{operatorName}
            and wmo.cancel_time = 0
        </if>
        <if test="containerNo != null">
            and weu.carrier_code = #{containerNo}
        </if>
        union all
        select
        wm.id
        , wm.create_time, wm.update_time, wm.cancel_time, wm.creator_name, wm.`state`, wmsp.source_order_no,
        wmsp.source_type, wmsp.source_id, wm.mission_type, wm.mission_no, wm.warehouse_no, wm.p_mission_no,
        wm.operator_name,
        wm.creator_id, wm.operator_id, wm.p_mission_type, wm.tenant_id
        from wms_mission wm
        left join wms_execute_unit weu on wm.mission_no = weu.mission_no
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        where wm.state = 10 and wm.warehouse_no = #{warehouseNo} and weu.state in (20)
        and wmsp.source_type not in (18,21)
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="containerNo != null">
            and weu.carrier_code = #{containerNo}
        </if>
        ) tem
        order by tem.state desc, tem.create_time asc, tem.id asc
    </select>

    <select id="selectByPMissionNoAndContainerNo" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select wm.mission_no
        from wms_mission wm
                 left join wms_execute_unit weu on wm.mission_no = weu.mission_no
        where wm.p_mission_no = #{pMissionNo}
          and wm.p_mission_type = #{pMissionType}
          and wm.mission_type = #{missionType}
          and weu.carrier_code = #{containerNo}
    </select>

    <select id="countPickNotFinishedForOnSaleStocktaking" resultType="java.lang.Integer"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        SELECT COUNT(*)
        FROM `wms_mission` wm INNER JOIN `wms_mission_detail` wmd ON wm.`mission_no` = wmd.`mission_no`
        INNER JOIN `wms_mission_source_property` wmsp ON wm.`mission_no` = wmsp.`mission_no`
        WHERE wm.`warehouse_no` = #{warehouseNo}
        AND wm.`mission_type` = 40
        AND wm.`state` IN (10,20,30,40)
        AND wmsp.`expect_time` = #{expectTime}
        <if test="bizType != null and bizType.size() > 0">
            AND wmsp.source_type IN
            <foreach collection="bizType" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="zoneCodes != null and zoneCodes.size() > 0">
            AND (
            <foreach collection="zoneCodes" item="zoneCode" separator=" or " open="(" close=")">
                wmd.`cabinet_no` LIKE concat(#{zoneCode}, '%')
            </foreach>
            )
        </if>
    </select>

    <select id="findPickFinishedForOnSaleStocktaking"
            resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionSkuCabinetDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        SELECT DISTINCT wmd.`sku` sku, wmd.`cabinet_no` cabinetNo
        FROM `wms_mission` wm INNER JOIN `wms_mission_detail` wmd ON wm.`mission_no` = wmd.`mission_no`
        INNER JOIN `wms_mission_source_property` wmsp ON wm.`mission_no` = wmsp.`mission_no`
        WHERE wm.`warehouse_no` = #{warehouseNo}
        AND wm.`mission_type` = 40
        AND wm.`state` IN (60,70)
        AND wmsp.`expect_time` = #{expectTime}
        AND wmd.`cabinet_no` LIKE concat(#{zoneCode}, '%')
        <if test="bizType != null and bizType.size() > 0">
            AND wmsp.source_type IN
            <foreach collection="bizType" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findPickFinishedZone" resultType="java.lang.String"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        SELECT DISTINCT substr(wmd.`cabinet_no`,1,3) zoneCode
        FROM `wms_mission` wm INNER JOIN `wms_mission_detail` wmd ON wm.`mission_no` = wmd.`mission_no`
        INNER JOIN `wms_mission_source_property` wmsp ON wm.`mission_no` = wmsp.`mission_no`
        WHERE wm.`warehouse_no` = #{warehouseNo}
        AND wm.`mission_type` = 40
        AND wm.`state` IN (60,70)
        AND wmsp.`expect_time` = #{expectTime}
        <if test="bizType != null and bizType.size() > 0">
            AND wmsp.source_type IN
            <foreach collection="bizType" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="zoneCodes != null and zoneCodes.size() > 0">
            AND (
            <foreach collection="zoneCodes" item="zoneCode" separator=" or " open="(" close=")">
                wmd.`cabinet_no` LIKE concat(#{zoneCode}, '%')
            </foreach>
            )
        </if>
    </select>

    <select id="queryTaskPanelQuantity" resultType="net.summerfarm.wms.common.dto.TaskPanelQuantityDTO">
        SELECT COUNT(DISTINCT wm.mission_no) AS notFinishTaskNum,
               COUNT(DISTINCT CASE WHEN NOW() >= DATE (wm.create_time) + INTERVAL 1 DAY
                     THEN wm.mission_no
                     ELSE NULL
                     END) AS notFinishInTimeTaskNum
        FROM wms_mission wm
        WHERE wm.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wm.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
          AND wm.state in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
          AND wm.mission_type = #{type,jdbcType=INTEGER}
          AND wm.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
    </select>
    <select id="queryTaskPanelNotFinishTask" resultType="java.lang.String">
    SELECT DISTINCT wm.mission_no
        FROM wms_mission wm
        WHERE wm.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wm.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
          AND wm.state in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
          AND wm.mission_type = #{type,jdbcType=INTEGER}
          AND wm.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
    </select>
    <select id="queryTaskPanelNotFinishInTimeTask" resultType="java.lang.String">
        SELECT distinct wm.mission_no
        FROM wms_mission wm
        WHERE wm.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND wm.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND wm.state in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status,jdbcType=INTEGER}
        </foreach>
        AND wm.mission_type = #{type,jdbcType=INTEGER}
        AND wm.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
        AND NOW() >= DATE(wm.create_time) + INTERVAL 1 DAY
    </select>
</mapper>