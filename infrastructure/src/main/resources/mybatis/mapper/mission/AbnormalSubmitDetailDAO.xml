<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.AbnormalSubmitDetailDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.AbnormalSubmitDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="unit_no" jdbcType="VARCHAR" property="unitNo"/>
        <result column="source_carrier_type" jdbcType="INTEGER" property="sourceCarrierType"/>
        <result column="source_carrier_code" jdbcType="VARCHAR" property="sourceCarrierCode"/>
        <result column="target_carrier_type" jdbcType="INTEGER" property="targetCarrierType"/>
        <result column="target_carrier_code" jdbcType="VARCHAR" property="targetCarrierCode"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="produce_time" jdbcType="TIMESTAMP" property="produceTime"/>
        <result column="shelf_life" jdbcType="TIMESTAMP" property="shelfLife"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="abnormal_quantity" jdbcType="INTEGER" property="abnormalQuantity"/>
        <result column="cargo_owner" jdbcType="VARCHAR" property="cargoOwner"/>
        <result column="opreator_name" jdbcType="VARCHAR" property="opreatorName"/>
        <result column="operator_id" jdbcType="INTEGER" property="operatorId"/>
        <result column="mission_type" jdbcType="INTEGER" property="missionType"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="abnormal_reason" jdbcType="VARCHAR" property="abnormalReason"/>
        <result column="split_type" jdbcType="INTEGER" property="splitType"/>
        <result column="split_info" jdbcType="VARCHAR" property="splitInfo"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, mission_no, unit_no, source_carrier_type, source_carrier_code,
    target_carrier_type, target_carrier_code, warehouse_no, sku, produce_time, shelf_life, 
    batch_no, abnormal_quantity, cargo_owner, opreator_name, operator_id, mission_type, 
    tenant_id, abnormal_reason,
        split_type, split_info
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_abnormal_submit_detail
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByMissionNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_abnormal_submit_detail
        where mission_no = #{missionNo}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_abnormal_submit_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.AbnormalSubmitDetailDO"
            useGeneratedKeys="true">
        insert into wms_abnormal_submit_detail (create_time, update_time, mission_no,
                                                unit_no, source_carrier_type, source_carrier_code,
                                                target_carrier_type, target_carrier_code, warehouse_no,
                                                sku, produce_time, shelf_life,
                                                batch_no, abnormal_quantity, cargo_owner,
                                                opreator_name, operator_id, mission_type,
                                                tenant_id, abnormal_reason, split_type, split_info)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{missionNo,jdbcType=VARCHAR},
                #{unitNo,jdbcType=VARCHAR}, #{sourceCarrierType,jdbcType=INTEGER},
                #{sourceCarrierCode,jdbcType=VARCHAR},
                #{targetCarrierType,jdbcType=INTEGER}, #{targetCarrierCode,jdbcType=VARCHAR},
                #{warehouseNo,jdbcType=BIGINT},
                #{sku,jdbcType=VARCHAR}, #{produceTime,jdbcType=TIMESTAMP}, #{shelfLife,jdbcType=TIMESTAMP},
                #{batchNo,jdbcType=VARCHAR}, #{abnormalQuantity,jdbcType=INTEGER}, #{cargoOwner,jdbcType=VARCHAR},
                #{opreatorName,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, #{missionType,jdbcType=INTEGER},
                #{tenantId,jdbcType=BIGINT}, #{abnormalReason,jdbcType=VARCHAR}, #{splitType}, #{splitInfo})
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.AbnormalSubmitDetailDO"
            useGeneratedKeys="true">
        insert into wms_abnormal_submit_detail (mission_no,
        unit_no, source_carrier_type, source_carrier_code,
        target_carrier_type, target_carrier_code, warehouse_no,
        sku, produce_time, shelf_life,
        batch_no, abnormal_quantity, cargo_owner,
        opreator_name, operator_id, mission_type,
        tenant_id, abnormal_reason, split_type, split_info)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.missionNo,jdbcType=VARCHAR},
            #{item.unitNo,jdbcType=VARCHAR}, #{item.sourceCarrierType,jdbcType=INTEGER},
            #{item.sourceCarrierCode,jdbcType=VARCHAR},
            #{item.targetCarrierType,jdbcType=INTEGER}, #{item.targetCarrierCode,jdbcType=VARCHAR},
            #{item.warehouseNo,jdbcType=BIGINT},
            #{item.sku,jdbcType=VARCHAR}, #{item.produceTime,jdbcType=TIMESTAMP}, #{item.shelfLife,jdbcType=TIMESTAMP},
            #{item.batchNo,jdbcType=VARCHAR}, #{item.abnormalQuantity,jdbcType=INTEGER},
            #{item.cargoOwner,jdbcType=VARCHAR},
            #{item.opreatorName,jdbcType=VARCHAR}, #{item.operatorId,jdbcType=INTEGER},
            #{item.missionType,jdbcType=INTEGER},
            #{item.tenantId,jdbcType=BIGINT}, #{item.abnormalReason,jdbcType=VARCHAR},
            #{item.splitType}, #{item.splitInfo}
            )
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.AbnormalSubmitDetailDO"
            useGeneratedKeys="true">
        insert into wms_abnormal_submit_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="unitNo != null">
                unit_no,
            </if>
            <if test="sourceCarrierType != null">
                source_carrier_type,
            </if>
            <if test="sourceCarrierCode != null">
                source_carrier_code,
            </if>
            <if test="targetCarrierType != null">
                target_carrier_type,
            </if>
            <if test="targetCarrierCode != null">
                target_carrier_code,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="produceTime != null">
                produce_time,
            </if>
            <if test="shelfLife != null">
                shelf_life,
            </if>
            <if test="batchNo != null">
                batch_no,
            </if>
            <if test="abnormalQuantity != null">
                abnormal_quantity,
            </if>
            <if test="cargoOwner != null">
                cargo_owner,
            </if>
            <if test="opreatorName != null">
                opreator_name,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="missionType != null">
                mission_type,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="abnormalReason != null">
                abnormal_reason,
            </if>
            <if test="splitType">
                split_type,
            </if>
            <if test="splitInfo">
                split_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="unitNo != null">
                #{unitNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceCarrierType != null">
                #{sourceCarrierType,jdbcType=INTEGER},
            </if>
            <if test="sourceCarrierCode != null">
                #{sourceCarrierCode,jdbcType=VARCHAR},
            </if>
            <if test="targetCarrierType != null">
                #{targetCarrierType,jdbcType=INTEGER},
            </if>
            <if test="targetCarrierCode != null">
                #{targetCarrierCode,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="produceTime != null">
                #{produceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="shelfLife != null">
                #{shelfLife,jdbcType=TIMESTAMP},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="abnormalQuantity != null">
                #{abnormalQuantity,jdbcType=INTEGER},
            </if>
            <if test="cargoOwner != null">
                #{cargoOwner,jdbcType=VARCHAR},
            </if>
            <if test="opreatorName != null">
                #{opreatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=INTEGER},
            </if>
            <if test="missionType != null">
                #{missionType,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="abnormalReason != null">
                #{abnormalReason,jdbcType=VARCHAR},
            </if>
            <if test="splitType != null">
                #{splitType},
            </if>
            <if test="splitInfo != null">
                #{splitInfo},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.AbnormalSubmitDetailDO">
        update wms_abnormal_submit_detail
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="unitNo != null">
                unit_no = #{unitNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceCarrierType != null">
                source_carrier_type = #{sourceCarrierType,jdbcType=INTEGER},
            </if>
            <if test="sourceCarrierCode != null">
                source_carrier_code = #{sourceCarrierCode,jdbcType=VARCHAR},
            </if>
            <if test="targetCarrierType != null">
                target_carrier_type = #{targetCarrierType,jdbcType=INTEGER},
            </if>
            <if test="targetCarrierCode != null">
                target_carrier_code = #{targetCarrierCode,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="produceTime != null">
                produce_time = #{produceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="shelfLife != null">
                shelf_life = #{shelfLife,jdbcType=TIMESTAMP},
            </if>
            <if test="batchNo != null">
                batch_no = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="abnormalQuantity != null">
                abnormal_quantity = #{abnormalQuantity,jdbcType=INTEGER},
            </if>
            <if test="cargoOwner != null">
                cargo_owner = #{cargoOwner,jdbcType=VARCHAR},
            </if>
            <if test="opreatorName != null">
                opreator_name = #{opreatorName,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=INTEGER},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="abnormalReason != null">
                abnormal_reason = #{abnormalReason,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.AbnormalSubmitDetailDO">
        update wms_abnormal_submit_detail
        set create_time         = #{createTime,jdbcType=TIMESTAMP},
            update_time         = #{updateTime,jdbcType=TIMESTAMP},
            mission_no          = #{missionNo,jdbcType=VARCHAR},
            unit_no             = #{unitNo,jdbcType=VARCHAR},
            source_carrier_type = #{sourceCarrierType,jdbcType=INTEGER},
            source_carrier_code = #{sourceCarrierCode,jdbcType=VARCHAR},
            target_carrier_type = #{targetCarrierType,jdbcType=INTEGER},
            target_carrier_code = #{targetCarrierCode,jdbcType=VARCHAR},
            warehouse_no        = #{warehouseNo,jdbcType=BIGINT},
            sku                 = #{sku,jdbcType=VARCHAR},
            produce_time        = #{produceTime,jdbcType=TIMESTAMP},
            shelf_life          = #{shelfLife,jdbcType=TIMESTAMP},
            batch_no            = #{batchNo,jdbcType=VARCHAR},
            abnormal_quantity   = #{abnormalQuantity,jdbcType=INTEGER},
            cargo_owner         = #{cargoOwner,jdbcType=VARCHAR},
            opreator_name       = #{opreatorName,jdbcType=VARCHAR},
            operator_id         = #{operatorId,jdbcType=INTEGER},
            mission_type        = #{missionType,jdbcType=INTEGER},
            tenant_id           = #{tenantId,jdbcType=BIGINT},
            abnormal_reason     = #{abnormalReason,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>