<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionLogDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionLogDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="mission_type" jdbcType="INTEGER" property="missionType"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="operate_type" jdbcType="VARCHAR" property="operateType"/>
        <result column="operate_info" jdbcType="VARCHAR" property="operateInfo"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, mission_no, mission_type, `operator`, operator_name,
    operate_type, operate_info
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByMissionNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_log
        where mission_no = #{missionNo}
        order by id desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_mission_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionLogDO"
            useGeneratedKeys="true">
        insert into wms_mission_log (create_time, update_time, mission_no,
                                     mission_type, `operator`, operator_name,
                                     operate_type, operate_info)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{missionNo,jdbcType=VARCHAR},
                #{missionType,jdbcType=INTEGER}, #{operator,jdbcType=VARCHAR}, #{operatorName,jdbcType=VARCHAR},
                #{operateType,jdbcType=VARCHAR}, #{operateInfo,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionLogDO"
            useGeneratedKeys="true">
        insert into wms_mission_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="missionType != null">
                mission_type,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="operateType != null">
                operate_type,
            </if>
            <if test="operateInfo != null">
                operate_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                #{missionType,jdbcType=INTEGER},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=VARCHAR},
            </if>
            <if test="operateInfo != null">
                #{operateInfo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionLogDO"
            useGeneratedKeys="true">
        insert into wms_mission_log (mission_no,
        mission_type, `operator`, operator_name,
        operate_type, operate_info)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.missionNo,jdbcType=VARCHAR},
            #{item.missionType,jdbcType=INTEGER}, #{item.operator,jdbcType=VARCHAR},
            #{item.operatorName,jdbcType=VARCHAR},
            #{item.operateType,jdbcType=VARCHAR}, #{item.operateInfo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionLogDO">
        update wms_mission_log
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=INTEGER},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType,jdbcType=VARCHAR},
            </if>
            <if test="operateInfo != null">
                operate_info = #{operateInfo,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionLogDO">
        update wms_mission_log
        set create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            mission_no    = #{missionNo,jdbcType=VARCHAR},
            mission_type  = #{missionType,jdbcType=INTEGER},
            `operator`    = #{operator,jdbcType=VARCHAR},
            operator_name = #{operatorName,jdbcType=VARCHAR},
            operate_type  = #{operateType,jdbcType=VARCHAR},
            operate_info  = #{operateInfo,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>