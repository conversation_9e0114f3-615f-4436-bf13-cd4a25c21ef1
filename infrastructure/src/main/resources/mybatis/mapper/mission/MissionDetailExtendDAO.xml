<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionDetailExtendDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailExtendDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="extend" jdbcType="VARCHAR" property="extend"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="supplier_id" jdbcType="BIGINT" property="supplierId"/>
        <result column="pd_id" jdbcType="BIGINT" property="pdId"/>
        <result column="category_id" jdbcType="INTEGER" property="categoryId"/>
        <result column="category_name" jdbcType="VARCHAR" property="categoryName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, mission_no, extend, sku, warehouse_no
            , supplier_id, pd_id, category_id, category_name
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/  select
        <include refid="Base_Column_List"/>
        from wms_mission_detail_extend
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByMissionNos" resultMap="BaseResultMap">
        /*FORCE_MASTER*/  select
        <include refid="Base_Column_List"/>
        from wms_mission_detail_extend
        where mission_no in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectByMissionNoAndSkus" resultMap="BaseResultMap">
        /*FORCE_MASTER*/  select
        <include refid="Base_Column_List"/>
        from wms_mission_detail_extend
        where mission_no = #{missionNo}
        and sku in
        <foreach collection="skus" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_mission_detail_extend
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailExtendDO"
            useGeneratedKeys="true">
        insert into wms_mission_detail_extend (create_time, update_time, mission_no,
                                               extend, sku, warehouse_no
            , supplier_id, pd_id, category_id, category_name)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{missionNo,jdbcType=VARCHAR},
                #{extend,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{warehouseNo}
            ,#{supplierId}, #{pdId}, #{categoryId}, #{categoryName})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailExtendDO"
            useGeneratedKeys="true">
        insert into wms_mission_detail_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="extend != null">
                extend,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="pdId != null">
                pd_id,
            </if>
            <if test="categoryId != null">
                category_id,
            </if>
            <if test="categoryName != null">
                category_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="extend != null">
                #{extend,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo},
            </if>
            <if test="supplierId != null">
                #{supplierId},
            </if>
            <if test="pdId != null">
                #{pdId},
            </if>
            <if test="categoryId != null">
                #{categoryId},
            </if>
            <if test="categoryName != null">
                #{categoryName},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailExtendDO"
            useGeneratedKeys="true">
        insert into wms_mission_detail_extend (mission_no,
        extend, sku, warehouse_no
        , supplier_id, pd_id, category_id, category_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.missionNo,jdbcType=VARCHAR},
            #{item.extend,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.warehouseNo}
            , #{item.supplierId}, #{item.pdId}, #{item.categoryId},  #{item.categoryName}
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailExtendDO">
        update wms_mission_detail_extend
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="extend != null">
                extend = #{extend,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="pdId != null">
                pd_id = #{pdId},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="categoryName != null">
                category_name = #{categoryName},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>