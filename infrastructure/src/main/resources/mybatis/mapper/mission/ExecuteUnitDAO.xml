<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.ExecuteUnitDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.ExecuteUnitDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="carrier_type" jdbcType="INTEGER" property="carrierType"/>
        <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode"/>
        <result column="mission_type" jdbcType="INTEGER" property="missionType"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="source_no" jdbcType="VARCHAR" property="sourceNo"/>
        <result column="cancel_time" jdbcType="BIGINT" property="cancelTime"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="unit_no" jdbcType="VARCHAR" property="unitNo"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, warehouse_no, carrier_type, carrier_code, mission_type,
    mission_no, source_type, source_no, cancel_time, `state`, unit_no, tenant_id
    </sql>
    <select id="listByStateAndCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_execute_unit
        <where>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo}
            </if>
            <if test="missionNo != null">
                and mission_no = #{missionNo}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="states != null and states.size() > 0">
                and state in
                <foreach collection="states" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="codes != null and codes.size() > 0">
                and carrier_code in
                <foreach collection="codes" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.ExecuteUnitDO"
            useGeneratedKeys="true">

        insert into wms_execute_unit (warehouse_no,
        carrier_type, carrier_code, mission_type,
        mission_no, source_type, source_no,
        cancel_time, `state`, unit_no, tenant_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warehouseNo,jdbcType=BIGINT},
            #{item.carrierType,jdbcType=INTEGER}, #{item.carrierCode,jdbcType=VARCHAR},
            #{item.missionType,jdbcType=BIGINT},
            #{item.missionNo,jdbcType=VARCHAR}, #{item.sourceType,jdbcType=INTEGER}, #{item.sourceNo,jdbcType=VARCHAR},
            #{item.cancelTime,jdbcType=BIGINT}, #{item.state,jdbcType=INTEGER}, #{item.unitNo,jdbcType=VARCHAR},
            #{item.tenantId})
        </foreach>
    </insert>
    <select id="listUnitNo" resultType="string">
        /*FORCE_MASTER*/ select unit_no
                         from wms_execute_unit
                         where create_time >= #{day}
    </select>
    <select id="selectByMissionNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_execute_unit
        where mission_no = #{missionNo}
    </select>
    <select id="selectByMissionNos" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_execute_unit
        where mission_no in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_execute_unit
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByContainerNoAndMissionType" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_execute_unit
        where warehouse_no = #{warehouseNo}
        and mission_type = #{missionType}
        and carrier_code = #{containerNo}
        <if test="state != null and state.size() > 0">
            and state in
            <foreach collection="state" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_execute_unit
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.ExecuteUnitDO"
            useGeneratedKeys="true">
        insert into wms_execute_unit (create_time, update_time, warehouse_no,
                                      carrier_type, carrier_code, mission_type,
                                      mission_no, source_type, source_no,
                                      cancel_time, `state`, unit_no, tenant_id)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{warehouseNo,jdbcType=BIGINT},
                #{carrierType,jdbcType=INTEGER}, #{carrierCode,jdbcType=VARCHAR}, #{missionType,jdbcType=BIGINT},
                #{missionNo,jdbcType=VARCHAR}, #{sourceType,jdbcType=INTEGER}, #{sourceNo,jdbcType=VARCHAR},
                #{cancelTime,jdbcType=BIGINT}, #{state,jdbcType=INTEGER}, #{unitNo,jdbcType=VARCHAR}, #{tenantId})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.ExecuteUnitDO"
            useGeneratedKeys="true">
        insert into wms_execute_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="carrierType != null">
                carrier_type,
            </if>
            <if test="carrierCode != null">
                carrier_code,
            </if>
            <if test="missionType != null">
                mission_type,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="sourceNo != null">
                source_no,
            </if>
            <if test="cancelTime != null">
                cancel_time,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="unitNo != null">
                unit_no,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="carrierType != null">
                #{carrierType,jdbcType=INTEGER},
            </if>
            <if test="carrierCode != null">
                #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                #{missionType,jdbcType=BIGINT},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceNo != null">
                #{sourceNo,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="unitNo != null">
                #{unitNo,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.ExecuteUnitDO">
        update wms_execute_unit
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="carrierType != null">
                carrier_type = #{carrierType,jdbcType=INTEGER},
            </if>
            <if test="carrierCode != null">
                carrier_code = #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=BIGINT},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceNo != null">
                source_no = #{sourceNo,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="unitNo != null">
                unit_no = #{unitNo,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeySelectiveByUnitNo"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.ExecuteUnitDO">
        update wms_execute_unit
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="carrierType != null">
                carrier_type = #{carrierType,jdbcType=INTEGER},
            </if>
            <if test="carrierCode != null">
                carrier_code = #{carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=BIGINT},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceNo != null">
                source_no = #{sourceNo,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="unitNo != null">
                unit_no = #{unitNo,jdbcType=VARCHAR},
            </if>
        </set>
        where unit_no = #{unitNo}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.ExecuteUnitDO">
        update wms_execute_unit
        set create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            carrier_type = #{carrierType,jdbcType=INTEGER},
            carrier_code = #{carrierCode,jdbcType=VARCHAR},
            mission_type = #{missionType,jdbcType=BIGINT},
            mission_no   = #{missionNo,jdbcType=VARCHAR},
            source_type  = #{sourceType,jdbcType=INTEGER},
            source_no    = #{sourceNo,jdbcType=VARCHAR},
            cancel_time  = #{cancelTime,jdbcType=BIGINT},
            `state`      = #{state,jdbcType=INTEGER},
            unit_no      = #{unitNo,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="delete">
        delete
        from wms_execute_unit
        where id = #{id}
    </delete>
</mapper>