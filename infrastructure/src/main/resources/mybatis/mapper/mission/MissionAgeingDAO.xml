<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionAgeingDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionAgeingDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="mission_type" jdbcType="INTEGER" property="missionType"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="cabinet_no" jdbcType="VARCHAR" property="cabinetNo"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="ageing" jdbcType="TIME" property="ageing"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="opeartor_id" jdbcType="VARCHAR" property="opeartorId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, warehouse_no, mission_no, mission_type, sku, cabinet_no,
    finish_time, start_time, ageing, `operator`, opeartor_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_ageing
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByMissionNoAndSkuAndCabinetNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_mission_ageing
        where mission_no = #{missionNo} and sku = #{sku} and cabinet_no = #{cabinetNo}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_mission_ageing
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionAgeingDO"
            useGeneratedKeys="true">
        insert into wms_mission_ageing (create_time, update_time, warehouse_no,
                                        mission_no, mission_type, sku,
                                        cabinet_no, finish_time, start_time,
                                        ageing, `operator`, opeartor_id)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{warehouseNo,jdbcType=BIGINT},
                #{missionNo,jdbcType=VARCHAR}, #{missionType,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR},
                #{cabinetNo,jdbcType=VARCHAR}, #{finishTime,jdbcType=TIMESTAMP}, #{startTime,jdbcType=TIMESTAMP},
                #{ageing,jdbcType=TIME}, #{operator,jdbcType=VARCHAR}, #{opeartorId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionAgeingDO"
            useGeneratedKeys="true">
        insert into wms_mission_ageing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="missionType != null">
                mission_type,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="cabinetNo != null">
                cabinet_no,
            </if>
            <if test="finishTime != null">
                finish_time,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="ageing != null">
                ageing,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
            <if test="opeartorId != null">
                opeartor_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                #{missionType,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="cabinetNo != null">
                #{cabinetNo,jdbcType=VARCHAR},
            </if>
            <if test="finishTime != null">
                #{finishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ageing != null">
                #{ageing,jdbcType=TIME},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="opeartorId != null">
                #{opeartorId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionAgeingDO">
        update wms_mission_ageing
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="missionType != null">
                mission_type = #{missionType,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="cabinetNo != null">
                cabinet_no = #{cabinetNo,jdbcType=VARCHAR},
            </if>
            <if test="finishTime != null">
                finish_time = #{finishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ageing != null">
                ageing = #{ageing,jdbcType=TIME},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="opeartorId != null">
                opeartor_id = #{opeartorId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionAgeingDO">
        update wms_mission_ageing
        set create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            mission_no   = #{missionNo,jdbcType=VARCHAR},
            mission_type = #{missionType,jdbcType=INTEGER},
            sku          = #{sku,jdbcType=VARCHAR},
            cabinet_no   = #{cabinetNo,jdbcType=VARCHAR},
            finish_time  = #{finishTime,jdbcType=TIMESTAMP},
            start_time   = #{startTime,jdbcType=TIMESTAMP},
            ageing       = #{ageing,jdbcType=TIME},
            `operator`   = #{operator,jdbcType=VARCHAR},
            opeartor_id  = #{opeartorId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>