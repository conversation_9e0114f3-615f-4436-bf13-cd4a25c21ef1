<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.WmsSubmitDetailExtendMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsSubmitDetailExtendResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.WmsSubmitDetailExtend">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="NUMERIC"/>
		<result column="mission_no" property="missionNo" jdbcType="VARCHAR"/>
		<result column="mission_detail_id" property="missionDetailId" jdbcType="NUMERIC"/>
		<result column="mission_submit_id" property="missionSubmitId" jdbcType="NUMERIC"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="extend" property="extend" jdbcType="LONGVARCHAR"/>
		<result column="extend_type" property="extendType" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsSubmitDetailExtendColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.warehouse_no,
          t.mission_no,
          t.mission_detail_id,
          t.mission_submit_id,
          t.sku,
          t.extend,
          t.extend_type
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="missionNo != null and missionNo !=''">
                AND t.mission_no = #{missionNo}
            </if>
			<if test="missionDetailId != null">
                AND t.mission_detail_id = #{missionDetailId}
            </if>
			<if test="missionSubmitId != null">
                AND t.mission_submit_id = #{missionSubmitId}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="extend != null and extend !=''">
                AND t.extend = #{extend}
            </if>
			<if test="extendType != null">
                AND t.extend_type = #{extendType}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="missionNo != null">
                    t.mission_no = #{missionNo},
                </if>
                <if test="missionDetailId != null">
                    t.mission_detail_id = #{missionDetailId},
                </if>
                <if test="missionSubmitId != null">
                    t.mission_submit_id = #{missionSubmitId},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="extend != null">
                    t.extend = #{extend},
                </if>
                <if test="extendType != null">
                    t.extend_type = #{extendType},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsSubmitDetailExtendResultMap" >
        SELECT <include refid="wmsSubmitDetailExtendColumns" />
        FROM wms_submit_detail_extend t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.mission.param.query.WmsSubmitDetailExtendQueryParam"  resultType="net.summerfarm.wms.domain.mission.entity.WmsSubmitDetailExtendEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.warehouse_no warehouseNo,
            t.mission_no missionNo,
            t.mission_detail_id missionDetailId,
            t.mission_submit_id missionSubmitId,
            t.sku sku,
            t.extend extend,
            t.extend_type extendType
        FROM wms_submit_detail_extend t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.mission.param.query.WmsSubmitDetailExtendQueryParam" resultMap="wmsSubmitDetailExtendResultMap" >
        SELECT <include refid="wmsSubmitDetailExtendColumns" />
        FROM wms_submit_detail_extend t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.WmsSubmitDetailExtend" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_submit_detail_extend
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="missionNo != null">
				  mission_no,
              </if>
              <if test="missionDetailId != null">
				  mission_detail_id,
              </if>
              <if test="missionSubmitId != null">
				  mission_submit_id,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="extend != null">
				  extend,
              </if>
              <if test="extendType != null">
				  extend_type,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=NUMERIC},
              </if>
              <if test="missionNo != null">
				#{missionNo,jdbcType=VARCHAR},
              </if>
              <if test="missionDetailId != null">
				#{missionDetailId,jdbcType=NUMERIC},
              </if>
              <if test="missionSubmitId != null">
				#{missionSubmitId,jdbcType=NUMERIC},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="extend != null">
				#{extend,jdbcType=LONGVARCHAR},
              </if>
              <if test="extendType != null">
				#{extendType,jdbcType=TINYINT},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.WmsSubmitDetailExtend" >
        UPDATE wms_submit_detail_extend t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.WmsSubmitDetailExtend" >
        DELETE FROM wms_submit_detail_extend t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>

	<!-- 根据主键ID进行批量物理删除 -->
	<delete id="batchRemove" parameterType="java.util.List" >
        DELETE FROM wms_submit_detail_extend t
		WHERE t.id IN
        <foreach item="item" collection="list" index="index" open="("
                 separator="," close=")">
			#{item}
        </foreach>
    </delete>



</mapper>