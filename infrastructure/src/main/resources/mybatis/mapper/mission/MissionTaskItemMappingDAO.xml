<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionTaskItemMappingDAO">
    <resultMap id="MissionTaskItemMappingMap"
               type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionTaskItemMappingDO">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="stockTaskId" column="stock_task_id"/>
        <result property="missionNo" column="mission_no"/>
        <result property="bizType" column="biz_type"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createOperator" column="create_operator"/>
        <result property="updateOperator" column="update_operator"/>
        <result property="status" column="status"/>
        <result property="sku" column="sku"/>
        <result property="pickQuantity" column="pick_quantity"/>
        <result property="cabinetCode" column="cabinet_code"/>
        <result property="productionDate" column="production_date"/>
        <result property="qualityDate" column="quality_date"/>
        <result property="shouldPickQuantity" column="should_pick_quantity"/>
        <result property="abnormalQuantity" column="abnormal_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="initPickQuantity" column="init_pick_quantity"/>
    </resultMap>

    <sql id="table_name">
        wms_mission_task_item_mapping
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `create_time`, `update_time`, `stock_task_id`, `mission_no`, `biz_type`, `is_deleted`, `create_operator`,
        `update_operator`, `status`, `sku`, `pick_quantity`, `cabinet_code`, `production_date`, `quality_date`,
            `should_pick_quantity`, `abnormal_quantity`, `actual_quantity`, `init_pick_quantity`
    </sql>

    <sql id="values_exclude_id">
        #{createTime}, #{updateTime}, #{stockTaskId}, #{missionNo}, #{bizType}, #{isDeleted}, #{createOperator},
        #{updateOperator}, #{status}, #{sku}, #{pickQuantity}, #{cabinetCode}, #{productionDate}, #{qualityDate},
#{shouldPickQuantity}, #{abnormalQuantity}, #{actualQuantity}, #{initPickQuantity}
    </sql>

    <sql id="query">
        <where>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
            <if test="stockTaskId != null">AND `stock_task_id` = #{stockTaskId}</if>
            <if test="missionNo != null">AND `mission_no` = #{missionNo}</if>
            <if test="bizType != null">AND `biz_type` = #{bizType}</if>
            <if test="isDeleted != null">AND `is_deleted` = #{isDeleted}</if>
            <if test="createOperator != null">AND `create_operator` = #{createOperator}</if>
            <if test="updateOperator != null">AND `update_operator` = #{updateOperator}</if>
            <if test="status != null">AND `status` = #{status}</if>
            <if test="sku != null">AND `sku` = #{sku}</if>
            <if test="pickQuantity != null">AND `pick_quantity` = #{pickQuantity}</if>
            <if test="cabinetCode != null">AND `cabinet_code` = #{cabinetCode}</if>
            <if test="productionDate != null">AND `production_date` = #{productionDate}</if>
            <if test="qualityDate != null">AND `quality_date` = #{qualityDate}</if>
            <if test="shouldPickQuantity != null">AND `should_pick_quantity` = #{shouldPickQuantity}</if>
            <if test="abnormalQuantity != null">AND `abnormal_quantity` = #{abnormalQuantity}</if>
            <if test="actualQuantity != null">AND `actual_quantity` = #{actualQuantity}</if>
            <if test="skus != null and skus.size() > 0">
                AND `sku` in
                <foreach collection="skus" item="sku1" open="(" close=")" separator=",">
                    #{sku1}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                AND `id` in
                <foreach collection="ids" item="id1" open="(" close=")" separator=",">
                    #{id1}
                </foreach>
            </if>
            <if test="initQuantityGtZero != null">
                <if test="initQuantityGtZero == true">
                    and init_pick_quantity > 0
                </if>
                <if test="initQuantityGtZero == false">
                    and init_pick_quantity >= 0
                </if>
            </if>
        </where>
    </sql>

    <sql id="orderByQuery">
        <if test="sorts != null and sorts.size() > 0">
            ORDER BY
            <foreach collection="sorts" item="i" index="index" separator=",">
                ${i.columnName} ${i.sortType}
            </foreach>
        </if>
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionTaskItemMappingDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="stockTaskId != null">`stock_task_id`,</if>
            <if test="missionNo != null">`mission_no`,</if>
            <if test="bizType != null">`biz_type`,</if>
            <if test="isDeleted != null">`is_deleted`,</if>
            <if test="createOperator != null">`create_operator`,</if>
            <if test="updateOperator != null">`update_operator`,</if>
            <if test="status != null">`status`,</if>
            <if test="sku != null">`sku`,</if>
            <if test="pickQuantity != null">`pick_quantity`,</if>
            <if test="cabinetCode != null">`cabinet_code`,</if>
            <if test="productionDate != null">`production_date`,</if>
            <if test="qualityDate != null">`quality_date`,</if>
            <if test="shouldPickQuantity != null">`should_pick_quantity`,</if>
            <if test="abnormalQuantity != null">`abnormal_quantity`,</if>
            <if test="actualQuantity != null">`actual_quantity`,</if>
            <if test="initPickQuantity != null">`init_pick_quantity`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="stockTaskId != null">#{stockTaskId},</if>
            <if test="missionNo != null">#{missionNo},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createOperator != null">#{createOperator},</if>
            <if test="updateOperator != null">#{updateOperator},</if>
            <if test="status != null">#{status},</if>
            <if test="sku != null">#{sku},</if>
            <if test="pickQuantity != null">#{pickQuantity},</if>
            <if test="cabinetCode != null">#{cabinetCode},</if>
            <if test="productionDate != null">#{productionDate},</if>
            <if test="qualityDate != null">#{qualityDate},</if>
            <if test="shouldPickQuantity != null">#{shouldPickQuantity},</if>
            <if test="abnormalQuantity != null">#{abnormalQuantity},</if>
            <if test="actualQuantity != null">#{actualQuantity},</if>
            <if test="initPickQuantity != null">#{initPickQuantity},</if>
        </trim>
    </insert>

    <insert id="creates" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionTaskItemMappingDO"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.createTime}, #{i.updateTime}, #{i.stockTaskId}, #{i.missionNo}, #{i.bizType}, #{i.isDeleted},
            #{i.createOperator}, #{i.updateOperator}, #{i.status}, #{i.sku}, #{i.pickQuantity},
            #{i.cabinetCode}, #{i.productionDate}, #{i.qualityDate}, #{i.shouldPickQuantity},
            #{i.abnormalQuantity}, #{i.actualQuantity}, #{i.initPickQuantity})
        </foreach>
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionTaskItemMappingDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            <if test="stockTaskId != null">`stock_task_id` = #{stockTaskId},</if>
            <if test="missionNo != null">`mission_no` = #{missionNo},</if>
            <if test="bizType != null">`biz_type` = #{bizType},</if>
            <if test="isDeleted != null">`is_deleted` = #{isDeleted},</if>
            <if test="createOperator != null">`create_operator` = #{createOperator},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="sku != null">`sku` = #{sku},</if>
            <if test="pickQuantity != null">`pick_quantity` = #{pickQuantity},</if>
            <if test="cabinetCode != null">`cabinet_code` = #{cabinetCode},</if>
            <if test="productionDate != null">`production_date` = #{productionDate},</if>
            <if test="qualityDate != null">`quality_date` = #{qualityDate},</if>
            <if test="shouldPickQuantity != null">`should_pick_quantity` = #{shouldPickQuantity},</if>
            <if test="abnormalQuantity != null">`abnormal_quantity` = #{abnormalQuantity},</if>
            <if test="actualQuantity != null">`actual_quantity` = #{actualQuantity},</if>
            <if test="initPickQuantity != null">`init_pick_quantity` = #{initPickQuantity},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="MissionTaskItemMappingMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="MissionTaskItemMappingMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionTaskItemMappingQueryDO"
            resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionTaskItemMappingQueryDO"
            resultMap="MissionTaskItemMappingMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
        limit 1
    </select>

    <select id="paging" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionTaskItemMappingQueryDO"
            resultMap="MissionTaskItemMappingMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="list" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionTaskItemMappingQueryDO"
            resultMap="MissionTaskItemMappingMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    <select id="listByMaster" parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.MissionTaskItemMappingQueryDO"
            resultMap="MissionTaskItemMappingMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    <update id="updatePickQuantity">
        UPDATE
        <include refid="table_name"/>
        SET `pick_quantity` = #{pickQuantity}
        WHERE id = #{id}
    </update>

    <update id="updateShouldAndPickQuantity">
        UPDATE
        <include refid="table_name"/>
        SET `pick_quantity` = #{pickQuantity}, `should_pick_quantity` = #{shouldPickQuantity}
        WHERE id = #{id}
    </update>

    <update id="updateShouldQuantity">
        UPDATE
        <include refid="table_name"/>
        SET `should_pick_quantity` = #{shouldPickQuantity}
        WHERE id = #{id}
    </update>

    <update id="updateAbnormalQuantity">
        UPDATE
        <include refid="table_name"/>
        SET `abnormal_quantity` = #{abnormalQuantity}
        WHERE id = #{id}
    </update>

    <update id="updateShouldAndAbnormalQuantity">
        UPDATE
        <include refid="table_name"/>
        SET `abnormal_quantity` = #{abnormalQuantity}, `should_pick_quantity` = #{shouldPickQuantity}
        WHERE id = #{id}
    </update>

    <update id="updatePickFinishStatusByMissionNo">
        UPDATE
        <include refid="table_name"/>
        SET `status` = 1
        WHERE mission_no = #{missionNo}
        AND status IN (0, 1)
    </update>

    <update id="updatePickFinishStatusById">
        UPDATE
        <include refid="table_name"/>
        SET `status` = 1
        WHERE id = #{id}
        AND status IN (0, 1)
    </update>

    <update id="updateActualQuantity">
        UPDATE
        <include refid="table_name"/>
        SET `actual_quantity` = #{actualQuantity}, `status` = 2
        WHERE id = #{id}
        AND status = 1
    </update>

    <update id="updateDiffConfirmedStatus">
        UPDATE
        <include refid="table_name"/>
        SET `status` = 3
        WHERE id IN
        <foreach item="id1" collection="ids" open="(" separator="," close=")">
            #{id1}
        </foreach>
        AND status = 2
    </update>

    <update id="updateDiffConfirmedOutStatus">
        UPDATE
        <include refid="table_name"/>
        SET `status` = 4
        WHERE id IN
        <foreach item="id1" collection="ids" open="(" separator="," close=")">
            #{id1}
        </foreach>
        AND status = 3
    </update>

    <update id="updatePickAndAbnormalQuantity">
        UPDATE
        <include refid="table_name"/>
        SET `abnormal_quantity` = #{abnormalQuantity}, `pick_quantity` = #{pickQuantity}
        WHERE id = #{id}
    </update>

    <select id="findByStockTaskIds" parameterType="java.lang.Long" resultMap="MissionTaskItemMappingMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        where
        stock_task_id in
        <foreach collection="stockTaskIdList" open="(" close=")" separator="," item="stockTaskId">
            #{stockTaskId}
        </foreach>
    </select>

    <select id="findByMissionNoList" parameterType="java.lang.String" resultMap="MissionTaskItemMappingMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        where
        mission_no in
        <foreach collection="missionNos" open="(" close=")" separator="," item="missionNo">
            #{missionNo}
        </foreach>
         and is_deleted = 0
    </select>

    <select id="findByStockTaskIdAndSku" resultMap="MissionTaskItemMappingMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        where
        stock_task_id = #{stockTaskId}
        and status = #{status}
        and sku = #{sku}
    </select>

    <select id="findByStockTaskIdAndSkuList" resultMap="MissionTaskItemMappingMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        where
        stock_task_id = #{stockTaskId}
        and status = #{status}
        and sku in <foreach collection="skuList" open="(" close=")" separator="," item="sku">#{sku}</foreach>
    </select>

</mapper>
