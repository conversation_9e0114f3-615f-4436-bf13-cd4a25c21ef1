<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.mission.MissionDetailDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="mission_no" jdbcType="VARCHAR" property="missionNo"/>
        <result column="unit_no" jdbcType="VARCHAR" property="unitNo"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="deleted_at" jdbcType="BIGINT" property="deletedAt"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="cabinet_no" jdbcType="VARCHAR" property="cabinetNo"/>
        <result column="produce_time" jdbcType="TIMESTAMP" property="produceTime"/>
        <result column="shelf_life" jdbcType="TIMESTAMP" property="shelfLife"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="operator_id" jdbcType="VARCHAR" property="operatorId"/>
        <result column="should_in_quantity" jdbcType="INTEGER" property="shouldInQuantity"/>
        <result column="exe_quantity" jdbcType="INTEGER" property="exeQuantity"/>
        <result column="init" jdbcType="INTEGER" property="init"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="abnormal_quantity" jdbcType="INTEGER" property="abnormalQuantity"/>
        <result column="split_type" jdbcType="INTEGER" property="splitType"/>
        <result column="split_info" jdbcType="VARCHAR" property="splitInfo"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, mission_no, warehouse_no, sku, produce_time, shelf_life,
    batch_no, should_in_quantity, exe_quantity, tenant_id, unit_no, cabinet_no, operator, operator_id, state,
    ifnull(abnormal_quantity,0) abnormal_quantity,deleted_at,ifnull(init,0) init, split_type, split_info
    </sql>
    <sql id="Base_Column_List_Join">
        wmd
        .
        id
        , wmd.create_time, wmd.update_time, wmd.mission_no, wmd.warehouse_no, wmd.sku, wmd.produce_time, wmd.shelf_life,
    wmd.batch_no, wmd.should_in_quantity, wmd.exe_quantity, wmd.tenant_id, wmd.unit_no, wmd.cabinet_no, wmd.operator,
    wmd.operator_id, wmd.state, wmd.abnormal_quantity, wmd.init, wmd.deleted_at
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByPrimaryKeys" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where id in
        <foreach collection="list" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>
    <select id="selectByMissionNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where mission_no = #{missionNo} and deleted_at = 0
    </select>
    <select id="selectByMissionNoAndSkus" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where mission_no = #{missionNo}
        and deleted_at = 0
        <if test="skus != null and skus.size() > 0">
            and sku in
            <foreach collection="skus" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="batch != null">
            and batch_no = #{batch}
        </if>
        <if test="produceTime != null">
            and produce_time = #{produceTime}
        </if>
        <if test="qualityDate != null">
            and shelf_life = #{qualityDate}
        </if>
        <if test="splitType != null">
            and split_type = #{splitType}
        </if>
        <if test="splitInfoList != null and splitInfoList.size() >0">
            and split_info in
            <foreach collection="splitInfoList" item="splitInfo" separator="," open="(" close=")">
                #{splitInfo}
            </foreach>
        </if>
    </select>
    <select id="selectByMissionNos" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where mission_no in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and deleted_at = 0
    </select>

    <select id="findDetailByMissionNoListAndSkuList" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where mission_no in
        <foreach collection="missionNoList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="skuList != null and skuList.size() > 0">
            and sku in
            <foreach collection="skuList" item="sku" separator="," open="(" close=")">
                #{sku}
            </foreach>
        </if>
    </select>


    <select id="pagePda" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageOrderPickingDetailDO">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List_Join"/>
        from wms_mission_detail wmd
        inner join wms_cabinet wc on wc.cabinet_code = wmd.cabinet_no and wmd.`warehouse_no` = wc.`warehouse_no`
        inner join inventory as i on wmd.sku = i.sku
        inner join products as p on p.pd_id = i.pd_id
        where wmd.mission_no = #{missionNo}
        and wmd.deleted_at = 0
        and wmd.warehouse_no = #{warehouseNo}
        and wc.warehouse_no = #{warehouseNo}
        <if test="cabinetNo != null and cabinetNo != ''">
            and wmd.cabinet_no = #{cabinetNo}
        </if>
        <if test="skus != null and skus.size() > 0">
            and wmd.sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        <if test="skuExclude != null  and cabinetNo != ''">
            and wmd.sku != #{skuExclude}
        </if>
        <if test="detailState != null">
            and wmd.state = #{detailState}
        </if>
        order by wmd.state asc, wc.sequence is null, wc.sequence asc, wmd.cabinet_no asc, wmd.id desc
    </select>

    <select id="listStocktakingSort" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageStocktakingDetailDO">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List_Join"/>
        from wms_mission_detail wmd
        left join wms_cabinet wc on wc.cabinet_code = wmd.cabinet_no and wmd.`warehouse_no` = wc.`warehouse_no`
        where wmd.mission_no = #{missionNo}
        and wmd.deleted_at = 0
        and wmd.warehouse_no = #{warehouseNo}
        <if test="cabinetNo != null and cabinetNo != ''">
            and wmd.cabinet_no = #{cabinetNo}
        </if>
        <if test="skus != null and skus.size() > 0">
            and wmd.sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        <if test="skuExclude != null  and cabinetNo != ''">
            and wmd.sku != #{skuExclude}
        </if>
        <if test="detailState != null">
            and wmd.state = #{detailState}
        </if>
        order by wmd.state asc, wmd.cabinet_no is null, wmd.cabinet_no asc, wmd.id desc
    </select>

    <select id="listStocktakingSortByCabinetNo" resultMap="BaseResultMap"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageStocktakingDetailDO">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List_Join"/>
        from wms_mission_detail wmd
        left join wms_cabinet wc on wc.cabinet_code = wmd.cabinet_no and wmd.`warehouse_no` = wc.`warehouse_no`
        where wmd.mission_no = #{missionNo}
        and wmd.deleted_at = 0
        and wmd.warehouse_no = #{warehouseNo}
        <if test="cabinetNo != null and cabinetNo != ''">
            and wmd.cabinet_no = #{cabinetNo}
        </if>
        <if test="skus != null and skus.size() > 0">
            and wmd.sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        <if test="skuExclude != null  and cabinetNo != ''">
            and wmd.sku != #{skuExclude}
        </if>
        <if test="detailState != null">
            and wmd.state = #{detailState}
        </if>
        order by wmd.cabinet_no is null, wmd.cabinet_no asc, wmd.id desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_mission_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO"
            useGeneratedKeys="true">
        insert into wms_mission_detail (create_time, update_time, mission_no,
                                        warehouse_no, sku, produce_time,
                                        shelf_life, batch_no, should_in_quantity,
                                        exe_quantity, tenant_id, unit_no, abnormal_quantity
            , state, operator_id, operator, cabinet_no, deleted_at, init, split_type, split_info)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{missionNo,jdbcType=VARCHAR},
                #{warehouseNo,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{produceTime,jdbcType=TIMESTAMP},
                #{shelfLife,jdbcType=TIMESTAMP}, #{batchNo,jdbcType=VARCHAR}, #{shouldInQuantity,jdbcType=INTEGER},
                #{exeQuantity,jdbcType=INTEGER}, #{tenantId}, #{unitNo}, #{abnormalQuantity}, #{state},
                #{operatorId}, #{operator}, #{cabinetNo}, #{deletedAt}, #{init}, #{splitType}, #{splitInfo})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO"
            useGeneratedKeys="true">
        insert into wms_mission_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="missionNo != null">
                mission_no,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="produceTime != null">
                produce_time,
            </if>
            <if test="shelfLife != null">
                shelf_life,
            </if>
            <if test="batchNo != null">
                batch_no,
            </if>
            <if test="shouldInQuantity != null">
                should_in_quantity,
            </if>
            <if test="exeQuantity != null">
                exe_quantity,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="unitNo != null">
                unit_no,
            </if>
            <if test="abnormalQuantity != null">
                abnormal_quantity,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="cabinetNo != null">
                cabinet_no,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="deletedAt != null">
                deleted_at,
            </if>
            <if test="init != null">
                init,
            </if>
            <if test="splitType != null">
                split_type,
            </if>
            <if test="splitInfo != null">
                split_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="produceTime != null">
                #{produceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="shelfLife != null">
                #{shelfLife,jdbcType=TIMESTAMP},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="shouldInQuantity != null">
                #{shouldInQuantity,jdbcType=INTEGER},
            </if>
            <if test="exeQuantity != null">
                #{exeQuantity,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="unitNo != null">
                #{unitNo},
            </if>
            <if test="abnormalQuantity != null">
                #{abnormalQuantity},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="cabinetNo != null">
                #{cabinetNo},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
            <if test="operatorId != null">
                #{operatorId},
            </if>
            <if test="deletedAt != null">
                #{deletedAt},
            </if>
            <if test="init != null">
                #{init},
            </if>
            <if test="split_type != null">
                #{splitType},
            </if>
            <if test="splitInfo != null">
                #{splitInfo},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO"
            useGeneratedKeys="true">
        insert into wms_mission_detail (mission_no,
        warehouse_no, sku, produce_time,
        shelf_life, batch_no, should_in_quantity,
        exe_quantity, tenant_id, unit_no, abnormal_quantity
        ,state, operator_id, operator, cabinet_no, deleted_at, init, split_type, split_info)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.missionNo,jdbcType=VARCHAR},
            #{item.warehouseNo,jdbcType=BIGINT}, #{item.sku,jdbcType=VARCHAR}, #{item.produceTime,jdbcType=TIMESTAMP},
            #{item.shelfLife,jdbcType=TIMESTAMP}, #{item.batchNo,jdbcType=VARCHAR},
            #{item.shouldInQuantity,jdbcType=INTEGER},
            #{item.exeQuantity,jdbcType=INTEGER}, #{item.tenantId}, #{item.unitNo}, #{item.abnormalQuantity},
            #{item.state},
            #{item.operatorId}, #{item.operator}, #{item.cabinetNo}, #{item.deletedAt}, #{item.init}, #{item.splitType},
            #{item.splitInfo})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO">
        update wms_mission_detail
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="missionNo != null">
                mission_no = #{missionNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="produceTime != null">
                produce_time = #{produceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="shelfLife != null">
                shelf_life = #{shelfLife,jdbcType=TIMESTAMP},
            </if>
            <if test="batchNo != null">
                batch_no = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="shouldInQuantity != null">
                should_in_quantity = #{shouldInQuantity,jdbcType=INTEGER},
            </if>
            <if test="exeQuantity != null">
                exe_quantity = #{exeQuantity,jdbcType=INTEGER},
            </if>
            <if test="abnormalQuantity != null">
                abnormal_quantity = #{abnormalQuantity},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="cabinetNo != null">
                cabinet_no = #{cabinetNo},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="deletedAt != null">
                deleted_at = #{deletedAt},
            </if>
            <if test="init != null">
                init = #{init},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateQuantityAndState"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO">
        update wms_mission_detail
        <set>
            <if test="exeQuantity != null">
                exe_quantity = exe_quantity + #{exeQuantity},
            </if>
            <if test="abnormalQuantity != null">
                abnormal_quantity = ifnull(abnormal_quantity,0) + #{abnormalQuantity},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
        and should_in_quantity >= ifnull(exe_quantity,0) + ifnull(#{exeQuantity},0) + ifnull(abnormal_quantity,0) +
        ifnull(#{abnormalQuantity},0)
        and abs(should_in_quantity - ifnull(exe_quantity,0) - ifnull(#{exeQuantity},0)) >=
        abs(ifnull(abnormal_quantity,0) + ifnull(#{abnormalQuantity},0))
    </update>
    <update id="updateExeQuantityById"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO">
        update wms_mission_detail
        set exe_quantity = exe_quantity + #{exeQuantity}
        where id = #{id,jdbcType=BIGINT}
          and should_in_quantity >= exe_quantity + #{exeQuantity}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO">
        update wms_mission_detail
        set create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            mission_no         = #{missionNo,jdbcType=VARCHAR},
            warehouse_no       = #{warehouseNo,jdbcType=BIGINT},
            sku                = #{sku,jdbcType=VARCHAR},
            produce_time       = #{produceTime,jdbcType=TIMESTAMP},
            shelf_life         = #{shelfLife,jdbcType=TIMESTAMP},
            batch_no           = #{batchNo,jdbcType=VARCHAR},
            should_in_quantity = #{shouldInQuantity,jdbcType=INTEGER},
            exe_quantity       = #{exeQuantity,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryPickSkuListByMissionNo" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select wmd.id,
               wmd.sku,
               wmd.cabinet_no,
               wmd.state,
               wmd.produce_time,
               wmd.shelf_life,
               wmd.operator_id,
               wmd.operator
        from wms_mission_detail wmd
                 inner join wms_cabinet wc on wc.cabinet_code = wmd.cabinet_no
                 inner join inventory as i on wmd.sku = i.sku
                 inner join products as p on p.pd_id = i.pd_id
        where wmd.mission_no = #{missionNo}
          and wmd.deleted_at = 0
          and wmd.warehouse_no = #{warehouseNo}
          and wc.warehouse_no = #{warehouseNo}
        order by wmd.state asc, wc.sequence asc, wmd.cabinet_no asc, wmd.id desc
    </select>

    <select id="listMissionDetail"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.FindMissionDetailDO"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where warehouse_no = #{warehouseNo}
        and mission_no = #{missionNo}
        and mission_type = #{missionType}
        and sku = #{sku}
        and cabinet_no = #{cabinetNo}
        and produce_time = #{produceTime}
        and shelf_life = #{shelfLife}
        and batch_no = #{batch}
    </select>

    <select id="sumMissionPickRate" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select sum(exe_quantity)       as exe_quantity,
               sum(should_in_quantity) as should_in_quantity,
               sum(abnormal_quantity)  as abnormal_quantity
        from wms_mission_detail
        where mission_no = #{missionNo}
          and deleted_at = 0
    </select>

    <select id="findMissionDetailForStocktaking" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where mission_no = #{missionNo}
        and sku = #{sku}
        and deleted_at > 0
        and produce_time is null
        and shelf_life is null
        <if test="cabinetNo != null and cabinetNo != ''">
            and (cabinet_no = #{cabinetNo}
            or cabinet_no is null)
        </if>
        <if test="cabinetNo == null ">
            and cabinet_no is null
        </if>
        limit 1
    </select>

    <select id="findMissionDetailForStocktakingUnfinishedSku" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="Base_Column_List"/>
        from wms_mission_detail
        where mission_no = #{missionNo}
        and state in (10,20)
        and sku in
        <foreach collection="skus" item="sku" separator="," open="(" close=")">
            #{sku}
        </foreach>
    </select>

    <select id="pcDetailPage" resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        wm.mission_no missionNo, wmd.id, wmd.sku sku
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_execute_unit weu on wm.mission_no = weu.mission_no
        </if>
        <if test="storeNo != null or sourceOrderNo != null or bizType != null">
            inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        </if>
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="categoryId != null or supplierId != null">
            inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
            <if test="categoryId != null ">
                and wmse.category_id = #{categoryId}
            </if>
            <if test="supplierId != null ">
                and wmse.supplier_id = #{supplierId}
            </if>
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
        </if>
        <where>
            <if test="warehouseNo != null">
                wm.warehouse_no = #{warehouseNo}
            </if>
            <if test="storeNo != null">
                and wmsp.store_no = #{storeNo}
            </if>
            <if test="sourceOrderNo != null">
                and wmsp.source_order_no = #{sourceOrderNo}
            </if>
            <if test="missionType != null">
                and wm.mission_type = #{missionType,jdbcType=INTEGER}
            </if>
            <if test="bizType != null and bizType.size() > 0">
                and wmsp.source_type in
                <foreach collection="bizType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="state != null and state.size() > 0">
                and wm.state in
                <foreach collection="state" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="missionNo != null">
                and wm.mission_no = #{missionNo,jdbcType=VARCHAR}
            </if>
            <if test="sku != null">
                and wmd.sku = #{sku}
            </if>
            <if test="batchNo != null">
                and wmd.batch_no = #{batchNo}
            </if>
            <if test="skuList != null and skuList.size() > 0">
                AND wmd.`sku` in
                <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                    #{sku1}
                </foreach>
            </if>
            <if test="operatorName != null">
                and wmo.operator_name = #{operatorName}
                and wmo.cancel_time = 0
            </if>
            <if test="containerNo != null">
                and weu.carrier_code = #{containerNo}
            </if>
            <if test="startTime != null">
                and wm.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and wm.create_time &lt; #{endTime}
            </if>
            <if test="updateStartTime != null">
                and wm.update_time &gt;= #{updateStartTime}
            </if>
            <if test="updateEndTime != null">
                and wm.update_time &lt; #{updateEndTime}
            </if>
        </where>
        group by wm.mission_no, wmd.id
        order by wm.mission_no desc, wmd.id desc
    </select>


    <select id="pdaDetailPage"  resultType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.MissionDetailDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
        tem.state, tem.missionNo, tem.id, tem.sku
        from (
        select
        wm.state, wm.mission_no missionNo, wmd.id, wmd.sku sku
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_execute_unit weu on wm.mission_no = weu.mission_no
            and weu.state in (20)
            and weu.carrier_code = #{containerNo}
        </if>
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            AND wmd.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        <if test="batchNo != null">
            and wmd.batch_no = #{batchNo}
        </if>
        <if test="batchNoList != null and batchNoList.size() > 0">
            AND wmd.`batch_no` in
            <foreach collection="batchNoList" item="batchNo1" open="(" close=")" separator=",">
                #{batchNo1}
            </foreach>
        </if>
        <if test="categoryId != null or supplierId != null">
            inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
            <if test="categoryId != null ">
                and wmse.category_id = #{categoryId}
            </if>
            <if test="supplierId != null ">
                and wmse.supplier_id = #{supplierId}
            </if>
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
            and wmo.operator_name = #{operatorName}
            and wmo.cancel_time = 0
        </if>
        where wm.state in (20,30,40)
        and wmsp.source_type not in (18,21)
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo} and wmsp.mission_no = wm.mission_no)
        </if>
        union all
        select
            wm.state, wm.mission_no missionNo, wmd.id, wmd.sku sku
        from wms_mission wm
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
            and wmsp.source_type not in (18,21)
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            AND wmd.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        <if test="batchNo != null">
            and wmd.batch_no = #{batchNo}
        </if>
        <if test="batchNoList != null and batchNoList.size() > 0">
            AND wmd.`batch_no` in
            <foreach collection="batchNoList" item="batchNo1" open="(" close=")" separator=",">
                #{batchNo1}
            </foreach>
        </if>
        <if test="categoryId != null or supplierId != null">
            inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
            <if test="categoryId != null ">
                and wmse.category_id = #{categoryId}
            </if>
            <if test="supplierId != null ">
                and wmse.supplier_id = #{supplierId}
            </if>
        </if>
        where wm.state = 10
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo}
              and wmsp.mission_no = wm.mission_no)
        </if>
        ) tem
        order by concat(tem.state,tem.id) desc
    </select>

    <select id="pageMissionCategoryPda"
            resultType="net.summerfarm.wms.domain.mission.domainobject.queryres.MissionDetailCategoryRes"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
            distinct tem.category_id as categoryId, tem.category_name as categoryName
        from (
        select
            wmse.category_id, wmse.category_name
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_execute_unit weu on wm.mission_no = weu.mission_no
            and weu.state in (20)
            and weu.carrier_code = #{containerNo}
        </if>
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            AND wmd.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        <if test="batchNo != null">
            and wmd.batch_no = #{batchNo}
        </if>
        <if test="missionDetailId != null">
            and wmd.id = #{missionDetailId}
        </if>
        inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
            and wmse.category_id is not null
            and wmse.category_id != ''
        <if test="categoryId != null ">
            and wmse.category_id = #{categoryId}
        </if>
        <if test="supplierId != null ">
            and wmse.supplier_id = #{supplierId}
        </if>
        <if test="operatorName != null">
            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no
            and wmo.operator_name = #{operatorName}
            and wmo.cancel_time = 0
        </if>
        where wm.state in (20,30,40)
        and wmsp.source_type not in (18,21)
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo} and wmsp.mission_no = wm.mission_no)
        </if>
        union all
        select
            wmse.category_id, wmse.category_name
        from wms_mission wm
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        and wmsp.source_type not in (18,21)
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            AND wmd.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        <if test="batchNo != null">
            and wmd.batch_no = #{batchNo}
        </if>
        <if test="missionDetailId != null">
            and wmd.id = #{missionDetailId}
        </if>
        inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
            and wmse.category_id is not null
            and wmse.category_id != ''
        <if test="categoryId != null ">
            and wmse.category_id = #{categoryId}
        </if>
        <if test="supplierId != null ">
            and wmse.supplier_id = #{supplierId}
        </if>
        where wm.state = 10
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo}
            and wmsp.mission_no = wm.mission_no)
        </if>
        ) tem
    </select>

    <select id="pageMissionSupplierPda"
            resultType="net.summerfarm.wms.domain.mission.domainobject.queryres.MissionDetailSupplierRes"
            parameterType="net.summerfarm.wms.infrastructure.dao.mission.dataobject.query.PageMissionQueryDO">
        select
            distinct tem.supplier_id as supplierId
        from (
        select
        wmse.supplier_id
        from wms_mission wm
        <if test="containerNo != null">
            inner join wms_execute_unit weu on wm.mission_no = weu.mission_no
            and weu.state in (20)
            and weu.carrier_code = #{containerNo}
        </if>
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            AND wmd.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        <if test="batchNo != null">
            and wmd.batch_no = #{batchNo}
        </if>
        <if test="missionDetailId != null">
            and wmd.id = #{missionDetailId}
        </if>
        inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
        and wmse.category_id is not null
        and wmse.category_id != ''
        <if test="categoryId != null ">
            and wmse.category_id = #{categoryId}
        </if>
        <if test="supplierId != null ">
            and wmse.supplier_id = #{supplierId}
        </if>
<!--        <if test="operatorName != null">-->
<!--            inner join wms_mission_operator wmo on wm.mission_no = wmo.mission_no-->
<!--            and wmo.operator_name = #{operatorName}-->
<!--            and wmo.cancel_time = 0-->
<!--        </if>-->
        where wm.state in (20,30,40)
        and wmsp.source_type not in (18,21)
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo} and wmsp.mission_no = wm.mission_no)
        </if>
        union all
        select
        wmse.supplier_id
        from wms_mission wm
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        and wmsp.source_type not in (18,21)
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            AND wmd.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        <if test="batchNo != null">
            and wmd.batch_no = #{batchNo}
        </if>
        <if test="missionDetailId != null">
            and wmd.id = #{missionDetailId}
        </if>
        inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
        and wmse.category_id is not null
        and wmse.category_id != ''
        <if test="categoryId != null ">
            and wmse.category_id = #{categoryId}
        </if>
        <if test="supplierId != null ">
            and wmse.supplier_id = #{supplierId}
        </if>
        where wm.state = 10
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo}
            and wmsp.mission_no = wm.mission_no)
        </if>
        union all
        select
        wmse.supplier_id
        from wms_mission wm
        inner join wms_mission_source_property wmsp on wm.mission_no = wmsp.mission_no
        and wmsp.source_type not in (18,21)
        inner join wms_mission_detail wmd on wm.mission_no = wmd.mission_no
        <if test="sku != null">
            and wmd.sku = #{sku}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            AND wmd.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        <if test="batchNo != null">
            and wmd.batch_no = #{batchNo}
        </if>
        <if test="missionDetailId != null">
            and wmd.id = #{missionDetailId}
        </if>
        inner join wms_mission_detail_extend wmse on wmse.mission_no = wmd.mission_no and wmse.sku = wmd.sku
        and wmse.category_id is not null
        and wmse.category_id != ''
        <if test="categoryId != null ">
            and wmse.category_id = #{categoryId}
        </if>
        <if test="supplierId != null ">
            and wmse.supplier_id = #{supplierId}
        </if>
        where wm.state = 60
        <if test="warehouseNo != null">
            and wm.warehouse_no = #{warehouseNo}
        </if>
        <if test="missionType != null">
            and wm.mission_type = #{missionType,jdbcType=INTEGER}
        </if>
        <if test="missionNo != null">
            and wm. mission_no = #{missionNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and exists (
            select 1
            from wms_mission_source_property wmsp
            where wmsp.store_no=#{storeNo}
            and wmsp.mission_no = wm.mission_no)
        </if>
        and wm.update_time &gt;= DATE_FORMAT(CURDATE(), '%Y-%m-%d 00:00:00')
        and wm.update_time &lt; DATE_FORMAT(CURDATE() + INTERVAL 1 DAY, '%Y-%m-%d 00:00:00')
        ) tem
        where tem.supplier_id is not null
    </select>
</mapper>