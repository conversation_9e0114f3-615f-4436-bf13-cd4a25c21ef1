<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.crosswarehouse.CrossWarehouseSortInfoDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.crosswarehouse.dataobject.CrossWarehouseSortInfoDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="store_no" jdbcType="INTEGER" property="storeNo"/>
        <result column="pso_no" jdbcType="VARCHAR" property="psoNo"/>
        <result column="sale_order_no" jdbcType="VARCHAR" property="saleOrderNo"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="generate_quantity" jdbcType="INTEGER" property="generateQuantity"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="supplier_id" jdbcType="INTEGER" property="supplierId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="fulfillment_no" jdbcType="VARCHAR" property="fulfillmentNo"/>
        <result column="init_quantity" jdbcType="INTEGER" property="initQuantity"/>
        <result column="shelve_quantity" jdbcType="INTEGER" property="shelveQuantity"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , create_time, update_time, warehouse_no, store_no, pso_no, sale_order_no, sku,
    total_quantity, generate_quantity, remark, supplier_id, tenant_id, fulfillment_no, init_quantity, shelve_quantity
    </sql>

    <select id="findNoAllocationComplete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_cross_warehouse_sort_info
        where
        warehouse_no = #{warehouseNo}
        and pso_no = #{psoNo}
        and sku = #{sku}
        and total_quantity &gt; generate_quantity
        order by store_no asc, id asc
    </select>

    <select id="findNoAllocationCompleteBySkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_cross_warehouse_sort_info
        where
        warehouse_no = #{warehouseNo}
        and pso_no = #{psoNo}
        and sku in <foreach collection="skuList" item="sku" open="(" close=")" separator=",">#{sku}</foreach>
        and total_quantity &gt; generate_quantity
        order by store_no asc, id asc
    </select>

    <update id="updateAllocationQuantity">
        update
            wms_cross_warehouse_sort_info
        set generate_quantity = #{newGenerateQuantity}
        where id = #{id}
          and generate_quantity = #{oldGenerateQuantity}
    </update>

    <update id="updateAllocationShelveQuantity">
        update
            wms_cross_warehouse_sort_info
        set shelve_quantity = shelve_quantity + #{allocationQuantity}
        where id = #{id}
          and init_quantity >= #{allocationQuantity} + shelve_quantity + generate_quantity
    </update>

    <select id="findSimpleByUnique" resultMap="BaseResultMap">
        select
        pso_no, sku, sale_order_no, fulfillment_no
        from wms_cross_warehouse_sort_info
        where
        pso_no in
        <foreach collection="psoNoList" item="psoNo" separator="," open="(" close=")">
            #{psoNo}
        </foreach>
        and sale_order_no in
        <foreach collection="saleOrderNoList" item="saleOrderNo" separator="," open="(" close=")">
            #{saleOrderNo}
        </foreach>
        and sku in
        <foreach collection="skuCodeList" item="sku" separator="," open="(" close=")">
            #{sku}
        </foreach>
        <if test="fulfillmentNoList != null and fulfillmentNoList.size > 0">
            and fulfillment_no in
            <foreach collection="fulfillmentNoList" item="fulfillmentNo" separator="," open="(" close=")">
                #{fulfillmentNo}
            </foreach>
        </if>
    </select>

    <insert id="batchSave" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.crosswarehouse.dataobject.CrossWarehouseSortInfoDO"
            useGeneratedKeys="true">
        insert ignore into wms_cross_warehouse_sort_info (
            create_time,
            update_time,
            warehouse_no,
            store_no,
            pso_no,
            sale_order_no,
            sku,
            total_quantity,
            generate_quantity,
            remark,
            supplier_id,
            tenant_id,
            fulfillment_no,
            init_quantity,
            shelve_quantity)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.warehouseNo,jdbcType=BIGINT},
            #{item.storeNo},
            #{item.psoNo},
            #{item.saleOrderNo},
            #{item.sku},
            #{item.totalQuantity},
            #{item.generateQuantity},
            #{item.remark},
            #{item.supplierId},
            #{item.tenantId},
            #{item.fulfillmentNo},
            #{item.initQuantity},
            #{item.shelveQuantity}
            )
        </foreach>
    </insert>

    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_cross_warehouse_sort_info
        where
        id = #{id}
    </select>

    <select id="findCanAllocationShelveInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_cross_warehouse_sort_info
        where
        warehouse_no = #{warehouseNo}
        and pso_no = #{psoNo}
        and sku in <foreach collection="skuList" item="sku" open="(" close=")" separator=",">#{sku}</foreach>
        and total_quantity = generate_quantity
        and init_quantity > (generate_quantity + shelve_quantity)
        order by warehouse_no asc, store_no asc, id asc
    </select>

    <select id="findByPsoNo" resultMap="BaseResultMap">
        select
            warehouse_no,
            store_no,
            pso_no,
            sale_order_no,
            sku,
            total_quantity,
            generate_quantity,
            supplier_id,
            tenant_id,
            fulfillment_no,
            init_quantity,
            shelve_quantity
        from wms_cross_warehouse_sort_info
        where
           pso_no = #{psoNo}
    </select>

    <select id="findByFulfillmentNoAndPsoAndSku" resultMap="BaseResultMap">
        select
            id,
            warehouse_no,
            store_no,
            pso_no,
            sale_order_no,
            sku,
            total_quantity,
            generate_quantity,
            fulfillment_no,
            init_quantity,
            shelve_quantity
        from wms_cross_warehouse_sort_info
        where
            fulfillment_no = #{fulfillmentNo}
            and pso_no = #{psoNo}
            and sku = #{skuCode}
    </select>

    <update id="deductionTotalQuantityById">
        update wms_cross_warehouse_sort_info
        set
            total_quantity = total_quantity - #{quantity}
        where id = #{id} and total_quantity = #{oldTotalQuantity}
    </update>

    <select id="findByPsoNoAndSkuList" resultMap="BaseResultMap">
        select
            id,
            warehouse_no,
            store_no,
            pso_no,
            sale_order_no,
            sku,
            total_quantity,
            generate_quantity,
            fulfillment_no,
            init_quantity,
            shelve_quantity
        from wms_cross_warehouse_sort_info
        where
            pso_no = #{psoNo}
          and sku in <foreach collection="skuList" item="sku" open="(" close=")" separator=",">#{sku}</foreach>
        order by warehouse_no asc, store_no asc, id asc
    </select>

    <update id="updateQuantityById">
        update wms_cross_warehouse_sort_info
        set
            generate_quantity = generate_quantity + #{addGenerateQuantity},
            shelve_quantity = shelve_quantity + #{addShelveQuantity}
        where id = #{id}
            and total_quantity &gt;= (generate_quantity + #{addGenerateQuantity})
            and init_quantity &gt;= (generate_quantity + #{addGenerateQuantity} + shelve_quantity + #{addShelveQuantity})
    </update>

    <select id="findByFulfillmentNoAndSkuCodeList" resultMap="BaseResultMap">
        select
        id,
        warehouse_no,
        store_no,
        pso_no,
        sale_order_no,
        sku,
        total_quantity,
        generate_quantity,
        fulfillment_no,
        init_quantity,
        shelve_quantity
        from wms_cross_warehouse_sort_info
        where fulfillment_no = #{fulfillmentNo}
        <if test="skuCodeList != null and skuCodeList.size > 0">
            and sku in
            <foreach collection="skuCodeList" item="sku" separator="," open="(" close=")">
                #{sku}
            </foreach>
        </if>
    </select>

    <update id="updateRemarkById">
        update wms_cross_warehouse_sort_info
        set remark = #{remark}
        where id = #{id}
    </update>

    <update id="updateStoreNoById">
        update wms_cross_warehouse_sort_info
        set store_no = #{storeNo}
        where id = #{id}
    </update>

    <select id="findNotNullRemarkDataByCreateTimeBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_cross_warehouse_sort_info
        where
        create_time between #{startCreateTime} and #{endCreateTime}
        and remark is not null
    </select>
</mapper>