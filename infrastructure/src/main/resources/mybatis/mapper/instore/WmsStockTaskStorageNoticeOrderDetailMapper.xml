<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.instore.WmsStockTaskStorageNoticeOrderDetailMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsStockTaskStorageNoticeOrderDetailResultMap" type="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrderDetail">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="notice_order_id" property="noticeOrderId" jdbcType="NUMERIC"/>
		<result column="goods_recycle_order_no" property="goodsRecycleOrderNo" jdbcType="VARCHAR"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="operator" property="operator" jdbcType="VARCHAR"/>
		<result column="gmt_created" property="gmtCreated" jdbcType="TIMESTAMP"/>
		<result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
		<result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
		<result column="last_ver" property="lastVer" jdbcType="INTEGER"/>
		<result column="warehouse_tenant_id" property="warehouseTenantId" jdbcType="NUMERIC"/>
		<result column="customer_sku_code" property="customerSkuCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsStockTaskStorageNoticeOrderDetailColumns">
          t.id,
          t.notice_order_id,
          t.goods_recycle_order_no,
          t.tenant_id,
          t.warehouse_no,
          t.sku,
          t.goods_name,
          t.quantity,
          t.creator,
          t.operator,
          t.gmt_created,
          t.gmt_modified,
          t.is_deleted,
          t.last_ver,
          t.warehouse_tenant_id,
          t.customer_sku_code
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="noticeOrderId != null">
                AND t.notice_order_id = #{noticeOrderId}
            </if>
            <if test="noticeOrderIds != null and noticeOrderIds.size() > 0">
                AND t.notice_order_id IN
                <foreach collection="noticeOrderIds" item="noticeOrderId" open="(" close=")" separator=",">
                    #{noticeOrderId}
                </foreach>
            </if>
			<if test="goodsRecycleOrderNo != null and goodsRecycleOrderNo !=''">
                AND t.goods_recycle_order_no = #{goodsRecycleOrderNo}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="goodsName != null and goodsName !=''">
                AND t.goods_name = #{goodsName}
            </if>
			<if test="quantity != null">
                AND t.quantity = #{quantity}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="operator != null and operator !=''">
                AND t.operator = #{operator}
            </if>
			<if test="gmtCreated != null">
                AND t.gmt_created = #{gmtCreated}
            </if>
			<if test="gmtModified != null">
                AND t.gmt_modified = #{gmtModified}
            </if>
			<if test="isDeleted != null">
                AND t.is_deleted = #{isDeleted}
            </if>
			<if test="lastVer != null">
                AND t.last_ver = #{lastVer}
            </if>
			<if test="warehouseTenantId != null">
                AND t.warehouse_tenant_id = #{warehouseTenantId}
            </if>
			<if test="customerSkuCode != null and customerSkuCode !=''">
                AND t.customer_sku_code = #{customerSkuCode}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="noticeOrderId != null">
                    t.notice_order_id = #{noticeOrderId},
                </if>
                <if test="goodsRecycleOrderNo != null">
                    t.goods_recycle_order_no = #{goodsRecycleOrderNo},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="goodsName != null">
                    t.goods_name = #{goodsName},
                </if>
                <if test="quantity != null">
                    t.quantity = #{quantity},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="operator != null">
                    t.operator = #{operator},
                </if>
                <if test="gmtCreated != null">
                    t.gmt_created = #{gmtCreated},
                </if>
                <if test="gmtModified != null">
                    t.gmt_modified = #{gmtModified},
                </if>
                <if test="isDeleted != null">
                    t.is_deleted = #{isDeleted},
                </if>
                <if test="lastVer != null">
                    t.last_ver = #{lastVer},
                </if>
                <if test="warehouseTenantId != null">
                    t.warehouse_tenant_id = #{warehouseTenantId},
                </if>
                <if test="customerSkuCode != null">
                    t.customer_sku_code = #{customerSkuCode},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsStockTaskStorageNoticeOrderDetailResultMap" >
        /*FORCE_MASTER*/ SELECT <include refid="wmsStockTaskStorageNoticeOrderDetailColumns" />
        FROM wms_stock_task_storage_notice_order_detail t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.instore.param.WmsStockTaskStorageNoticeOrderDetailQueryParam"  resultType="net.summerfarm.wms.domain.instore.entity.WmsStockTaskStorageNoticeOrderDetailEntity" >
        /*FORCE_MASTER*/ SELECT
            t.id id,
            t.notice_order_id noticeOrderId,
            t.goods_recycle_order_no goodsRecycleOrderNo,
            t.tenant_id tenantId,
            t.warehouse_no warehouseNo,
            t.sku sku,
            t.goods_name goodsName,
            t.quantity quantity,
            t.creator creator,
            t.operator operator,
            t.gmt_created gmtCreated,
            t.gmt_modified gmtModified,
            t.is_deleted isDeleted,
            t.last_ver lastVer,
            t.warehouse_tenant_id warehouseTenantId,
            t.customer_sku_code customerSkuCode
        FROM wms_stock_task_storage_notice_order_detail t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.instore.param.WmsStockTaskStorageNoticeOrderDetailQueryParam" resultMap="wmsStockTaskStorageNoticeOrderDetailResultMap" >
        /*FORCE_MASTER*/ SELECT <include refid="wmsStockTaskStorageNoticeOrderDetailColumns" />
        FROM wms_stock_task_storage_notice_order_detail t
        <include refid="whereColumnBySelect"></include>
    </select>

    <select id="selectSkuQuantityByNoticeOrderId" resultMap="wmsStockTaskStorageNoticeOrderDetailResultMap" >
        /*FORCE_MASTER*/ SELECT sku, SUM(quantity) quantity
        FROM wms_stock_task_storage_notice_order_detail
        WHERE notice_order_id IN
        <foreach collection="noticeOrderIds" item="noticeOrderId" open="(" close=")" separator=",">
            #{noticeOrderId}
        </foreach>
        GROUP BY sku
    </select>


	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrderDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_stock_task_storage_notice_order_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="noticeOrderId != null">
				  notice_order_id,
              </if>
              <if test="goodsRecycleOrderNo != null">
				  goods_recycle_order_no,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="goodsName != null">
				  goods_name,
              </if>
              <if test="quantity != null">
				  quantity,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="operator != null">
				  operator,
              </if>
              <if test="gmtCreated != null">
				  gmt_created,
              </if>
              <if test="gmtModified != null">
				  gmt_modified,
              </if>
              <if test="isDeleted != null">
				  is_deleted,
              </if>
              <if test="lastVer != null">
				  last_ver,
              </if>
              <if test="warehouseTenantId != null">
				  warehouse_tenant_id,
              </if>
              <if test="customerSkuCode != null">
				  customer_sku_code,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="noticeOrderId != null">
				#{noticeOrderId,jdbcType=NUMERIC},
              </if>
              <if test="goodsRecycleOrderNo != null">
				#{goodsRecycleOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="goodsName != null">
				#{goodsName,jdbcType=VARCHAR},
              </if>
              <if test="quantity != null">
				#{quantity,jdbcType=INTEGER},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="operator != null">
				#{operator,jdbcType=VARCHAR},
              </if>
              <if test="gmtCreated != null">
				#{gmtCreated,jdbcType=TIMESTAMP},
              </if>
              <if test="gmtModified != null">
				#{gmtModified,jdbcType=TIMESTAMP},
              </if>
              <if test="isDeleted != null">
				#{isDeleted,jdbcType=TINYINT},
              </if>
              <if test="lastVer != null">
				#{lastVer,jdbcType=INTEGER},
              </if>
              <if test="warehouseTenantId != null">
				#{warehouseTenantId,jdbcType=NUMERIC},
              </if>
              <if test="customerSkuCode != null">
				#{customerSkuCode,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrderDetail">
        INSERT INTO wms_stock_task_storage_notice_order_detail (notice_order_id,goods_recycle_order_no,tenant_id,warehouse_no,sku,goods_name,quantity,creator,operator,gmt_created,gmt_modified,is_deleted,last_ver,warehouse_tenant_id,customer_sku_code)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
                (#{item.noticeOrderId,jdbcType=NUMERIC},#{item.goodsRecycleOrderNo,jdbcType=VARCHAR},#{item.tenantId,jdbcType=NUMERIC},
                 #{item.warehouseNo,jdbcType=INTEGER},#{item.sku,jdbcType=VARCHAR},#{item.goodsName,jdbcType=VARCHAR},
                 #{item.quantity,jdbcType=INTEGER},#{item.creator,jdbcType=VARCHAR},#{item.operator,jdbcType=VARCHAR},
                 #{item.gmtCreated,jdbcType=TIMESTAMP},#{item.gmtModified,jdbcType=TIMESTAMP},#{item.isDeleted,jdbcType=TINYINT},
                 #{item.lastVer,jdbcType=INTEGER},#{item.warehouseTenantId,jdbcType=NUMERIC},#{item.customerSkuCode,jdbcType=VARCHAR})
        </foreach>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrderDetail" >
        UPDATE wms_stock_task_storage_notice_order_detail t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrderDetail" >
        DELETE FROM wms_stock_task_storage_notice_order_detail
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>