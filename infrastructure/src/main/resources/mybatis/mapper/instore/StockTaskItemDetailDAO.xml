<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.instore.StockTaskItemDetailDAO">

    <select id="selectByItemId" resultType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskItemDetailDO">
        select id, stock_task_item_id stockTaskItemId, sku, list_no listNo,quality_date qualityDate,
        quantity, remark, production_date productionDate, should_quantity shouldQuantity,
        out_store_quantity outStoreQuantity, add_time addTime, update_time updateTime
        from stock_task_item_detail
        WHERE stock_task_item_id in
        <foreach collection="list" item="itemId" separator="," close=")" open="(">
            #{itemId}
        </foreach>
    </select>

    <select id="selectByItemIdAndSku" resultType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskItemDetailDO">
        select id,
               stock_task_item_id stockTaskItemId,
               sku,
               list_no            listNo,
               quality_date       qualityDate,
               quantity,
               remark,
               production_date    productionDate,
               should_quantity    shouldQuantity,
               out_store_quantity outStoreQuantity,
               add_time           addTime,
               update_time        updateTime
        from stock_task_item_detail
        WHERE stock_task_item_id = #{itemId}
          and sku = #{sku}
          and production_date = #{produceDate}
          and quality_date = #{qualityDate} limit 1
    </select>

    <select id="queryStockTaskItemPeriodAllNoDetail" resultType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.AllocationOrderItemPeriodDO">
        /*FORCE_MASTER*/
        SELECT
            saiqp.id,
            saiqp.stock_allocation_item_id as stockAllocationItemId,
            saiqp.produce_period_start_time producePeriodStartTime,
            saiqp.produce_period_end_time producePeriodEndTime
        FROM stock_allocation_item_quality_period saiqp
        WHERE saiqp.is_delete = 0
        and saiqp.stock_allocation_item_id in
        <foreach collection="itemIdList" item="itemId1" open="(" close=")" separator=",">
            #{itemId1}
        </foreach>
    </select>

    <select id="selectByStockTaskId"  resultType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskProcessDetailDO">
        select stpd.id,
               stock_task_process_id stockTaskProcessId,
               item_id itemId,
               sku,
               list_no listNo,
               quality_date qualityDate,
               quantity,
               production_date productionDate,
               remark,
               transfer_sku transferSku,
               transfer_quantity transferQuantity,
               transfer_scale transferScale,
               create_time createTime,
               creator,
               gl_no glNo,
               in_gl_no inGlNo,
               status
        from stock_task_process stp
                 left join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id
        where stp.stock_task_id = #{stockTaskId} and stpd.sku=#{sku}
    </select>
</mapper>