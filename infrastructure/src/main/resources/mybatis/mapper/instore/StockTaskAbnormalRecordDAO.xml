<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.instore.StockTaskAbnormalRecordDAO">

    <insert id="insert"
            parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskAbnormalRecordDO">
        insert into stock_task_abnormal_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            create_time,
            <if test="stockTaskId != null">
                stock_task_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="reasonType != null">
                reason_type,
            </if>
            <if test="createAdminId != null">
                create_admin_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            now(),
            <if test="stockTaskId != null">
                #{stockTaskId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="reasonType != null">
                #{reasonType},
            </if>
            <if test="createAdminId != null">
                #{createAdminId},
            </if>
        </trim>
    </insert>

    <select id="select" resultType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskAbnormalRecordDO">
        select stock_task_id stockTaskId ,sku ,quantity ,reason_type reasonType ,create_admin_id createAdminId
        from stock_task_abnormal_record
        <where>
            <if test="stockTaskId != null">
                AND stock_task_id = #{stockTaskId}
            </if>
            <if test="sku != null">
                AND sku= #{sku}
            </if>
        </where>
    </select>

    <select id="list" resultType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskAbnormalRecordDO">
        /*FORCE_MASTER*/  select stock_task_id stockTaskId ,sku ,quantity ,reason_type reasonType ,create_admin_id createAdminId
        from stock_task_abnormal_record
        <where>
            <if test="stockTaskId != null">
                AND stock_task_id = #{stockTaskId}
            </if>
            <if test="skus != null and skus.size() > 0">
                AND sku in
                <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listByTaskIds"
            resultType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.StockTaskAbnormalRecordDO">
        select stock_task_id stockTaskId ,sku ,quantity ,reason_type reasonType ,create_admin_id createAdminId
        from stock_task_abnormal_record
        where stock_task_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>