<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.instore.WmsStockTaskStorageNoticeOrderMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsStockTaskStorageNoticeOrderResultMap" type="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrder">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="shop_id" property="shopId" jdbcType="NUMERIC"/>
		<result column="shop_name" property="shopName" jdbcType="VARCHAR"/>
		<result column="goods_recycle_order_no" property="goodsRecycleOrderNo" jdbcType="VARCHAR"/>
		<result column="out_order_no" property="outOrderNo" jdbcType="VARCHAR"/>
		<result column="after_sale_order_no" property="afterSaleOrderNo" jdbcType="VARCHAR"/>
		<result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="store_no" property="storeNo" jdbcType="INTEGER"/>
		<result column="after_sale_order_type" property="afterSaleOrderType" jdbcType="INTEGER"/>
		<result column="except_time" property="exceptTime" jdbcType="TIMESTAMP"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="stock_task_storage_id" property="stockTaskStorageId" jdbcType="NUMERIC"/>
		<result column="stock_task_storage_create_time" property="stockTaskStorageCreateTime" jdbcType="TIMESTAMP"/>
		<result column="receiver" property="receiver" jdbcType="VARCHAR"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="province" property="province" jdbcType="VARCHAR"/>
		<result column="city" property="city" jdbcType="VARCHAR"/>
		<result column="area" property="area" jdbcType="VARCHAR"/>
		<result column="detail_address" property="detailAddress" jdbcType="VARCHAR"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="operator" property="operator" jdbcType="VARCHAR"/>
		<result column="gmt_created" property="gmtCreated" jdbcType="TIMESTAMP"/>
		<result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
		<result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
		<result column="last_ver" property="lastVer" jdbcType="INTEGER"/>
		<result column="warehouse_tenant_id" property="warehouseTenantId" jdbcType="NUMERIC"/>
		<result column="notice_batch" property="noticeBatch" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsStockTaskStorageNoticeOrderColumns">
          t.id,
          t.tenant_id,
          t.shop_id,
          t.shop_name,
          t.goods_recycle_order_no,
          t.out_order_no,
          t.after_sale_order_no,
          t.warehouse_name,
          t.warehouse_no,
          t.store_no,
          t.after_sale_order_type,
          t.except_time,
          t.status,
          t.stock_task_storage_id,
          t.stock_task_storage_create_time,
          t.receiver,
          t.phone,
          t.province,
          t.city,
          t.area,
          t.detail_address,
          t.creator,
          t.operator,
          t.gmt_created,
          t.gmt_modified,
          t.is_deleted,
          t.last_ver,
          t.warehouse_tenant_id,
          t.notice_batch
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="shopId != null">
                AND t.shop_id = #{shopId}
            </if>
			<if test="shopName != null and shopName !=''">
                AND t.shop_name = #{shopName}
            </if>
			<if test="goodsRecycleOrderNo != null and goodsRecycleOrderNo !=''">
                AND t.goods_recycle_order_no = #{goodsRecycleOrderNo}
            </if>
            <if test="goodsRecycleOrderNos != null and goodsRecycleOrderNos.size() > 0">
                AND t.goods_recycle_order_no IN
                <foreach collection="goodsRecycleOrderNos" item="goodsRecycleOrderNo" open="(" close=")" separator=",">
                    #{goodsRecycleOrderNo}
                </foreach>
            </if>
			<if test="outOrderNo != null and outOrderNo !=''">
                AND t.out_order_no = #{outOrderNo}
            </if>
			<if test="afterSaleOrderNo != null and afterSaleOrderNo !=''">
                AND t.after_sale_order_no = #{afterSaleOrderNo}
            </if>
			<if test="warehouseName != null and warehouseName !=''">
                AND t.warehouse_name = #{warehouseName}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
            <if test="warehouseNos != null and warehouseNos.size() > 0">
                AND t.warehouse_no IN
                <foreach collection="warehouseNos" item="warehouseNo" open="(" close=")" separator=",">
                    #{warehouseNo}
                </foreach>
            </if>
			<if test="storeNo != null">
                AND t.store_no = #{storeNo}
            </if>
			<if test="afterSaleOrderType != null">
                AND t.after_sale_order_type = #{afterSaleOrderType}
            </if>
			<if test="exceptTime != null">
                AND t.except_time = #{exceptTime}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="stockTaskStorageId != null">
                AND t.stock_task_storage_id = #{stockTaskStorageId}
            </if>
			<if test="stockTaskStorageCreateTime != null">
                AND t.stock_task_storage_create_time = #{stockTaskStorageCreateTime}
            </if>
			<if test="receiver != null and receiver !=''">
                AND t.receiver = #{receiver}
            </if>
			<if test="phone != null and phone !=''">
                AND t.phone = #{phone}
            </if>
			<if test="province != null and province !=''">
                AND t.province = #{province}
            </if>
			<if test="city != null and city !=''">
                AND t.city = #{city}
            </if>
			<if test="area != null and area !=''">
                AND t.area = #{area}
            </if>
			<if test="detailAddress != null and detailAddress !=''">
                AND t.detail_address = #{detailAddress}
            </if>
			<if test="creator != null and creator !=''">
                AND t.creator = #{creator}
            </if>
			<if test="operator != null and operator !=''">
                AND t.operator = #{operator}
            </if>
			<if test="gmtCreated != null">
                AND t.gmt_created = #{gmtCreated}
            </if>
			<if test="gmtModified != null">
                AND t.gmt_modified = #{gmtModified}
            </if>
			<if test="isDeleted != null">
                AND t.is_deleted = #{isDeleted}
            </if>
			<if test="lastVer != null">
                AND t.last_ver = #{lastVer}
            </if>
			<if test="warehouseTenantId != null">
                AND t.warehouse_tenant_id = #{warehouseTenantId}
            </if>
			<if test="noticeBatch != null and noticeBatch !=''">
                AND t.notice_batch = #{noticeBatch}
            </if>
            <if test="isSaasIn != null">
                <if test="isSaasIn == true">
                    AND t.tenant_id > 1 AND t.warehouse_tenant_id = 1
                </if>
                <if test="isSaasIn == false">
                    AND t.tenant_id = 1 AND t.warehouse_tenant_id = 1
                </if>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="shopId != null">
                    t.shop_id = #{shopId},
                </if>
                <if test="shopName != null">
                    t.shop_name = #{shopName},
                </if>
                <if test="goodsRecycleOrderNo != null">
                    t.goods_recycle_order_no = #{goodsRecycleOrderNo},
                </if>
                <if test="outOrderNo != null">
                    t.out_order_no = #{outOrderNo},
                </if>
                <if test="afterSaleOrderNo != null">
                    t.after_sale_order_no = #{afterSaleOrderNo},
                </if>
                <if test="warehouseName != null">
                    t.warehouse_name = #{warehouseName},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="storeNo != null">
                    t.store_no = #{storeNo},
                </if>
                <if test="afterSaleOrderType != null">
                    t.after_sale_order_type = #{afterSaleOrderType},
                </if>
                <if test="exceptTime != null">
                    t.except_time = #{exceptTime},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="stockTaskStorageId != null">
                    t.stock_task_storage_id = #{stockTaskStorageId},
                </if>
                <if test="stockTaskStorageCreateTime != null">
                    t.stock_task_storage_create_time = #{stockTaskStorageCreateTime},
                </if>
                <if test="receiver != null">
                    t.receiver = #{receiver},
                </if>
                <if test="phone != null">
                    t.phone = #{phone},
                </if>
                <if test="province != null">
                    t.province = #{province},
                </if>
                <if test="city != null">
                    t.city = #{city},
                </if>
                <if test="area != null">
                    t.area = #{area},
                </if>
                <if test="detailAddress != null">
                    t.detail_address = #{detailAddress},
                </if>
                <if test="creator != null">
                    t.creator = #{creator},
                </if>
                <if test="operator != null">
                    t.operator = #{operator},
                </if>
                <if test="gmtCreated != null">
                    t.gmt_created = #{gmtCreated},
                </if>
                <if test="gmtModified != null">
                    t.gmt_modified = #{gmtModified},
                </if>
                <if test="isDeleted != null">
                    t.is_deleted = #{isDeleted},
                </if>
                <if test="lastVer != null">
                    t.last_ver = #{lastVer},
                </if>
                <if test="warehouseTenantId != null">
                    t.warehouse_tenant_id = #{warehouseTenantId},
                </if>
                <if test="noticeBatch != null">
                    t.notice_batch = #{noticeBatch},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsStockTaskStorageNoticeOrderResultMap" >
        /*FORCE_MASTER*/ SELECT <include refid="wmsStockTaskStorageNoticeOrderColumns" />
        FROM wms_stock_task_storage_notice_order t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.instore.param.WmsStockTaskStorageNoticeOrderQueryParam"  resultType="net.summerfarm.wms.domain.instore.entity.WmsStockTaskStorageNoticeOrderEntity" >
        /*FORCE_MASTER*/ SELECT
            t.id id,
            t.tenant_id tenantId,
            t.shop_id shopId,
            t.shop_name shopName,
            t.goods_recycle_order_no goodsRecycleOrderNo,
            t.out_order_no outOrderNo,
            t.after_sale_order_no afterSaleOrderNo,
            t.warehouse_name warehouseName,
            t.warehouse_no warehouseNo,
            t.store_no storeNo,
            t.after_sale_order_type afterSaleOrderType,
            t.except_time exceptTime,
            t.status status,
            t.stock_task_storage_id stockTaskStorageId,
            t.stock_task_storage_create_time stockTaskStorageCreateTime,
            t.receiver receiver,
            t.phone phone,
            t.province province,
            t.city city,
            t.area area,
            t.detail_address detailAddress,
            t.creator creator,
            t.operator operator,
            t.gmt_created gmtCreated,
            t.gmt_modified gmtModified,
            t.is_deleted isDeleted,
            t.last_ver lastVer,
            t.warehouse_tenant_id warehouseTenantId,
            t.notice_batch noticeBatch
        FROM wms_stock_task_storage_notice_order t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.instore.param.WmsStockTaskStorageNoticeOrderQueryParam" resultMap="wmsStockTaskStorageNoticeOrderResultMap" >
        /*FORCE_MASTER*/ SELECT <include refid="wmsStockTaskStorageNoticeOrderColumns" />
        FROM wms_stock_task_storage_notice_order t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrder" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_stock_task_storage_notice_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="shopId != null">
				  shop_id,
              </if>
              <if test="shopName != null">
				  shop_name,
              </if>
              <if test="goodsRecycleOrderNo != null">
				  goods_recycle_order_no,
              </if>
              <if test="outOrderNo != null">
				  out_order_no,
              </if>
              <if test="afterSaleOrderNo != null">
				  after_sale_order_no,
              </if>
              <if test="warehouseName != null">
				  warehouse_name,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="storeNo != null">
				  store_no,
              </if>
              <if test="afterSaleOrderType != null">
				  after_sale_order_type,
              </if>
              <if test="exceptTime != null">
				  except_time,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="stockTaskStorageId != null">
				  stock_task_storage_id,
              </if>
              <if test="stockTaskStorageCreateTime != null">
				  stock_task_storage_create_time,
              </if>
              <if test="receiver != null">
				  receiver,
              </if>
              <if test="phone != null">
				  phone,
              </if>
              <if test="province != null">
				  province,
              </if>
              <if test="city != null">
				  city,
              </if>
              <if test="area != null">
				  area,
              </if>
              <if test="detailAddress != null">
				  detail_address,
              </if>
              <if test="creator != null">
				  creator,
              </if>
              <if test="operator != null">
				  operator,
              </if>
              <if test="gmtCreated != null">
				  gmt_created,
              </if>
              <if test="gmtModified != null">
				  gmt_modified,
              </if>
              <if test="isDeleted != null">
				  is_deleted,
              </if>
              <if test="lastVer != null">
				  last_ver,
              </if>
              <if test="warehouseTenantId != null">
				  warehouse_tenant_id,
              </if>
              <if test="noticeBatch != null">
				  notice_batch,
              </if>
              <if test="closeTime != null">
                  close_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="shopId != null">
				#{shopId,jdbcType=NUMERIC},
              </if>
              <if test="shopName != null">
				#{shopName,jdbcType=VARCHAR},
              </if>
              <if test="goodsRecycleOrderNo != null">
				#{goodsRecycleOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="outOrderNo != null">
				#{outOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="afterSaleOrderNo != null">
				#{afterSaleOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="warehouseName != null">
				#{warehouseName,jdbcType=VARCHAR},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="storeNo != null">
				#{storeNo,jdbcType=INTEGER},
              </if>
              <if test="afterSaleOrderType != null">
				#{afterSaleOrderType,jdbcType=INTEGER},
              </if>
              <if test="exceptTime != null">
				#{exceptTime,jdbcType=TIMESTAMP},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="stockTaskStorageId != null">
				#{stockTaskStorageId,jdbcType=NUMERIC},
              </if>
              <if test="stockTaskStorageCreateTime != null">
				#{stockTaskStorageCreateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="receiver != null">
				#{receiver,jdbcType=VARCHAR},
              </if>
              <if test="phone != null">
				#{phone,jdbcType=VARCHAR},
              </if>
              <if test="province != null">
				#{province,jdbcType=VARCHAR},
              </if>
              <if test="city != null">
				#{city,jdbcType=VARCHAR},
              </if>
              <if test="area != null">
				#{area,jdbcType=VARCHAR},
              </if>
              <if test="detailAddress != null">
				#{detailAddress,jdbcType=VARCHAR},
              </if>
              <if test="creator != null">
				#{creator,jdbcType=VARCHAR},
              </if>
              <if test="operator != null">
				#{operator,jdbcType=VARCHAR},
              </if>
              <if test="gmtCreated != null">
				#{gmtCreated,jdbcType=TIMESTAMP},
              </if>
              <if test="gmtModified != null">
				#{gmtModified,jdbcType=TIMESTAMP},
              </if>
              <if test="isDeleted != null">
				#{isDeleted,jdbcType=TINYINT},
              </if>
              <if test="lastVer != null">
				#{lastVer,jdbcType=INTEGER},
              </if>
              <if test="warehouseTenantId != null">
				#{warehouseTenantId,jdbcType=NUMERIC},
              </if>
              <if test="noticeBatch != null">
				#{noticeBatch,jdbcType=VARCHAR},
              </if>
              <if test="closeTime != null">
                #{closeTime,jdbcType=TIMESTAMP},
             </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrder" >
        UPDATE wms_stock_task_storage_notice_order t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>

    <update id="updateNoticeBatchAndStatusById" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrder" >
        update wms_stock_task_storage_notice_order
        set notice_batch = #{noticeBatch}, status = #{status}
        where id = #{id} and status = 1
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.instore.dataobject.WmsStockTaskStorageNoticeOrder" >
        DELETE FROM wms_stock_task_storage_notice_order
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>