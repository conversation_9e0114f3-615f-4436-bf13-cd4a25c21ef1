<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stocktaking.StocktakingDAO">
    <resultMap id="baseResultMap" type="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="area_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="type" jdbcType="BIGINT" property="dimension"/>
        <result column="cycle" jdbcType="BIGINT" property="cycle"/>
        <result column="checker" jdbcType="VARCHAR" property="operator"/>
        <result column="stock_taking_no" jdbcType="VARCHAR" property="stockTakingNo"/>
        <result column="status" jdbcType="BIGINT" property="state"/>
        <result column="audit_state" jdbcType="BIGINT" property="auditState"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="check_date" jdbcType="DATE" property="checkDate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="method" jdbcType="BIGINT" property="method"/>
        <result column="system_source" jdbcType="BIGINT" property="systemSource"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,area_no,type,cycle,checker,status,audit_state,create_time,update_time,stock_taking_no, check_date, remark, tenant_id, method, system_source
    </sql>

    <insert id="insert" keyColumn="id" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingDO">
        insert into stocktaking(area_no,
                                type,
                                cycle,
                                checker,
                                status,
                                audit_state
                                <if test="stockTakingNo != null">
                                    ,stock_taking_no
                                </if>
                                <if test="tenantId != null">
                                    ,tenant_id
                                </if>
                                <if test="method != null">
                                    ,method
                                </if>
                                <if test="systemSource != null">
                                    ,system_source
                                </if>
                                )
        values (#{warehouseNo},
                #{dimension},
                #{cycle},
                #{operator},
                #{state},
                #{auditState}
                <if test="stockTakingNo != null">
                    ,#{stockTakingNo,jdbcType=VARCHAR}
                </if>
                <if test="tenantId != null">
                    ,#{tenantId,jdbcType=BIGINT}
                </if>
                <if test="method != null">
                    ,#{method,jdbcType=BIGINT}
                </if>
                <if test="systemSource != null">
                    ,#{systemSource,jdbcType=BIGINT}
                </if>
                )
    </insert>

    <update id="update">
        update stocktaking
        <set>
            <if test="state != null">
                status = #{state},
            </if>
            <if test="auditState != null">
                audit_state = #{auditState},
            </if>
            <if test="cycle != null">
                cycle = #{cycle},
            </if>
            <if test="dimension != null">
                type = #{dimension},
            </if>
            <if test="operator != null">
                checker = #{operator}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectById" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stocktaking
        where id = #{id}
    </select>

    <select id="selectByIds" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stocktaking
        where id in
        <foreach collection="list" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>

    <select id="selectUnFinishSku" resultType="java.lang.String">
        /*FORCE_MASTER*/
        select DISTINCT(b.sku)
        FROM `stocktaking` as a
        LEFT JOIN `stock_taking_item` as b
        on a.`id` = b.taking_id
        WHERE a.`status` in (0,1)
        and a.`area_no` = #{warehouseNo}
        and b.sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
    </select>

    <select id="selectUnFinishSkuItemId" resultType="java.lang.Long">
        select DISTINCT(b.id)
        FROM `stocktaking` as a
        LEFT JOIN `stock_taking_item` as b
        on a.`id` = b.taking_id
        WHERE a.`status` in (0,1)
        and a.`area_no` = #{warehouseNo}
        and b.sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
    </select>

    <select id="listByCd" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stocktaking
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="warehouseNo != null">
                and area_no = #{warehouseNo}
            </if>
            <if test="auditState != null">
                and audit_state = #{auditState}
            </if>
            <if test="state != null">
                and status = #{state}
            </if>
            <if test="dimension != null">
                and type = #{dimension}
            </if>
            <if test="cycle != null">
                and cycle = #{cycle}
            </if>
            <if test="startAt != null">
                and create_time <![CDATA[>=]]> #{startAt}
            </if>
            <if test="endAt != null">
                and create_time <![CDATA[<=]]> #{endAt}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId,jdbcType=BIGINT}
            </if>
            <if test="method != null">
                and method = #{method,jdbcType=BIGINT}
            </if>
            <if test="tenantWarehouseNoList != null and tenantWarehouseNoList.size() != 0">
                and area_no in
                <foreach collection="tenantWarehouseNoList" item="warehouseNo" separator="," open="(" close=")">
                    #{warehouseNo}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="listBySaas" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stocktaking
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" open="(" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="warehouseNo != null">
                and area_no = #{warehouseNo}
            </if>
            <if test="creator != null">
                and checker = #{creator}
            </if>
            <if test="startAt != null">
                and create_time <![CDATA[>=]]> #{startAt}
            </if>
            <if test="endAt != null">
                and create_time <![CDATA[<]]> #{endAt}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="tenantWarehouseNoList != null and tenantWarehouseNoList.size() != 0">
                and area_no in
                <foreach collection="tenantWarehouseNoList" item="warehouseNo" separator="," open="(" close=")">
                    #{warehouseNo}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAllForSync" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stocktaking
        where id <![CDATA[<=]]> 14634
        order by id desc
    </select>

    <select id="countUnfinishedTask" resultType="java.lang.Long">
        select count(*)
        from stocktaking
        where area_no = #{warehouseNo}
        and status in (0,1)
    </select>

    <select id="selectByTakingNoAndWarehouseNoAndTenant" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stocktaking
        where stock_taking_no = #{stockTakingNo} and area_no = #{warehouseNo} and tenant_id = #{tenantId}
        order by id desc limit 1
    </select>

    <select id="queryStockTakingTaskPanelQuantity" resultType="net.summerfarm.wms.common.dto.TaskPanelQuantityDTO">
        SELECT COUNT(DISTINCT st.id) AS notFinishTaskNum,
               COUNT(DISTINCT CASE
                                  WHEN NOW() >= DATE (st.create_time) + INTERVAL 1 DAY AND st.cycle = #{cycle,jdbcType=INTEGER}
                     THEN st.id
                     ELSE NULL
                     END) AS notFinishInTimeTaskNum
        FROM stocktaking st
        WHERE st.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
          AND st.status in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
          AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
    </select>

    <select id="queryStockTakingTaskPanelNotFinishTask" resultType="java.lang.String">
        SELECT DISTINCT st.id
        FROM stocktaking st
        WHERE st.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND st.status in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status,jdbcType=INTEGER}
        </foreach>
        AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
    </select>

    <select id="queryStockTakingTaskPanelNotFinishInTimeTask" resultType="java.lang.String">
        SELECT DISTINCT st.id
        FROM stocktaking st
        WHERE st.create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        AND st.status in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status,jdbcType=INTEGER}
        </foreach>
        AND st.area_no = #{warehouseNo,jdbcType=INTEGER}
        AND st.cycle = #{cycle,jdbcType=INTEGER}
        AND NOW() >= DATE(st.create_time) + INTERVAL 1 DAY
    </select>
</mapper>