<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stocktaking.StocktakingItemDetailDAO">
    <resultMap id="baseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingItemDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="stocktaking_item_id" jdbcType="BIGINT" property="itemId"/>
        <result column="batch" jdbcType="VARCHAR" property="batch"/>
        <result column="quality_date" jdbcType="TIMESTAMP" property="shelfLife"/>
        <result column="production_date" jdbcType="TIMESTAMP" property="produceAt"/>
        <result column="real_quantity" jdbcType="BIGINT" property="stockTakingNum"/>
        <result column="quantity" jdbcType="BIGINT" property="batchNum"/>
        <result column="produce_batch" jdbcType="BIGINT" property="produceBatch"/>
        <result column="diff_stock" jdbcType="BIGINT" property="diffStock"/>
        <result column="reason_type" jdbcType="BIGINT" property="reason"/>
        <result column="deleted_at" jdbcType="BIGINT" property="deletedAt"/>
        <result column="reason" jdbcType="VARCHAR" property="remark"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="cabinet_inventory_id" jdbcType="BIGINT" property="cabinetInventoryId"/>
        <result column="cabinet_code" jdbcType="VARCHAR" property="cabinetCode"/>
        <result column="owner_code" jdbcType="VARCHAR" property="ownerCode"/>
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName"/>
        <result column="cost" property="cost"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="biz_option" property="bizOption"/>
        <result column="source_detail_id" property="sourceDetailId"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,
        stocktaking_item_id,
        batch,
        quality_date,
        production_date,
        real_quantity,
        reason_type,
        deleted_at,
        reason,
        sku,
        add_time,
        update_time,
        quantity,
        produce_batch,
        diff_stock,
        cabinet_inventory_id,
        cabinet_code,
        cost,
        biz_option,
        source_detail_id
    </sql>

    <insert id="insert" keyColumn="id" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingItemDetailDO">

        insert into stock_taking_list_detail(stocktaking_item_id, batch, quality_date, production_date, real_quantity,
                                             reason_type, deleted_at, reason, sku, produce_batch, diff_stock, quantity,
                                             cabinet_inventory_id, cabinet_code, owner_code, owner_name, cost, source_detail_id)
        values (#{itemId},
                #{batch},
                #{shelfLife},
                #{produceAt},
                #{stockTakingNum},
                #{reason},
                #{deletedAt},
                #{remark},
                #{sku},
                #{produceBatch},
                #{diffStock},
                #{batchNum},
                #{cabinetInventoryId},
                #{cabinetCode},
                #{ownerCode},
                #{ownerName},
                #{cost},
                #{sourceDetailId})
    </insert>

    <insert id="batchInsert">
        insert into stock_taking_list_detail(
        stocktaking_item_id,
        batch,
        quality_date,
        production_date,
        deleted_at,
        quantity,
        produce_batch,
        sku, cabinet_inventory_id, cabinet_code, owner_code, owner_name,tenant_id,cost,real_quantity,diff_stock,source_detail_id)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.itemId},
            #{item.batch},
            #{item.shelfLife},
            #{item.produceAt},
            #{item.deletedAt},
            #{item.batchNum},
            #{item.produceBatch},
            #{item.sku},
            #{item.cabinetInventoryId},
            #{item.cabinetCode},
            #{item.ownerCode},
            #{item.ownerName},
            #{item.tenantId,jdbcType=BIGINT},
            #{item.cost},
            #{item.stockTakingNum},
            #{item.diffStock},
            #{item.sourceDetailId}
            )
        </foreach>

    </insert>

    <update id="update"
            parameterType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingItemDetailDO">
        update stock_taking_list_detail
        <set>
            <trim suffixOverrides=",">
                <if test="stockTakingNum != null">
                    real_quantity = #{stockTakingNum},
                </if>
                <if test="reason != null">
                    reason_type = #{reason},
                </if>
                <if test="deletedAt != null">
                    deleted_at = #{deletedAt},
                </if>
                <if test="remark != null">
                    reason = #{remark},
                </if>
                <if test="batchNum != null">
                    quantity = #{batchNum},
                </if>
                <if test="diffStock != null">
                    diff_stock = #{diffStock},
                </if>
                <if test="bizOption != null">
                    biz_option = #{bizOption}
                </if>
            </trim>
        </set>
        where id = #{id}
    </update>
    <update id="updateRealNumAndDiffStockForAgain"
            parameterType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingItemDetailDO">
        update stock_taking_list_detail
        set real_quantity = #{stockTakingNum},
            diff_stock    = #{diffStock},
            reason_type   = #{reason},
            reason        = #{remark}
        where id = #{id}
    </update>

    <update id="updateForSync"
            parameterType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingItemDetailDO">
        update stock_taking_list_detail
        <set>
            <trim suffixOverrides=",">
                reason_type = #{reason},
                <if test="stockTakingNum != null">
                    real_quantity = #{stockTakingNum},
                </if>
                <if test="deletedAt != null">
                    deleted_at = #{deletedAt},
                </if>
                <if test="remark != null">
                    reason = #{remark},
                </if>
                <if test="batchNum != null">
                    quantity = #{batchNum},
                </if>
                <if test="diffStock != null">
                    diff_stock = #{diffStock},
                </if>
                <if test="itemId != null">
                    stocktaking_item_id = #{itemId}
                </if>
            </trim>
        </set>
        where id = #{id}
    </update>

    <select id="countByItemId" resultType="java.lang.Integer">
        select count(*)
        from stock_taking_list_detail
        where (deleted_at = 0 or deleted_at is null) and stocktaking_item_id in
        <foreach collection="list" item="itemId" separator="," close=")" open="(">
            #{itemId}
        </foreach>
    </select>

    <select id="selectByItemIds" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_taking_list_detail
        where (deleted_at = 0 or deleted_at is null) and stocktaking_item_id in
        <foreach collection="list" item="itemId" separator="," close=")" open="(">
            #{itemId}
        </foreach>
        order by cabinet_code, id
    </select>

    <select id="selectJoinItemByItemIds" resultType="net.summerfarm.wms.infrastructure.dao.stocktaking.dataobject.StocktakingItemDetailDO">
        select
        stld.id id
        ,
        sti.id itemId,
        stld.batch batch,
        stld.quality_date shelfLife,
        stld.production_date produceAt,
        stld.real_quantity stockTakingNum,
        stld.reason_type reason,
        stld.deleted_at deletedAt,
        stld.reason remark,
        sti.sku sku,
        stld.add_time,
        stld.update_time,
        stld.quantity batchNum,
        stld.produce_batch produceBatch,
        stld.diff_stock diffStock,
        stld.cabinet_inventory_id cabinetInventoryId,
        stld.cabinet_code cabinetCode,
        stld.cost cost,
        stld.biz_option bizOption
        from stock_taking_item sti
        left join stock_taking_list_detail stld on sti.id = stld.stocktaking_item_id and ifnull(stld.deleted_at, 0) = 0
        where sti.taking_id = #{takingId}
        order by cabinet_code is null, cabinet_code, stld.id
    </select>

    <select id="selectByItemIdAndDate" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_taking_list_detail
        where (deleted_at = 0 or deleted_at is null) and stocktaking_item_id = #{itemId}
        and production_date = #{produceAt} and quality_date = #{shelfLife}
        <if test="cabinetCode != null">
            and cabinet_code = #{cabinetCode}
        </if>
    </select>

    <select id="selectByListIds" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_taking_list_detail
        where (deleted_at = 0 or deleted_at is null) and stock_taking_list_id in
        <foreach collection="list" item="itemId" separator="," close=")" open="(">
            #{itemId}
        </foreach>
    </select>

    <select id="selectAll" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_taking_list_detail
        order by id desc
    </select>

    <select id="selectEmptyQualityBySkuAndCabinet" resultMap="baseResultMap">
        select <include refid="BASE_COLUMN"/>
        from stock_taking_list_detail
        where stocktaking_item_id = #{itemId}
        and sku = #{sku}
        and cabinet_code = #{cabinetCode}
        and quality_date is null
        and (deleted_at = 0 or deleted_at is null)
    </select>

    <delete id="deleteEmptyQualityBySkuAndCabinet">
        delete from stock_taking_list_detail
        where stocktaking_item_id = #{itemId}
        and sku = #{sku}
        and cabinet_code = #{cabinetCode}
        and quality_date is null
    </delete>

</mapper>