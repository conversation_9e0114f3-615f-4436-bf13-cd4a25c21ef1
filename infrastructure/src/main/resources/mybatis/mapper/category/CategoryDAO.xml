<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.category.CategoryDAO">

    <sql id="Base_Column_List">
        id
        , parent_id, category,type
    </sql>

    <select id="selectById" resultType="net.summerfarm.wms.infrastructure.dao.category.dataobject.CategoryDO">
        select
        c3.id,c3.type,concat(c1.category,'-',c2.category,'-', c3.category) secondCategory,c3.parent_id parentId
        from category c3
        left join category c2 on c2.id = c3.parent_id
        left join category c1 on c1.id = c2.parent_id
        where c3.id = #{id}
    </select>


    <select id="selectByIds" resultType="net.summerfarm.wms.infrastructure.dao.category.dataobject.CategoryDO">
        select
        id,type,category,parent_id parentId
        from category
        where
        id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectSubIds" resultType="net.summerfarm.wms.infrastructure.dao.category.dataobject.CategoryDO">
        select
        b.id
        from category as a
        right join category as b
        on b.parent_id = a.id
        where
        a.id in
        <foreach collection="subIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultType="net.summerfarm.wms.infrastructure.dao.category.dataobject.CategoryDO">
        SELECT id, parent_id parentId, type, category
        FROM category
        where id = #{categoryId,jdbcType=INTEGER}
    </select>

</mapper>