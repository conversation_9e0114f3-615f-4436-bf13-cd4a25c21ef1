<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.skushare.mapper.InventorySkuShareTransferDAO">

    <update id="updateSkuShare">
        UPDATE `inventory_sku_share_transfer`
        SET update_time = NOW()
        <if test="remainingTransferOutQuantityChange != null">
            ,remaining_transfer_out_quantity = remaining_transfer_out_quantity + #{remainingTransferOutQuantityChange}
        </if>
        <if test="remainingTransferInQuantityChange != null">
            ,remaining_transfer_in_quantity = remaining_transfer_in_quantity + #{remainingTransferInQuantityChange}
        </if>
        <if test="operator != null">
            ,operator = #{operator}
        </if>
        WHERE warehouse_no = #{warehouseNo} AND sku = #{sku}
    </update>

</mapper>
