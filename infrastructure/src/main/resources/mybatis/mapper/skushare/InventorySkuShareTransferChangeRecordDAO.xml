<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.skushare.mapper.InventorySkuShareTransferChangeRecordDAO">

    <select id="selectLastChangeRecord" resultType="net.summerfarm.wms.infrastructure.dao.skushare.entity.InventorySkuShareTransferChangeRecordDO">
        /*FORCE_MASTER*/  select
        id, warehouse_no, sku, out_order_no, new_remaining_transfer_out_quantity, old_remaining_transfer_out_quantity, new_remaining_transfer_in_quantity,
        old_remaining_transfer_in_quantity, record_type, record_type_desc, remark, create_time, update_time, creator, operator
        from inventory_sku_share_transfer_change_record
        where warehouse_no = #{warehouseNo} AND sku = #{sku}
        order by id desc
        limit 1
    </select>

</mapper>
