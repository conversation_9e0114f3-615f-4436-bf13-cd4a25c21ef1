<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.outstore.WmsDamageStockItemDAO">

    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.outstore.dataobject.WmsDamageStockItemDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="damage_stock_task_id" jdbcType="BIGINT" property="damageStockTaskId" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="list_no" jdbcType="VARCHAR" property="listNo" />
        <result column="quality_date" jdbcType="DATE" property="qualityDate" />
        <result column="quantity" jdbcType="INTEGER" property="quantity" />
        <result column="production_date" jdbcType="DATE" property="productionDate" />
        <result column="gl_no" jdbcType="VARCHAR" property="glNo" />
        <result column="should_quantity" jdbcType="INTEGER" property="shouldQuantity" />
        <result column="out_store_quantity" jdbcType="INTEGER" property="outStoreQuantity" />
        <result column="reason_type" jdbcType="VARCHAR" property="reasonType" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
    </resultMap>

    <sql id="Base_Column_List">
        id, create_time, update_time, damage_stock_task_id, sku, list_no, quality_date, quantity, production_date, gl_no, should_quantity, out_store_quantity, reason_type, reason
    </sql>

    <select id="selectByTaskNoAndType" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select wdsi.sku,wdsi.quality_date,wdsi.should_quantity,wdsi.list_no,wdsi.production_date  from stock_task st
     left join wms_damage_stock_task wdst on wdst.stock_task_id= st.id
     left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
     where st.task_no=#{taskNo} and st.type=#{type}
    </select>

    <select id="selectDamageItemBatchByTaskNoAndType" resultType="net.summerfarm.wms.domain.outstore.valueObject.WmsDamageItemBatchValueObject">
        select
        st.task_no AS taskNo,
        wdsi.sku,
        wdsi.production_date AS productionDate,
        wdsi.quality_date AS qualityDate,
        wdsi.should_quantity AS shouldQuantity,
        wdsi.list_no AS purchaseNo
        from stock_task st
        join wms_damage_stock_task wdst on wdst.stock_task_id= st.`id`
        join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
        where st.type = #{type}
        <if test="taskNos != null and taskNos.size() > 0">
            and st.task_no in
            <foreach collection="taskNos" item="taskNo" separator="," open="(" close=")">
                #{taskNo}
            </foreach>
        </if>
        <if test="sku != null">
            and wdsi.`sku` = #{sku}
        </if>
    </select>

    <select id="selectByDamageId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_damage_stock_item
        where damage_stock_task_id = #{damageStockTaskId}
    </select>
    
    <select id="selectTaskStatusByTaskId" resultType="java.lang.Integer">
        select status
        from wms_damage_stock_task
        where id = #{damageStockTaskId}
    </select>

    <select id="selectWarehouseNoByTaskId" resultType="java.lang.Integer">
        select warehouse_no
        from wms_damage_stock_task
        where id = #{damageStockTaskId}
    </select>

    <select id="selectLockBatch" resultType="java.lang.Integer">
        select ifnull(sum(wdsi.should_quantity), 0)
        from wms_damage_stock_task wdst
                 left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
        where wdst.status in (0,1)
          and wdst.warehouse_no = #{warehouseNo}
          and wdsi.list_no= #{batch}
          and wdsi.quality_date = #{qualityDate}
          and wdsi.sku= #{sku}
    </select>

</mapper>