<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.outstore.RecheckOutStoreTaskDAO">

    <select id="list" resultType="net.summerfarm.wms.infrastructure.dao.outstore.dataobject.RecheckOutStoreTaskDO">
        select maxTaskId, storeNo, warehouseNo, sku, stockTaskIdList, expectTime, waveOutQuantity from(
        select MAX(st.id) maxTaskId,st.out_store_no storeNo, st.area_no warehouseNo, sti.sku sku, GROUP_CONCAT(st.id)
        stockTaskIdList,
        st.expect_time expectTime, SUM(sti.quantity) waveOutQuantity
        from stock_task st
        left join stock_task_item sti on sti.stock_task_id = st.id
        where st.area_no = #{warehouseNo} and st.type in
        <foreach collection="types" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        <if test="expectTime != null">
            and st.expect_time &gt;= #{expectTime}
        </if>
        <if test="endExpectTime != null">
            and st.expect_time &lt;= #{endExpectTime}
        </if>
        <!--        <if test="expectTime == null">-->
        <!--            and st.expect_time >= DATE_FORMAT(now(),'%Y-%m-%d')-->
        <!--        </if>-->
        <if test="storeNo != null">
            and st.out_store_no = #{storeNo}
        </if>
        <if test="sku != null">
            and sti.sku = #{sku}
        </if>
        <if test="querySkus != null and querySkus.size > 0">
            and sti.sku in
            <foreach collection="querySkus" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by st.out_store_no, st.area_no, st.expect_time, sti.sku) temp
        <where>
            <if test="outTaskId != null">
                temp.stockTaskIdList like concat('%', #{outTaskId},'%')
            </if>
        </where>
        order by temp.expectTime desc
    </select>
</mapper>