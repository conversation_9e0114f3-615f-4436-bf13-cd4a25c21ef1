<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.external.WmsExternalBusinessTransferRecordDAO">
    <resultMap id="WmsExternalBusinessTransferRecordMap"
               type="net.summerfarm.wms.infrastructure.dao.external.dataobject.WmsExternalBusinessTransferRecordDO">
        <id property="id" column="id"/>
        <result property="externalAppKey" column="external_app_key"/>
        <result property="orderType" column="order_type"/>
        <result property="abilityCode" column="ability_code"/>
        <result property="bizCode" column="biz_code"/>
        <result property="externalStatus" column="external_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="table_name">
        wms_external_business_transfer_record
    </sql>

    <sql id="columns_all">
        id, `external_app_key`,  `order_type`, `biz_code`, `ability_code`, `external_status`, `create_time`, `update_time`
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WmsExternalBusinessTransferRecordDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="externalAppKey != null">`external_app_key`,</if>
            <if test="bizCode != null">`biz_code`,</if>
            <if test="orderType != null">`order_type`,</if>
            <if test="abilityCode != null">`ability_code`,</if>
            <if test="externalStatus != null">`external_status`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="externalAppKey != null">#{externalAppKey},</if>
            <if test="bizCode != null">#{bizCode},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="abilityCode != null">#{abilityCode},</if>
            <if test="externalStatus != null">#{externalStatus},</if>
        </trim>
    </insert>


    <select id="findByCondition" resultMap="WmsExternalBusinessTransferRecordMap">
        /*FORCE_MASTER*/
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE external_app_key = #{externalAppKey}
        AND order_type = #{orderType}
        AND ability_code = #{abilityCode}
        AND biz_code = #{bizCode}
        limit 1
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM wms_external_business_transfer_record
        WHERE id = #{id}
    </delete>


</mapper>
