<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.external.WmsExternalOrderMappingMapper">
    <!-- 结果集映射 -->
    <resultMap id="wmsExternalOrderMappingResultMap" type="net.summerfarm.wms.infrastructure.dao.external.dataobject.WmsExternalOrderMapping">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="external_no" property="externalNo" jdbcType="VARCHAR"/>
		<result column="biz_no" property="bizNo" jdbcType="VARCHAR"/>
		<result column="internal_no" property="internalNo" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="app_key" property="appKey" jdbcType="VARCHAR"/>
		<result column="warehouse_no" property="warehouseNo" jdbcType="INTEGER"/>
		<result column="tenant_id" property="tenantId" jdbcType="NUMERIC"/>
		<result column="warehouse_tenant_id" property="warehouseTenantId" jdbcType="NUMERIC"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="wmsExternalOrderMappingColumns">
          t.id,
          t.create_time,
          t.update_time,
          t.external_no,
          t.biz_no,
          t.internal_no,
          t.type,
          t.app_key,
          t.warehouse_no,
          t.tenant_id,
          t.warehouse_tenant_id
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="externalNo != null and externalNo !=''">
                AND t.external_no = #{externalNo}
            </if>
			<if test="bizNo != null and bizNo !=''">
                AND t.biz_no = #{bizNo}
            </if>
			<if test="internalNo != null and internalNo !=''">
                AND t.internal_no = #{internalNo}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
            <if test="types != null and types.size() > 0">
                AND t.type IN
                <foreach collection="types" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
			<if test="appKey != null and appKey !=''">
                AND t.app_key = #{appKey}
            </if>
			<if test="warehouseNo != null">
                AND t.warehouse_no = #{warehouseNo}
            </if>
			<if test="tenantId != null">
                AND t.tenant_id = #{tenantId}
            </if>
			<if test="warehouseTenantId != null">
                AND t.warehouse_tenant_id = #{warehouseTenantId}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="externalNo != null">
                    t.external_no = #{externalNo},
                </if>
                <if test="bizNo != null">
                    t.biz_no = #{bizNo},
                </if>
                <if test="internalNo != null">
                    t.internal_no = #{internalNo},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="appKey != null">
                    t.app_key = #{appKey},
                </if>
                <if test="warehouseNo != null">
                    t.warehouse_no = #{warehouseNo},
                </if>
                <if test="tenantId != null">
                    t.tenant_id = #{tenantId},
                </if>
                <if test="warehouseTenantId != null">
                    t.warehouse_tenant_id = #{warehouseTenantId},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="wmsExternalOrderMappingResultMap" >
        SELECT <include refid="wmsExternalOrderMappingColumns" />
        FROM wms_external_order_mapping t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.wms.domain.external.param.WmsExternalOrderMappingQueryParam"  resultType="net.summerfarm.wms.domain.external.entity.WmsExternalOrderMappingEntity" >
        SELECT
            t.id id,
            t.create_time createTime,
            t.update_time updateTime,
            t.external_no externalNo,
            t.biz_no bizNo,
            t.internal_no internalNo,
            t.type type,
            t.app_key appKey,
            t.warehouse_no warehouseNo,
            t.tenant_id tenantId,
            t.warehouse_tenant_id warehouseTenantId
        FROM wms_external_order_mapping t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.wms.domain.external.param.WmsExternalOrderMappingQueryParam" resultMap="wmsExternalOrderMappingResultMap" >
        SELECT <include refid="wmsExternalOrderMappingColumns" />
        FROM wms_external_order_mapping t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WmsExternalOrderMapping" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO wms_external_order_mapping
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="externalNo != null">
				  external_no,
              </if>
              <if test="bizNo != null">
				  biz_no,
              </if>
              <if test="internalNo != null">
				  internal_no,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="appKey != null">
				  app_key,
              </if>
              <if test="warehouseNo != null">
				  warehouse_no,
              </if>
              <if test="tenantId != null">
				  tenant_id,
              </if>
              <if test="warehouseTenantId != null">
				  warehouse_tenant_id,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="externalNo != null">
				#{externalNo,jdbcType=VARCHAR},
              </if>
              <if test="bizNo != null">
				#{bizNo,jdbcType=VARCHAR},
              </if>
              <if test="internalNo != null">
				#{internalNo,jdbcType=VARCHAR},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="appKey != null">
				#{appKey,jdbcType=VARCHAR},
              </if>
              <if test="warehouseNo != null">
				#{warehouseNo,jdbcType=INTEGER},
              </if>
              <if test="tenantId != null">
				#{tenantId,jdbcType=NUMERIC},
              </if>
              <if test="warehouseTenantId != null">
				#{warehouseTenantId,jdbcType=NUMERIC},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WmsExternalOrderMapping" >
        UPDATE wms_external_order_mapping t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WmsExternalOrderMapping" >
        DELETE FROM wms_external_order_mapping
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>