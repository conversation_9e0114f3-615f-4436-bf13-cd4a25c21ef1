<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.external.WarehouseExternalInventoryRecordDAO">
    <resultMap id="WmsExternalInventoryRecordMap"
               type="net.summerfarm.wms.infrastructure.dao.external.dataobject.WarehouseExternalInventoryRecordDO">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="warehouseNo" column="warehouse_no"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="externalWarehouseNo" column="external_warehouse_no"/>
        <result property="sku" column="sku"/>
        <result property="pdId" column="pd_id"/>
        <result property="pdName" column="pd_name"/>
        <result property="specification" column="specification"/>
        <result property="packaging" column="packaging"/>
        <result property="storageLocation" column="storage_location"/>
        <result property="type" column="type"/>
        <result property="brandName" column="brand_name"/>
        <result property="placeType" column="place_type"/>
        <result property="produceDate" column="produce_date"/>
        <result property="qualityDate" column="quality_date"/>
        <result property="quantity" column="quantity"/>
        <result property="externalQuantity" column="external_quantity"/>
        <result property="externalLockQuantity" column="external_lock_quantity"/>
        <result property="differOption" column="differ_option"/>
        <result property="recordStatus" column="record_status"/>
        <result property="pt" column="pt"/>
    </resultMap>

    <sql id="columns_all">
        id
        , `create_time`, `update_time`,
    `warehouse_no`, `warehouse_name`, `external_warehouse_no`, `sku`, `pd_id`, `pd_name`, `specification`, `packaging`,
    `storage_location`, `type`, `brand_name`, `place_type`, `produce_date`, `quality_date`, `quantity`, `external_quantity`,
    `external_lock_quantity`, `differ_option`, `record_status`, `pt`
    </sql>

    <insert id="batchCreate"
            parameterType="net.summerfarm.wms.infrastructure.dao.external.dataobject.WmsExternalBusinessTransferRecordDO">
        insert into wms_warehouse_external_inventory_record (
        warehouse_no, warehouse_name, external_warehouse_no, sku, pd_id, pd_name, specification, packaging,
        storage_location, type, brand_name, place_type, produce_date, quality_date, quantity, external_quantity,
        external_lock_quantity, differ_option, record_status, pt
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.warehouseNo}, #{item.warehouseName}, #{item.externalWarehouseNo}, #{item.sku}, #{item.pdId},
            #{item.pdName}, #{item.specification}, #{item.packaging}, #{item.storageLocation}, #{item.type},
            #{item.brandName}, #{item.placeType}, #{item.produceDate}, #{item.qualityDate}, #{item.quantity}, #{item.externalQuantity},
            #{item.externalLockQuantity}, #{item.differOption}, #{item.recordStatus}, #{item.pt})
        </foreach>
    </insert>

    <update id="batchUpdateByWarehouseNoAndPt">
        update wms_warehouse_external_inventory_record
        set record_status = #{recordStatus}
        where warehouse_no = #{warehouseNo}
          and pt = #{pt}
    </update>

    <select id="getByWarehouseNoAndPt" resultMap="WmsExternalInventoryRecordMap">
        select
        <include refid="columns_all"/>
        from wms_warehouse_external_inventory_record
        where warehouse_no = #{warehouseNo}
        and pt = #{pt}
        and record_status = #{recordStatus}
        limit 1
    </select>

    <select id="countBySkuAndPdId" resultType="java.lang.Integer">
        select count(*)
        from wms_warehouse_external_inventory_record
        where warehouse_no = #{warehouseNo}
        and record_status = #{recordStatus}
        <if test="sku != null">
            and sku = #{sku}
        </if>
        <if test="pdId != null">
            and pd_id = #{pdId}
        </if>
        <if test="differOption != null">
            and differ_option = #{differOption}
        </if>
    </select>

    <select id="queryBySkuAndPdId" resultMap="WmsExternalInventoryRecordMap">
        select
        <include refid="columns_all"/>
        from wms_warehouse_external_inventory_record
        where warehouse_no = #{warehouseNo}
        and record_status = #{recordStatus}
        <if test="sku != null">
            and sku = #{sku}
        </if>
        <if test="pdId != null">
            and pd_id = #{pdId}
        </if>
        <if test="differOption != null">
            and differ_option = #{differOption}
        </if>
        limit #{pageIndex}, #{pageSize}
    </select>

    <select id="queryBySkuAndPdIdWithNoPage" resultMap="WmsExternalInventoryRecordMap">
        select
        <include refid="columns_all"/>
        from wms_warehouse_external_inventory_record
        where warehouse_no = #{warehouseNo}
        and record_status = #{recordStatus}
        <if test="sku != null">
            and sku = #{sku}
        </if>
        <if test="pdId != null">
            and pd_id = #{pdId}
        </if>
        <if test="differOption != null">
            and differ_option = #{differOption}
        </if>
    </select>
</mapper>
