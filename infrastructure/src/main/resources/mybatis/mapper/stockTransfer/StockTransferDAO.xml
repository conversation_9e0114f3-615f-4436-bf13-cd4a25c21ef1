<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTransfer.StockTransferDAO">
    <resultMap id="baseResultMap" type="net.summerfarm.wms.infrastructure.dao.stockTransfer.dataobject.StockTransferDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="transfer_dimension" jdbcType="TINYINT" property="transferDimension"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="task_source" jdbcType="INTEGER" property="taskSource"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,remark,operator,state,transfer_dimension,warehouse_no,created_at,updated_at,task_source
    </sql>

    <insert id="insertStockTransfer" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into stock_transfer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="transferDimension != null">
                transfer_dimension,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="taskSource != null">
                task_source
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">
                #{warehouseNo},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="transferDimension != null">
                #{transferDimension},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="taskSource != null">
                #{taskSource}
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into stock_transfer(
        <if test="warehouseNo != null">
            warehouse_no,
        </if>
        <if test="state != null">
            state,
        </if>
        <if test="remark != null">
            remark,
        </if>
        <if test="transferDimension != null">
            transfer_dimension,
        </if>
        <if test="operator != null">
            operator
        </if>
        )values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            <if test="warehouseNo != null">
                #{item.warehouseNo},
            </if>
            <if test="state != null">
                #{item.state},
            </if>
            <if test="remark != null">
                #{item.remark},
            </if>
            <if test="transferDimension != null">
                #{item.transferDimension},
            </if>
            <if test="operator != null">
                #{item.operator}
            </if>
            )
        </foreach>
    </insert>

    <update id="updateStateById">
        update stock_transfer
        <set>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="operator != null">
                operator = #{operator}
            </if>
        </set>
        where
        id = #{id}
    </update>

    <select id="selectById" resultMap="baseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer
        where
        id = #{id}
    </select>

    <select id="selectByCreatedAt" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer
        where
        remark = #{remark}
        and date(created_at) = #{createdAt}
        and state = 3
    </select>

    <select id="selectByIdsAndRemark" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer
        where
        id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and warehouse_no = #{warehouseNo}
        and state in (0,1)
        and remark = #{remark}
    </select>

    <select id="selectByIdAndWarehouseNo" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer
        where
        id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and warehouse_no = #{warehouseNo}
        <if test="states != null and states.size() > 0">
            and state in
            <foreach collection="states" item="state" open="(" close=")" separator=",">
                #{state}
            </foreach>
        </if>
    </select>

    <select id="selectByRemarkAndWarehouseNo" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer
        where warehouse_no = #{warehouseNo}
        and remark = #{remark}
        and state in (0,1)
    </select>

    <select id="countByWarehouseNo" resultType="java.lang.Long">
        select count(*)
        from stock_transfer
        where warehouse_no = #{warehouseNo}
          AND created_at <![CDATA[>=]]> #{startTime}
          AND created_at <![CDATA[<]]> #{endTime}
    </select>

    <select id="pageByIdsAndCondition" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer
        <where>
            <if test="ids != null and ids.size() > 0">
                id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="item.transferDimension != null">
                and transfer_dimension = #{item.transferDimension}
            </if>
            <if test="item.warehouseNo != null">
                and warehouse_no = #{item.warehouseNo}
            </if>
            <if test="item.state != null">
                and state = #{item.state}
            </if>
            <if test="item.createdAt != null">
                and created_at <![CDATA[>=]]> #{item.createdAt}
            </if>
            <if test="item.remark != null">
                and remark = #{item.remark}
            </if>
        </where>
        order by id desc
        limit #{pageNum}, #{pageSize}
    </select>

    <select id="countByIdsAndCondition" resultType="java.lang.Integer">
        select
        count(*)
        from stock_transfer
        <where>
            <if test="ids != null and ids.size() > 0">
                id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="item.transferDimension != null">
                and transfer_dimension = #{item.transferDimension}
            </if>
            <if test="item.warehouseNo != null">
                and warehouse_no = #{item.warehouseNo}
            </if>
            <if test="item.state != null">
                and state = #{item.state}
            </if>
            <if test="item.createdAt != null">
                and created_at <![CDATA[>=]]> #{item.createdAt}
            </if>
            <if test="item.remark != null">
                and remark = #{item.remark}
            </if>
        </where>
    </select>

    <select id="countStockTransfer" parameterType="net.summerfarm.wms.infrastructure.dao.stockTransfer.dataobject.StockTransferDO"
            resultType="java.lang.Integer">
        select
        count(*)
        from stock_transfer
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="transferDimension != null">
                and transfer_dimension = #{transferDimension}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="createdAt != null">
                and created_at = #{createdAt}
            </if>
            <if test="remark != null">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <select id="repairData" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from
        stock_transfer
        where id > #{startId}
        and id <![CDATA[<=]]> #{maxId}
        <if test="id != null">
            and id = #{id}
        </if>
        order by id
    </select>

    <select id="countUnfinishedTask" resultType="java.lang.Long">
        select count(*)
        from stock_transfer
        where warehouse_no = #{warehouseNo}
        and state in (0,1,2)
    </select>

    <select id="listByParams" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer
        <where>
            <if test="idList != null and idList.size() > 0">
                id in
                <foreach collection="idList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="transferDimension != null">
                and transfer_dimension = #{transferDimension}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="createdAt != null">
                and created_at <![CDATA[>=]]> #{createdAt}
            </if>
            <if test="createdAtBegin != null">
                and created_at <![CDATA[>=]]> #{createdAtBegin}
            </if>
            <if test="createdAtEnd != null">
                and created_at <![CDATA[<]]> #{createdAtEnd}
            </if>
            <if test="remark != null">
                and remark = #{remark}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectByCreatedAtAndSource" resultMap="baseResultMap">
        select <include refid="BASE_COLUMN"/>
        from stock_transfer
        where task_source = #{taskSource}
        and date(created_at) = #{createdAt}
        and state = #{status,jdbcType=INTEGER}
    </select>

    <select id="queryTaskPanelQuantity" resultType="net.summerfarm.wms.common.dto.TaskPanelQuantityDTO">
        SELECT COUNT(DISTINCT st.id) AS notFinishTaskNum,
           COUNT(DISTINCT CASE
                              WHEN NOW() > DATE(st.created_at) + INTERVAL 14 HOUR  AND st.remark = #{remark,jdbcType=VARCHAR} AND st.task_source = #{taskSource,jdbcType=INTEGER} THEN st.id
                              ELSE NULL
               END) AS notFinishInTimeTaskNum
        FROM stock_transfer st
                 LEFT JOIN stock_transfer_item sti ON sti.stock_transfer_id = st.id
        WHERE st.created_at >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.created_at &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
          AND st.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
          AND st.state in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
</select>
    <select id="queryTaskPanelNotFinishTask" resultType="java.lang.Long">
    SELECT DISTINCT st.id
        FROM stock_transfer st
                 LEFT JOIN stock_transfer_item sti ON sti.stock_transfer_id = st.id
        WHERE st.created_at >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.created_at &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
          AND st.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
          AND st.state in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
</select>
    <select id="queryTaskPanelNotFinishInTimeTask" resultType="java.lang.Long">
        SELECT DISTINCT st.id AS taskId
        FROM stock_transfer st
        WHERE st.created_at >= #{createTimeStart,jdbcType=TIMESTAMP}
        AND st.created_at &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
          AND st.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
          AND st.state in
          <foreach collection="statusList" item="status" open="(" separator="," close=")">
              #{status,jdbcType=INTEGER}
          </foreach>
          AND st.remark = #{remark,jdbcType=VARCHAR}
          AND st.task_source = #{taskSource,jdbcType=INTEGER}
        AND NOW() > DATE(st.created_at) + INTERVAL 14 HOUR
</select>


</mapper>