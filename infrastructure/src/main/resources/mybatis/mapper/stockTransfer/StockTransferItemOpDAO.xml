<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTransfer.StockTransferItemOpDAO">
    <resultMap id="baseResultMap" type="net.summerfarm.wms.infrastructure.dao.stockTransfer.dataobject.StockTransferItemOpDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="shelf_life" jdbcType="BIGINT" property="shelfLife"/>
        <result column="produce_date" jdbcType="BIGINT" property="produceDate"/>
        <result column="transfer_out_sku" jdbcType="VARCHAR" property="transferOutSku"/>
        <result column="transfer_ratio" jdbcType="VARCHAR" property="transferRatio"/>
        <result column="type" jdbcType="BIGINT" property="type"/>
        <result column="stock_transfer_item_id" jdbcType="BIGINT" property="stockTransferItemId"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,operator,shelf_life,produce_date,transfer_out_sku,transfer_ratio,type,stock_transfer_item_id,created_at,updated_at
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into stock_transfer_item_op
        <trim prefix="(" suffix=")" suffixOverrides=",">
            operator,
            shelf_life,
            produce_date,
            transfer_out_sku,
            transfer_ratio,
            type,
            stock_transfer_item_id,
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="createdAt != null">
                created_at
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{operator},
            #{shelfLife},
            #{produceDate},
            #{transferOutSku},
            #{transferRatio},
            #{type},
            #{stockTransferItemId},
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
            <if test="createdAt != null">
                #{createdAt}
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into stock_transfer_item_op
        (operator,
        shelf_life,
        produce_date,
        transfer_out_sku,
        transfer_ratio,
        type,
        stock_transfer_item_id)
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.operator},
            #{item.shelfLife},
            #{item.produceDate},
            #{item.transferOutSku},
            #{item.transferRatio},
            #{item.type},
            #{item.stockTransferItemId})
        </foreach>
    </insert>

    <select id="listByItemId" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer_item_op
        where
        stock_transfer_item_id in
        <foreach collection="itemIds" item="itemId" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
        order by id desc
    </select>

    <select id="selectById" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer_item_op
        where
        id = #{id}
    </select>

    <delete id="delete">
        delete
        from stock_transfer_item_op
        where stock_transfer_item_id = #{itemId}
    </delete>

    <select id="selectByType" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from stock_transfer_item_op
        where
        type = 1
    </select>
</mapper>