<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTransfer.StockTransferItemOpDetailDAO">
    <resultMap id="baseResultMap" type="net.summerfarm.wms.infrastructure.dao.stockTransfer.dataobject.StockTransferItemOpDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="transfer_in_batch" jdbcType="VARCHAR" property="transferInBatch"/>
        <result column="transfer_out_batch" jdbcType="BIGINT" property="transferOutBatch"/>
        <result column="stock_transfer_item_op_id" jdbcType="BIGINT" property="stockTransferItemOpId"/>
        <result column="produce_at" jdbcType="BIGINT" property="produceAt"/>
        <result column="shelf_life" jdbcType="BIGINT" property="shelfLife"/>
        <result column="transfer_out_num" jdbcType="BIGINT" property="transferOutNum"/>
        <result column="transfer_out_cabinet" jdbcType="VARCHAR" property="transferOutCabinet"/>
        <result column="transfer_in_cabinet" jdbcType="VARCHAR" property="transferInCabinet"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="external_transfer_in_num" jdbcType="INTEGER" property="externalTransferInNum"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,transfer_in_batch,transfer_out_batch,stock_transfer_item_op_id,transfer_out_num,
created_at,updated_at,produce_at,shelf_life,transfer_out_cabinet,transfer_in_cabinet, external_transfer_in_num
    </sql>

    <select id="listByOpIds" resultMap="baseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="BASE_COLUMN"/>
        from
        stock_transfer_item_op_detail
        where
        stock_transfer_item_op_id in
        <foreach collection="opIds" item="opId" open="(" close=")" separator=",">
            #{opId}
        </foreach>
    </select>

    <select id="selectByBatch" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from
        stock_transfer_item_op_detail
        where stock_transfer_item_op_id = #{opId}
        and
        transfer_out_batch = #{batch}
        limit 1
    </select>

    <insert id="insert" keyProperty="id" keyColumn="id" useGeneratedKeys="true">
        insert into stock_transfer_item_op_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            transfer_in_batch,
            transfer_out_batch,
            stock_transfer_item_op_id,
            transfer_out_num,
            <if test="produceAt != null">
                produce_at,
            </if>
            <if test="shelfLife != null">
                shelf_life,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="transferOutCabinet != null">
                transfer_out_cabinet,
            </if>
            <if test="transferInCabinet != null">
                transfer_in_cabinet
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{transferInBatch},
            #{transferOutBatch},
            #{stockTransferItemOpId},
            #{transferOutNum},
            <if test="produceAt != null">
                #{produceAt},
            </if>
            <if test="shelfLife != null">
                #{shelfLife},
            </if>
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="transferOutCabinet != null">
                #{transferOutCabinet},
            </if>
            <if test="transferInCabinet != null">
                #{transferInCabinet}
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" keyProperty="id" keyColumn="id" useGeneratedKeys="true">
        insert into stock_transfer_item_op_detail
        (transfer_in_batch,
        transfer_out_batch,
        stock_transfer_item_op_id,
        transfer_out_num,
        shelf_life,
        produce_at,transfer_out_cabinet,transfer_in_cabinet)
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.transferInBatch},
            #{item.transferOutBatch},
            #{item.stockTransferItemOpId},
            #{item.transferOutNum},
            #{item.shelfLife},
            #{item.produceAt},#{item.transferOutCabinet},#{item.transferInCabinet})
        </foreach>
    </insert>

    <select id="listByTransferNo" resultMap="baseResultMap">
        select
        <include refid="BASE_COLUMN"/>
        from
        stock_transfer_item_op_detail
        where transfer_in_batch = #{transferNo}
    </select>

    <update id="updateExternalTransferInNum">
        update stock_transfer_item_op_detail
        set external_transfer_in_num = #{externalTransferInNum}
        where id = #{id}
    </update>
</mapper>