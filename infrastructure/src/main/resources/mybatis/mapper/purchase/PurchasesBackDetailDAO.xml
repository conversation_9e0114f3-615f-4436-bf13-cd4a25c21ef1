<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.purchase.PurchasesBackDetailDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.purchase.dataobject.PurchasesBackDetailDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchases_back_no" property="purchasesBackNo" jdbcType="VARCHAR"/>
        <result column="batch" property="batch" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate"/>
        <result column="production_date" property="productionDate"/>
        <result column="cost" property="cost" jdbcType="DECIMAL"/>
        <result column="out_quantity" property="outQuantity" jdbcType="INTEGER"/>
        <result column="total_cost" property="totalCost" jdbcType="DECIMAL"/>
        <result column="actual_out_quantity" property="actualOutQuantity" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="BaseColumn">
        id, purchases_back_no, batch, sku, area_no, quality_date, production_date, cost, out_quantity, total_cost, actual_out_quantity,type
    </sql>

    <select id="selectByNo" parameterType="java.lang.String"
            resultType="net.summerfarm.wms.infrastructure.dao.purchase.dataobject.PurchasesBackDetailDO">
        /*FORCE_MASTER*/
        SELECT pbd.id,
               pbd.purchases_back_no             purchasesBackNo,
               pbd.batch,
               pbd.sku,
               pbd.area_no                       areaNo,
               pbd.quality_date                  qualityDate,
               pbd.production_date               productionDate,
               pbd.cost,
               pbd.total_cost                    totalCost,
               pbd.out_quantity                  outQuantity,
               pbd.total_cost                    totalCost,
               pbd.actual_out_quantity           actualOutQuantity,
               pbd.gl_no                         glNo,
               pbd.supplier_id                   supplierId
        FROM purchases_back_detail pbd
        WHERE pbd.purchases_back_no = #{purchasesBackNo}
    </select>

    <select id="selectByBatch" parameterType="java.lang.String"
            resultType="net.summerfarm.wms.infrastructure.dao.purchase.dataobject.PurchasesBackDetailDO">
        /*FORCE_MASTER*/
        SELECT pbd.id,
               pbd.purchases_back_no             purchasesBackNo,
               pbd.batch,
               pbd.sku,
               pbd.area_no                       areaNo,
               pbd.quality_date                  qualityDate,
               pbd.production_date               productionDate,
               pbd.cost,
               pbd.total_cost                    totalCost,
               pbd.out_quantity                  outQuantity,
               pbd.total_cost                    totalCost,
               pbd.actual_out_quantity           actualOutQuantity,
               pbd.gl_no                         glNo,
               pbd.supplier_id                   supplierId
        FROM purchases_back_detail pbd
        WHERE pbd.batch = #{batch}
    </select>

    <select id="sumOutQuantity" resultType="java.lang.Integer">
        /*FORCE_MASTER*/ SELECT ifnull(sum(pbd.out_quantity), 0)
        FROM purchases_back_detail pbd
        where pbd.sku= #{sku}
          and pbd.purchases_back_no = #{purchasesBackNo}
    </select>

    <select id="selectLockBatch" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity -pbd.actual_out_quantity), 0)
        from purchases_back pb
        left join purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no
        left join stock_task st on st.task_no=pb.purchases_back_no
        <where>
            st.state in (0,1)
            <if test="batch != null">
                and pbd.batch = #{batch}
            </if>
            <if test="sku != null">
                and pbd.sku = #{sku}
            </if>
            <if test="qualityDate != null">
                and pbd.quality_date = #{qualityDate}
            </if>
            <if test="type != null">
                and pbd.type = #{type}
            </if>
            <if test="status != null">
                and pb.status = #{status}
            </if>
            <if test="areaNo != null">
                and pb.store_no = #{areaNo}
            </if>
        </where>
    </select>

</mapper>
