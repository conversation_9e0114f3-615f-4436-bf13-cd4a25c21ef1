<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.pickuptask.WmsPickUpTaskDao">

    <select id="queryAllByLimitWithMerchant" resultType="net.summerfarm.wms.domain.pickuptask.domainobject.entity.PickUpTask">
        SELECT wim.warehouse_no warehouseNo,
               dp.order_store_no storeNo,
               o.m_id adminId,
               m.mname adminName,
               oi.sku skuCode,
                dp.delivery_time deliveryTime
        FROM ( SELECT * FROM  delivery_plan
        <where>
            AND status in (3,6)
            <if test="query.storeNo != null">
                AND order_store_no = #{query.storeNo}
            </if>
            <if test="query.deliveryTime != null">
                AND delivery_time = #{query.deliveryTime}
            </if>
            <if test="query.deliveryType != null">
                AND deliverytype = #{query.deliveryType}
            </if>
        </where>
        ) dp
        INNER JOIN orders o ON dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN merchant m ON m.m_id = o.m_id
        INNER JOIN admin a ON a.admin_id = m.admin_id
        <if test="query.adminId != null">
            AND m.admin_id = #{query.adminId}
        </if>
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku
        <if test="query.warehouseNo != null">
            AND wim.warehouse_no = #{query.warehouseNo}
        </if>
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        <where>
            <if test="query.storeNo != null">
                dp.order_store_no = #{query.storeNo}
            </if>
        </where>
        AND oi.sku != 'DF001TD0001'
        group by warehouseNo, storeNo, skuCode, m.m_id
    </select>

    <select id="queryAllByLimitWithoutMerchant" resultType="net.summerfarm.wms.domain.pickuptask.domainobject.entity.PickUpTask">
        SELECT wim.warehouse_no warehouseNo,
               dp.order_store_no storeNo,
               oi.sku skuCode,
               dp.delivery_time deliveryTime
        FROM ( SELECT * FROM  delivery_plan
        <where>
            AND status in (3,6)
            <if test="query.storeNo != null">
                AND order_store_no = #{query.storeNo}
            </if>
            <if test="query.deliveryTime != null">
                AND delivery_time = #{query.deliveryTime}
            </if>
            <if test="query.deliveryType != null">
                AND deliverytype = #{query.deliveryType}
            </if>
        </where>
        ) dp
        INNER JOIN orders o ON dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku
        <if test="query.warehouseNo != null">
            AND wim.warehouse_no = #{query.warehouseNo}
        </if>
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        <where>
            <if test="query.storeNo != null">
                AND dp.order_store_no = #{query.storeNo}
            </if>
            <if test="query.adminId != null">
                AND o.m_id IN (
                    SELECT mc.m_id
                    FROM merchant mc
                    INNER JOIN admin am
                    ON am.admin_id = mc.admin_id
                    WHERE am.admin_id = #{query.adminId}
                )
            </if>
        </where>
        AND oi.sku != 'DF001TD0001'
        group by warehouseNo, storeNo, skuCode
    </select>

    <select id="queryAllOrderInfoWithMerchant" resultType="net.summerfarm.wms.domain.pickuptask.domainobject.entity.PickUpTaskOrderInfo">
        SELECT o.order_no orderNo, wim.warehouse_no warehouseNo, dp.order_store_no storeNo, o.m_id adminId, oi.sku skuCode, oi.amount amount
        FROM ( SELECT * FROM  delivery_plan
        <where>
            AND status in (3,6)
            <if test="query.storeNo != null">
                AND order_store_no = #{query.storeNo}
            </if>
            <if test="query.deliveryTime != null">
                AND delivery_time = #{query.deliveryTime}
            </if>
            <if test="query.deliveryType != null">
                AND deliverytype = #{query.deliveryType}
            </if>
        </where>
        ) dp
        INNER JOIN orders o ON dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN merchant m ON m.m_id = o.m_id
        INNER JOIN admin a ON a.admin_id = m.admin_id
        <if test="query.adminId != null">
            AND m.admin_id = #{query.adminId}
        </if>
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku
        <if test="query.warehouseNo != null">
            AND wim.warehouse_no = #{query.warehouseNo}
        </if>
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        <where>
            <if test="query.storeNo != null">
                AND dp.order_store_no = #{query.storeNo}
            </if>
        </where>
        AND oi.sku != 'DF001TD0001'
    </select>

    <select id="queryAllOrderInfoWithoutMerchant" resultType="net.summerfarm.wms.domain.pickuptask.domainobject.entity.PickUpTaskOrderInfo">
        SELECT o.order_no orderNo, wim.warehouse_no warehouseNo, dp.order_store_no storeNo, oi.sku skuCode, oi.amount amount
        FROM ( SELECT * FROM  delivery_plan
        <where>
            AND status in (3,6)
            <if test="query.storeNo != null">
                AND order_store_no = #{query.storeNo}
            </if>
            <if test="query.deliveryTime != null">
                AND delivery_time = #{query.deliveryTime}
            </if>
            <if test="query.deliveryType != null">
                AND deliverytype = #{query.deliveryType}
            </if>
        </where>
        ) dp
        INNER JOIN orders o ON dp.order_no = o.order_no
        AND o.status in (3,6) AND o.type in (0,1,3,12)
        INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3,6)
        INNER JOIN warehouse_inventory_mapping wim on wim.store_no = dp.order_store_no and oi.sku = wim.sku
        <if test="query.warehouseNo != null">
            AND wim.warehouse_no = #{query.warehouseNo}
        </if>
        INNER JOIN area_store ar ON oi.sku = ar.sku AND wim.warehouse_no = ar.area_no
        <where>
            <if test="query.storeNo != null">
                AND dp.order_store_no = #{query.storeNo}
            </if>
            <if test="query.adminId != null">
                AND o.m_id IN (
                    SELECT mc.m_id
                    FROM merchant mc
                    INNER JOIN admin am
                    ON am.admin_id = mc.admin_id
                    WHERE am.admin_id = #{query.adminId}
                )
            </if>
        </where>
        AND oi.sku != 'DF001TD0001'
    </select>
</mapper>

