<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockshipment.StockShipmentItemDetailDAO">

    <select id="selectShipmentItemDetailBatchByTaskNoAndSku" parameterType="java.lang.Integer"
            resultType="net.summerfarm.wms.domain.stockshipment.valueObject.StockShipmentItemDetailBatchValueObject">
        /*FORCE_MASTER*/
        SELECT
        st.`area_no` AS warehouseNo,
        ssi.`sku`,
        st.`task_no` AS listNo,
        ssid.`purchase_no` AS purchaseNo,
        ssid.`production_date` AS productionDate,
        ssid.`quality_date` AS qualityDate,
        ssid.`actual_out_quantity` AS actualOutQuantity
        FROM `stock_task` st
        JOIN `stock_shipment_item` ssi ON ssi.`stock_task_id` = st.`id`
        JOIN `stock_shipment_item_detail` ssid ON ssid.`stock_shipment_item_id` = ssi.`id`
        WHERE st.`type` = 50
        <if test="taskNos != null and taskNos.size() > 0">
            AND st.`task_no` IN
            <foreach collection="taskNos" item="taskNo" separator="," open="(" close=")">
                #{taskNo}
            </foreach>
        </if>
        <if test="sku != null">
            AND ssi.`sku` = #{sku}
        </if>
    </select>

</mapper>