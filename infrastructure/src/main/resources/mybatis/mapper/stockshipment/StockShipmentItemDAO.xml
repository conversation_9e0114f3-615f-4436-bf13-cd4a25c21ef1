<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockshipment.StockShipmentItemDAO">

    <select id="countCapacity" resultType="java.math.BigDecimal">
        SELECT ROUND(sum(capacity)/1000,2) total_capacity from (
        SELECT i.weight_num* sti.quantity capacity FROM `stock_shipment_item` sti LEFT JOIN inventory i on sti.sku=i.sku WHERE stock_task_id in
        <foreach collection="stockTaskIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ) s
    </select>
</mapper>