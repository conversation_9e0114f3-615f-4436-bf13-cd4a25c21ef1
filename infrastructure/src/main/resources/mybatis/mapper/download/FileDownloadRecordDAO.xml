<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.download.FileDownloadRecordDAO">

    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.download.dataobject.FileDownloadRecordDO">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
        <result property="params" column="params" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="startTime" column="start_time" jdbcType="DATE"/>
        <result property="endTime" column="end_time" jdbcType="DATE"/>
        <result property="nameDetail" column="name_detail" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="expirationTime" column="expiration_time" jdbcType="INTEGER"/>
        <result property="fileNameAddress" column="file_name_address" jdbcType="VARCHAR"/>
        <result property="nameDetailAddress" column="name_detail_address" jdbcType="VARCHAR"/>
        <result property="ossType" column="oss_type" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="base_column_filed">
        id,status,`type`,file_name,admin_id,params,create_time,update_time,start_time,end_time,name_detail,creator,expiration_time,oss_type
    </sql>

    <select id="selectByAdminId" resultMap="BaseResultMap">
        select <include refid="base_column_filed"/>
        from file_download_record
        where admin_id = #{adminId}
        order by id desc
    </select>

    <select id="select" resultMap="BaseResultMap">
        select <include refid="base_column_filed"/>
        from file_download_record
        where status <![CDATA[<>]]> 3 and expiration_time is not null
        order by id desc
    </select>


    <select id="selectByType" resultMap="BaseResultMap">
        select <include refid="base_column_filed"/>
        from file_download_record
        where `type` = #{type}
        <if test="startTime != null">
            and end_time <![CDATA[>=]]> #{startTime} and start_time <![CDATA[<=]]> #{startTime}
        </if>
        order by id desc
    </select>

    <insert id="insert" parameterType="net.summerfarm.wms.infrastructure.dao.download.dataobject.FileDownloadRecordDO" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into file_download_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                file_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="uId != null">
                u_id,
            </if>
            <if test="params != null">
                params,
            </if>
            <if test="nameDetail != null">
                name_detail,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="expirationTime != null">
                expiration_time,
            </if>
            <if test="fileNameAddress != null">
                file_name_address,
            </if>
            <if test="nameDetailAddress != null">
                name_detail_address,
            </if>
            <if test="ossType != null">
                oss_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                #{fileName},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="uId != null">
                #{uId},
            </if>
            <if test="params != null">
                #{params},
            </if>
            <if test="nameDetail != null">
                #{nameDetail},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="expirationTime != null">
                #{expirationTime},
            </if>
            <if test="fileNameAddress != null">
                #{fileNameAddress},
            </if>
            <if test="nameDetailAddress != null">
                #{nameDetailAddress},
            </if>
            <if test="ossType != null">
                #{ossType},
            </if>
        </trim>
    </insert>

    <update id="updateById">
        update file_download_record
        set status = #{status},file_name_address = #{fileNameAddress}
        where id = #{id}
    </update>

    <update id="update">
        update file_download_record
        set status = #{status}
        where u_id = #{uId}
    </update>

    <update id="updateFileName" parameterType="net.summerfarm.wms.infrastructure.dao.download.dataobject.FileDownloadRecordDO">
        update file_download_record
        set status = #{status},
            update_time = now()
        <if test="fileNameAddress!= null and fileNameAddress !=''">
            ,file_name_address=#{fileNameAddress}
        </if>
        where file_name = #{fileName} and status = 0
    </update>

    <update id="expireDownloadRecords" parameterType="long">
        update file_download_record
        set status = 3,
            update_time = now()
        where id = #{id} and oss_type = 1
    </update>
    <update id="updateNameDetail" parameterType="net.summerfarm.wms.infrastructure.dao.download.dataobject.FileDownloadRecordDO">
        update file_download_record
        set status = #{status},
            update_time = now()
        where file_name = #{nameDetail} and status = 0
    </update>
    <update id="updateExpiredData" parameterType="net.summerfarm.wms.infrastructure.dao.download.dataobject.FileDownloadRecordDO">
        update file_download_record
        set status = 3,
            update_time = now()
        where id = #{id}
    </update>
    <select id="selectByUid" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT <include refid="base_column_filed"/> FROM file_download_record WHERE u_id = #{uId} LIMIT 1
    </select>

    <delete id="delete" parameterType="long">
        delete from file_download_record
        where id = #{id}
    </delete>

    <delete id="deleteAll" parameterType="long">
        delete from file_download_record
        where admin_id = #{adminId}
    </delete>
    <select id="selectById" parameterType="long"  resultType="net.summerfarm.wms.infrastructure.dao.download.dataobject.FileDownloadRecordDO">
        select id,status,`type`,file_name fileName,admin_id adminId,params,create_time createTime,update_time updateTime,start_time startTime,end_time endTime,name_detail nameDetail,
               expiration_time expirationTime,oss_type ossType,file_name_address fileNameAddress, name_detail_address nameDetailAddress
        from file_download_record
        where id = #{id}
    </select>
</mapper>
