<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.transaction.AreaStoreInventoryTransactionDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="param" jdbcType="VARCHAR" property="param"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="transaction_state" jdbcType="INTEGER" property="transactionState"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, warehouse_no, biz_id, biz_type, inventory_type, param,
    transaction_state, sku
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_area_store_inventory_transaction
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectNormalByBiz" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_area_store_inventory_transaction
        where warehouse_no = #{warehouseNo} and biz_id = #{bizId} and biz_type = #{bizType} and inventory_type =
        #{inventoryType} and sku = #{sku} and transaction_state in (10,30)
        limit 1
    </select>

    <select id="selectByWarehouseAndBizId" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_area_store_inventory_transaction
        where warehouse_no = #{warehouseNo} and biz_id = #{bizId} and biz_type = #{bizType}
        and inventory_type =#{inventoryType}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_area_store_inventory_transaction
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO"
            useGeneratedKeys="true">
        insert into wms_area_store_inventory_transaction (create_time, update_time, warehouse_no,
                                                          biz_id, biz_type, inventory_type,
                                                          param, transaction_state, sku)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{warehouseNo,jdbcType=BIGINT},
                #{bizId,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, #{inventoryType,jdbcType=VARCHAR},
                #{param,jdbcType=VARCHAR}, #{transactionState,jdbcType=INTEGER}, #{sku})
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO"
            useGeneratedKeys="true">
        insert into wms_area_store_inventory_transaction (warehouse_no,
        biz_id, biz_type, inventory_type,
        param, transaction_state, sku)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warehouseNo,jdbcType=BIGINT},
            #{item.bizId,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, #{item.inventoryType,jdbcType=VARCHAR},
            #{item.param,jdbcType=VARCHAR}, #{item.transactionState,jdbcType=INTEGER}, #{item.sku})
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO"
            useGeneratedKeys="true">
        insert into wms_area_store_inventory_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="inventoryType != null">
                inventory_type,
            </if>
            <if test="param != null">
                param,
            </if>
            <if test="transactionState != null">
                transaction_state,
            </if>
            <if test="sku != null">
                sku,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="inventoryType != null">
                #{inventoryType,jdbcType=VARCHAR},
            </if>
            <if test="param != null">
                #{param,jdbcType=VARCHAR},
            </if>
            <if test="transactionState != null">
                #{transactionState,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO">
        update wms_area_store_inventory_transaction
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="inventoryType != null">
                inventory_type = #{inventoryType,jdbcType=VARCHAR},
            </if>
            <if test="param != null">
                param = #{param,jdbcType=VARCHAR},
            </if>
            <if test="transactionState != null">
                transaction_state = #{transactionState,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO">
        update wms_area_store_inventory_transaction
        set create_time       = #{createTime,jdbcType=TIMESTAMP},
            update_time       = #{updateTime,jdbcType=TIMESTAMP},
            warehouse_no      = #{warehouseNo,jdbcType=BIGINT},
            biz_id            = #{bizId,jdbcType=VARCHAR},
            biz_type          = #{bizType,jdbcType=VARCHAR},
            inventory_type    = #{inventoryType,jdbcType=VARCHAR},
            param             = #{param,jdbcType=VARCHAR},
            transaction_state = #{transactionState,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByIds" parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.AreaStoreInventoryTransactionDO">
        update wms_area_store_inventory_transaction
        set transaction_state = #{status, jdbcType=INTEGER}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id, jdbcType=BIGINT}
        </foreach>
    </update>



</mapper>