<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.transaction.TransactionLogDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.TransactionLogDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="local_state" jdbcType="INTEGER" property="localState"/>
        <result column="param" jdbcType="VARCHAR" property="param"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, update_time, warehouse_no, biz_id, biz_type, local_state, param
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_transaction_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByBiz" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_transaction_log
        where warehouse_no = #{warehouseNo} and biz_id = #{bizId} and biz_type = #{bizType}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_transaction_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.TransactionLogDO"
            useGeneratedKeys="true">
        insert into wms_transaction_log (create_time, update_time, warehouse_no,
                                         biz_id, biz_type, local_state,
                                         param)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{warehouseNo,jdbcType=BIGINT},
                #{bizId,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, #{localState,jdbcType=INTEGER},
                #{param,jdbcType=VARCHAR})
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.TransactionLogDO"
            useGeneratedKeys="true">
        insert into wms_transaction_log (warehouse_no,
        biz_id, biz_type, local_state,
        param)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.warehouseNo,jdbcType=BIGINT},
            #{item.bizId,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, #{item.localState,jdbcType=INTEGER},
            #{item.param,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.TransactionLogDO"
            useGeneratedKeys="true">
        insert into wms_transaction_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="localState != null">
                local_state,
            </if>
            <if test="param != null">
                param,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="localState != null">
                #{localState,jdbcType=INTEGER},
            </if>
            <if test="param != null">
                #{param,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.TransactionLogDO">
        update wms_transaction_log
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="localState != null">
                local_state = #{localState,jdbcType=INTEGER},
            </if>
            <if test="param != null">
                param = #{param,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.transaction.dataobject.TransactionLogDO">
        update wms_transaction_log
        set create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            biz_id       = #{bizId,jdbcType=VARCHAR},
            biz_type     = #{bizType,jdbcType=VARCHAR},
            local_state  = #{localState,jdbcType=INTEGER},
            param        = #{param,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>