<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockarrage.StockArrangeDAO">

    <select id="selectByPrimaryKey" resultType="net.summerfarm.wms.infrastructure.dao.stockarrage.dataobject.StockArrangeDO">
        select
              id, purchase_no purchaseNo, stock_task_id stockTaskId, state, arrange_time arrangeTime, admin_id adminId,
              admin_name adminName, arrange_remark arrangeRemark
        from stock_arrange
        where stock_task_id = #{stockTaskId}
    </select>
</mapper>