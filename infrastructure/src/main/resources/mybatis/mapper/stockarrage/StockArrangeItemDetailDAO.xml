<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockarrage.StockArrangeItemDetailDAO">

    <select id="selectByTaskId"
            resultType="net.summerfarm.wms.infrastructure.dao.stockarrage.dataobject.StockArrangeItemDetailDO">
        select id, purchase_no purchaseNo, actual_in_quantity actualInQuantity, quality_date qualityDate,
        production_date productionDate
        from stock_storage_item_detail
        where stock_storage_item_id in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

</mapper>