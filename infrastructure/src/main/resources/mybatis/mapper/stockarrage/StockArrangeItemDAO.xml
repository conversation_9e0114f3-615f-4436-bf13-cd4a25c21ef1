<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockarrage.StockArrangeItemDAO">

    <select id="selectByTaskId" resultType="net.summerfarm.wms.infrastructure.dao.stockarrage.dataobject.StockArrangeItemDO" >
        select
        sai.id, sai.stock_arrange_id stockArrangeId,sai.sku,sai.arrival_quantity arrivalQuantity,sai.actual_quantity actualQuantity,sai.pd_name pdName,sai.weight,
        sai.supplier,sai.type,sai.quality_time qualityTime,sai.quality_time_unit qualityTimeUnit,sai.supplier_id supplierId
        from  stock_arrange sa
        left join  stock_arrange_item sai on sai.stock_arrange_id= sa.id
        where sa.stock_task_id = #{taskId}
    </select>

</mapper>