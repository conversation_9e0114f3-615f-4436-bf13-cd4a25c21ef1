<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskProcessOrderSkuDAO">

    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskProcessOrderSkuDO">
        <!--@mbg.generated-->
        <!--@Table wms_stock_task_process_order_sku-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_process_id" jdbcType="BIGINT" property="stockTaskProcessId" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
        <result column="goods_supply_no" jdbcType="VARCHAR" property="goodsSupplyNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="quantity" jdbcType="INTEGER" property="quantity" />
        <result column="purchase_batch" jdbcType="VARCHAR" property="purchaseBatch" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
        <result column="abnormal_quantity" property="abnormalQuantity" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, stock_task_process_id, stock_task_id, out_order_no, sku, quantity, purchase_batch,quality_date
        creator, `operator`, gmt_created, gmt_modified, is_deleted, last_ver, goods_supply_no, abnormal_quantity
    </sql>

    <select id="findByStockTaskIdAndGoodsSupplyOrderNoList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from wms_stock_task_process_order_sku
        where goods_supply_no in
        <foreach collection="goodsSupplyOrderNoList" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        and stock_task_id in
        <foreach collection="stockTaskIdList" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>