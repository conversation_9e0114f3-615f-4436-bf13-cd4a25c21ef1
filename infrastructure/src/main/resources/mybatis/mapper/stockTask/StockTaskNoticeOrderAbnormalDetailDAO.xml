<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskNoticeOrderAbnormalDetailDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderAbnormalDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="notice_order_id" jdbcType="BIGINT" property="noticeOrderId"/>
        <result column="goods_supply_no" jdbcType="VARCHAR" property="goodsSupplyNo"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="warehouse_tenant_id" jdbcType="BIGINT" property="warehouseTenantId"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="customer_sku_code" jdbcType="VARCHAR" property="customerSkuCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , notice_order_id, goods_supply_no, tenant_id, warehouse_tenant_id, warehouse_no, sku, quantity, create_time,
    update_time, is_deleted, last_ver, customer_sku_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_notice_order_abnormal_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="queryNoticeOrderAbnormal" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_notice_order_abnormal_detail
        <where>
            <if test="goodsSupplyNo != null and goodsSupplyNo != ''">
                goods_supply_no = #{goodsSupplyNo}
            </if>
            <if test="skus != null and skus.size > 0">
                and sku in
                <foreach collection="skus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_stock_task_notice_order_abnormal_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderAbnormalDetailDO"
            useGeneratedKeys="true">
        insert into wms_stock_task_notice_order_abnormal_detail (notice_order_id, goods_supply_no, tenant_id,
                                                                 warehouse_tenant_id, warehouse_no, sku, quantity,
                                                                 is_deleted, last_ver, state, customer_sku_code)
        values (#{noticeOrderId,jdbcType=BIGINT}, #{goodsSupplyNo,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT},
                #{warehouseTenantId,jdbcType=BIGINT},
                #{warehouseNo,jdbcType=INTEGER},
                #{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER},
                #{isDeleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}, #{state}, #{customerSkuCode,jdbcType=VARCHAR})
    </insert>
    <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        insert into wms_stock_task_notice_order_abnormal_detail (notice_order_id, goods_supply_no, tenant_id,
        warehouse_tenant_id, warehouse_no, sku, quantity,
        is_deleted, last_ver, state, customer_sku_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.noticeOrderId,jdbcType=BIGINT}, #{item.goodsSupplyNo,jdbcType=VARCHAR},
            #{item.tenantId,jdbcType=BIGINT},
            #{item.warehouseTenantId,jdbcType=BIGINT},
            #{item.warehouseNo,jdbcType=INTEGER},
            #{item.sku,jdbcType=VARCHAR}, #{item.quantity,jdbcType=INTEGER},
            #{item.isDeleted,jdbcType=TINYINT}, #{item.lastVer,jdbcType=INTEGER}, #{item.state}, #{item.customerSkuCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderAbnormalDetailDO"
            useGeneratedKeys="true">
        insert into wms_stock_task_notice_order_abnormal_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeOrderId != null">
                notice_order_id,
            </if>
            <if test="goodsSupplyNo != null">
                goods_supply_no,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="warehouseTenantId != null">
                warehouse_tenant_id,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="lastVer != null">
                last_ver,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="customerSkuCode != null">
                customer_sku_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeOrderId != null">
                #{noticeOrderId,jdbcType=BIGINT},
            </if>
            <if test="goodsSupplyNo != null">
                #{goodsSupplyNo,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="warehouseTenantId != null">
                #{warehouseTenantId,jdbcType=BIGINT},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="lastVer != null">
                #{lastVer,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="customerSkuCode != null">
                #{customerSkuCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="finishAbnormalDetail">
        update wms_stock_task_notice_order_abnormal_detail
        set state = 20
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderAbnormalDetailDO">
        update wms_stock_task_notice_order_abnormal_detail
        <set>
            <if test="noticeOrderId != null">
                notice_order_id = #{noticeOrderId,jdbcType=BIGINT},
            </if>
            <if test="goodsSupplyNo != null">
                goods_supply_no = #{goodsSupplyNo,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="warehouseTenantId != null">
                warehouse_tenant_id = #{warehouseTenantId,jdbcType=BIGINT},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="customerSkuCode != null">
                customer_sku_code = #{customerSkuCode},
            </if>
            last_ver = last_ver + 1
        </set>
        where id = #{id,jdbcType=BIGINT} and last_ver = #{lastVer}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderAbnormalDetailDO">
        update wms_stock_task_notice_order_abnormal_detail
        set notice_order_id     = #{noticeOrderId,jdbcType=BIGINT},
            goods_supply_no     = #{goodsSupplyNo,jdbcType=VARCHAR},
            tenant_id           = #{tenantId,jdbcType=BIGINT},
            warehouse_tenant_id = #{warehouseTenantId,jdbcType=BIGINT},
            warehouse_no        = #{warehouseNo,jdbcType=INTEGER},
            sku                 = #{sku,jdbcType=VARCHAR},
            quantity            = #{quantity,jdbcType=INTEGER},
            is_deleted          = #{isDeleted,jdbcType=TINYINT},
            state               = #{state},
            customer_sku_code   = #{customerSkuCode,jdbcType=VARCHAR},
            last_ver            = last_ver + 1
        where id = #{id,jdbcType=BIGINT}
          and last_ver = #{lastVer}
    </update>

    <select id="findByGoodsSupplyNoList" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_notice_order_abnormal_detail
        where
        goods_supply_no in
        <foreach collection="goodsSupplyOrderNoList" item="goodsSupplyOrderNo" open="(" close=")" separator=",">
            #{goodsSupplyOrderNo}
        </foreach>

    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_notice_order_abnormal_detail
        where
        id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>

    <select id="findByExceptTimeAndWarehouseStore" resultMap="BaseResultMap">
        select t2.id, t2.notice_order_id, t2.goods_supply_no, t2.tenant_id, t2.warehouse_tenant_id, t2.warehouse_no, t2.sku, t2.quantity, t2.create_time, t2.update_time, t2.is_deleted, t2.last_ver, t2.customer_sku_code
        from wms_stock_task_notice_order t1 inner join wms_stock_task_notice_order_abnormal_detail t2 on t1.id = t2.notice_order_id
        where t1.warehouse_no = #{warehouseNo}
        and t1.store_no = #{storeNo}
        and t1.except_time = #{exceptTime}
        and t1.out_order_type = #{type}
        and t1.supply_mode = #{supplyMode}
        and t1.out_order_no in
        <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
    </select>

    <select id="findByStockTaskId" resultMap="BaseResultMap">
        select t2.id, t2.notice_order_id, t2.goods_supply_no, t2.tenant_id, t2.warehouse_tenant_id, t2.warehouse_no, t2.sku, t2.quantity, t2.create_time, t2.update_time, t2.is_deleted, t2.last_ver, t2.customer_sku_code
        from wms_stock_task_notice_order t1 inner join wms_stock_task_notice_order_abnormal_detail t2 on t1.id = t2.notice_order_id
        where t1.stock_task_id = #{stockTaskId}
    </select>
</mapper>