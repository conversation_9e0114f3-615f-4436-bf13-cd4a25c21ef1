<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskPurchasesBackDetailDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskPurchasesBackDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId"/>
        <result column="stock_task_item_id" jdbcType="BIGINT" property="stockTaskItemId"/>
        <result column="purchases_back_no" jdbcType="VARCHAR" property="purchasesBackNo"/>
        <result column="batch" jdbcType="VARCHAR" property="batch"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="quality_date" jdbcType="DATE" property="qualityDate"/>
        <result column="production_date" jdbcType="DATE" property="productionDate"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="supplier_id" jdbcType="INTEGER" property="supplierId"/>
        <result column="cost" jdbcType="DECIMAL" property="cost"/>
        <result column="out_quantity" jdbcType="INTEGER" property="outQuantity"/>
        <result column="total_cost" jdbcType="DECIMAL" property="totalCost"/>
        <result column="actual_out_quantity" jdbcType="INTEGER" property="actualOutQuantity"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , stock_task_id, stock_task_item_id, purchases_back_no, batch, sku, warehouse_no,
    quality_date, production_date, type, supplier_id, cost, out_quantity, total_cost,
    actual_out_quantity, create_time, update_time, creator, operator
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_purchases_back_detail
        where id = #{id}
    </select>
    <insert id="insert"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskPurchasesBackDetailDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into wms_stock_task_purchases_back_detail (stock_task_id, stock_task_item_id, purchases_back_no,
        batch, sku, warehouse_no,
        quality_date, production_date, type,
        supplier_id, cost, out_quantity,
        total_cost, actual_out_quantity, create_time,
        update_time, creator, operator
        )
        values (#{stockTaskId,jdbcType=BIGINT}, #{stockTaskItemId,jdbcType=BIGINT}, #{purchasesBackNo,jdbcType=VARCHAR},
        #{batch,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER},
        #{qualityDate,jdbcType=DATE}, #{productionDate,jdbcType=DATE}, #{type,jdbcType=INTEGER},
        #{supplierId,jdbcType=INTEGER}, #{cost,jdbcType=DECIMAL}, #{outQuantity,jdbcType=INTEGER},
        #{totalCost,jdbcType=DECIMAL}, #{actualOutQuantity,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskPurchasesBackDetailDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into wms_stock_task_purchases_back_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                stock_task_id,
            </if>
            <if test="stockTaskItemId != null">
                stock_task_item_id,
            </if>
            <if test="purchasesBackNo != null">
                purchases_back_no,
            </if>
            <if test="batch != null">
                batch,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="cost != null">
                cost,
            </if>
            <if test="outQuantity != null">
                out_quantity,
            </if>
            <if test="totalCost != null">
                total_cost,
            </if>
            <if test="actualOutQuantity != null">
                actual_out_quantity,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="operator != null">
                operator,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                #{stockTaskId,jdbcType=BIGINT},
            </if>
            <if test="stockTaskItemId != null">
                #{stockTaskItemId,jdbcType=BIGINT},
            </if>
            <if test="purchasesBackNo != null">
                #{purchasesBackNo,jdbcType=VARCHAR},
            </if>
            <if test="batch != null">
                #{batch,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="qualityDate != null">
                #{qualityDate,jdbcType=DATE},
            </if>
            <if test="productionDate != null">
                #{productionDate,jdbcType=DATE},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="supplierId != null">
                #{supplierId,jdbcType=INTEGER},
            </if>
            <if test="cost != null">
                #{cost,jdbcType=DECIMAL},
            </if>
            <if test="outQuantity != null">
                #{outQuantity,jdbcType=INTEGER},
            </if>
            <if test="totalCost != null">
                #{totalCost,jdbcType=DECIMAL},
            </if>
            <if test="actualOutQuantity != null">
                #{actualOutQuantity,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="update"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskPurchasesBackDetailDO">
        update wms_stock_task_purchases_back_detail
        <set>
            <if test="stockTaskId != null">
                stock_task_id = #{stockTaskId,jdbcType=BIGINT},
            </if>
            <if test="stockTaskItemId != null">
                stock_task_item_id = #{stockTaskItemId,jdbcType=BIGINT},
            </if>
            <if test="purchasesBackNo != null">
                purchases_back_no = #{purchasesBackNo,jdbcType=VARCHAR},
            </if>
            <if test="batch != null">
                batch = #{batch,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="qualityDate != null">
                quality_date = #{qualityDate,jdbcType=DATE},
            </if>
            <if test="productionDate != null">
                production_date = #{productionDate,jdbcType=DATE},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId,jdbcType=INTEGER},
            </if>
            <if test="cost != null">
                cost = #{cost,jdbcType=DECIMAL},
            </if>
            <if test="outQuantity != null">
                out_quantity = #{outQuantity,jdbcType=INTEGER},
            </if>
            <if test="totalCost != null">
                total_cost = #{totalCost,jdbcType=DECIMAL},
            </if>
            <if test="actualOutQuantity != null">
                actual_out_quantity = #{actualOutQuantity,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO wms_stock_task_purchases_back_detail (
        stock_task_id, stock_task_item_id, purchases_back_no,
        batch, sku, warehouse_no,
        quality_date, production_date, type,
        supplier_id, cost, out_quantity,
        total_cost, actual_out_quantity, create_time,
        update_time, creator, operator
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.stockTaskId, jdbcType=BIGINT},
            #{item.stockTaskItemId, jdbcType=BIGINT},
            #{item.purchasesBackNo, jdbcType=VARCHAR},
            #{item.batch, jdbcType=VARCHAR},
            #{item.sku, jdbcType=VARCHAR},
            #{item.warehouseNo, jdbcType=INTEGER},
            #{item.qualityDate, jdbcType=DATE},
            #{item.productionDate, jdbcType=DATE},
            #{item.type, jdbcType=INTEGER},
            #{item.supplierId, jdbcType=INTEGER},
            #{item.cost, jdbcType=DECIMAL},
            #{item.outQuantity, jdbcType=INTEGER},
            #{item.totalCost, jdbcType=DECIMAL},
            #{item.actualOutQuantity, jdbcType=INTEGER},
            now(),
            now(),
            #{item.creator, jdbcType=VARCHAR},
            #{item.operator, jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectListByTaskNoAndSku"
            resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskPurchasesBackDetailDO">
        SELECT
        wstpbd.id,
        wstpbd.purchases_back_no purchasesBackNo,
        wstpbd.batch,
        wstpbd.sku,
        wstpbd.warehouse_no warehouseNo,
        wstpbd.quality_date qualityDate,
        wstpbd.production_date productionDate,
        wstpbd.cost,
        wstpbd.total_cost totalCost,
        wstpbd.out_quantity outQuantity,
        wstpbd.total_cost totalCost,
        wstpbd.actual_out_quantity actualOutQuantity,
        wse.status
        FROM wms_stock_task_purchases_back_detail wstpbd
        LEFT JOIN warehouse_stock_ext wse ON wstpbd.sku = wse.sku AND wstpbd.warehouse_no = wse.warehouse_no
        WHERE wstpbd.purchases_back_no = #{taskNo}
        <if test="sku != null">
            and wstpbd.sku = #{sku}
        </if>
        <if test="querySkus != null and querySkus.size > 0">
            and wstpbd.sku in
            <foreach collection="querySkus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="sumOutQuantity" resultType="java.lang.Integer">
        /*FORCE_MASTER*/ SELECT ifnull(sum(wstpbd.out_quantity), 0)
                         FROM wms_stock_task_purchases_back_detail wstpbd
                         where wstpbd.sku = #{sku}
                           and wstpbd.purchases_back_no = #{taskNo}
    </select>

    <select id="selectListByTaskNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_purchases_back_detail
        where purchases_back_no = #{purchaseBackNo}
    </select>

    <select id="selectListByTaskNoAndSkus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_purchases_back_detail
        where purchases_back_no = #{purchaseBackNo}
        and sku in
        <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
    </select>

    <select id="selectListByTaskNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_purchases_back_detail
        where purchases_back_no in
        <foreach collection="purchaseBackNoList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="statisticsLockQuantity" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity -pbd.actual_out_quantity), 0)
        from wms_stock_task_purchases_back_detail pbd
        left join stock_task st on st.task_no=pbd.purchases_back_no
        <where>
            st.state in (0,1)
            <if test="batch != null">
                and pbd.batch = #{batch}
            </if>
            <if test="sku != null">
                and pbd.sku = #{sku}
            </if>
            <if test="qualityDate != null">
                and pbd.quality_date = #{qualityDate}
            </if>
            <if test="type != null">
                and pbd.type = #{type}
            </if>
            <if test="warehouseNo != null">
                and pbd.warehouse_no = #{warehouseNo}
            </if>
        </where>
    </select>

    <update id="updatePurchaseBackNoByStockTaskId">
        update
            wms_stock_task_purchases_back_detail
        set purchases_back_no = #{cancelTaskNo}
        where stock_task_id = #{stockTaskId}
          and purchases_back_no = #{taskNo}
    </update>
</mapper>