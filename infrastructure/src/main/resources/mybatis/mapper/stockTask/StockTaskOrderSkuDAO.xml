<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskOrderSkuDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskOrderSkuDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="quantity" jdbcType="INTEGER" property="quantity" />
        <result column="actual_quantity" jdbcType="INTEGER" property="actualQuantity" />
        <result column="abnormal_quantity" jdbcType="INTEGER" property="abnormalQuantity" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, stock_task_id, out_order_no, sku, quantity, actual_quantity, abnormal_quantity, creator, `operator`,
        gmt_created, gmt_modified, is_deleted, last_ver
    </sql>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskOrderSkuDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into wms_stock_task_order_sku (stock_task_id, out_order_no, sku,
        quantity, actual_quantity, abnormal_quantity, creator,
        `operator`, gmt_created, gmt_modified,
        is_deleted, last_ver)
        values (#{stockTaskId,jdbcType=BIGINT}, #{outOrderNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR},
        #{quantity,jdbcType=INTEGER}, #{actualQuantity,jdbcType=INTEGER}, #{abnormalQuantity,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR},
        #{operator,jdbcType=VARCHAR}, #{gmtCreated,jdbcType=BIGINT}, #{gmtModified,jdbcType=BIGINT},
        0, 1)
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskOrderSkuDO">
        <!--@mbg.generated-->
        update wms_stock_task_order_sku
        <set>
            <if test="stockTaskId != null">
                stock_task_id = #{stockTaskId,jdbcType=BIGINT},
            </if>
            <if test="outOrderNo != null">
                out_order_no = #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="actualQuantity != null">
                actual_quantity = #{actualQuantity,jdbcType=INTEGER},
            </if>
            <if test="abnormalQuantity != null">
                abnormal_quantity = #{abnormalQuantity,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreated != null">
                gmt_created = #{gmtCreated,jdbcType=BIGINT},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="lastVer != null">
                last_ver = #{lastVer,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskOrderSkuDO">
        <!--@mbg.generated-->
        update wms_stock_task_order_sku
        set stock_task_id = #{stockTaskId,jdbcType=BIGINT},
        out_order_no = #{outOrderNo,jdbcType=VARCHAR},
        sku = #{sku,jdbcType=VARCHAR},
        quantity = #{quantity,jdbcType=INTEGER},
        actual_quantity = #{actualQuantity,jdbcType=INTEGER},
        abnormal_quantity = #{abnormalQuantity},
        creator = #{creator,jdbcType=VARCHAR},
        operator = #{operator,jdbcType=VARCHAR},
        gmt_created = #{gmtCreated,jdbcType=BIGINT},
        gmt_modified = #{gmtModified,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=TINYINT},
        last_ver = #{lastVer,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectListByTaskIdAndSkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where stock_task_id = #{stockTaskId} and sku in
        <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
            #{sku,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectListByTaskIdAndSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where stock_task_id = #{stockTaskId} and sku = #{sku,jdbcType=VARCHAR}
    </select>

    <update id="updateActualQuantityById">
        update wms_stock_task_order_sku
        set actual_quantity = #{actualQuantity},
            gmt_modified = unix_timestamp(now()) * 1000,
            last_ver = last_ver + 1
        where id = #{id,jdbcType=BIGINT} and quantity >= #{actualQuantity}
    </update>

    <select id="selectListByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where stock_task_id = #{stockTaskId}
    </select>

    <select id="selectListByOutOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskOrderSkuDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into wms_stock_task_order_sku (stock_task_id, out_order_no, sku,
        quantity, actual_quantity, abnormal_quantity, creator,
        `operator`, gmt_created, gmt_modified,
        is_deleted, last_ver)
        values
            <foreach collection="orderSkuDOList" item="item" separator=",">
                    (#{item.stockTaskId,jdbcType=BIGINT}, #{item.outOrderNo,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR},
                #{item.quantity,jdbcType=INTEGER}, #{item.actualQuantity,jdbcType=INTEGER},#{item.abnormalQuantity,jdbcType=INTEGER}, #{item.creator,jdbcType=VARCHAR},
                #{item.operator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=BIGINT}, #{item.gmtModified,jdbcType=BIGINT},
                0, 1)
            </foreach>
    </insert>

    <select id="selectListByOutOrderNoAndSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
        and sku = #{sku,jdbcType=VARCHAR}
    </select>
</mapper>