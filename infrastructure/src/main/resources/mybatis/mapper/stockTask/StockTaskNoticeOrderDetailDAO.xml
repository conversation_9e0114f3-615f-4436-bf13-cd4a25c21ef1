<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskNoticeOrderDetailDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="notice_order_id" jdbcType="BIGINT" property="noticeOrderId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="warehouse_tenant_id" jdbcType="BIGINT" property="warehouseTenantId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="store_no" jdbcType="INTEGER" property="storeNo"/>
        <result column="out_order_type" jdbcType="INTEGER" property="outOrderType"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated"/>
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="customer_sku_code" jdbcType="VARCHAR" property="customerSkuCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , notice_order_id, tenant_id, shop_id, out_order_no, warehouse_no, store_no, out_order_type, sku,
    quantity, creator, operator, gmt_created, gmt_modified, is_deleted, last_ver, goods_name, warehouse_tenant_id, customer_sku_code
    </sql>

    <sql id="table_name">
        wms_stock_task_notice_order_detail
    </sql>

    <sql id="query">
        <where>
            <if test="tenantId != null">AND `tenant_id` = #{tenantId}</if>
            <if test="warehouseTenantId != null">AND `warehouse_tenant_id` = #{warehouseTenantId}</if>
            <if test="shopId != null">AND `shop_id` = #{shopId}</if>
            <if test="outOrderNo != null">AND `out_order_no` = #{outOrderNo}</if>
            <if test="warehouseNo != null">AND `warehouse_no` = #{warehouseNo}</if>
            <if test="storeNo != null">AND `store_no` = #{storeNo}</if>
            <if test="outOrderType != null">AND `out_order_type` = #{outOrderType}</if>
            <if test="sku != null">AND `sku` = #{sku}</if>
            <if test="quantity != null">AND `quantity` = #{quantity}</if>
            <if test="creator != null">AND `creator` = #{creator}</if>
            <if test="operator != null">AND `operator` = #{operator}</if>
            <if test="gmtCreated != null">AND `gmt_created` = #{gmtCreated}</if>
            <if test="gmtModified != null">AND `gmt_modified` = #{gmtModified}</if>
            <if test="isDeleted != null">AND `is_deleted` = #{isDeleted}</if>
            <if test="lastVer != null">AND `last_ver` = #{lastVer}</if>
            <if test="goodsName != null">AND `goods_name` = #{goodsName}</if>
            <if test="noticeOrderId != null">AND `notice_order_id` = #{noticeOrderId}</if>
            <if test="customerSkuCode != null">AND `customer_sku_code` = #{customerSkuCode}</if>
        </where>
    </sql>

    <insert id="insert"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDetailDO">
        insert into wms_stock_task_notice_order_detail (notice_order_id, tenant_id, shop_id, out_order_no,
                                                        warehouse_no, store_no, out_order_type,
                                                        sku, quantity, creator,
                                                        operator, gmt_created, gmt_modified,
                                                        is_deleted, last_ver, goods_name, warehouse_tenant_id, customer_sku_code)
        values (#{noticeOrderId,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT},
                #{outOrderNo,jdbcType=VARCHAR},
                #{warehouseNo,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, #{outOrderType,jdbcType=INTEGER},
                #{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR}, #{gmtCreated,jdbcType=BIGINT}, #{gmtModified,jdbcType=BIGINT},
                0, 1, #{goodsName,jdbcType=VARCHAR}, #{warehouseTenantId}, #{customerSkuCode,jdbcType=VARCHAR})
    </insert>

    <select id="selectListByNoticeOrderIdList" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_notice_order_detail
        where notice_order_id in
        <foreach collection="noticeOrderIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDetailDO"
            resultType="java.lang.Long">
        /*FORCE_MASTER*/ SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDetailDO"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/  SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        limit 1
    </select>

    <select id="list"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDetailDO"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/  SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="listByNoticeOrderId" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/  SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE notice_order_id = #{noticeOrderId}
    </select>

    <select id="findOrderDetailByOrderIdAndSku" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE
        notice_order_id in
        <foreach collection="noticeOrderIdList" item="noticeOrderId" open="(" close=")" separator=",">#{noticeOrderId}
        </foreach>
        and sku = #{sku}
    </select>

    <update id="deleteByNoticeId">
        update
        <include refid="table_name"/>
        set is_deleted = 1,
        last_ver = last_ver + 1
        where notice_order_id = #{noticeId}
    </update>

    <update id="updateCustomerSkuCode">
        update
        <include refid="table_name"/>
        set customer_sku_code = #{customerSkuCode},
        last_ver = last_ver + 1
        where id = #{id}
    </update>

    <select id="findOrderDetailByNoticeOrderIdListAndSku" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        where notice_order_id in
        <foreach collection="noticeOrderIdList" item="noticeOrderId" open="(" close=")" separator=",">
            #{noticeOrderId}
        </foreach>
        and sku = #{sku}
    </select>
</mapper>