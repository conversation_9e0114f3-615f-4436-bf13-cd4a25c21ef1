<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskItemCabinetOccupyDAO">
    <resultMap id="BaseResultMap" type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.WmsStockTaskItemCabinetOccupyDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="stock_task_item_id" jdbcType="BIGINT" property="stockTaskItemId" />
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="cabinet_code" jdbcType="VARCHAR" property="cabinetCode" />
        <result column="production_date" jdbcType="DATE" property="productionDate" />
        <result column="quality_date" jdbcType="DATE" property="qualityDate" />
        <result column="occupy_quantity" jdbcType="INTEGER" property="occupyQuantity" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
        <result column="pick_quantity" jdbcType="INTEGER" property="pickQuantity" />
        <result column="release_quantity" jdbcType="INTEGER" property="releaseQuantity" />
        <result column="should_pick_quantity" jdbcType="INTEGER" property="shouldPickQuantity" />
        <result column="actual_pick_quantity" jdbcType="INTEGER" property="actualPickQuantity" />
        <result column="abnormal_quantity" jdbcType="INTEGER" property="abnormalQuantity" />
    </resultMap>
    <sql id="Base_Column_List">
        id, stock_task_id, stock_task_item_id, warehouse_no, sku, cabinet_code, production_date,
    quality_date, occupy_quantity, create_time, update_time, is_deleted, last_ver, pick_quantity, release_quantity, should_pick_quantity, actual_pick_quantity, abnormal_quantity
    </sql>

    <insert id="insert" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.WmsStockTaskItemCabinetOccupyDO">
        insert into wms_stock_task_item_cabinet_occupy (id, stock_task_id, stock_task_item_id,
                                                        warehouse_no, sku, cabinet_code,
                                                        production_date, quality_date, occupy_quantity,
                                                        create_time, update_time, is_deleted,
                                                        last_ver, pick_quantity)
        values (#{id,jdbcType=BIGINT}, #{stockTaskId,jdbcType=BIGINT}, #{stockTaskItemId,jdbcType=BIGINT},
                #{warehouseNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{cabinetCode,jdbcType=VARCHAR},
                #{productionDate,jdbcType=DATE}, #{qualityDate,jdbcType=DATE}, #{occupyQuantity,jdbcType=INTEGER},
                now(), now(), 0,1, 0)
    </insert>

    <select id="selectListByStockTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectListByStockTaskIdExcludedJJAndCY" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cabinet_code not in ('JJ01','CY01')
    </select>

    <select id="selectListByStockTaskIdExcludedCY" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cabinet_code not in ('CY01')
    </select>

    <select id="selectListByStockTaskIdAndQualityExcludedJJAndCY" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cabinetCode != null">
            and cabinet_code = #{cabinetCode,jdbcType=VARCHAR}
        </if>
        <if test="productionDate != null">
            and production_date = #{productionDate,jdbcType=DATE}
        </if>
        <if test="qualityDate != null">
            and quality_date = #{qualityDate,jdbcType=VARCHAR}
        </if>
        and cabinet_code not in ('JJ01','CY01')
    </select>

    <select id="selectListByStockTaskIdForJJ" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and cabinet_code in ('JJ01')
    </select>

    <select id="selectListByStockTaskIdList" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and stock_task_id in
        <foreach collection="stockTaskIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectListByStockTaskIdListExcludedJJAndCY" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and stock_task_id in
        <foreach collection="stockTaskIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and cabinet_code not in ('JJ01','CY01')
    </select>

    <delete id="softDeleteCabinetInventory">
        update wms_stock_task_item_cabinet_occupy
        set is_deleted = 1
        where stock_task_id = #{stockTaskId,jdbcType=INTEGER}
          and sku = #{sku,jdbcType=VARCHAR}
          and cabinet_code = #{cabinetCode,jdbcType=VARCHAR}
    </delete>

    <update id="updateReleaseChange">
        update wms_stock_task_item_cabinet_occupy
        set occupy_quantity = occupy_quantity - #{releaseChangeQuantity},
            release_quantity = ifnull(release_quantity, 0) + #{releaseChangeQuantity}
        where id = #{id}
          and occupy_quantity >= #{releaseChangeQuantity}
    </update>

    <update id="updateOccupyQuantityAdd">
        update wms_stock_task_item_cabinet_occupy
        set occupy_quantity = occupy_quantity + #{occupyQuantityAdd}
        where id = #{id}
          and #{occupyQuantityAdd} >= 0
    </update>

    <update id="updateShouldPickQuantity">
        update wms_stock_task_item_cabinet_occupy
        set should_pick_quantity = ifnull(should_pick_quantity, 0) + #{shouldPickQuantity}
        where id = #{id}
    </update>

    <update id="updateActualPickQuantity">
        update wms_stock_task_item_cabinet_occupy
        set actual_pick_quantity = ifnull(actual_pick_quantity, 0) + #{actualPickQuantity}
        where id = #{id}
    </update>

    <update id="updateShouldAndActualPickQuantity">
        update wms_stock_task_item_cabinet_occupy
        set should_pick_quantity = ifnull(should_pick_quantity, 0) + #{shouldPickQuantity},
        actual_pick_quantity = ifnull(actual_pick_quantity, 0) + #{actualPickQuantity}
        where id = #{id}
    </update>

    <update id="updateShouldAndAbnormalPickQuantity">
        update wms_stock_task_item_cabinet_occupy
        set should_pick_quantity = ifnull(should_pick_quantity, 0) + #{shouldPickQuantity},
            abnormal_quantity = ifnull(abnormal_quantity, 0) + #{abnormalQuantity}
        where id = #{id}
    </update>

    <update id="updateShouldAndActualAndAbnormalPickQuantity">
        update wms_stock_task_item_cabinet_occupy
        set should_pick_quantity = ifnull(should_pick_quantity, 0) + #{shouldPickQuantity},
            actual_pick_quantity = ifnull(actual_pick_quantity, 0) + #{actualPickQuantity},
            abnormal_quantity = ifnull(abnormal_quantity, 0) + #{abnormalQuantity}
        where id = #{id}
    </update>

    <update id="updateAbnormalQuantity">
        update wms_stock_task_item_cabinet_occupy
        set abnormal_quantity = ifnull(abnormal_quantity, 0) + #{abnormalQuantity}
        where id = #{id}
    </update>

    <select id="selectListByStockTaskIdOccupyed" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_item_cabinet_occupy
        where is_deleted = 0
        and (occupy_quantity > 0 or pick_quantity > 0)
        and stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>