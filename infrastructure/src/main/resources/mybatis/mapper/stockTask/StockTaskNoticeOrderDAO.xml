<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.stockTask.StockTaskNoticeOrderDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="shop_name" jdbcType="VARCHAR" property="shopName"/>
        <result column="goods_supply_no" jdbcType="VARCHAR" property="goodsSupplyNo"/>
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo"/>
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName"/>
        <result column="store_no" jdbcType="INTEGER" property="storeNo"/>
        <result column="out_order_type" jdbcType="INTEGER" property="outOrderType"/>
        <result column="except_time" jdbcType="DATE" property="exceptTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="supply_mode" javaType="INTEGER" property="supplyMode"/>
        <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="detail_address" jdbcType="VARCHAR" property="detailAddress"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="gmt_created" jdbcType="DATE" property="gmtCreated"/>
        <result column="gmt_modified" jdbcType="DATE" property="gmtModified"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="last_ver" jdbcType="INTEGER" property="lastVer"/>
        <result column="stock_task_create_time" jdbcType="DATE" property="stockTaskCreateTime"/>
        <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName"/>
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId"/>
        <result column="warehouse_tenant_id" jdbcType="BIGINT" property="warehouseTenantId"/>
        <result column="notice_sku_flag" javaType="INTEGER" property="noticeSkuFlag"/>
        <result column="external_option" javaType="INTEGER" property="externalOption"/>
        <result column="external_status" javaType="INTEGER" property="externalStatus"/>
        <result column="push_mode" javaType="INTEGER" property="pushMode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , tenant_id, shop_id,shop_name, goods_supply_no, out_order_no, warehouse_no, store_no, out_order_type, supply_mode,
    except_time, status, receiver, phone, province, city, area, detail_address, creator,
    operator, gmt_created, gmt_modified, is_deleted, last_ver, stock_task_create_time, warehouse_name, warehouse_tenant_id, stock_task_id, notice_sku_flag, external_option, external_status,
    push_mode,close_time
    </sql>

    <sql id="table_name">
        wms_stock_task_notice_order
    </sql>

    <sql id="query">
        <where>
            <if test="tenantId != null">AND `tenant_id` = #{tenantId}</if>
            <if test="shopId != null">AND `shop_id` = #{shopId}</if>
            <if test="goodsSupplyNo != null">AND `goods_supply_no` = #{goodsSupplyNo}</if>
            <if test="outOrderNo != null">AND `out_order_no` = #{outOrderNo}</if>
            <if test="warehouseNo != null">AND `warehouse_no` = #{warehouseNo}</if>
            <if test="storeNo != null">AND `store_no` = #{storeNo}</if>
            <if test="outOrderType != null">AND `out_order_type` = #{outOrderType}</if>
            <if test="exceptTime != null">AND `except_time` = #{exceptTime}</if>
            <if test="status != null">AND `status` = #{status}</if>
            <if test="receiver != null">AND `receiver` = #{receiver}</if>
            <if test="phone != null">AND `phone` = #{phone}</if>
            <if test="province != null">AND `province` = #{province}</if>
            <if test="city != null">AND `city` = #{city}</if>
            <if test="area != null">AND `area` = #{area}</if>
            <if test="detailAddress != null">AND `detail_address` = #{detailAddress}</if>
            <if test="creator != null">AND `creator` = #{creator}</if>
            <if test="operator != null">AND `operator` = #{operator}</if>
            <if test="gmtCreated != null">AND `gmt_created` = #{gmtCreated}</if>
            <if test="gmtModified != null">AND `gmt_modified` = #{gmtModified}</if>
            <if test="isDeleted != null">AND `is_deleted` = #{isDeleted}</if>
            <if test="lastVer != null">AND `last_ver` = #{lastVer}</if>
            <if test="stockTaskCreateTime != null">AND `stock_task_create_time` = #{stockTaskCreateTime}</if>
            <if test="warehouseName != null">AND `warehouse_name` = #{warehouseName}</if>
            <if test="warehouseTenantId != null">AND `warehouse_tenant_id` = #{warehouseTenantId}</if>
            <if test="supplyMode != null">AND `supply_mode` = #{supplyMode}</if>
            <if test="stock_task_id != null">AND `stock_task_id` = #{stockTaskId}</if>
            <if test="external_option != null">AND `external_option` = #{externalOption}</if>
            <if test="external_status != null">AND `external_status` = #{externalStatus}</if>
            <if test="pushMode != null">AND `push_mode` = #{pushMode}</if>
        </where>
    </sql>

    <insert id="insert"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into wms_stock_task_notice_order (tenant_id, shop_id,shop_name, goods_supply_no,
        out_order_no, warehouse_no, store_no,
        out_order_type, except_time, status, supply_mode,
        receiver, phone, province,
        city, area, detail_address,
        creator, operator, gmt_created,
        gmt_modified, is_deleted, last_ver, stock_task_create_time, warehouse_name, warehouse_tenant_id, stock_task_id, notice_sku_flag, external_option, external_status,
        push_mode,close_time
        )
        values (#{tenantId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{shopName,jdbcType=VARCHAR}
        ,#{goodsSupplyNo,jdbcType=VARCHAR},
        #{outOrderNo,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER} ,#{storeNo,jdbcType=INTEGER},
        #{outOrderType,jdbcType=INTEGER}, #{exceptTime,jdbcType=DATE}, #{status,jdbcType=INTEGER},
        #{supplyMode,jdbcType=INTEGER},
        #{receiver,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{detailAddress,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{gmtCreated,jdbcType=DATE},
        #{gmtModified,jdbcType=DATE}, 0, 1, #{stockTaskCreateTime,jdbcType=DATE}, #{warehouseName,jdbcType=VARCHAR},
        #{warehouseTenantId}, #{stockTaskId,jdbcType=BIGINT}, #{noticeSkuFlag,jdbcType=INTEGER}, #{externalOption,jdbcType=INTEGER}, #{externalStatus,jdbcType=INTEGER},
        #{pushMode},#{closeTime}
        )
    </insert>

    <select id="selectListById" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_stock_task_notice_order
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="BaseResultMap">
        /*FORCE_MASTER*/  SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDO"
            resultType="java.lang.Long">
        /*FORCE_MASTER*/ SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDO"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/  SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderDO"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="listByCondition"
            parameterType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StockTaskNoticeOrderQueryDO"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT DISTINCT
        stno.id, stno.tenant_id, stno.shop_id,stno.shop_name, stno.goods_supply_no, stno.out_order_no,
        stno.warehouse_no, stno.store_no, stno.out_order_type,
        stno.except_time, stno.status, stno.supply_mode, stno.receiver, stno.phone, stno.province, stno.city, stno.area,
        stno.detail_address, stno.creator,
        stno.operator, stno.gmt_created, stno.gmt_modified, stno.is_deleted, stno.last_ver, stno.stock_task_create_time,
        stno.warehouse_name, stno.stock_task_id
        FROM wms_stock_task_notice_order stno
        INNER JOIN wms_stock_task_notice_order_detail stnod
        ON stno.id = stnod.notice_order_id
        <where>
            stno.`tenant_id` = #{tenantId}
            <if test="id != null">
                AND stno.`id` = #{id}
            </if>
            <if test="startTime != null">
                AND stno.`gmt_created` <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND date(stno.`gmt_created`) <![CDATA[<=]]> #{endTime}
            </if>
            <if test="warehouseNo != null">
                AND stno.`warehouse_no` = #{warehouseNo}
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size != 0 ">
                AND stno.`warehouse_no` IN
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                AND stno.`status` = #{status}
            </if>
            <if test="outOrderNo != null">
                AND stno.`out_order_no` = #{outOrderNo}
            </if>
            <if test="outOrderType != null">
                AND stno.`out_order_type` = #{outOrderType}
            </if>
            <if test="sku != null">
                AND stnod.`sku` = #{sku}
            </if>
            <if test="goodsName != null">
                AND stnod.`goods_name` = #{goodsName}
            </if>

        </where>
        order by stno.id desc
    </select>

    <update id="finishNoticeOrder">
        update wms_stock_task_notice_order
        set
        `stock_task_create_time` = now(),
        `status` = 2,
        `stock_task_id` = #{stockTaskId}
        where status = 1
        and id in
        <foreach collection="orderNoticeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </update>

    <select id="findByGoodsSupplyNo" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where goods_supply_no = #{goodsSupplyNo,jdbcType=VARCHAR}
    </select>

    <select id="selectListByGoodsSupplyNo" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where is_deleted = 0
        and goods_supply_no in
        <foreach collection="goodsSupplyNoList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findNoticeOrderList" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where is_deleted = 0
        <if test="warehouseNo != null">
            and warehouse_no = #{warehouseNo}
        </if>
        <if test="storeNo != null">
            and store_no = #{storeNo}
        </if>
        <if test="statusList != null and statusList.size > 0">
            and status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">#{status}</foreach>
        </if>
        <if test="outOrderTypeList != null and outOrderTypeList.size > 0">
            and out_order_type in
            <foreach collection="outOrderTypeList" item="outOrderType" open="(" close=")" separator=",">#{outOrderType}
            </foreach>
        </if>
        <if test="supplyMode != null">
            and supply_mode = #{supplyMode}
        </if>
        <if test="exceptTime != null">
            and except_time = #{exceptTime}
        </if>
        <if test="noticeSkuFlag != null">
            and notice_sku_flag = #{noticeSkuFlag}
        </if>
        <if test="pushMode != null">
            and push_mode = #{pushMode}
        </if>
        <if test="leCloseTime != null">
            and close_time &lt;= #{leCloseTime}
        </if>
    </select>

    <update id="updateStatusByIdList">
        update
        wms_stock_task_notice_order
        set
        status = #{status},
        last_ver = last_ver + 1
        where
        id in
        <foreach collection="orderNoticeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateExternalStatusByIdList">
        update
        wms_stock_task_notice_order
        set
        external_status = #{externalStatus},
        last_ver = last_ver + 1
        where
        id in
        <foreach collection="orderNoticeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateStatusByIdListAndStatus">
        update
        wms_stock_task_notice_order
        set
        status = #{status},
        last_ver = last_ver + 1
        where
        id in
        <foreach collection="orderNoticeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and status in
        <foreach collection="oldStatusList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="cancelNotice">
        update
            wms_stock_task_notice_order
        set status     = #{status},
            is_deleted = #{isDeleted},
            last_ver   = last_ver + 1
        where id = #{id}
          and last_ver = #{lastVer}
    </update>

    <update id="preCancelNotice">
        update
            wms_stock_task_notice_order
        set status   = #{status},
            last_ver = last_ver + 1
        where id = #{id}
          and last_ver = #{lastVer}
          and status not in (2, 10, 30, 40)
    </update>

    <update id="finishGenOutTaskNoticeOrder">
        update wms_stock_task_notice_order
        set
        `stock_task_create_time` = now(),
        `status` = 2,
        `stock_task_id` = #{stockTaskId},
        last_ver = last_ver + 1
        where status = 10
        and id in
        <foreach collection="orderNoticeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </update>

    <select id="findByStockTaskId" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where is_deleted = 0
        <if test="warehouseNo != null">
            and warehouse_no = #{warehouseNo}
        </if>
        <if test="storeNo != null">
            and store_no = #{storeNo}
        </if>
        <if test="stockTaskId != null">
            and stock_task_id = #{stockTaskId}
        </if>
    </select>

    <select id="findByExceptTime" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where is_deleted = 0
        <if test="warehouseNo != null">
            and warehouse_no = #{warehouseNo}
        </if>
        <if test="storeNo != null">
            and store_no = #{storeNo}
        </if>
        <if test="exceptTime != null">
            and except_time = #{exceptTime}
        </if>
    </select>

    <select id="findOrderByStockTaskId" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where is_deleted = 0
        <if test="stockTaskId != null">
            and stock_task_id = #{stockTaskId}
        </if>
    </select>

    <select id="findAllNoticeByGoodsSupplyNoList" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where goods_supply_no in
        <foreach collection="goodsSupplyNoList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateInitToCreatingStatusByIdList">
        update wms_stock_task_notice_order
        set status = 10, last_ver = last_ver + 1
        where status = 1
        and id in
        <foreach collection="noticeOrderIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="findByIdListAndStatus" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="item" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and status = #{status}
    </select>

    <select id="findByStockTaskIdAndOrderNo" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        WHERE stock_task_id = #{stockTaskId}
        and out_order_no = #{outOrderNo}
    </select>

    <select id="mapUnUseNoticeNum"
            resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.UnUseNoticeNumDO">
        /*FORCE_MASTER*/ select
        nod.sku sku, sum(nod.quantity) num
        from wms_stock_task_notice_order tno
        inner join wms_stock_task_notice_order_detail nod on tno.id = nod.notice_order_id
        where tno.warehouse_no = #{warehouseNo}
        and tno.except_time = #{exceptTime}
        <if test="skus != null and skus.size() > 0">
            and nod.sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        and tno.status = 1
        and tno.stock_task_id is null
        <if test="supplyModes != null and supplyModes.size() > 0">
            and tno.supply_mode in
            <foreach collection="supplyModes" item="supplyMode" open="(" close=")" separator=",">
                #{supplyMode}
            </foreach>
        </if>
        group by nod.sku
    </select>

    <select id="mapUnUseNoticeNumAfterSomeDay"
            resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.UnUseNoticeNumDO">
        /*FORCE_MASTER*/ select
        nod.sku sku, sum(nod.quantity) num
        from wms_stock_task_notice_order tno
        inner join wms_stock_task_notice_order_detail nod on tno.id = nod.notice_order_id
        where tno.warehouse_no = #{warehouseNo}
        and tno.except_time >= #{exceptTime}
        <if test="skus != null and skus.size() > 0">
            and nod.sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        and tno.status = 1
        and tno.stock_task_id is null
        group by nod.sku
    </select>

    <select id="findByStockTaskIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wms_stock_task_notice_order
        where is_deleted = 0
        <if test="stockTaskIdList != null and stockTaskIdList.size > 0">
            and stock_task_id in
            <foreach collection="stockTaskIdList" item="stockTaskId" open="(" close=")" separator=",">
                #{stockTaskId}
            </foreach>
        </if>
    </select>

    <select id="findWaitStateStoreNoByWarehouseNosAndDeliveryTime" resultType="net.summerfarm.wms.infrastructure.dao.stockTask.dataobject.StoreNoWarehouseNoDO">
        select  store_no     as storeNo,
                warehouse_no as warehouseNo
        from wms_stock_task_notice_order
        where status = 1
        and is_deleted = 0
        <if test="warehouseStorageNoList != null and warehouseStorageNoList.size > 0">
            and warehouse_no in
            <foreach collection="warehouseStorageNoList" item="warehouseNo" open="(" close=")" separator=",">
                #{warehouseNo}
            </foreach>
        </if>
        and except_time = #{exceptTime}
        group by store_no, warehouse_no
    </select>

    <select id="findShopTaskNoticeOrder" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table_name"/>
        where except_time = #{exceptTime}
        <if test="shopIdList != null and shopIdList.size > 0">
            and shop_id in
            <foreach collection="shopIdList" item="shopId" open="(" close=")" separator=",">
                #{shopId}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size > 0">
            and status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
    </select>
</mapper>