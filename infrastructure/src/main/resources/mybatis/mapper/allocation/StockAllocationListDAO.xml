<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.allocation.StockAllocationListDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="list_no" property="listNo" jdbcType="VARCHAR"/>
        <result column="create_admin" property="createAdmin" jdbcType="INTEGER"/>
        <result column="create_admin_name" property="createAdminName" jdbcType="VARCHAR"/>
        <result column="audit_admin" property="auditAdmin" jdbcType="INTEGER"/>
        <result column="audit_admin_name" property="auditAdminName" jdbcType="VARCHAR"/>
        <result column="out_store" property="outStore" jdbcType="INTEGER"/>
        <result column="out_store_name" property="outStoreName" jdbcType="VARCHAR"/>
        <result column="in_store" property="inStore" jdbcType="INTEGER"/>
        <result column="in_store_name" property="inStoreName" jdbcType="VARCHAR"/>
        <result column="out_time" property="outTime"/>
        <result column="expect_time" property="expectTime"/>
        <result column="expect_out_time" property="expectOutTime"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="in_time" property="inTime"/>
        <result column="in_status" property="inStatus"/>
        <result column="out_status" property="outStatus"/>
        <result column="transport" property="transport" jdbcType="INTEGER"/>
        <result column="tracking_no" property="trackingNo" jdbcType="VARCHAR"/>
        <result column="addtime" property="addtime"/>
        <result column="updatetime" property="updatetime"/>
        <result column="plan_list_no" property="planListNo"/>
        <result column="next_day_arrive" property="nextDayArrive"/>
        <result column="plan_list_id" property="planListId"/>
    </resultMap>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from stock_allocation_list
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectByListNo" parameterType="string"
            resultType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        /*FORCE_MASTER*/
        select t.list_no              listNo,
               t.create_admin_name    createAdminName,
               t.create_admin         createAdmin,
               t.out_store_name       outStoreName,
               t.in_store             inStore,
               t.out_store            outStore,
               t.in_store_name        inStoreName,
               t.status,
               t.addtime,
               t.updatetime,
               t.order_type           orderType,
               t.in_store_admin       inStoreAdmin,
               t.in_store_admin_name  inStoreAdminName,
               t.out_store_admin      outStoreAdmin,
               t.out_store_admin_name outStoreAdminName,
               t.in_status            inStatus,
               t.out_status           outStatus,
               t.audit_admin          auditAdmin,
               t.audit_admin_name     auditAdminName,
               t.out_time             outTime,
               t.in_time              inTime,
               t.expect_time          expectTime,
               t.transport,
               t.tracking_no          trackingNo,
               t.expect_out_time      expectOutTime,
               t.plan_list_no         planListNo,
               t.next_day_arrive      nextDayArrive,
               t.plan_list_id         planListId
        FROM stock_allocation_list t
        WHERE t.list_no = #{listNo}
    </select>

    <insert id="insertSelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO"
            keyColumn="id" useGeneratedKeys="true" keyProperty="id">
        insert into stock_allocation_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="listNo != null">
                list_no,
            </if>
            <if test="createAdmin != null">
                create_admin,
            </if>
            <if test="createAdminName != null">
                create_admin_name,
            </if>
            <if test="auditAdmin != null">
                audit_admin,
            </if>
            <if test="outStore != null">
                out_store,
            </if>
            <if test="inStore != null">
                in_store,
            </if>
            <if test="outTime != null">
                out_time,
            </if>
            <if test="expectOutTime != null">
                expect_out_time,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="inTime != null">
                in_time,
            </if>
            <if test="transport != null">
                transport,
            </if>
            <if test="trackingNo != null">
                tracking_no,
            </if>
            <if test="auditAdminName !=null">
                audit_admin_name,
            </if>
            <if test="outStoreName != null">
                out_store_name,
            </if>
            <if test="inStoreName != null">
                in_store_name,
            </if>
            <if test="updatetime != null">
                updatetime,
            </if>
            <if test="inStoreAdmin != null">
                in_store_admin,
            </if>
            <if test="inStoreAdminName != null">
                in_store_admin_name,
            </if>
            <if test="outStoreAdmin != null">
                out_store_admin,
            </if>
            <if test="outStoreAdminName != null">
                out_store_admin_name,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="planListNo != null">
                plan_list_no,
            </if>
            <if test="nextDayArrive != null">
                next_day_arrive,
            </if>
            <if test="planListId != null">
                plan_list_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="listNo != null">
                #{listNo,jdbcType=VARCHAR},
            </if>
            <if test="createAdmin != null">
                #{createAdmin,jdbcType=INTEGER},
            </if>
            <if test="createAdminName != null">
                #{createAdminName},
            </if>
            <if test="auditAdmin != null">
                #{auditAdmin,jdbcType=INTEGER},
            </if>
            <if test="outStore != null">
                #{outStore,jdbcType=INTEGER},
            </if>
            <if test="inStore != null">
                #{inStore,jdbcType=INTEGER},
            </if>
            <if test="outTime != null">
                #{outTime},
            </if>
            <if test="expectOutTime != null">
                #{expectOutTime},
            </if>
            <if test="expectTime != null">
                #{expectTime},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="inTime != null">
                #{inTime},
            </if>
            <if test="transport != null">
                #{transport,jdbcType=INTEGER},
            </if>
            <if test="trackingNo != null">
                #{trackingNo,jdbcType=VARCHAR},
            </if>
            <if test="auditAdminName !=null">
                #{auditAdminName},
            </if>
            <if test="outStoreName != null">
                #{outStoreName},
            </if>
            <if test="inStoreName != null">
                #{inStoreName},
            </if>
            <if test="updatetime != null">
                #{updatetime},
            </if>
            <if test="inStoreAdmin != null">
                #{inStoreAdmin},
            </if>
            <if test="inStoreAdminName != null">
                #{inStoreAdminName},
            </if>
            <if test="outStoreAdmin != null">
                #{outStoreAdmin},
            </if>
            <if test="outStoreAdminName != null">
                #{outStoreAdminName},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
            <if test="orderType != null">
                #{orderType},
            </if>
            <if test="planListNo != null">
                #{planListNo},
            </if>
            <if test="nextDayArrive != null">
                #{nextDayArrive},
            </if>
            <if test="planListId != null">
                #{planListId},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        update stock_allocation_list
        <set>
            <if test="auditAdmin != null">
                audit_admin = #{auditAdmin,jdbcType=INTEGER},
            </if>
            <if test="auditAdminName != null">
                audit_admin_name =#{auditAdminName},
            </if>
            <if test="outStore != null">
                out_store = #{outStore,jdbcType=INTEGER},
            </if>
            <if test="outStoreName != null">
                out_store_name = #{outStoreName},
            </if>
            <if test="inStore != null">
                in_store = #{inStore},
            </if>
            <if test="inStoreName != null">
                in_store_name = #{inStoreName},
            </if>
            <if test="outTime != null">
                out_time = #{outTime},
            </if>
            <if test="expectTime != null">
                expect_time = #{expectTime},
            </if>
            <if test="expectOutTime != null">
                expect_out_time = #{expectOutTime},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="inTime != null">
                in_time = #{inTime},
            </if>
            <if test="transport != null">
                transport = #{transport,jdbcType=INTEGER},
            </if>
            <if test="trackingNo != null">
                tracking_no = #{trackingNo,jdbcType=VARCHAR},
            </if>
            <if test="inStoreAdmin != null">
                in_store_admin = #{inStoreAdmin},
            </if>
            <if test=" inStoreAdminName!= null">
                in_store_admin_name= #{inStoreAdminName},
            </if>
            <if test=" outStoreAdmin!= null">
                out_store_admin= #{outStoreAdmin},
            </if>
            <if test=" outStoreAdminName!= null">
                out_store_admin_name = #{outStoreAdminName},
            </if>
            <if test=" outStatus!= null">
                out_status= #{outStatus},
            </if>
            <if test=" inStatus!= null">
                in_status= #{inStatus},
            </if>
            <if test="updatetime != null">
                updatetime = #{updatetime},
            </if>
            <if test="nextDayArrive != null">
                next_day_arrive = #{nextDayArrive}
            </if>
        </set>
        where list_no = #{listNo}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        update stock_allocation_list
        set list_no      = #{listNo,jdbcType=VARCHAR},
            create_admin = #{createAdmin,jdbcType=INTEGER},
            audit_admin  = #{auditAdmin,jdbcType=INTEGER},
            out_store    = #{outStore,jdbcType=INTEGER},
            in_store     = #{inStore,jdbcType=INTEGER},
            out_time     = #{outTime},
            expect_time  = #{expectTime},
            status       = #{status,jdbcType=INTEGER},
            in_time      = #{inTime},
            transport    = #{transport,jdbcType=INTEGER},
            tracking_no  = #{trackingNo,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectStockAllocationList"
            parameterType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO"
            resultType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        SELECT id,list_no listNo,create_admin createAdmin,audit_admin auditAdmin,out_store outStore,in_store
        inStore,out_time outTime,expect_time expectTime,status,
        in_time inTime,transport,tracking_no trackingNo,addtime,create_admin_name createAdminName,audit_admin_name
        auditAdminName,out_store_name outStoreName,in_store_name inStoreName,
        updatetime,in_store_admin inStoreAdmin,in_store_admin_name inStoreAdminName,out_store_admin
        outStoreAdmin,out_store_admin_name outStoreAdminName,out_status outStatus,in_status inStatus
        FROM stock_allocation_list
        WHERE status IN (4,5)
        <if test="inStore != null">
            AND in_store = #{inStore}
        </if>
        <if test="outStore != null">
            AND out_store = #{outStore}
        </if>
        <if test="addtime != null">
            AND DATE_FORMAT(addtime,'%Y-%m-%d') <![CDATA[>]]> DATE_FORMAT(#{addtime},'%Y-%m-%d')
        </if>
    </select>
    <select id="selectNearlyStockAllocationList"
            parameterType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO"
            resultType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        SELECT s1.list_no listNo, s1.addtime addtime
        FROM stock_allocation_list s1,
             (SELECT MAX(addtime) addtime
              FROM stock_allocation_list
              WHERE in_store = #{inStore}
                AND status IN (4, 5)
             ) s2
        WHERE s1.in_store = #{inStore}
          AND s1.status IN (4, 5)
          AND s1.addtime = s2.addtime

    </select>

    <select id="selectLastList"
            resultType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        select out_time outTime
        from stock_allocation_list
        where out_store = #{outStore}
          and in_store = #{inStore}
          and out_time <![CDATA[<]]> #{outTime}
          and create_admin is null
        order by out_time desc limit 1
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        select *
        from stock_allocation_list
        where status between 1 and 5
          and addtime >= '2022-09-01'
    </select>

    <select id="selectProcessingAllocationListBySkuAndWarehouseNo" resultType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        select DISTINCT t.list_no listNo, t.create_admin_name createAdminName, t.out_store_name outStoreName, t.in_store_name inStoreName, t.status, t.addtime, t.updatetime,
        t.out_store outStore, t.in_store inStore
        from stock_allocation_list t
        <if test="sku != null">
        LEFT JOIN stock_allocation_item sai on t.list_no = sai.list_no
        </if>
        where 1 = 1
        <if test="sku != null">
            and sai.sku = #{sku}
        </if>
        <if test="warehouseNo != null">
            and (t.in_store = #{warehouseNo} or t.out_store = #{warehouseNo})
        </if>
        and t.status in (3, 4)
        ORDER BY t.addtime DESC
    </select>

    <select id="selectByInStoreAndSku" resultType="net.summerfarm.wms.infrastructure.dao.allocation.dataobject.StockAllocationListDO">
        SELECT sal.id,sal.list_no listNo,sal.create_admin createAdmin,sal.audit_admin auditAdmin,sal.out_store outStore,sal.in_store
        inStore,sal.out_time outTime,sal.expect_time expectTime,sal.status,sal.
        in_time inTime,sal.transport,sal.tracking_no trackingNo,sal.addtime,sal.create_admin_name createAdminName,sal.audit_admin_name
        auditAdminName,sal.out_store_name outStoreName,sal.in_store_name inStoreName,sal.
        updatetime,sal.in_store_admin inStoreAdmin,sal.in_store_admin_name inStoreAdminName,sal.out_store_admin
        outStoreAdmin,sal.out_store_admin_name outStoreAdminName,sal.out_status outStatus,sal.in_status inStatus
        FROM `stock_allocation_list` sal
        JOIN `stock_allocation_item` sai ON sai.`list_no` = sal.`list_no`
        WHERE sal.in_store = #{inStore}
        AND sai.sku = #{sku}
        <if test="nextDayArrive != null">
            AND sal.`next_day_arrive` = #{nextDayArrive}
        </if>
        <if test="status != null">
            AND sal.`status` = #{status}
        </if>
    </select>

    <select id="queryAllocationRoadQuantity" resultType="net.summerfarm.wms.domain.allocation.valueobject.AllocationRoadQuantityValueObject">
        select al.`in_store` as warehouseNo, al.`list_no` as listNo , ai.`sku`,
        ai.`actual_out_quantity` - ai.`actual_in_quantity` - ifnull(damage.damage_quantity, 0) - ifnull(back.back_quantity, 0) as roadQuantity
        from `stock_allocation_list` al
        join `stock_allocation_item` ai on al.`list_no` = ai.`list_no`
        left join (
            select
            al.`in_store`, al.`list_no` , ai.`sku`, sum(ifnull(wdsi.should_quantity, 0)) as damage_quantity
            from `stock_allocation_list` al
            join `stock_allocation_item` ai on al.`list_no` = ai.`list_no`
            left join stock_task st on st.type = 59 and st.task_no = al.list_no
            left join wms_damage_stock_task wdst on wdst.stock_task_id = st.`id`
            left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id and wdsi.`sku` = ai.`sku`
            where al.`status` = 5
            and al.`next_day_arrive` = 1
            and al.`in_store` = #{warehouseNo}
            and ai.`actual_out_quantity` != ai.`actual_in_quantity`
            group by al.`in_store`, al.`list_no` , ai.`sku`
        ) damage on damage.in_store = al.`in_store` and damage.list_no = al.`list_no` and damage.sku = ai.`sku`
        left join (
            select
            al.`in_store`, al.`list_no` , ai.`sku`, sum(ifnull(ssti.actual_quantity, 0)) as back_quantity
            from `stock_allocation_list` al
            join `stock_allocation_item` ai on al.`list_no` = ai.`list_no`
            left join stock_task st on st.type = 18 and st.task_no = al.list_no
            left join stock_storage_item ssti on ssti.stock_task_id = st.`id` and ssti.`sku` = ai.`sku`
            where al.`status` = 5
            and al.`next_day_arrive` = 1
            and al.`in_store` = #{warehouseNo}
            and ai.`actual_out_quantity` != ai.`actual_in_quantity`
            group by al.`in_store`, al.`list_no` , ai.`sku`
        ) back on back.in_store = al.`in_store` and back.list_no = al.`list_no` and back.sku = ai.`sku`
        where al.`status` = 5
        and al.`next_day_arrive` = 1
        and al.`in_store` = #{warehouseNo}
        and ai.`actual_out_quantity` != ai.`actual_in_quantity`
        and ai.`actual_out_quantity` - ai.`actual_in_quantity` - ifnull(damage.damage_quantity, 0) - ifnull(back.back_quantity, 0) > 0
    </select>

</mapper>