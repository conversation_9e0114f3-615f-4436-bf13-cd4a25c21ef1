<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.WarehouseStockExtDAO">

    <insert id="saveWarehouseStockExt"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WarehouseStockExtDO">
        insert into warehouse_stock_ext (update_time, warehouse_no, sku, status)
        values (now(), #{warehouseNo}, #{sku}, #{status})
    </insert>

    <insert id="saveWarehouseStockExtBatch"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WarehouseStockExtDO">
        insert into warehouse_stock_ext (update_time,warehouse_no,sku,status)
        values
        <foreach collection="list" item="item" separator=",">
            (now(),#{item.warehouseNo},#{item.sku},#{item.status})
        </foreach>
    </insert>

    <select id="selectWarehouseStockExt"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WarehouseStockExtDO"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WarehouseStockExtDO">
        select id,
               create_time  createTime,
               update_time  updateTime,
               warehouse_no warehouseNo,
               sku,
               status
        from warehouse_stock_ext
        where sku = #{sku}
          and warehouse_no = #{warehouseNo}
    </select>

    <select id="listWarehouseStockExt"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WarehouseStockExtDO"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WarehouseStockExtDO">
        select id,
        create_time createTime,
        update_time updateTime,
        warehouse_no warehouseNo,
        sku,
        status
        from
        warehouse_stock_ext
        where warehouse_no = #{warehouseNo}
        and sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
    </select>

    <update id="updateWarehouseStockExt"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WarehouseStockExtDO">
        update warehouse_stock_ext
        set status = #{status}
        where sku = #{sku}
          and warehouse_no = #{warehouseNo}
    </update>

    <insert id="initWarehouseStockExt" parameterType="string">
        insert into warehouse_stock_ext(warehouse_no, sku, update_time)
        select warehouse_no, #{sku}, now()
        from warehouse_storage_center wsc
    </insert>

    <insert id="initBatchWarehouseStockExt">
        insert into warehouse_stock_ext(warehouse_no, sku, create_time)
        select #{warehouseNo}, sku, now()
        from inventory i
        where i.tenant_id = #{warehouseTenantId}
    </insert>

    <insert id="initBatchXMWarehouseStockExt">
        insert into warehouse_stock_ext(warehouse_no,sku,create_time)
        select #{warehouseNo},sku,now() from inventory i
    </insert>
</mapper>