<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.FreezeInvImbalanceDAO">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.FreezeInvImbalanceDO" id="FreezeInvImbalanceMap">
        <result property="storeId" column="id" jdbcType="INTEGER"/>
        <result property="change" column="change" jdbcType="INTEGER"/>
        <result property="safeQuantity" column="safe_quantity" jdbcType="INTEGER"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="VARCHAR"/>
        <result property="warehouseNo" column="area_no" jdbcType="INTEGER"/>
        <result property="pdName" column="pd_name" jdbcType="VARCHAR"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="sync" column="sync" jdbcType="INTEGER"/>
        <result property="onlineQuantity" column="online_quantity" jdbcType="INTEGER"/>
        <result property="quantity" column="quantity" jdbcType="INTEGER"/>
        <result property="lockQuantity" column="lock_quantity" jdbcType="INTEGER"/>
        <result property="saleLockQuantity" column="sale_lock_quantity" jdbcType="INTEGER"/>
        <result property="orderLock" column="orderlock" jdbcType="INTEGER"/>
        <result property="orderAndTaskLock" column="orderAndTasklock" jdbcType="INTEGER"/>
        <result property="diffLock" column="diffLock" jdbcType="INTEGER"/>
        <result property="finalLock" column="finallock" jdbcType="INTEGER"/>
        <result property="outTaskUnOutInv" column="outTaskUnOutInv" jdbcType="INTEGER"/>
        <result property="saleLock" column="saleLock" jdbcType="INTEGER"/>
        <result property="sampleLock" column="sampleLock" jdbcType="INTEGER"/>
        <result property="afterLock" column="afterLock" jdbcType="INTEGER"/>
        <result property="saasSaleLock" column="saasSaleLock" jdbcType="INTEGER"/>
        <result property="saasAfterLock" column="saasAfterLock" jdbcType="INTEGER"/>
        <result property="saleOutTaskUnOutInv" column="saleOutTaskUnOutInv" jdbcType="INTEGER"/>
        <result property="allocateOutTaskUnOutInv" column="allocateOutTaskUnOutInv" jdbcType="INTEGER"/>
        <result property="purchaseOutTaskUnOutInv" column="purchaseOutTaskUnOutInv" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="afterOrderNo" column="afterOrderNo" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryFreezeInvImbalance" resultMap="FreezeInvImbalanceMap" parameterType="java.lang.Integer">

        SELECT
            store.id,
            store.change,
            store.safe_quantity,
            wsc.warehouse_name,
            store.area_no,
            spu.pd_name,
            store.sku,
	        store.sync,
            store.online_quantity,
            store.quantity,
            store.lock_quantity,
            store.sale_lock_quantity,
	        (ifnull(xianmu_sale_lock.amount, 0)
	            + ifnull(xianmu_sample_lock.amount, 0)
	            + ifnull(xianmu_after_lock.amount, 0)
	            + ifnull(saas_sale_lock.amount,	0)
	            + ifnull(saas_after_lock.amount, 0)) AS 'orderlock',
	        (ifnull(xianmu_sale_lock.amount, 0)
	            + ifnull(xianmu_sample_lock.amount,	0)
	            + ifnull(xianmu_after_lock.amount, 0)
	            + ifnull(saas_sale_lock.amount,	0)
	            + ifnull(saas_after_lock.amount, 0))
	        + ifnull(sale_task_out.amount, 0) AS 'orderAndTasklock',
	        (store.sale_lock_quantity -
	            (
	                ifnull(xianmu_sale_lock.amount, 0)
	                + ifnull(xianmu_sample_lock.amount,	0)
	                + ifnull(xianmu_after_lock.amount, 0)
	                + ifnull(saas_sale_lock.amount,	0)
	                + ifnull(saas_after_lock.amount, 0)
                ) - ifnull(sale_task_out.amount, 0)
            ) AS 'diffLock',
	        (ifnull(xianmu_sale_lock.amount, 0)
	            + ifnull(xianmu_sample_lock.amount,	0)
	            + ifnull(xianmu_after_lock.amount, 0)
	            + ifnull(saas_sale_lock.amount,	0)
	            + ifnull(saas_after_lock.amount, 0)
	            - ifnull(sale_task_out.amount, 0)
	            - ifnull(allocate_task_out.amount, 0)
	            - ifnull(purchase_back_out.amount, 0)
            ) AS 'finallock',
            (0 - ifnull(sale_task_out.amount, 0)
                - ifnull(allocate_task_out.amount, 0)
                - ifnull(purchase_back_out.amount, 0)) 'outTaskUnOutInv',
            ifnull(xianmu_sale_lock.amount, 0) AS 'saleLock',
            ifnull(xianmu_sample_lock.amount, 0) AS 'sampleLock',
            ifnull(xianmu_after_lock.amount, 0) AS 'afterLock',
            ifnull(saas_sale_lock.amount, 0) AS 'saasSaleLock',
            ifnull(saas_after_lock.amount, 0) AS 'saasAfterLock',
            ifnull(sale_task_out.amount, 0) AS 'saleOutTaskUnOutInv',
            ifnull(allocate_task_out.amount, 0) AS 'allocateOutTaskUnOutInv',
            ifnull(purchase_back_out.amount, 0) AS 'purchaseOutTaskUnOutInv'
        FROM
            area_store store
            INNER JOIN inventory sku ON
                sku.outdated = 0 AND sku.sku = store.sku
            INNER JOIN products spu ON spu.pd_id = sku.pd_id
            INNER JOIN warehouse_storage_center wsc ON
                wsc.warehouse_no = store.area_no
                AND wsc.status = 1
                AND wsc.tenant_id = 1
            LEFT JOIN (
                SELECT
                    wim.warehouse_no,
                    oi.sku,
                    sum(IF(o.type = 1, dp.quantity, oi.amount)) amount
                FROM
                    delivery_plan dp
                INNER JOIN orders o ON
                    dp.order_no = o.order_no
                    AND o.status IN (1, 3, 6)
                    AND o.type IN (0, 1, 3, 12)
                INNER JOIN order_item oi ON
                    oi.order_no = o.order_no
                    AND oi.category_id != 3
                    AND oi.status IN (1, 3, 6)
                INNER JOIN warehouse_inventory_mapping wim ON
                    wim.store_no = dp.order_store_no
                    AND oi.sku = wim.sku
                INNER JOIN area_store ar ON
                    oi.sku = ar.sku
                    AND wim.warehouse_no = ar.area_no
                WHERE
                    dp.order_store_no in (select distinct(store_no) from warehouse_inventory_mapping where warehouse_no = #{warehouseNo})
                    and dp.delivery_time >= now()
                    AND dp.status IN (1, 3, 6)
                    GROUP BY
                    wim.warehouse_no,
                    oi.sku
            ) xianmu_sale_lock ON
                xianmu_sale_lock.warehouse_no = store.area_no
                AND xianmu_sale_lock.sku = store.sku
            LEFT JOIN (
                SELECT
                    wim.warehouse_no,
                    ss.sku,
                    sum(ss.amount) amount
                FROM
                    sample_sku ss
                INNER JOIN sample_apply sa ON
                    ss.sample_id = sa.sample_id
                    AND sa.delivery_time >= now()
                    AND sa.status IN(0, 1)
                INNER JOIN warehouse_inventory_mapping wim ON
                    wim.store_no = sa.store_no
                    AND wim.sku = ss.sku
                INNER JOIN area_store ar ON
                    ss.sku = ar.sku
                    AND wim.warehouse_no = ar.area_no
                where
                    wim.warehouse_no = #{warehouseNo}
                    and sa.store_no in (select distinct(store_no) from warehouse_inventory_mapping where warehouse_no = #{warehouseNo})
                GROUP BY
                    wim.warehouse_no,
                    ss.sku
            ) xianmu_sample_lock ON
                xianmu_sample_lock.warehouse_no = store.area_no
                AND xianmu_sample_lock.sku = store.sku
            LEFT JOIN (
                SELECT
                    wim.warehouse_no,
                    asdd.sku,
                    sum(asdd.quantity) amount
                FROM
                    after_sale_delivery_path asdp
                INNER JOIN after_sale_delivery_detail asdd ON
                    asdp.id = asdd.as_delivery_path_id
                    AND asdd.type = 0
                    AND asdd.status = 1
                INNER JOIN warehouse_inventory_mapping wim ON
                    wim.store_no = asdp.out_store_no
                    AND wim.sku = asdd.sku
                WHERE
                    asdp.delivery_time >= now()
                    AND asdp.status = 1
                GROUP BY
                    wim.warehouse_no,
                    asdd.sku
            ) xianmu_after_lock ON
                xianmu_after_lock.warehouse_no = store.area_no
                AND xianmu_after_lock.sku = store.sku
            LEFT JOIN (
                SELECT
                wim.warehouse_no,
                dpd.sku,
                sum(dpd.amount) amount
            FROM
                tms_delivery_plan dp
            INNER JOIN tms_delivery_plan_detail dpd ON
                dp.id = dpd.tms_delivery_plan_id
            INNER JOIN warehouse_inventory_mapping wim ON
                wim.store_no = dp.store_no
                AND dpd.sku = wim.sku
            WHERE
                dp.store_no in (select distinct(store_no) from warehouse_inventory_mapping where warehouse_no = #{warehouseNo})
                and dp.delivery_time >= now()
                AND dp.status IN (0, 1)
                AND dpd.status = 0
                AND dp.type = 1
                AND dpd.sku != 'DF001TD0001'
            GROUP BY
                wim.warehouse_no,
                dpd.sku
            ) saas_sale_lock ON
                saas_sale_lock.warehouse_no = store.area_no
                AND saas_sale_lock.sku = store.sku
            LEFT JOIN (
                SELECT
                    wim.warehouse_no,
                    s.sku,
                    sum(s.amount) amount
                FROM
                    tms_delivery_plan t
                INNER JOIN tms_delivery_plan_detail s ON
                    t.id = s.tms_delivery_plan_id
                INNER JOIN warehouse_inventory_mapping wim ON
                    wim.store_no = t.store_no
                    AND wim.sku = s.sku
                WHERE
                    t.type = 2
                    and  t.store_no in (select distinct(store_no) from warehouse_inventory_mapping where warehouse_no = #{warehouseNo})
                    AND t.delivery_time >= now()
                    AND t.`status` IN (0, 1)
                    AND s.`status` = 0
                    AND s.delivery_type = 0
                GROUP BY
                    wim.warehouse_no,
                    s.sku
            ) saas_after_lock ON
                saas_after_lock.warehouse_no = store.area_no
                AND saas_after_lock.sku = store.sku
            LEFT JOIN (
                -- 销售
                SELECT
                    st.area_no,
                    sti.sku,
                    sum(sti.quantity-actual_quantity) amount
                FROM
                    stock_task st
                INNER JOIN stock_task_item sti ON
                    sti.stock_task_id = st.id
                WHERE
                    st.area_no = #{warehouseNo}
                    and st.state IN (0, 1, 5)
                    AND st.type IN (51, 52, 53, 58, 59, 57, 60, 63)
                GROUP BY
                    st.area_no,
                    sti.sku
            ) sale_task_out ON
                sale_task_out.area_no = store.area_no
                AND sale_task_out.sku = store.sku
            LEFT JOIN (
            -- 调拨 50
                SELECT
                    st.area_no,
                    sti.sku,
                    sum(sti.actual_quantity) amount
                FROM
                    stock_task st
                INNER JOIN stock_shipment_item sti ON
                    sti.stock_task_id = st.id
                WHERE
                    st.area_no = #{warehouseNo}
                    and st.state IN (0, 1, 5)
                    -- and sti.sku = ''
                    AND st.type IN (50)
                GROUP BY
                    st.area_no,
                    sti.sku
            ) allocate_task_out ON
                allocate_task_out.area_no = store.area_no
                AND allocate_task_out.sku = store.sku
            LEFT JOIN (
                -- 采购退货 56
                SELECT
                    st.area_no,
                    sti.sku,
                    sum(sti.actual_out_quantity) amount
                FROM
                    stock_task st
                INNER JOIN purchases_back_detail sti ON sti.purchases_back_no = st.task_no
                WHERE
                    st.area_no = #{warehouseNo}
                    and st.state IN (0, 1, 5)
                    AND st.type IN (56)
                    GROUP BY
                    st.area_no,
                    sti.sku
            ) purchase_back_out ON
            purchase_back_out.area_no = store.area_no
            AND purchase_back_out.sku = store.sku
            LEFT JOIN (
                -- 多出入库
                SELECT
                    sts.in_warehouse_no area_no,
                    ssi.sku,
                    sum(ssi.quantity - ssi.`actual_quantity`) amount
                FROM `stock_task_storage` sts
                INNER JOIN `stock_storage_item` ssi on sts.id = ssi.`stock_task_storage_id`
                WHERE sts.`process_state` in (0, 1, 3)
                  AND sts.`type` = 23
                  AND sts.`in_warehouse_no` = #{warehouseNo}
                GROUP BY sts.in_warehouse_no, ssi.sku
            ) out_more_in on out_more_in.area_no = store.area_no and out_more_in.sku = store.sku
            LEFT JOIN (
                -- 销转采后退单
                SELECT t1.warehouse_no area_no,
                       t1.sku,
                       sum(t1.init_quantity) amount
                FROM (
                         SELECT * FROM `wms_cross_warehouse_sort_info`
                         WHERE `init_quantity` > 0
                           AND `warehouse_no` = #{warehouseNo}
                     ) t1 INNER JOIN (
                    SELECT * from `stock_task_storage` sts
                    WHERE sts.`process_state` in (0, 1, 3)
                      AND sts.`state` in (0,1,3)
                      AND sts.`type` in (11,25)
                      AND sts.`in_warehouse_no` = #{warehouseNo}
                ) t2 ON t1.pso_no = t2.pso_no
                WHERE t1.total_quantity = 0
                GROUP BY t1.warehouse_no, t1.sku
            ) cross_back on cross_back.area_no = store.area_no and cross_back.sku = store.sku
        WHERE
            store.area_no = #{warehouseNo}
          and store.sync = 1
          AND store.sale_lock_quantity != (ifnull(xianmu_sale_lock.amount, 0)
            + ifnull(xianmu_sample_lock.amount, 0)
            + ifnull(xianmu_after_lock.amount, 0)
            + ifnull(saas_sale_lock.amount, 0)
            + ifnull(saas_after_lock.amount, 0))
            + ifnull(sale_task_out.amount, 0)
            + ifnull(out_more_in.amount, 0)
            + ifnull(cross_back.amount, 0)
    </select>

    <select id="queryXianMuSaleLock" resultMap="FreezeInvImbalanceMap">
        SELECT
            wim.warehouse_no,
            oi.order_no,
            oi.sku,
            sum(IF(o.type = 1, dp.quantity, oi.amount)) amount
        FROM
            delivery_plan dp
                INNER JOIN orders o ON
                        dp.order_no = o.order_no
                    AND o.status IN (1, 3, 6)
                    AND o.type IN (0, 1, 3, 12)
                INNER JOIN order_item oi ON
                        oi.order_no = o.order_no
                    AND oi.category_id != 3
                    AND oi.status IN (1, 3, 6)
                INNER JOIN warehouse_inventory_mapping wim ON
            wim.store_no = dp.order_store_no
            AND oi.sku = wim.sku
            INNER JOIN area_store ar ON
            oi.sku = ar.sku
            AND wim.warehouse_no = ar.area_no
        WHERE
            dp.order_store_no in (select distinct(store_no) from warehouse_inventory_mapping where warehouse_no = #{warehouseNo})
          and dp.delivery_time >= now()
          AND dp.status IN (1, 3, 6)
        and oi.sku in <foreach collection="skuList" item="sku" open="(" close=")" separator=",">#{sku}</foreach>
        GROUP BY
            wim.warehouse_no,
            oi.order_no,
            oi.sku
    </select>

    <select id="querySaasSaleLock" resultMap="FreezeInvImbalanceMap">
        SELECT
            wim.warehouse_no,
            dp.order_no,
            dpd.sku
        FROM
            tms_delivery_plan dp
                INNER JOIN tms_delivery_plan_detail dpd ON
                dp.id = dpd.tms_delivery_plan_id
                INNER JOIN warehouse_inventory_mapping wim ON
                        wim.store_no = dp.store_no
                    AND dpd.sku = wim.sku
        WHERE
          dp.store_no in (select distinct(store_no) from warehouse_inventory_mapping where warehouse_no = #{warehouseNo})
          and dp.delivery_time >= now()
          AND dp.status IN (0, 1)
          AND dpd.status = 0
          and dpd.sku in <foreach collection="skuList" open="(" close=")" separator="," item="sku">#{sku}</foreach>
          AND dp.type = 1
          AND dpd.sku != 'DF001TD0001'
        GROUP BY
            wim.warehouse_no,
            dp.order_no,
            dpd.sku
    </select>

    <select id="querySaasAfterSaleLock" resultMap="FreezeInvImbalanceMap">
        SELECT
            wim.warehouse_no,
            t.order_no as afterOrderNo,
            s.sku
        FROM
            tms_delivery_plan t
                INNER JOIN tms_delivery_plan_detail s ON
                t.id = s.tms_delivery_plan_id
                INNER JOIN warehouse_inventory_mapping wim ON
                        wim.store_no = t.store_no
                    AND wim.sku = s.sku
        WHERE
            t.type = 2
          and  t.store_no in (select distinct(store_no) from warehouse_inventory_mapping where warehouse_no = #{warehouseNo})
          AND t.delivery_time >= now()
          AND t.`status` IN (0, 1)
          AND s.`status` = 0
          AND s.delivery_type = 0
          and s.sku in <foreach collection="skuList" open="(" close=")" separator="," item="sku">#{sku}</foreach>
        GROUP BY
            wim.warehouse_no,
            t.order_no,
            s.sku
    </select>
</mapper>