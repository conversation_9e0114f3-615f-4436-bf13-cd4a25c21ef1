<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.CabinetBatchInventoryDAO">
    <resultMap id="CabinetBatchInventoryMap"
               type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryDO">
        <id property="id" column="id"/>
        <result property="warehouseNo" column="warehouse_no"/>
        <result property="sku" column="sku"/>
        <result property="cabinetCode" column="cabinet_code"/>
        <result property="cabinetId" column="cabinet_id"/>
        <result property="quantity" column="quantity"/>
        <result property="lockQuantity" column="lock_quantity"/>
        <result property="availableQuantity" column="available_quantity"/>
        <result property="produceDate" column="produce_date"/>
        <result property="qualityDate" column="quality_date"/>
        <result property="batchNo" column="batch_no"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createOperator" column="create_operator"/>
        <result property="updateOperator" column="update_operator"/>
        <result property="ownerCode" column="owner_code"/>
        <result property="ownerName" column="owner_name"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="zoneCode" column="zone_code"/>
        <result property="zoneType" column="zone_type"/>
        <result property="cabinetType" column="cabinet_type"/>
        <result property="cabinetPurpose" column="cabinet_purpose"/>
    </resultMap>

    <sql id="table_name">
        wms_cabinet_batch_inventory
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `warehouse_no`, `sku`, `cabinet_code`, `cabinet_id`, `quantity`, `lock_quantity`, `available_quantity`,
        `produce_date`, `quality_date`, `batch_no`, `create_time`, `update_time`, `create_operator`,
        `update_operator`, `owner_code`, `owner_name`, `tenant_id`
            , `zone_code`, `zone_type`, `cabinet_type`, `cabinet_purpose`
    </sql>

    <sql id="values_exclude_id">
        #{warehouseNo}, #{sku}, #{cabinetCode}, #{cabinetId}, #{quantity}, #{lockQuantity}, #{availableQuantity},
        #{produceDate}, #{qualityDate}, #{batchNo}, #{createTime}, #{updateTime}, #{createOperator},
        #{updateOperator}, #{ownerCode}, #{ownerName}, #{tenantId}
            , #{zoneCode}, #{zoneType}, #{cabinetType}, #{cabinetPurpose}
    </sql>

    <sql id="query">
        <where>
            <if test="warehouseNo != null">AND `warehouse_no` = #{warehouseNo}</if>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                AND `warehouse_no` in
                <foreach collection="warehouseNoList" item="warehouseNo1" open="(" close=")" separator=",">
                    #{warehouseNo1}
                </foreach>
            </if>
            <if test="sku != null">AND `sku` = #{sku}</if>
            <if test="skuList != null and skuList.size() > 0">
                AND `sku` in
                <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                    #{sku1}
                </foreach>
            </if>
            <if test="cabinetCode != null">AND `cabinet_code` = #{cabinetCode}</if>
            <if test="cabinetCodeList != null and cabinetCodeList.size() > 0">
                AND `cabinet_code` in
                <foreach collection="cabinetCodeList" item="cabinetCode1" open="(" close=")" separator=",">
                    #{cabinetCode1}
                </foreach>
            </if>
            <if test="cabinetId != null">AND `cabinet_id` = #{cabinetId}</if>
            <if test="quantity != null">AND `quantity` = #{quantity}</if>
            <if test="lockQuantity != null">AND `lock_quantity` = #{lockQuantity}</if>
            <if test="availableQuantity != null">AND `available_quantity` = #{availableQuantity}</if>
            <if test="availableQuantityGtZero != null">AND `available_quantity` > 0</if>
            <if test="quantityGtZero != null">
                <if test="quantityGtZero == true">
                    and quantity > 0
                </if>
                <if test="quantityGtZero == false">
                    and quantity &lt;= 0
                </if>
            </if>
            <if test="saleOut != null">
                <if test="saleOut == true">
                    and available_quantity &lt;= 0
                </if>
                <if test="saleOut == false">
                    and available_quantity > 0
                </if>
            </if>
            <if test="produceDate != null">AND `produce_date` = #{produceDate}</if>
            <if test="produceDateGe != null">AND `produce_date` >= #{produceDateGe}</if>
            <if test="produceDateLe != null">AND `produce_date` &lt;= #{produceDateLe}</if>
            <if test="produceDateList != null and produceDateList.size() > 0">
                AND `produce_date` in
                <foreach collection="produceDateList" item="produceDate1" open="(" close=")" separator=",">
                    #{produceDate1}
                </foreach>
            </if>
            <if test="skuProductRangeQueryList != null and skuProductRangeQueryList.size() > 0">
                AND
                <foreach collection="skuProductRangeQueryList" item="skuProductRangeQuery1" open="(" close=")" separator="or">
                    <if test="skuProductRangeQuery1.produceDateGe != null and skuProductRangeQuery1.produceDateLe != null">
                        (`produce_date` >= #{skuProductRangeQuery1.produceDateGe}
                        and `produce_date` &lt;= #{skuProductRangeQuery1.produceDateLe})
                    </if>
                    <if test="skuProductRangeQuery1.produceDateGe == null and skuProductRangeQuery1.produceDateLe != null">
                        (`produce_date` &lt;= #{skuProductRangeQuery1.produceDateLe})
                    </if>
                    <if test="skuProductRangeQuery1.produceDateGe != null and skuProductRangeQuery1.produceDateLe == null">
                        (`produce_date` >= #{skuProductRangeQuery1.produceDateGe})
                    </if>
                </foreach>
            </if>
            <if test="qualityDate != null">AND `quality_date` = #{qualityDate}</if>
            <if test="qualityDateGe != null">AND `quality_date` >= #{qualityDateGe}</if>
            <if test="qualityDateLt != null">AND `quality_date` &lt; #{qualityDateLt}</if>
            <if test="qualityDateList != null and qualityDateList.size() > 0">
                AND `quality_date` in
                <foreach collection="qualityDateList" item="qualityDate1" open="(" close=")" separator=",">
                    #{qualityDate1}
                </foreach>
            </if>
            <if test="batchNo != null">AND `batch_no` = #{batchNo}</if>
            <if test="batchNoList != null and batchNoList.size() > 0">
                AND `batch_no` in
                <foreach collection="batchNoList" item="batchNo1" open="(" close=")" separator=",">
                    #{batchNo1}
                </foreach>
            </if>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
            <if test="createOperator != null">AND `create_operator` = #{createOperator}</if>
            <if test="updateOperator != null">AND `update_operator` = #{updateOperator}</if>
            <if test="ownerCode != null">AND `owner_code` = #{ownerCode}</if>
            <if test="ownerName != null">AND `owner_name` = #{ownerName}</if>
            <if test="tenantId != null">AND `tenant_id` = #{tenantId}</if>
            <if test="zoneCode != null">AND `zone_code` = #{zoneCode}</if>
            <if test="zoneType != null">AND `zone_type` = #{zoneType}</if>
            <if test="cabinetType != null">AND `cabinet_type` = #{cabinetType}</if>
            <if test="cabinetPurpose != null">AND `cabinet_purpose` = #{cabinetPurpose}</if>
            <if test="cabinetPurposeList != null and cabinetPurposeList.size() > 0">
                AND `cabinet_purpose` in
                <foreach collection="cabinetPurposeList" item="cabinetPurpose1" open="(" close=")" separator=",">
                    #{cabinetPurpose1}
                </foreach>
            </if>
            <if test="cabinetCodeNotInList != null and cabinetCodeNotInList.size() > 0">
                AND `cabinet_code` not in
                <foreach collection="cabinetCodeNotInList" item="cabinetCode1" open="(" close=")" separator=",">
                    #{cabinetCode1}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="orderByQuery">
        <if test="sorts != null and sorts.size() > 0">
            ORDER BY
            <foreach collection="sorts" item="i" index="index" separator=",">
                ${i.columnName} ${i.sortType}
            </foreach>
        </if>
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">`warehouse_no`,</if>
            <if test="sku != null">`sku`,</if>
            <if test="cabinetCode != null">`cabinet_code`,</if>
            <if test="cabinetId != null">`cabinet_id`,</if>
            <if test="quantity != null">`quantity`,</if>
            <if test="lockQuantity != null">`lock_quantity`,</if>
            <if test="availableQuantity != null">`available_quantity`,</if>
            <if test="produceDate != null">`produce_date`,</if>
            <if test="qualityDate != null">`quality_date`,</if>
            <if test="batchNo != null">`batch_no`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="createOperator != null">`create_operator`,</if>
            <if test="updateOperator != null">`update_operator`,</if>
            <if test="ownerCode != null">`owner_code`,</if>
            <if test="ownerName != null">`owner_name`,</if>
            <if test="tenantId != null">`tenant_id`,</if>
            <if test="zoneCode != null">`zone_code`,</if>
            <if test="zoneType != null">`zone_type`,</if>
            <if test="cabinetType != null">`cabinet_type`,</if>
            <if test="cabinetPurpose != null">`cabinet_purpose`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">#{warehouseNo},</if>
            <if test="sku != null">#{sku},</if>
            <if test="cabinetCode != null">#{cabinetCode},</if>
            <if test="cabinetId != null">#{cabinetId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="lockQuantity != null">#{lockQuantity},</if>
            <if test="availableQuantity != null">#{availableQuantity},</if>
            <if test="produceDate != null">#{produceDate},</if>
            <if test="qualityDate != null">#{qualityDate},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createOperator != null">#{createOperator},</if>
            <if test="updateOperator != null">#{updateOperator},</if>
            <if test="ownerCode != null">#{ownerCode},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="zoneCode != null">#{zoneCode},</if>
            <if test="zoneType != null">#{zoneType},</if>
            <if test="cabinetType != null">#{cabinetType},</if>
            <if test="cabinetPurpose != null">#{cabinetPurpose},</if>
        </trim>
    </insert>

    <insert id="creates" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.warehouseNo}, #{i.sku}, #{i.cabinetCode}, #{i.cabinetId}, #{i.quantity}, #{i.lockQuantity},
            #{i.availableQuantity}, #{i.produceDate}, #{i.qualityDate}, #{i.batchNo}, #{i.createTime},
            #{i.updateTime}, #{i.createOperator}, #{i.updateOperator}, #{i.ownerCode}, #{i.ownerName}, #{i.tenantId}
                , #{i.zoneCode}, #{i.zoneType}, #{i.cabinetType}, #{i.cabinetPurpose})
        </foreach>
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.cabinet.CabinetBatchInventoryUpdateDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="warehouseNo != null">`warehouse_no` = #{warehouseNo},</if>
            <if test="sku != null">`sku` = #{sku},</if>
            <if test="cabinetCode != null">`cabinet_code` = #{cabinetCode},</if>
            <if test="cabinetId != null">`cabinet_id` = #{cabinetId},</if>
            <if test="quantity != null">`quantity` = #{quantity},</if>
            <if test="lockQuantity != null">`lock_quantity` = #{lockQuantity},</if>
            <if test="availableQuantity != null">`available_quantity` = #{availableQuantity},</if>
            <if test="produceDate != null">`produce_date` = #{produceDate},</if>
            <if test="qualityDate != null">`quality_date` = #{qualityDate},</if>
            <if test="batchNo != null">`batch_no` = #{batchNo},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="updateTime != null">`update_time` = #{updateTime},</if>
            <if test="createOperator != null">`create_operator` = #{createOperator},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            <if test="ownerCode != null">`owner_code` = #{ownerCode},</if>
            <if test="ownerName != null">`owner_name` = #{ownerName},</if>
            <if test="tenantId != null">`tenant_id` = #{tenantId},</if>
            <if test="zoneCode != null">`zone_code` = #{zoneCode},</if>
            <if test="zoneType != null">`zone_type` = #{zoneType},</if>
            <if test="cabinetType != null">`cabinet_type` = #{cabinetType},</if>
            <if test="cabinetPurpose != null">`cabinet_purpose` = #{cabinetPurpose},</if>
            <if test="addQuantity != null">`quantity` = `quantity`  + #{addQuantity},</if>
            <if test="addLockQuantity != null">`lock_quantity` = `lock_quantity`  + #{addLockQuantity},</if>
            <if test="addAvailableQuantity != null">`available_quantity` = `available_quantity` + #{addAvailableQuantity},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
        <if test="addQuantity != null">
            and  `quantity` + #{addQuantity} >= 0
        </if>
        <if test="addLockQuantity != null">
            and  `lock_quantity` + #{addLockQuantity} >= 0
        </if>
        <if test="addAvailableQuantity != null">
            and  `available_quantity` + #{addAvailableQuantity} >= 0
        </if>
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="CabinetBatchInventoryMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="CabinetBatchInventoryMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryQueryDO"
            resultType="long">
        /*FORCE_MASTER*/ SELECT COUNT(*)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryQueryDO"
            resultMap="CabinetBatchInventoryMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryQueryDO"
            resultMap="CabinetBatchInventoryMap">
        /*FORCE_MASTER*/ SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    <update id="updateCabinetBatchInventoryCabinetInfo100">
        UPDATE
            wms_cabinet_batch_inventory
        set
            cabinet_type = #{cabinetType},
            cabinet_purpose = #{cabinetPurpose}
        where warehouse_no = #{warehouseNo}
          and cabinet_code = #{cabinetCode}
          and (cabinet_type is null or cabinet_type != #{cabinetType})
          and (cabinet_purpose is null or cabinet_purpose != #{cabinetPurpose})
            limit 100;
    </update>

    <update id="updateCabinetBatchInventoryZoneInfo100">
        UPDATE
            wms_cabinet_batch_inventory
        set
            zone_type = #{zoneType}
        where warehouse_no = #{warehouseNo}
          and zone_code = #{zoneCode}
          and (zone_type is null or zone_type != #{zoneType})
            limit 100;
    </update>

    <select id="findAvailableQuantitySum" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryQueryDO"
            resultType="java.lang.Integer">
        /*FORCE_MASTER*/ SELECT IFNULL(SUM(available_quantity), 0) availableQuantity
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    <select id="findLockQuantitySum" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.CabinetBatchInventoryQueryDO"
            resultType="java.lang.Integer">
        /*FORCE_MASTER*/ SELECT IFNULL(SUM(lock_quantity), 0) lockQuantity  
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    
</mapper>
