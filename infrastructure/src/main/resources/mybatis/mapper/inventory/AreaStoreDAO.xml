<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.AreaStoreDAO">

    <select id="selectByStockCd" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SkuStockDO">
        select b.sku, b.quantity as stockNum
        from area_store as b
        left join area_sku as a
        on b.sku = a.sku
        <where>
            <if test="warehouseNo != null">
                b.area_no = #{warehouseNo}
            </if>
            <if test="stockCd == 1">
                and b.`quantity` > 0
            </if>
            <if test="stockCd == 2">
                and b.`quantity` = 0
            </if>
            <if test="saleOpCd != null and saleOpCd.size() > 0">
                and a.`on_sale` in
                <foreach collection="saleOpCd" item="saleTypeItem" open="(" close=")" separator=",">
                    #{saleTypeItem}
                </foreach>
            </if>
        </where>
        group by b.sku, b.quantity
    </select>

    <select id="selectBySkus" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SkuStockDO">
        select sku, quantity as stockNum
        from area_store
        where area_no = #{warehouseNo}
        and sku in
        <foreach collection="skus" separator="," item="sku" open="(" close=")">
            #{sku}
        </foreach>
    </select>

    <insert id="updateStoreStockByStoreNo" useGeneratedKeys="true">
        update area_store
        set quantity = quantity + #{quantity}
        where area_no = #{warehouseNo}
          AND sku = #{sku}
    </insert>

    <update id="updateLockStockByStoreNo">
        update area_store ar
        set ar.lock_quantity = #{quantity} + ar.lock_quantity
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{warehouseNo}

    </update>

    <update id="updateOnlineQuantityAndChange">
        UPDATE area_store ar
        SET ar.online_quantity = #{onlineChange} + ar.online_quantity,
            ar.`change`        = #{changeChange} + ar.`change`
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{warehouseNo}
    </update>

    <update id="updateChangeQuantity">
        UPDATE area_store ar
        SET ar.`change`        = #{changeChange} + ar.`change`
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{warehouseNo}
    </update>

    <update id="updateRoadStockByStoreNo">
        update area_store ar
        set ar.road_quantity = #{quantity} + ar.road_quantity
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{warehouseNo}
    </update>

    <update id="updateSafeStockByStoreNo">
        update area_store ar
        set ar.safe_quantity = #{quantity}
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{storeNo}
    </update>

    <update id="updateStatusByStoreNo">
        update area_store ar
        SET ar.status = #{status}
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{storeNo}
    </update>

    <update id="updateAdvanceStockByWarehouseNo">
        update area_store ar
        set ar.advance_quantity = ifnull(#{quantity} + ar.advance_quantity, 0)
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{warehouseNo}
    </update>

    <update id="updateCostPriceAndMarketPrice">
        update area_store
        <set>
            <if test="costPrice != null">
                cost_price = #{costPrice},
            </if>
            <if test="marketPrice != null">
                market_price = #{marketPrice}
            </if>
        </set>
        where area_no = #{storeNo} and sku = #{sku}
    </update>

    <select id="listBySkus" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,
        t.safe_quantity safeQuantity,t.change,t.status,t.cost_price costPrice,t.market_price marketPrice,
        t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,t.support_reserved supportReserved,t.advance_quantity advanceQuantity,
        t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        <where>
            <if test="warehouseNo != null">
                and t.area_no = #{warehouseNo}
            </if>
            <if test="skus != null and skus.size() > 0">
                and t.sku in
                <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOne" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        /*FORCE_MASTER*/ SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,
        t.safe_quantity safeQuantity,t.change,t.status,t.cost_price costPrice,t.market_price marketPrice,
        t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,t.support_reserved supportReserved,t.advance_quantity advanceQuantity,
        t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        <where>
            <if test="areaNo != null">
                and t.area_no = #{areaNo}
            </if>
            <if test="sku != null">
                and t.sku = #{sku}
            </if>
        </where>
    </select>

    <select id="selectWithOutDataPermission"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time
        leadTime,t.sync,t.auto_transfer autoTransfer,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,t.safe_quantity
        safeQuantity,t.change,t.status,advance_quantity advanceQuantity,
        t.cost_price costPrice,t.market_price marketPrice,t.reserve_max_quantity
        reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity ,t.reserve_use_quantity reserveUseQuantity,
        t.support_reserved supportReserved,t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        <where>
            <if test="areaNo != null">
                and t.area_no = #{areaNo}
            </if>
            <if test="sku != null">
                and t.sku = #{sku}
            </if>
        </where>
    </select>

    <select id="selectById" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id,
               t.area_no              areaNo,
               t.sku,
               t.quantity,
               t.admin_id             adminId,
               t.lead_time            leadTime,
               t.sync,
               t.online_quantity      onlineQuantity,
               t.road_quantity        roadQuantity,
               t.lock_quantity        lockQuantity,
               t.safe_quantity        safeQuantity,
               t.advance_quantity     advanceQuantity,
               t.`change`,
               t.reserve_max_quantity reserveMaxQuantity,
               t.reserve_min_quantity reserveMinQuantity,
               t.reserve_use_quantity reserveUseQuantity,
               t.support_reserved     supportReserved
                , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        where id = #{id}
    </select>


    <update id="updateSaleLockStock">
        update area_store ar
        set ar.lock_quantity=#{lockQuantity} + ar.lock_quantity,
            ar.sale_lock_quantity = #{lockQuantity} + ar.sale_lock_quantity
        WHERE ar.sku = #{sku}
          and ar.area_no = #{warehouseNo}
    </update>

    <update id="updateCostPrice">
        update area_store
        set cost_price   = #{cost},
            market_price = #{marketCost}
        WHERE sku = #{sku}
          and area_no = #{warehouseNo}
    </update>


    <update id="updateReserveQuantity">
        update area_store ar
        <set>
            <if test="maxQuantity != null">
                ar.reserve_max_quantity = #{maxQuantity}
            </if>
            <if test="minQuantity != null">
                ,ar.reserve_min_quantity = #{minQuantity}
            </if>
        </set>
        WHERE ar.sku=#{sku} AND ar.area_no= #{storeNo}
    </update>

    <update id="changeToReserve">
        update area_store ar set ar.reserve_max_quantity = #{maxQuantity}+ar.reserve_max_quantity
        ,ar.reserve_min_quantity = #{minQuantity} + ar.reserve_min_quantity
        ,ar.reserve_use_quantity = #{useQuantity} + ar.reserve_use_quantity
        <if test="support != null">
            ,ar.support_reserved = #{support}
        </if>
        WHERE ar.sku=#{sku} AND ar.area_no= #{storeNo}
    </update>
    <update id="changeToNoSupport">
        update area_store ar
        set ar.reserve_max_quantity = 0,
            ar.reserve_min_quantity = 0,
            ar.reserve_use_quantity = 0,
            ar.support_reserved     = 0
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{storeNo}
    </update>

    <update id="changeCenterToNoSupport">
        UPDATE warehouse_inventory_mapping wim
        on wim.store_no = center.area_no and wim.sku = center.sku
            SET
                center.reserve_max_quantity = center.reserve_max_quantity - wim.reserve_max_quantity,
                center.reserve_min_quantity = center.reserve_min_quantity - wim.reserve_min_quantity,
                center.reserve_use_quantity = center.reserve_use_quantity - wim.reserve_use_quantity
        WHERE store.sku=#{sku}
          AND store.area_no=#{storeNo}
    </update>

    <update id="updateAllSupportReservedQuantity">
        update area_store ar
        set ar.reserve_max_quantity = 0,
            ar.reserve_min_quantity = 0,
            ar.reserve_use_quantity = 0
        WHERE ar.reserve_max_quantity > 0
    </update>

    <select id="selectCostPriceByArea" resultType="java.math.BigDecimal">
        select ifnull(sr.cost, 0)
        from store_record sr
                 left join purchases p on p.purchase_no = sr.batch
        where sr.type in (10, 11, 16)
          and sr.sku = #{sku}
          and sr.area_no = #{warehouseNo}
        order by p.purchase_time desc, p.add_time desc limit 1
    </select>


    <insert id="initAfterCreateSku" parameterType="string">
        insert into area_store(area_no, sku, update_time)
        select warehouse_no, #{sku}, now()
        from warehouse_storage_center wsc
    </insert>

    <insert id="initAfterCreateWarehouse">
        insert into area_store(tenant_id, area_no, warehouse_tenant_id, sku, update_time)
        select i.tenant_id, #{warehouseNo}, #{warehouseTenantId}, sku, now()
        from inventory i
        where i.tenant_id = #{warehouseTenantId}
    </insert>

    <update id="changeToReserveNew">
        update area_store ar
        set ar.reserve_max_quantity = #{maxQuantity} + ar.reserve_max_quantity
          , ar.reserve_min_quantity = #{minQuantity} + ar.reserve_min_quantity
          , ar.reserve_use_quantity = #{useQuantity} + ar.reserve_use_quantity
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{storeNo}
          and ar.support_reserved = 1
    </update>

    <update id="updateWarningQuantity">
        update area_store
        set warning_quantity = #{warningInventory}
        where sku = #{sku}
          and area_no = #{warehouseNo}
    </update>

    <select id="selectAvailableQuantity" resultType="java.lang.Integer">
        select IFNULL(ar.quantity, 0) - IFNULL(ar.lock_quantity, 0) - IFNULL(ar.safe_quantity, 0)
        from area_store ar
        where sku = #{sku}
          and area_no = #{warehouseNo}
    </select>


    <select id="selectSaleLockQuantity" parameterType="java.lang.Integer"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        select sku, area_no areaNo
        from area_store
        where sale_lock_quantity > 0
          and sync = 1
          and area_no = #{areaNo}
    </select>

    <select id="selectStoreQuantity" parameterType="java.lang.Integer"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        select sku, area_no areaNo
        from area_store
        where quantity > 0
          and area_no = #{areaNo}
    </select>
    <select id="selectOneNoAuth" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,
        t.safe_quantity safeQuantity,t.change ,t.cost_price costPrice,t.market_price marketPrice,
        t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,t.support_reserved supportReserved,t.advance_quantity advanceQuantity,
        t.sale_lock_quantity saleLockQuantity,wse.status
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        left join warehouse_stock_ext wse on t.sku = wse.sku and t.area_no=wse.warehouse_no
        <where>
            <if test="areaNo != null">
                and t.area_no = #{areaNo}
            </if>
            <if test="sku != null">
                and t.sku = #{sku}
            </if>
        </where>
    </select>

    <update id="updateLockQuantity">
        update area_store
        set lock_quantity      = lock_quantity + #{updateQuantity},
            sale_lock_quantity = sale_lock_quantity + #{updateQuantity},
            online_quantity    = online_quantity - #{updateQuantity}
        where id = #{id}
          and sync = 1
    </update>

    <select id="selectByStoreNoAndSkuNew"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id,
               t.area_no              areaNo,
               t.sku,
               t.quantity,
               t.admin_id             adminId,
               t.lead_time            leadTime,
               t.sync,
               t.online_quantity      onlineQuantity,
               t.lock_quantity        lockQuantity,
               t.change,
               auto_transfer          autoTransfer,
               t.safe_quantity        safeQuantity,
               t.reserve_max_quantity reserveMaxQuantity,
               t.reserve_min_quantity reserveMinQuantity,
               t.reserve_use_quantity reserveUseQuantity,
               t.support_reserved     supportReserved,
               t.sale_lock_quantity   saleLockQuantity
                , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
                 left join warehouse_inventory_mapping wim on wim.warehouse_no = t.area_no and t.sku = wim.sku
        WHERE wim.store_no = #{storeNo}
          and t.sku = #{sku}
    </select>
    <update id="updateReserveByWarehouseNo">
        update area_store
        set reserve_max_quantity = 0,
            reserve_min_quantity = 0,
            reserve_use_quantity = 0
        where reserve_max_quantity > 0
          and area_no = #{warehouseNo}
    </update>

    <update id="saasUpdateQuantity">
        UPDATE area_store ar
        SET ar.online_quantity    = #{onlineChange} + ar.online_quantity,
            ar.`change`           = #{changeChange} + ar.`change`,
            ar.lock_quantity      = #{lockQuantity} + ar.lock_quantity,
            ar.sale_lock_quantity = #{lockQuantity} + ar.sale_lock_quantity
        WHERE ar.sku = #{sku}
          AND ar.area_no = #{storeNo}
    </update>

    <select id="selectAllAreaNo" resultType="java.lang.String">
        SELECT DISTINCT area_no
        FROM area_store
    </select>

    <select id="selectOneByCondition"
            resultType="net.summerfarm.wms.domain.areaStore.domainobject.AreaStoreWithAllocationRoadData">
    SELECT
    SUM(r.quantity) quantity,
    SUM(r.lock_quantity) lockQuantity,
    SUM(r.safe_quantity) safeQuantity,
    SUM(r.road_quantity) roadQuantity,
    SUM(r.online_quantity) onlineQuantity,
    SUM(r.allocation_road_quantity) allocationRoadQuantity,
    SUM(r.allocation_road_goods_value) allocationRoadGoodsValue
    FROM (
        SELECT
        MAX(t.`quantity`) quantity,
        MAX(t.`lock_quantity`) lock_quantity,
        MAX(t.`safe_quantity`) safe_quantity,
        MAX(t.`road_quantity`) road_quantity,
        MAX(t.`online_quantity`) online_quantity,
        SUM(IFNULL(a.`road_quantity`, 0)) allocation_road_quantity,
        SUM(IFNULL(a.`road_quantity`, 0) * IFNULL(a.`cost`, 0)) allocation_road_goods_value
        FROM `area_store` t
        LEFT JOIN `inventory_allocation_road_cost_batch` a ON a.`warehouse_no` = t.`area_no` AND a.`sku` = t.`sku` AND a.`road_quantity` > 0
        <where>
            <if test="skuList != null and skuList.size() > 0">
                and t.sku in
                <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                and t.area_no in
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="saleOut != null">
                <if test="saleOut == true">
                    and t.`online_quantity` &lt;= 0
                </if>
                <if test="saleOut == false">
                    and t.`online_quantity` > 0
                </if>
            </if>
            <if test="sync != null">
                and
                <if test="sync == true">
                    t.sync = 1
                </if>
                <if test="sync == false">
                    t.sync = 0
                </if>
            </if>
            <if test="inOutRecord != null">
                and
                <if test="inOutRecord == true">
                    EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
                </if>
                <if test="inOutRecord == false">
                    NOT EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
                </if>
            </if>
        </where>
        GROUP BY t.`area_no` , t.`sku`
    ) r
    </select>

    <select id="selectListByCondition"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">

        SELECT
        t.sku sku,
        sum(t.quantity) quantity,
        sum(t.online_quantity) onlineQuantity,
        sum(t.lock_quantity) lockQuantity,
        sum(t.safe_quantity) safeQuantity,
        sum(t.road_quantity) roadQuantity
        FROM
        area_store t
        <where>1=1
            <if test="skuList != null and skuList.size() > 0">
                and t.sku in
                <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                and t.area_no in
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="saleOut != null">
                <if test="saleOut == true">
                    and t.`online_quantity` &lt;= 0
                </if>
                <if test="saleOut == false">
                    and t.`online_quantity` > 0
                </if>
            </if>
            <if test="inOutRecord != null">
                and
                <if test="inOutRecord == true">
                    EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
                </if>
                <if test="inOutRecord == false">
                    NOT EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
                </if>
            </if>
        </where>
        GROUP BY
        t.`sku`
    </select>

    <select id="selectAreaNoAndSyncListBySku" resultType="net.summerfarm.wms.domain.areaStore.domainobject.WarehouseAndSyncBySkuCode">
        select
            t.area_no as warehouseNo,
            t.sync,
            t.sku as skuCode,
            t.quantity,
            t.online_quantity      onlineQuantity,
            t.road_quantity        roadQuantity,
            t.lock_quantity        lockQuantity,
            t.sale_lock_quantity        saleLockQuantity,
            t.safe_quantity        safeQuantity,
            SUM(IFNULL(a.`road_quantity`, 0)) allocationRoadQuantity,
            SUM(IFNULL(a.`road_quantity`, 0) * IFNULL(a.`cost`, 0)) allocationRoadGoodsValue
        from area_store t
        LEFT JOIN `inventory_allocation_road_cost_batch` a ON a.`warehouse_no` = t.`area_no` AND a.`sku` = t.`sku` AND a.`road_quantity` > 0
        <where>
            t.sku = #{sku,jdbcType=VARCHAR}
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                and t.area_no in
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="saleOut != null">
                <if test="saleOut == true">
                    and t.`online_quantity`  &lt;= 0
                </if>
                <if test="saleOut == false">
                    and t.`online_quantity`  > 0
                </if>
            </if>
            <if test="inOutRecord != null">
                and
                <if test="inOutRecord == true">
                    EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
                </if>
                <if test="inOutRecord == false">
                    NOT EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
                </if>
            </if>
        </where>
        group by t.area_no, t.sku
    </select>

    <select id="selectList" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT
            id,
            area_no              areaNo,
            sku,
            quantity,
            admin_id             adminId,
            lead_time            leadTime,
            sync,
            online_quantity      onlineQuantity,
            road_quantity        roadQuantity,
            lock_quantity        lockQuantity,
            safe_quantity        safeQuantity,
            advance_quantity     advanceQuantity,
            `change`,
            reserve_max_quantity reserveMaxQuantity,
            reserve_min_quantity reserveMinQuantity,
            reserve_use_quantity reserveUseQuantity,
            support_reserved     supportReserved,
            warning_quantity warningQuantity,
            sale_lock_quantity saleLockQuantity,
            cost_price costPrice,
            market_price marketPrice,
            tenant_id tenantId,
            warehouse_tenant_id warehouseTenantId
        FROM
            `area_store`
        <where>
            <if test="skuList != null and skuList.size() > 0">
                and sku in
                <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                and area_no in
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="saleOut != null">
                <if test="saleOut == true">
                    and `online_quantity`  &lt;= 0
                </if>
                <if test="saleOut == false">
                    and `online_quantity`  > 0
                </if>
            </if>
            <if test="quantityGtZero != null">
                <if test="quantityGtZero == true">
                    and `quantity`  > 0
                </if>
                <if test="quantityGtZero == false">
                    and `quantity`  &lt;= 0
                </if>
            </if>
        </where>
    </select>

    <select id="selectListForceMaster" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        /*FORCE_MASTER*/ SELECT
        id,
        area_no              areaNo,
        sku,
        quantity,
        admin_id             adminId,
        lead_time            leadTime,
        sync,
        online_quantity      onlineQuantity,
        road_quantity        roadQuantity,
        lock_quantity        lockQuantity,
        safe_quantity        safeQuantity,
        advance_quantity     advanceQuantity,
        `change`,
        reserve_max_quantity reserveMaxQuantity,
        reserve_min_quantity reserveMinQuantity,
        reserve_use_quantity reserveUseQuantity,
        support_reserved     supportReserved,
        warning_quantity warningQuantity,
        sale_lock_quantity saleLockQuantity,
        cost_price costPrice,
        market_price marketPrice,
        tenant_id tenantId,
        warehouse_tenant_id warehouseTenantId
        FROM
        `area_store`
        <where>
            <if test="skuList != null and skuList.size() > 0">
                and sku in
                <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                and area_no in
                <foreach collection="warehouseNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="saleOut != null">
                <if test="saleOut == true">
                    and `online_quantity`  &lt;= 0
                </if>
                <if test="saleOut == false">
                    and `online_quantity`  > 0
                </if>
            </if>
            <if test="quantityGtZero != null">
                <if test="quantityGtZero == true">
                    and `quantity`  > 0
                </if>
                <if test="quantityGtZero == false">
                    and `quantity`  &lt;= 0
                </if>
            </if>
        </where>
    </select>


    <select id="selectExistSkuBySkuList" resultType="java.lang.String" >
        select
            distinct sku
        FROM area_store
        where (quantity > 0 or road_quantity > 0 or online_quantity > 0)
        and sku IN
        <foreach collection="skuList" item="skuCode1" open="(" separator="," close=")">
            #{skuCode1}
        </foreach>
        <if test="warehouseNoList != null and warehouseNoList.size() > 0">
            and area_no in
            <foreach collection="warehouseNoList" item="warehouseNo1" open="(" close=")" separator=",">
                #{warehouseNo1}
            </foreach>
        </if>
    </select>

    <select id="selectExistWarehouseBySkuList" resultType="java.lang.Integer" >
        select
        distinct area_no
        FROM area_store
        where (quantity > 0 or road_quantity > 0 or online_quantity > 0)
        and sku IN
        <foreach collection="skuList" item="skuCode1" open="(" separator="," close=")">
            #{skuCode1}
        </foreach>
        <if test="warehouseNoList != null and warehouseNoList.size() > 0">
            and area_no in
            <foreach collection="warehouseNoList" item="warehouseNo1" open="(" close=")" separator=",">
                #{warehouseNo1}
            </foreach>
        </if>
    </select>

    <select id="pagingAbleAreaStoreByWarehouseNo" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT
        id,
        area_no              areaNo,
        sku,
        quantity,
        admin_id             adminId,
        lead_time            leadTime,
        sync,
        online_quantity      onlineQuantity,
        road_quantity        roadQuantity,
        lock_quantity        lockQuantity,
        safe_quantity        safeQuantity,
        `change`,
        advance_quantity     advanceQuantity,
        reserve_max_quantity reserveMaxQuantity,
        reserve_min_quantity reserveMinQuantity,
        reserve_use_quantity reserveUseQuantity,
        support_reserved     supportReserved
        , tenant_id tenantId, warehouse_tenant_id warehouseTenantId
        FROM
        `area_store`
        where area_no = #{warehouseNo}
        and quantity > 0
    </select>

    <select id="queryInventoryBySkuCodeList" resultType="net.summerfarm.wms.domain.areaStore.domainobject.InventoryBySkuCode" >
        select
            sku as skuCode,
            sum(quantity) quantity,
            sum(online_quantity) onlineQuantity,
            sum(road_quantity) roadQuantity
        FROM area_store
        where 1 = 1
        <if test="skuList == null">
            and 1 = 2
        </if>
        <if test="skuList != null and skuList.size() > 0">
            and sku IN
            <foreach collection="skuList" item="skuCode1" open="(" separator="," close=")">
                #{skuCode1}
            </foreach>
        </if>
        group by sku;
    </select>

    <select id="selectListBySkuAndWareNoList"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,
        t.safe_quantity safeQuantity,t.change,t.status,t.cost_price costPrice,t.market_price marketPrice,
        t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,t.support_reserved supportReserved,t.advance_quantity advanceQuantity,
        t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        <where>
            <if test="sku != null">
                and t.sku = #{sku,jdbcType=VARCHAR}
            </if>
            <if test="warehouseNoList != null and warehouseNoList.size() > 0">
                and t.area_no in
                <foreach collection="warehouseNoList" item="wareNo" open="(" close=")" separator=",">
                    #{wareNo}
                </foreach>
            </if>
        </where>

    </select>

    <select id="listAreaStoreByWarehouseNo"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT  t.area_no as areaNo, t.sku, t.quantity, t.sale_lock_quantity saleLockQuantity
        FROM `area_store` as t
        left join inventory as inv on t.sku = inv.sku
        <where>
            t.area_no = #{warehouseNo,jdbcType=INTEGER}
            and t.sale_lock_quantity > 0
            and inv.sub_type != 1
            and inv.sub_type != 5
            <if test="sync != null">
               and t.sync = #{sync,jdbcType=INTEGER}
            </if>
        </where>
        limit #{pageStart,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <select id="countAreaStoreByWarehouseNo"
            resultType="java.lang.Long">
        SELECT  count(*)
        FROM `area_store` as t
        left join inventory as inv on t.sku = inv.sku
        <where>
            t.area_no = #{warehouseNo,jdbcType=INTEGER}
            and t.sale_lock_quantity > 0
            and inv.sub_type != 1
            and inv.sub_type != 5
            <if test="sync != null">
                and t.sync = #{sync,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="listAreaStoreByWarehouseNoAndSkuList"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT
        id,
        area_no              areaNo,
        sku,
        quantity,
        admin_id             adminId,
        lead_time            leadTime,
        sync,
        online_quantity      onlineQuantity,
        road_quantity        roadQuantity,
        lock_quantity        lockQuantity,
        safe_quantity        safeQuantity,
        advance_quantity     advanceQuantity,
        `change`,
        reserve_max_quantity reserveMaxQuantity,
        reserve_min_quantity reserveMinQuantity,
        reserve_use_quantity reserveUseQuantity,
        sale_lock_quantity saleLockQuantity,
        support_reserved     supportReserved,
        tenant_id tenantId, warehouse_tenant_id warehouseTenantId,
        sale_lock_quantity saleLockQuantity
        FROM
        `area_store`
        where sku IN
            <foreach collection="skuList" item="skuCode" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
        and area_no in
            <foreach collection="warehouseNoList" item="warehouseNo" open="(" close=")" separator=",">
                #{warehouseNo}
            </foreach>
    </select>

    <insert id="insertBatch" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        insert into area_store (area_no,sku,update_time,price_status,sync)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.areaNo},#{item.sku},now(),0,#{item.sync})
        </foreach>
    </insert>

    <select id="queryAreaStore" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        select * from (
        SELECT t.id,
        t.area_no areaNo,
        t.sku sku,
        t.quantity,
        t.admin_id adminId,
        t.lead_time leadTime,
        t.sync,
        t.online_quantity onlineQuantity,
        t.lock_quantity lockQuantity,
        t.change,
        auto_transfer autoTransfer,
        t.safe_quantity safeQuantity,
        t.reserve_max_quantity reserveMaxQuantity,
        t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,
        t.support_reserved supportReserved,
        t.warning_quantity warningQuantity,
        t.send_warning_flag sendWarningFlag,
        t.road_quantity roadQuantity
        FROM area_store t
        WHERE
        t.sku in
        <foreach collection="skuList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and t.area_no in
        <foreach collection="warehouseList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="saleOut != null">
            and
            <if test="saleOut == true">
                t.online_quantity &lt;= 0
            </if>
            <if test="saleOut == false">
                t.online_quantity > 0
            </if>
        </if>
        <if test="sync != null">
            and
            <if test="sync == true">
                t.sync = 1
            </if>
            <if test="sync == false">
                t.sync = 0
            </if>
        </if>
        <if test="inOutRecord != null">
            and
            <if test="inOutRecord == true">
                EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
            </if>
            <if test="inOutRecord == false">
                NOT EXISTS ( select id from `warehouse_produce_batch` where sku = t.sku and warehouse_no = t.area_no)
            </if>
        </if>
        ) c
        order by c.onlineQuantity desc
    </select>

    <update id="updateSyncStatus">
        update area_store ar
        set ar.sync = #{sync}
        WHERE ar.sku = #{sku}
        and ar.area_no = #{warehouseNo}
    </update>

    <insert id="initAfterCreateXMWarehouse">
        insert into area_store(tenant_id, area_no, warehouse_tenant_id, sku,update_time)
        select i.tenant_id, #{warehouseNo}, #{warehouseTenantId}, sku,now() from inventory i
        where i.create_type &lt;= 2
    </insert>

    <select id="listAreaStoreRelation" parameterType="net.summerfarm.wms.domain.areaStore.domainobject.QueryAreaStoreRelation"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreRelationDO">
        select
            ast.area_no areaNo,
            wsc.warehouse_name warehouseName,
            ast.sku,
            i.weight,
            p.pd_name pdName,
            i.volume,
            i.weight_num weightNum,
            p.storage_location storageLocation,
            i.unit,
            c.type firstCategoryType
        from area_store ast
        left join warehouse_storage_center wsc on wsc.warehouse_no = ast.area_no
        left join inventory i on ast.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        <where>
            <if test="batchSkuList != null and batchSkuList.size!=0">
                ast.sku in
                <foreach collection="batchSkuList" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
            <if test="warehouseNo != null">
                AND ast.area_no = #{warehouseNo}
            </if>
            <if test="sku != null">
                AND ast.sku = #{sku}
            </if>
            <if test="pdName != null">
                AND p.pd_name like concat('%',#{pdName},'%')
            </if>
            <if test="pdId != null">
                AND p.pd_id = #{pdId,jdbcType=BIGINT}
            </if>
            <if test="volumeIsNull == true">
                AND i.volume is null
            </if>
            <if test="volumeIsNull == false">
                AND i.volume is  not null
            </if>
            <if test="weightNumIsNull == true">
                AND i.weight_num is null
            </if>
            <if test="weightNumIsNull  == false">
                AND i.weight_num is  not null
            </if>
        </where>
    </select>

    <select id="listAreaStoreRelationNeedPermission" parameterType="net.summerfarm.wms.domain.areaStore.domainobject.QueryAreaStoreRelation"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreRelationDO">
        select
        ast.area_no areaNo,
        wsc.warehouse_name warehouseName,
        ast.sku,
        i.weight,
        p.pd_name pdName,
        i.volume,
        i.weight_num weightNum,
        p.storage_location storageLocation,
        i.unit,
        c.type firstCategoryType,
        i.type skuType,
        i.is_domestic        isDomestic
        from area_store ast
        left join warehouse_storage_center wsc on wsc.warehouse_no = ast.area_no
        left join inventory i on ast.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        <where>
            <if test="batchSkuList != null and batchSkuList.size!=0">
                ast.sku in
                <foreach collection="batchSkuList" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
            <if test="warehouseNo != null">
                AND ast.area_no = #{warehouseNo}
            </if>
            <if test="warehouseNo != null and needBatchRecord != null and needBatchRecord == true">
                and ast.area_no = #{warehouseNo}
                AND exists (select 1 from warehouse_produce_batch wpb where wpb.warehouse_no = #{warehouseNo} and wpb.sku = ast.sku)
            </if>
            <if test="sku != null">
                AND ast.sku = #{sku}
            </if>
            <if test="pdName != null">
                AND p.pd_name like concat('%',#{pdName},'%')
            </if>
            <if test="pdId != null">
                AND p.pd_id = #{pdId,jdbcType=BIGINT}
            </if>
            <if test="volumeIsNull == true">
                AND i.volume is null
            </if>
            <if test="volumeIsNull == false">
                AND i.volume is  not null
            </if>
            <if test="weightNumIsNull == true">
                AND i.weight_num is null
            </if>
            <if test="weightNumIsNull  == false">
                AND i.weight_num is  not null
            </if>
        </where>
    </select>

    <select id="selectStockListBySku" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO"
            parameterType="java.lang.String">
        select id, area_no areaNo, sku, quantity from area_store where sku = #{sku}
    </select>



    <select id="queryStoreInvAndOnlineImbalanceDataForSupplier"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreInvAndOnlineImbalanceDO"
            parameterType="java.lang.Integer">
        select
            store.`area_no` as warehouseNo,
            store.`sku`,
            sku.`sub_type` as skuSubType,
            ifnull(spstore.quantity,0) as spStoreQuantity,
            store.quantity as storeQuantity,
            store.`change`,
            ifnull(instore.in_quantity,0) as moreWaitInQuantity,
            store.lock_quantity as lockQuantity,
            store.online_quantity as onlineQuantity,
            store.safe_quantity as safeQuantity,
            store.road_quantity as roadQuantity,
            ifnull(inventory_road_sale.remaining_road_sale_quantity,0) remainingRoadSaleQuantity,
            ifnull(inventory_sku_share_transfer.remaining_transfer_in_quantity, 0) remainingTransferInQuantity,
            ifnull(inventory_sku_share_transfer.remaining_transfer_out_quantity, 0) remainingTransferOutQuantity
        from area_store store
        inner join inventory sku on sku.`sku`  = `store`.`sku`  and sku.`sub_type` in (1, 5)
        inner join (
            select sku.sku, psl.`warehouse_no`, spu.`pd_no`, psl.supplier_id
            from `pms_supply_list` psl
            inner join `products` spu on `psl`.`spu`  = `spu`.`pd_no`
            inner join `inventory` sku on sku.pd_id = spu.`pd_id` and sku.outdated = 0
            where psl.`default_supplier`  = 1
            and psl.`warehouse_no` = #{warehouseNo}
        ) tmp on tmp.sku = store.`sku` and tmp.warehouse_no = store.area_no
        inner join `inventory_supplier_warehouse_store` spstore
            on spstore.warehouse_no = `store`.`area_no`
            and spstore.sku_code = store.`sku`
            and spstore.supplier_id = tmp.supplier_id
        left join (
            select
                sti.sku sku,
                sum(sti.quantity - sti.actual_quantity) in_quantity
            from stock_task_storage sts
                     inner join stock_storage_item sti on sts.id = sti.stock_task_storage_id
            where sts.in_warehouse_no = #{warehouseNo}
              and (sts.type = 23 or sts.type = 25 or (sts.type = 11 and sts.purchase_mode = 3))
              and sts.state = 0
            group by sti.sku
            having sum(sti.quantity - sti.actual_quantity) > 0
        ) instore on instore.sku = store.sku
        LEFT JOIN inventory_road_sale inventory_road_sale
                  on inventory_road_sale.warehouse_no = store.area_no
                      and inventory_road_sale.sku = store.sku
        LEFT JOIN inventory_sku_share_transfer inventory_sku_share_transfer
                  on inventory_sku_share_transfer.warehouse_no = store.area_no
                      and inventory_sku_share_transfer.sku = store.sku
        where store.area_no = #{warehouseNo}
        and store.quantity + store.`change` + ifnull(spstore.quantity,0)  +  ifnull(instore.in_quantity,0)
                + ifnull(inventory_road_sale.remaining_road_sale_quantity,0)
                + ifnull(inventory_sku_share_transfer.remaining_transfer_in_quantity, 0)
            != store.lock_quantity + store.online_quantity + store.safe_quantity
                + ifnull(inventory_sku_share_transfer.remaining_transfer_out_quantity, 0)
        and store.area_no in (
            select warehouse_no from warehouse_storage_center
            where status = 1 and warehouse_name not like '%测试%'
        )
        and store.sync = 1
    </select>

    <select id="queryStoreInvAndOnlineImbalanceDataForNormal"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreInvAndOnlineImbalanceDO"
            parameterType="java.lang.Integer">
        select
            store.`area_no` as warehouseNo,
            store.`sku` ,
            sku.`sub_type` as skuSubType,
            0 as spStoreQuantity,
            store.quantity as storeQuantity,
            store.`change`,
            ifnull(tmp3.in_quantity,0) as moreWaitInQuantity,
            store.lock_quantity as lockQuantity,
            store.online_quantity as onlineQuantity,
            store.safe_quantity as safeQuantity,
            store.road_quantity as roadQuantity,
            ifnull(inventory_road_sale.remaining_road_sale_quantity,0) remainingRoadSaleQuantity,
            ifnull(inventory_sku_share_transfer.remaining_transfer_in_quantity, 0) remainingTransferInQuantity,
            ifnull(inventory_sku_share_transfer.remaining_transfer_out_quantity, 0) remainingTransferOutQuantity
        from area_store store
                 inner join inventory sku on sku.`sku`  = `store`.`sku`  and sku.`sub_type` not in (1, 5)
        left join (
            SELECT sts.in_warehouse_no as area_no, sti.sku, sum(sti.quantity-sti.actual_quantity) as in_quantity
            FROM `stock_task_storage` sts
            INNER JOIN `stock_storage_item` sti on sti.`stock_task_storage_id` = sts.`id`
            WHERE sts.`in_warehouse_no` = #{warehouseNo}
            and sts.`type` = 23
            and sts.state = 0
            group by sti.sku
            having sum(sti.quantity - sti.actual_quantity) > 0
        ) tmp3 on store.sku = tmp3.sku
         LEFT JOIN inventory_road_sale inventory_road_sale
                   on inventory_road_sale.warehouse_no = store.area_no
                       and inventory_road_sale.sku = store.sku
         LEFT JOIN inventory_sku_share_transfer inventory_sku_share_transfer
                   on inventory_sku_share_transfer.warehouse_no = store.area_no
                       and inventory_sku_share_transfer.sku = store.sku
        where store.area_no = #{warehouseNo}
        and store.quantity + store.`change` + ifnull(tmp3.in_quantity,0)
                + ifnull(inventory_road_sale.remaining_road_sale_quantity,0)
                + ifnull(inventory_sku_share_transfer.remaining_transfer_in_quantity, 0)
            != store.lock_quantity + store.online_quantity + store.safe_quantity
                + ifnull(inventory_sku_share_transfer.remaining_transfer_out_quantity, 0)
        and store.area_no in (
            select warehouse_no from warehouse_storage_center
            where status = 1 and warehouse_name not like '%测试%'
        )
        and store.sync = 1
    </select>

    <select id="queryStoreInvAndCabinetInvImbalanceData" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreInvImbalanceDO"
            parameterType="java.lang.Integer">
        select
            store.area_no as warehouseNo,
            store.sku,
            store.quantity as storeQuantity,
            wcbi.amount as cabinetQuantity
        from area_store store
                 left join (
            select
                wcbi.warehouse_no, wcbi.sku, sum(quantity) as amount
            from wms_cabinet_inventory wcbi
            where wcbi.warehouse_no = #{warehouseNo}
            group by wcbi.warehouse_no, wcbi.sku
        ) wcbi on wcbi.warehouse_no = store.area_no
            and wcbi.sku = store.sku
        where store.area_no = #{warehouseNo}
          and store.quantity &gt; 0
          and store.quantity != wcbi.amount
    </select>

    <select id="queryStoreInvAndBatchInvImbalanceData" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreInvImbalanceDO"
            parameterType="java.lang.Integer">
        select
            store.area_no as warehouseNo,
            store.sku,
            store.quantity as storeQuantity,
            tmp2.amount as batchQuantity
        from area_store store
                 left join (
            select
                wcb.`warehouse_no` area_no,
                wcb.`sku`,
                ifnull(sum(wcb.quantity), 0) as amount
            from warehouse_cost_batch wcb
            where wcb.warehouse_no = #{warehouseNo}
              and wcb.quantity > 0
            GROUP BY
                wcb.`warehouse_no`,
                wcb.`sku`
        ) tmp2 on tmp2.area_no = store.area_no and tmp2.sku = store.sku
        where store.area_no = #{warehouseNo}
          and store.quantity > 0
          and store.quantity != ifnull(tmp2.amount, 0)
    </select>

    <select id="querySafeQuantityGtZero" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,
        t.safe_quantity safeQuantity,t.change,t.status,t.cost_price costPrice,t.market_price marketPrice,
        t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,t.support_reserved supportReserved,t.advance_quantity advanceQuantity,
        t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        WHERE t.safe_quantity > 0
    </select>

    <select id="pagingAbleQueryRoadQuantityGtZero" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.AreaStoreDO">
        SELECT t.id, t.area_no areaNo, t.sku, t.quantity, t.admin_id adminId, t.lead_time leadTime,t.sync,
        t.online_quantity onlineQuantity,t.road_quantity roadQuantity,t.lock_quantity lockQuantity,
        t.safe_quantity safeQuantity,t.change,t.status,t.cost_price costPrice,t.market_price marketPrice,
        t.reserve_max_quantity reserveMaxQuantity,t.reserve_min_quantity reserveMinQuantity,
        t.reserve_use_quantity reserveUseQuantity,t.support_reserved supportReserved,t.advance_quantity advanceQuantity,
        t.sale_lock_quantity saleLockQuantity
        , t.tenant_id tenantId, t.warehouse_tenant_id warehouseTenantId
        FROM area_store t
        WHERE t.road_quantity > 0
        <if test="warehouseNo != null">
            AND t.area_no = #{warehouseNo}
        </if>
        <if test="sku != null">
            AND t.sku = #{sku}
        </if>
    </select>

</mapper>
