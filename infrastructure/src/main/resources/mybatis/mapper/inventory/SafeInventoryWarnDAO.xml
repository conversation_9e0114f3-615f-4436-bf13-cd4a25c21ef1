<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.SafeInventoryWarnDAO">
    <resultMap id="SafeInventoryWarnMap" type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryWarnDO">
        <id property="id" column="id"/>
        <result property="warehouseNo" column="warehouse_no"/>
        <result property="sku" column="sku"/>
        <result property="lockQuantity" column="lock_quantity"/>
        <result property="lockTime" column="lock_time"/>
        <result property="lockReason" column="lock_reason"/>
        <result property="warnStatus" column="warn_status"/>
        <result property="warnTime" column="warn_time"/>
        <result property="releaseQuantity" column="release_quantity"/>
        <result property="releaseReason" column="release_reason"/>
        <result property="createOperator" column="create_operator"/>
        <result property="createOperatorId" column="create_operator_id"/>
        <result property="updateOperator" column="update_operator"/>
        <result property="updateOperatorId" column="update_operator_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="needNotice" column="need_notice"/>
    </resultMap>

    <sql id="table_name">
        wms_safe_inventory_warn
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        `warehouse_no`, `sku`, `lock_quantity`, `lock_time`, `lock_reason`, `warn_status`, `warn_time`,
        `release_quantity`, `release_reason`, `create_operator`, `create_operator_id`, `update_operator`,
        `update_operator_id`, `create_time`, `update_time`, `need_notice`
    </sql>

    <sql id="values_exclude_id">
        #{warehouseNo}, #{sku}, #{lockQuantity}, #{lockTime}, #{lockReason}, #{warnStatus}, #{warnTime},
        #{releaseQuantity}, #{releaseReason}, #{createOperator}, #{createOperatorId}, #{updateOperator},
        #{updateOperatorId}, #{createTime}, #{updateTime}, #{needNotice}
    </sql>

    <sql id="query">
        <where>
            <if test="warehouseNo != null">AND `warehouse_no` = #{warehouseNo}</if>
            <if test="sku != null">AND `sku` = #{sku}</if>
            <if test="lockQuantity != null">AND `lock_quantity` = #{lockQuantity}</if>
            <if test="lockTime != null">AND `lock_time` = #{lockTime}</if>
            <if test="lockReason != null">AND `lock_reason` = #{lockReason}</if>
            <if test="warnStatus != null">AND `warn_status` = #{warnStatus}</if>
            <if test="warnTime != null">AND `warn_time` = #{warnTime}</if>
            <if test="releaseQuantity != null">AND `release_quantity` = #{releaseQuantity}</if>
            <if test="releaseReason != null">AND `release_reason` = #{releaseReason}</if>
            <if test="createOperator != null">AND `create_operator` = #{createOperator}</if>
            <if test="createOperatorId != null">AND `create_operator_id` = #{createOperatorId}</if>
            <if test="updateOperator != null">AND `update_operator` = #{updateOperator}</if>
            <if test="updateOperatorId != null">AND `update_operator_id` = #{updateOperatorId}</if>
            <if test="createTime != null">AND `create_time` = #{createTime}</if>
            <if test="updateTime != null">AND `update_time` = #{updateTime}</if>
            <if test="needNotice != null">AND `need_notice` = #{needNotice}</if>
        </where>
    </sql>

    <insert id="create" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryWarnDO" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">`warehouse_no`,</if>
            <if test="sku != null">`sku`,</if>
            <if test="lockQuantity != null">`lock_quantity`,</if>
            <if test="lockTime != null">`lock_time`,</if>
            <if test="lockReason != null">`lock_reason`,</if>
            <if test="warnStatus != null">`warn_status`,</if>
            <if test="warnTime != null">`warn_time`,</if>
            <if test="releaseQuantity != null">`release_quantity`,</if>
            <if test="releaseReason != null">`release_reason`,</if>
            <if test="createOperator != null">`create_operator`,</if>
            <if test="createOperatorId != null">`create_operator_id`,</if>
            <if test="updateOperator != null">`update_operator`,</if>
            <if test="updateOperatorId != null">`update_operator_id`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`,</if>
            <if test="needNotice != null">`need_notice`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">#{warehouseNo},</if>
            <if test="sku != null">#{sku},</if>
            <if test="lockQuantity != null">#{lockQuantity},</if>
            <if test="lockTime != null">#{lockTime},</if>
            <if test="lockReason != null">#{lockReason},</if>
            <if test="warnStatus != null">#{warnStatus},</if>
            <if test="warnTime != null">#{warnTime},</if>
            <if test="releaseQuantity != null">#{releaseQuantity},</if>
            <if test="releaseReason != null">#{releaseReason},</if>
            <if test="createOperator != null">#{createOperator},</if>
            <if test="createOperatorId != null">#{createOperatorId},</if>
            <if test="updateOperator != null">#{updateOperator},</if>
            <if test="updateOperatorId != null">#{updateOperatorId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="needNotice != null">#{needNotice},</if>
        </trim>
    </insert>

    <insert id="creates" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryWarnDO" useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.warehouseNo}, #{i.sku}, #{i.lockQuantity}, #{i.lockTime}, #{i.lockReason}, #{i.warnStatus},
            #{i.warnTime}, #{i.releaseQuantity}, #{i.releaseReason}, #{i.createOperator}, #{i.createOperatorId},
            #{i.updateOperator}, #{i.updateOperatorId}, #{i.createTime}, #{i.updateTime}, #{i.needNotice})
        </foreach>
    </insert>

    <update id="update" parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.SafeInventoryWarnDO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="warehouseNo != null">`warehouse_no` = #{warehouseNo},</if>
            <if test="sku != null">`sku` = #{sku},</if>
            <if test="lockQuantity != null">`lock_quantity` = #{lockQuantity},</if>
            <if test="lockTime != null">`lock_time` = #{lockTime},</if>
            <if test="lockReason != null">`lock_reason` = #{lockReason},</if>
            <if test="warnStatus != null">`warn_status` = #{warnStatus},</if>
            <if test="warnTime != null">`warn_time` = #{warnTime},</if>
            <if test="releaseQuantity != null">`release_quantity` = #{releaseQuantity},</if>
            <if test="releaseReason != null">`release_reason` = #{releaseReason},</if>
            <if test="createOperator != null">`create_operator` = #{createOperator},</if>
            <if test="createOperatorId != null">`create_operator_id` = #{createOperatorId},</if>
            <if test="updateOperator != null">`update_operator` = #{updateOperator},</if>
            <if test="updateOperatorId != null">`update_operator_id` = #{updateOperatorId},</if>
            <if test="createTime != null">`create_time` = #{createTime},</if>
            <if test="needNotice != null">`need_notice` = #{needNotice},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="SafeInventoryWarnMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="SafeInventoryWarnMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id1" collection="list" open="(" separator="," close=")">
            #{id1}
        </foreach>
    </select>

    <select id="count" parameterType="net.summerfarm.wms.domain.inventory.domainobject.query.SafeInventoryWarnQuery" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
    </select>

    <select id="findOne" parameterType="net.summerfarm.wms.domain.inventory.domainobject.query.SafeInventoryWarnQuery"
            resultMap="SafeInventoryWarnMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
        limit 1
    </select>

    <select id="list" parameterType="net.summerfarm.wms.domain.inventory.domainobject.query.SafeInventoryWarnQuery"
            resultMap="SafeInventoryWarnMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="query"/>
        <include refid="orderByQuery"/>
    </select>

    <sql id="orderByQuery">
        <if test="sorts != null and sorts.size() > 0">
            ORDER BY
            <foreach collection="sorts" item="i" index="index" separator=",">
                ${i.columnName} ${i.sortType}
            </foreach>
        </if>
    </sql>

    <select id="queryTaskPanelSkuCode" resultMap="SafeInventoryWarnMap">
        select distinct wsiw.sku AS sku, wsiw.lock_time
        from wms_safe_inventory_warn wsiw
        where wsiw.warehouse_no = #{warehouseNo,jdbcType=INTEGER}
        and wsiw.warn_status = #{status,jdbcType=INTEGER}
        limit 2000
    </select>

</mapper>
