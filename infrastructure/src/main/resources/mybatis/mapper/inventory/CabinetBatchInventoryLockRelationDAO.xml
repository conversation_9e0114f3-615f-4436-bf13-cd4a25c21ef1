<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.CabinetBatchInventoryLockRelationDAO">
    <resultMap id="BaseResultMap"
               type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CabinetBatchInventoryLockRelationDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="warehouse_no" jdbcType="BIGINT" property="warehouseNo"/>
        <result column="cabinet_no" jdbcType="VARCHAR" property="cabinetNo"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="quality_date" jdbcType="TIMESTAMP" property="qualityDate"/>
        <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate"/>
        <result column="lock_quantity" jdbcType="INTEGER" property="lockQuantity"/>
        <result column="batch" jdbcType="VARCHAR" property="batch"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="biz_type" jdbcType="INTEGER" property="bizType"/>
        <result column="biz_name" jdbcType="VARCHAR" property="bizName"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="deleted_at" jdbcType="BIGINT" property="deletedAt"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , create_time, update_time, warehouse_no, cabinet_no, sku, quality_date, produce_date,
    lock_quantity, batch, biz_id, biz_type, biz_name, deleted_at, operator
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_cabinet_batch_inventory_lock_relation
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByDupKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.UpdateBatchInventoryLockRelationDO"
            resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_cabinet_batch_inventory_lock_relation
        where warehouse_no = #{warehouseNo}
        and sku = #{sku}
        and produce_date = #{produceDate}
        and quality_date = #{qualityDate}
        <if test="cabinetNo != null">
            and cabinet_no = #{cabinetNo}
        </if>
        and batch = #{batch}
        and deleted_at = 0
        and biz_id = #{bizId}
        and biz_name = #{bizName}
    </select>

    <select id="listByWarehouseNoAndSku" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_cabinet_batch_inventory_lock_relation
        where warehouse_no = #{warehouseNo}
        <if test="skus != null and skus.size > 0">
            and sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        and deleted_at = 0
    </select>

    <select id="listBatch" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_cabinet_batch_inventory_lock_relation
        where warehouse_no = #{warehouseNo}
        <if test="skus != null and skus.size > 0">
            and sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        and deleted_at = 0
        <if test="bizId != null">
            and biz_id = #{bizId}
        </if>
        <if test="bizName != null">
            and biz_name = #{bizName}
        </if>
    </select>

    <select id="listBatchByExcludeBiz" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_cabinet_batch_inventory_lock_relation
        where warehouse_no = #{warehouseNo}
        <if test="skus != null and skus.size > 0">
            and sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        and deleted_at = 0
        <if test="excludeBizId != null">
            and biz_id != #{excludeBizId}
        </if>
        <if test="excludeBizName != null">
            and biz_name != #{excludeBizId}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from wms_cabinet_batch_inventory_lock_relation
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="deleteById" parameterType="java.lang.Long">
        update wms_cabinet_batch_inventory_lock_relation
        set deleted_at = #{deletedAt}
        where id = #{id,jdbcType=BIGINT}
          and deleted_at = 0
    </update>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CabinetBatchInventoryLockRelationDO"
            useGeneratedKeys="true">
        insert into wms_cabinet_batch_inventory_lock_relation (warehouse_no,
                                                               cabinet_no, sku, quality_date,
                                                               produce_date, lock_quantity, batch,
                                                               biz_id, biz_type, biz_name,
                                                               deleted_at, operator)
        values (#{warehouseNo,jdbcType=BIGINT},
                #{cabinetNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{qualityDate,jdbcType=TIMESTAMP},
                #{produceDate,jdbcType=TIMESTAMP}, #{lockQuantity,jdbcType=INTEGER}, #{batch,jdbcType=VARCHAR},
                #{bizId,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER}, #{bizName,jdbcType=VARCHAR},
                #{deletedAt, jdbcType = BIGINT}, #{operator})
    </insert>
    <insert id="insertSelect" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.UpdateBatchInventoryLockRelationDO"
            useGeneratedKeys="true">
        insert into wms_cabinet_batch_inventory_lock_relation (warehouse_no,
        cabinet_no, sku, quality_date,
        produce_date, lock_quantity, batch,
        biz_id, biz_type, biz_name,
        deleted_at, operator)
        select warehouse_no,
        cabinet_no,
        sku,
        quality_date,
        produce_date,
        lock_quantity + #{changeQuantity},
        batch,
        biz_id,
        #{bizType},
        biz_name,
        0,
        #{operator}
        from wms_cabinet_batch_inventory_lock_relation
        where warehouse_no = #{warehouseNo}
        and sku = #{sku}
        and produce_date = #{produceDate}
        and quality_date = #{qualityDate}
        <if test="cabinetNo != null">
            and cabinet_no = #{cabinetNo}
        </if>
        and deleted_at = 0
        and biz_id = #{bizId}
        and biz_name = #{bizName}
    </insert>
    <insert id="insertSelectForLog" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.query.UpdateBatchInventoryLockRelationDO"
            useGeneratedKeys="true">
        insert into wms_cabinet_batch_inventory_lock_relation (warehouse_no,
        cabinet_no, sku, quality_date,
        produce_date, lock_quantity, batch,
        biz_id, biz_type, biz_name,
        deleted_at, operator)
        select warehouse_no,
        cabinet_no,
        sku,
        quality_date,
        produce_date,
        lock_quantity + #{changeQuantity},
        batch,
        biz_id,
        #{bizType},
        biz_name,
        UNIX_TIMESTAMP() * 1000,
        #{operator}
        from wms_cabinet_batch_inventory_lock_relation
        where warehouse_no = #{warehouseNo}
        and sku = #{sku}
        and produce_date = #{produceDate}
        and quality_date = #{qualityDate}
        <if test="cabinetNo != null">
            and cabinet_no = #{cabinetNo}
        </if>
        and deleted_at = 0
        and biz_id = #{bizId}
        and biz_name = #{bizName}
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CabinetBatchInventoryLockRelationDO"
            useGeneratedKeys="true">
        insert into wms_cabinet_batch_inventory_lock_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="cabinetNo != null">
                cabinet_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="produceDate != null">
                produce_date,
            </if>
            <if test="lockQuantity != null">
                lock_quantity,
            </if>
            <if test="batch != null">
                batch,
            </if>
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="bizName != null">
                biz_name,
            </if>
            <if test="deletedAt != null">
                deleted_at,
            </if>
            <if test="operator != null">
                operator,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="cabinetNo != null">
                #{cabinetNo,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="qualityDate != null">
                #{qualityDate,jdbcType=TIMESTAMP},
            </if>
            <if test="produceDate != null">
                #{produceDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lockQuantity != null">
                #{lockQuantity,jdbcType=INTEGER},
            </if>
            <if test="batch != null">
                #{batch,jdbcType=VARCHAR},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null">
                #{bizType,jdbcType=INTEGER},
            </if>
            <if test="bizName != null">
                #{bizName,jdbcType=VARCHAR},
            </if>
            <if test="deletedAt != null">
                #{deletedAt,jdbcType=BIGINT},
            </if>
            <if test="operator != null">
                #{operator},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CabinetBatchInventoryLockRelationDO">
        update wms_cabinet_batch_inventory_lock_relation
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=BIGINT},
            </if>
            <if test="cabinetNo != null">
                cabinet_no = #{cabinetNo,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="qualityDate != null">
                quality_date = #{qualityDate,jdbcType=TIMESTAMP},
            </if>
            <if test="produceDate != null">
                produce_date = #{produceDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lockQuantity != null">
                lock_quantity = #{lockQuantity,jdbcType=INTEGER},
            </if>
            <if test="batch != null">
                batch = #{batch,jdbcType=VARCHAR},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType,jdbcType=INTEGER},
            </if>
            <if test="bizName != null">
                biz_name = #{bizName,jdbcType=VARCHAR},
            </if>
            <if test="deletedAt != null">
                deleted_at = #{deletedAt,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CabinetBatchInventoryLockRelationDO">
        update wms_cabinet_batch_inventory_lock_relation
        set create_time   = #{createTime,jdbcType=TIMESTAMP},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            warehouse_no  = #{warehouseNo,jdbcType=BIGINT},
            cabinet_no    = #{cabinetNo,jdbcType=VARCHAR},
            sku           = #{sku,jdbcType=VARCHAR},
            quality_date  = #{qualityDate,jdbcType=TIMESTAMP},
            produce_date  = #{produceDate,jdbcType=TIMESTAMP},
            lock_quantity = #{lockQuantity,jdbcType=INTEGER},
            batch         = #{batch,jdbcType=VARCHAR},
            biz_id        = #{bizId,jdbcType=VARCHAR},
            biz_type      = #{bizType,jdbcType=INTEGER},
            biz_name      = #{bizName,jdbcType=VARCHAR},
            deleted_at    = #{deletedAt,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="releaseLockByBiz">
        update wms_cabinet_batch_inventory_lock_relation
        set deleted_at = UNIX_TIMESTAMP() * 1000
        where biz_id = #{bizId,jdbcType=VARCHAR}
          and biz_name = #{bizName,jdbcType=VARCHAR}
    </update>

    <select id="listByWnoAndSkuAndBatch" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List"/>
        from wms_cabinet_batch_inventory_lock_relation
        where warehouse_no = #{warehouseNo}
        and sku = #{sku}
        and produce_date = #{produceDate}
        and quality_date = #{qualityDate}
        and batch = #{batch}
        and deleted_at = 0
        and biz_name = #{bizName}
    </select>
</mapper>