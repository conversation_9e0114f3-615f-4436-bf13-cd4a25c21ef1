<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.InventoryTransactionRequestDAO">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.InventoryTransactionRequestDO" id="InventoryTransactionRequestMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="warehouseTenantId" column="warehouse_tenant_id" jdbcType="INTEGER"/>
        <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
        <result property="operationQuantity" column="operation_quantity" jdbcType="INTEGER"/>
        <result property="operationType" column="operation_type" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderTypeName" column="order_type_name" jdbcType="VARCHAR"/>
        <result property="operationNo" column="operation_no" jdbcType="VARCHAR"/>
        <result property="idempotentNo" column="idempotent_no" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="InventoryTransactionRequestMap">
        select
          id, tenant_id, warehouse_no, warehouse_tenant_id, sku_code, operation_quantity, operation_type, order_no, order_type_name, operation_no, idempotent_no, creator, create_time, updater, update_time, delete_flag
        from inventory_transaction_request
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="InventoryTransactionRequestMap">
        select
          id, tenant_id, warehouse_no, warehouse_tenant_id, sku_code, operation_quantity, operation_type, order_no, order_type_name, operation_no, idempotent_no, creator, create_time, updater, update_time, delete_flag
        from inventory_transaction_request
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="warehouseTenantId != null">
                and warehouse_tenant_id = #{warehouseTenantId}
            </if>
            <if test="skuCode != null and skuCode != ''">
                and sku_code = #{skuCode}
            </if>
            <if test="operationQuantity != null">
                and operation_quantity = #{operationQuantity}
            </if>
            <if test="operationType != null">
                and operation_type = #{operationType}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="orderTypeName != null and orderTypeName != ''">
                and order_type_name = #{orderTypeName}
            </if>
            <if test="operationNo != null and operationNo != ''">
                and operation_no = #{operationNo}
            </if>
            <if test="idempotentNo != null and idempotentNo != ''">
                and idempotent_no = #{idempotentNo}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from inventory_transaction_request
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="warehouseTenantId != null">
                and warehouse_tenant_id = #{warehouseTenantId}
            </if>
            <if test="skuCode != null and skuCode != ''">
                and sku_code = #{skuCode}
            </if>
            <if test="operationQuantity != null">
                and operation_quantity = #{operationQuantity}
            </if>
            <if test="operationType != null">
                and operation_type = #{operationType}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="orderTypeName != null and orderTypeName != ''">
                and order_type_name = #{orderTypeName}
            </if>
            <if test="operationNo != null and operationNo != ''">
                and operation_no = #{operationNo}
            </if>
            <if test="idempotentNo != null and idempotentNo != ''">
                and idempotent_no = #{idempotentNo}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_transaction_request(tenant_id, warehouse_no, warehouse_tenant_id, sku_code, operation_quantity, operation_type, order_no, order_type_name, operation_no, idempotent_no, creator, create_time, updater, update_time, delete_flag)
        values (#{tenantId}, #{warehouseNo}, #{warehouseTenantId}, #{skuCode}, #{operationQuantity}, #{operationType}, #{orderNo}, #{orderTypeName}, #{operationNo}, #{idempotentNo}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_transaction_request(tenant_id, warehouse_no, warehouse_tenant_id, sku_code, operation_quantity, operation_type, order_no, order_type_name, operation_no, idempotent_no, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.tenantId}, #{entity.warehouseNo}, #{entity.warehouseTenantId}, #{entity.skuCode}, #{entity.operationQuantity}, #{entity.operationType}, #{entity.orderNo}, #{entity.orderTypeName}, #{entity.operationNo}, #{entity.idempotentNo}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_transaction_request(tenant_id, warehouse_no, warehouse_tenant_id, sku_code, operation_quantity, operation_type, order_no, order_type_name, operation_no, idempotent_no, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.tenantId}, #{entity.warehouseNo}, #{entity.warehouseTenantId}, #{entity.skuCode}, #{entity.operationQuantity}, #{entity.operationType}, #{entity.orderNo}, #{entity.orderTypeName}, #{entity.operationNo}, #{entity.idempotentNo}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
        tenant_id = values(tenant_id),
        warehouse_no = values(warehouse_no),
        warehouse_tenant_id = values(warehouse_tenant_id),
        sku_code = values(sku_code),
        operation_quantity = values(operation_quantity),
        operation_type = values(operation_type),
        order_no = values(order_no),
        order_type_name = values(order_type_name),
        operation_no = values(operation_no),
        idempotent_no = values(idempotent_no),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update inventory_transaction_request
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="warehouseTenantId != null">
                warehouse_tenant_id = #{warehouseTenantId},
            </if>
            <if test="skuCode != null and skuCode != ''">
                sku_code = #{skuCode},
            </if>
            <if test="operationQuantity != null">
                operation_quantity = #{operationQuantity},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType},
            </if>
            <if test="orderNo != null and orderNo != ''">
                order_no = #{orderNo},
            </if>
            <if test="orderTypeName != null and orderTypeName != ''">
                order_type_name = #{orderTypeName},
            </if>
            <if test="operationNo != null and operationNo != ''">
                operation_no = #{operationNo},
            </if>
            <if test="idempotentNo != null and idempotentNo != ''">
                idempotent_no = #{idempotentNo},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from inventory_transaction_request where id = #{id}
    </delete>

</mapper>

