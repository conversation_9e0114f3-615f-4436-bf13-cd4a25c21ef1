<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.QuantityChangeRecordDAO">

    <insert id="insertBySelect"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.QuantityChangeRecordDO">
        INSERT INTO quantity_change_record(area_no, sku, recorder, new_quantity, old_quantity, new_online_quantity,
                                           old_online_quantity,
                                           new_lock_quantity, old_lock_quantity, new_sale_lock_quantity,
                                           old_sale_lock_quantity, new_road_quantity, old_road_quantity,
                                           new_safe_quantity, old_safe_quantity, new_change, old_change, type_name,
                                           record_no, addtime, new_reserve_use_quantity,
                                           old_reserve_use_quantity, remark, new_advance_quantity, old_advance_quantity)
        SELECT area_no,
               sku,
               #{recorder},
               quantity,
               ifnull(#{oldQuantity}, quantity),
               online_quantity,
               ifnull(#{oldOnlineQuantity}, online_quantity),
               ifnull(#{newLockQuantity}, lock_quantity),
               ifnull(#{oldLockQuantity}, lock_quantity),
               sale_lock_quantity,
               ifnull(#{oldSaleLockQuantity}, sale_lock_quantity),
               road_quantity,
               ifnull(#{oldRoadQuantity}, road_quantity),
               safe_quantity,
               ifnull(#{oldSafeQuantity}, safe_quantity),
               `change`,
               ifnull(#{oldChange}, `change`),
               #{typeName},
               #{recordNo},
               now(),
               if(reserve_min_quantity + reserve_use_quantity > reserve_max_quantity,
                  reserve_max_quantity - reserve_min_quantity, reserve_use_quantity) reserve_use_quantity
                ,
               ifnull(#{oldReserveUseQuantity}, reserve_use_quantity),
               #{remark},
               advance_quantity,
               ifnull(#{oldAdvanceQuantity}, advance_quantity)
        FROM area_store
        WHERE sku = #{sku}
          AND area_no = #{areaNo}
    </insert>
</mapper>