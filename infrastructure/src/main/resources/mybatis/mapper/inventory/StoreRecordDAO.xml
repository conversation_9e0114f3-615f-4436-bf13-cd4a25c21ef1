<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.StoreRecordDAO">
    <sql id="Base_Column_List">
        id
        , batch, sku, type, quantity, unit, recorder, remark, update_time,quality_date,area_no,lot_type
            , tenant_id
    </sql>

    <select id="selectOne" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select
        sr.id, sr.batch, sr.sku, sr.type, sr.quantity, sr.unit, sr.recorder, sr.remark, sr.update_time updateTime,
        sr.quality_date qualityDate,sr.area_no areaNo,sr.store_quantity storeQuantity,sr.cost,sr.production_date
        productionDate
        , sr.tenant_id tenantId
        from store_record sr
        where sr.sku=#{sku} AND sr.area_no = #{warehouseNo}
        <if test="batch != null">
            and sr.batch= #{batch}
        </if>
        <choose>
            <when test="shelfLife == null">
                AND sr.quality_date is null
            </when>
            <otherwise>
                AND sr.quality_date = #{shelfLife}
            </otherwise>
        </choose>
        order by sr.id desc
        limit 1;
    </select>

    <select id="selectLastOne" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        /*FORCE_MASTER*/ select
        sr.id, sr.batch, sr.sku, sr.type, sr.quantity, sr.unit, sr.recorder, sr.remark, sr.update_time updateTime,
        sr.quality_date qualityDate,sr.area_no areaNo,sr.store_quantity storeQuantity,sr.cost,sr.production_date
        productionDate
        , sr.tenant_id tenantId
        from store_record sr
        where sr.sku=#{sku} AND sr.area_no = #{warehouseNo}
        <if test="batch != null">
            and sr.batch= #{batch}
        </if>
        <choose>
            <when test="shelfLife == null">
                AND sr.quality_date is null
            </when>
            <otherwise>
                AND sr.quality_date = #{shelfLife}
            </otherwise>
        </choose>
        <if test="productionDate != null">
            and sr.production_date = #{productionDate}
        </if>
        order by sr.id desc
        limit 1;
    </select>

    <select id="listPurchaseNo" resultType="java.lang.String">
        select batch
        from store_record
        where area_no = #{warehouseNo}
          and sku = #{sku}
          and quality_date = #{shelfLife}
          and production_date = #{produceAt}
          and type = #{type}
        group by batch
    </select>

    <select id="listStoreRecord" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select max(batch) batch, area_no areaNo,sku sku,quality_date qualityDate,production_date productionDate
        from store_record
        where area_no = #{warehouseNo}
        and sku = #{sku}
        and type = #{type}
        and quality_date in
        <foreach collection="shelfLifeList" item="shelfLife" open="(" close=")" separator=",">
            #{shelfLife}
        </foreach>
        and production_date in
        <foreach collection="produceAtList" item="produceAt" open="(" close=")" separator=",">
            #{produceAt}
        </foreach>
        group by area_no,sku,quality_date,production_date
    </select>

    <select id="selectRecordNear" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select
        max(id),
        area_no,
        sku,
        batch,
        quantity,
        production_date productionDate,
        quality_date qualityDate,
        store_quantity storeQuantity
        from xianmudb.store_record
        where sku in
        <foreach collection="skus" item="sku" open="(" close=")" separator=",">
            #{sku}
        </foreach>
        and area_no = #{warehouseNo}
        group by sku
    </select>

    <select id="listBatchBySkuAndWarehouseNoAndInventory"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        SELECT t.id ,
        t.batch ,
        t.sku ,
        t.type ,
        t.quantity ,
        t.areaNo ,
        t.storeQuantity ,
        t.qualityDate ,
        t.productionDate from (select
        s.id, s.batch, s.sku, s.type, s.quantity,s.area_no areaNo,s.store_quantity storeQuantity,s.quality_date
        qualityDate,s.production_date productionDate
        , s.tenant_id tenantId
        from
        (select *
        from store_record sr
        <where>
            <if test="warehouseNo != null">
                sr.area_no= #{warehouseNo}
            </if>
            <if test="skus != null">
                AND sr.sku in
                <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
            <if test="productionDateMin != null">
                AND sr.production_date >= #{productionDateMin}
            </if>
        </where>
        order by sr.id desc) s
        group by s.area_no, s.sku, s.batch, s.quality_date, s.production_date) t
        <where>
            <if test="storeQuantityMin != null">
                t.storeQuantity >= #{storeQuantityMin}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectOnePurchaseNo" resultType="java.lang.String">
        select batch
        from store_record
        where area_no = #{warehouseNo}
        and sku = #{sku}
        <if test="shelfLife != null">
            and quality_date = #{shelfLife}
        </if>
        <if test="produceAt != null">
            and production_date = #{produceAt}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
        order by id desc limit 1
    </select>


    <insert id="insert" keyProperty="id" useGeneratedKeys="true" keyColumn="id"
            parameterType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        insert into store_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batch != null">
                batch,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="recorder != null">
                recorder,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="storeQuantity != null">
                store_quantity,
            </if>
            <if test="cost != null">
                cost,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="insertType != null">
                insert_type,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batch != null">
                #{batch},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="unit != null">
                #{unit},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="recorder != null">
                #{recorder},
            </if>
            <if test="qualityDate != null">
                #{qualityDate},
            </if>
            <if test="areaNo != null">
                #{areaNo},
            </if>
            <if test="storeQuantity != null">
                #{storeQuantity},
            </if>
            <if test="cost != null">
                #{cost},
            </if>
            <if test="productionDate != null">
                #{productionDate},
            </if>
            <if test="insertType != null">
                #{insertType},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <select id="getPurchaseBatchInventory"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select
        sr.id,
        sr.batch,
        sr.sku,
        sr.type,
        sr.quantity,
        sr.unit,
        sr.recorder,
        sr.remark,
        sr.update_time updateTime,
        sr.quality_date qualityDate,
        sr.area_no areaNo,
        sr.store_quantity storeQuantity,
        sr.cost,
        sr.production_date productionDate
        , sr.tenant_id tenantId
        from store_record sr
        right join (
        select max(sr.id) id
        from store_record sr
        <where>
            <if test="warehouseNoList != null">
                <if test="warehouseNoList.size() > 0">
                    and sr.`area_no` in
                    <foreach collection="warehouseNoList" item="warehouseNo1" open="(" close=")" separator=",">
                        #{warehouseNo1}
                    </foreach>
                </if>
                <if test="warehouseNoList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="skuList != null">
                <if test="skuList.size() > 0">
                    and sr.sku in
                    <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                        #{sku1}
                    </foreach>
                </if>
                <if test="skuList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="warehouseNoList == null">
                and 1 = 2
            </if>
            <if test="skuList == null">
                and 1 = 2
            </if>
        </where>
        group by sr.area_no, sr.sku, sr.batch, sr.quality_date
        ) t on sr.id = t.id
        where sr.`store_quantity` > 0
    </select>


    <select id="findListByUnique"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select
        sr.id,
        sr.batch,
        sr.sku,
        sr.type,
        sr.quantity,
        sr.unit,
        sr.recorder,
        sr.remark,
        sr.update_time updateTime,
        sr.quality_date qualityDate,
        sr.area_no areaNo,
        sr.store_quantity storeQuantity,
        sr.cost,
        sr.production_date productionDate
        , sr.tenant_id tenantId
        from store_record sr
        right join (
        select max(sr.id) id
        from store_record sr
        <where>
            <if test="warehouseNoList != null">
                <if test="warehouseNoList.size() > 0">
                    and sr.`area_no` in
                    <foreach collection="warehouseNoList" item="warehouseNo1" open="(" close=")" separator=",">
                        #{warehouseNo1}
                    </foreach>
                </if>
                <if test="warehouseNoList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="skuList != null">
                <if test="skuList.size() > 0">
                    and sr.sku in
                    <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                        #{sku1}
                    </foreach>
                </if>
                <if test="skuList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="warehouseNoList == null">
                and 1 = 2
            </if>
            <if test="skuList == null">
                and 1 = 2
            </if>
            <if test="purchaseBatchList != null and purchaseBatchList.size() > 0">
                and sr.batch in
                <foreach collection="purchaseBatchList" item="purchaseBatch1" open="(" close=")" separator=",">
                    #{purchaseBatch1}
                </foreach>
            </if>
            <if test="productionDateList != null and productionDateList.size() > 0">
                and sr.`production_date` in
                <foreach collection="productionDateList" item="productionDate1" open="(" close=")" separator=",">
                    #{productionDate1}
                </foreach>
            </if>
            <if test="qualityDateList != null and qualityDateList.size() > 0">
                and sr.quality_date in
                <foreach collection="qualityDateList" item="qualityDate1" open="(" close=")" separator=",">
                    #{qualityDate1}
                </foreach>
            </if>
        </where>
        group by sr.area_no, sr.sku, sr.batch, sr.production_date, sr.quality_date
        ) t on sr.id = t.id
    </select>

    <select id="findGt0ListByUnique"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select
        sr.id,
        sr.batch,
        sr.sku,
        sr.type,
        sr.quantity,
        sr.unit,
        sr.recorder,
        sr.remark,
        sr.update_time updateTime,
        sr.quality_date qualityDate,
        sr.area_no areaNo,
        sr.store_quantity storeQuantity,
        sr.cost,
        sr.production_date productionDate
        , sr.tenant_id tenantId
        from store_record sr
        right join (
        select max(sr.id) id
        from store_record sr
        <where>
            <if test="warehouseNoList != null">
                <if test="warehouseNoList.size() > 0">
                    and sr.`area_no` in
                    <foreach collection="warehouseNoList" item="warehouseNo1" open="(" close=")" separator=",">
                        #{warehouseNo1}
                    </foreach>
                </if>
                <if test="warehouseNoList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="skuList != null">
                <if test="skuList.size() > 0">
                    and sr.sku in
                    <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                        #{sku1}
                    </foreach>
                </if>
                <if test="skuList.size() == 0">
                    and 1 = 2
                </if>
            </if>
            <if test="warehouseNoList == null">
                and 1 = 2
            </if>
            <if test="skuList == null">
                and 1 = 2
            </if>
            <if test="purchaseBatchList != null and purchaseBatchList.size() > 0">
                and sr.batch in
                <foreach collection="purchaseBatchList" item="purchaseBatch1" open="(" close=")" separator=",">
                    #{purchaseBatch1}
                </foreach>
            </if>
            <if test="productionDateList != null and productionDateList.size() > 0">
                and sr.`production_date` in
                <foreach collection="productionDateList" item="productionDate1" open="(" close=")" separator=",">
                    #{productionDate1}
                </foreach>
            </if>
            <if test="qualityDateList != null and qualityDateList.size() > 0">
                and sr.quality_date in
                <foreach collection="qualityDateList" item="qualityDate1" open="(" close=")" separator=",">
                    #{qualityDate1}
                </foreach>
            </if>
        </where>
        group by sr.area_no, sr.sku, sr.batch, sr.production_date, sr.quality_date
        ) t on sr.id = t.id
        where sr.store_quantity > 0
    </select>

    <select id="selectDifferentBatchStoreRecord"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDifferentBatchDO">
        SELECT *
        FROM
        (
        SELECT
        t1.area_no areaNo,
        t1.sku sku,
        t1.batch batch,
        t1.production_date productionDate,
        t1.quality_date qualityDate,
        IFNULL(t1.store_quantity, 0) storeRecordQuantity,
        IFNULL(t2.cost_quantity, 0) produceQuantity
        FROM
        (
        SELECT
        srd.area_no,
        srd.sku,
        sr.batch,
        sr.`store_quantity`,
        IFNULL(sr.`production_date`, '1970-01-01') production_date,
        IFNULL(sr.`quality_date`, '1970-01-01') quality_date
        FROM
        (
        SELECT
        `area_no`,
        `sku`,
        MAX(id) mid
        FROM `store_record`
        WHERE `area_no` = #{areaNo}
        <if test="skuList != null and skuList.size() > 0">
            AND `sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        GROUP BY
        `area_no`,
        `sku`,
        `production_date`,
        `quality_date`,
        `batch`
        ) srd
        INNER JOIN `store_record` sr ON srd.mid = sr.id
        WHERE sr.`store_quantity` >= 0
        ) t1
        LEFT JOIN (
        SELECT
        t5.warehouse_no,
        t5.sku,
        t5.produce_at,
        t5.shelf_life,
        t5.purchase_no,
        t5.cost_quantity
        FROM
        (
        SELECT
        t4.warehouse_no,
        t4.sku,
        t4.produce_at,
        t4.shelf_life,
        t4.`purchase_no`,
        SUM(t4.`cost_quantity`) cost_quantity
        FROM
        (
        SELECT
        wpb.`warehouse_no`,
        wpb.`sku`,
        IFNULL(FROM_UNIXTIME(wpb.`produce_at` / 1000, '%Y-%m-%d'), '1970-01-01') produce_at,
        IFNULL(FROM_UNIXTIME(wpb.`shelf_life` / 1000, '%Y-%m-%d'),'1970-01-01') shelf_life,
        wcb.`purchase_no` ,
        wcb.`quantity` cost_quantity
        FROM `warehouse_produce_batch` wpb
        LEFT JOIN `warehouse_cost_batch` wcb ON wpb.`warehouse_no` = wcb.`warehouse_no` AND wpb.`sku` = wcb.`sku` AND
        wpb.id = wcb.`produce_batch_id`
        WHERE wpb.warehouse_no = #{areaNo}
        <if test="skuList != null and skuList.size() > 0">
            AND wpb.`sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        ) t4
        GROUP BY t4.warehouse_no,t4.sku,t4.produce_at,t4.shelf_life,t4.purchase_no
        ) t5
        ) t2 ON t1.area_no = t2.warehouse_no
        AND t1.sku = t2.sku
        AND t1.production_date = t2.produce_at
        AND t1.quality_date = t2.shelf_life
        AND t1.batch = t2.purchase_no
        LEFT JOIN `area_store` ast ON t1.area_no = ast.`area_no`
        AND t1.sku = ast.`sku`
        ) t3
        WHERE
        t3.storeRecordQuantity != t3.produceQuantity
    </select>

    <select id="selectLastBatchByWarehouseAndSkuAndDate" resultType="java.lang.String">
        select batch
        from store_record
        where area_no = #{warehouseNo}
          and sku = #{sku}
          and quality_date = #{shelfLife}
          and production_date = #{produceAt}
        order by id desc limit 1
    </select>

    <select id="selectLastStoreRecordByWarehouseAndSku"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        SELECT
        srd.area_no areaNo,
        srd.sku,
        sr.batch,
        sr.`store_quantity` storeQuantity,
        IFNULL(sr.`production_date`, '1970-01-01') productionDate,
        IFNULL(sr.`quality_date`, '1970-01-01') qualityDate
        , sr.tenant_id tenantId
        FROM
        (
        SELECT
        `area_no`,
        `sku`,
        MAX(id) mid
        FROM `store_record`
        WHERE `area_no` = #{areaNo}
        <if test="skuList != null and skuList.size() > 0">
            AND `sku` in
            <foreach collection="skuList" item="sku1" open="(" close=")" separator=",">
                #{sku1}
            </foreach>
        </if>
        GROUP BY
        `area_no`,
        `sku`,
        `production_date`,
        `quality_date`,
        `batch`
        ) srd
        INNER JOIN `store_record` sr ON srd.mid = sr.id
        WHERE sr.`store_quantity` >= 0
    </select>

    <select id="selectLastQualityDate"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select quality_date qualityDate
        from (
                 select store_quantity, quality_date, create_time
                 from (
                          select batch, store_quantity, quality_date, create_time
                          from store_record
                          where sku = #{sku}
                            and area_no = #{warehouseNo}
                          order by id desc) a
                 group by batch, quality_date
             ) b
        WHERE store_quantity > 0
          and b.quality_date >= DATE (now())
        order by quality_date asc limit 1

    </select>

    <update id="updateCost100">
        UPDATE
            store_record
        set
            cost        = #{cost}
        where cost != #{cost}
          and area_no = #{warehouseNo}
          and sku = #{sku}
          and batch = #{batch}
            limit 100;
    </update>

    <select id="countBySkuCodeList"
            resultType="net.summerfarm.wms.domain.areaStore.domainobject.StoreRecordCountBySkuCode">
        select
        sku as skuCode,
        ifnull(count(1), 0) storeRecordCount
        FROM store_record
        where 1 = 1
        <if test="skuList == null">
            and 1 = 2
        </if>
        <if test="skuList != null and skuList.size() > 0">
            and sku IN
            <foreach collection="skuList" item="skuCode1" open="(" separator="," close=")">
                #{skuCode1}
            </foreach>
        </if>
        group by sku;
    </select>

    <select id="selectLastRecord" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select sr.lot_type        lotType,
               sr.id,
               sr.batch,
               sr.sku,
               sr.type,
               sr.quantity,
               sr.unit,
               sr.recorder,
               sr.remark,
               sr.update_time     updateTime,
               sr.quality_date    qualityDate,
               sr.area_no         areaNo,
               sr.store_quantity  storeQuantity,
               sr.cost,
               sr.production_date productionDate,
               sr.insert_type     insertType
        from store_record sr
        where sr.sku = #{sku}
          and sr.area_no = #{warehouseNo}
          and sr.batch = #{batch}
          and sr.quality_date = #{qualityDate}
          and sr.production_date = #{productionDate}
        order by sr.id desc limit 1;
    </select>

    <select id="checkQuantityRecordDiff"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CheckStoreQuantityDiffDO">
        select store.area_no       as warehouseNo,
               store.sku,
               store.quantity,
               tmp2.batch_quantity as batchFlowQuantity
        from area_store store
                 left join (
            select tmp.area_no,
                   tmp.sku,
                   sum(tmp.storeQuantity) as batch_quantity
            from (
                     SELECT sr.id,
                            srd.area_no,
                            srd.sku,
                            sr.batch,
                            sr.`store_quantity`                        storeQuantity,
                            IFNULL(sr.`production_date`, '1970-01-01') productionDate,
                            IFNULL(sr.`quality_date`, '1970-01-01')    qualityDate
                             ,
                            sr.tenant_id                               tenantId
                     FROM (
                              SELECT `area_no`,
                                     `sku`,
                                     MAX(id) mid
                              FROM `store_record`
                              WHERE `area_no` = #{warehouseNo}
                                AND `sku` = #{sku}
                              GROUP BY `area_no`,
                                       `sku`,
                                       `production_date`,
                                       `quality_date`,
                                       `batch`
                          ) srd
                              INNER JOIN `store_record` sr ON srd.mid = sr.id
                     where sr.`store_quantity` > 0
                 ) tmp
            group by tmp.area_no, tmp.sku
        ) tmp2 on tmp2.area_no = store.area_no and tmp2.sku = store.sku
        where store.area_no = #{warehouseNo}
          and store.sku = #{sku}
    </select>

    <select id="checkQuantityProduceDiff"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CheckStoreQuantityDiffDO">
        select tmp2.area_no        as warehouseNo,
               tmp2.sku,
               tmp2.productionDate as produceAt,
               tmp2.qualityDate    as shelfLife,
               tmp2.batch_quantity as batchFlowQuantity,
               store.produceBatchQuantity
        from (
                 select tmp.area_no,
                        tmp.sku,
                        tmp.productionDate,
                        tmp.qualityDate,
                        sum(tmp.storeQuantity) as batch_quantity
                 from (
                          SELECT sr.id,
                                 srd.area_no,
                                 srd.sku,
                                 sr.batch,
                                 sr.`store_quantity`                        storeQuantity,
                                 IFNULL(sr.`production_date`, '1970-01-01') productionDate,
                                 IFNULL(sr.`quality_date`, '1970-01-01')    qualityDate
                                  ,
                                 sr.tenant_id                               tenantId
                          FROM (
                                   SELECT `area_no`,
                                          `sku`,
                                          MAX(id) mid
                                   FROM `store_record`
                                   WHERE `area_no` = #{warehouseNo}
                                     AND `sku` = #{sku}
                                   GROUP BY `area_no`,
                                            `sku`,
                                            `production_date`,
                                            `quality_date`,
                                            `batch`
                               ) srd
                                   INNER JOIN `store_record` sr ON srd.mid = sr.id
                          where sr.`store_quantity` > 0
                      ) tmp
                 group by tmp.area_no, tmp.sku, tmp.productionDate, tmp.qualityDate
             ) tmp2
                 left join (
            select wpb.`warehouse_no` area_no,
                   wpb.`sku`,
                   FROM_UNIXTIME(wpb.`produce_at` / 1000, '%Y-%m-%d') as 'produce_at', FROM_UNIXTIME(wpb.`shelf_life` / 1000, '%Y-%m-%d') as 'shelf_life', sum(wpb.quantity) as produceBatchQuantity
            from warehouse_produce_batch wpb
            where wpb.warehouse_no = #{warehouseNo}
              and wpb.sku = #{sku}
            GROUP BY wpb.`warehouse_no`,
                     wpb.`sku`,
                     wpb.`produce_at`,
                     wpb.`shelf_life`
        ) store on tmp2.area_no = store.area_no and tmp2.sku = store.sku
            and store.produce_at = tmp2.productionDate and store.shelf_life = tmp2.qualityDate
        where store.area_no = #{warehouseNo}
          and store.sku = #{sku}
    </select>

    <select id="checkQuantityCostDiff"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.CheckStoreQuantityDiffDO">
        select tmp2.area_no        as warehouseNo,
               tmp2.sku,
               tmp2.productionDate as produceAt,
               tmp2.qualityDate    as shelfLife,
               tmp2.batch          as purchaseNo,
               tmp2.batch_quantity as batchFlowQuantity,
               store.costBatchQuantity
        from (
                 select tmp.area_no,
                        tmp.sku,
                        tmp.productionDate,
                        tmp.qualityDate,
                        tmp.batch,
                        sum(tmp.storeQuantity) as batch_quantity
                 from (
                          SELECT sr.id,
                                 srd.area_no,
                                 srd.sku,
                                 sr.batch,
                                 sr.`store_quantity`                        storeQuantity,
                                 IFNULL(sr.`production_date`, '1970-01-01') productionDate,
                                 IFNULL(sr.`quality_date`, '1970-01-01')    qualityDate
                                  ,
                                 sr.tenant_id                               tenantId
                          FROM (
                                   SELECT `area_no`,
                                          `sku`,
                                          MAX(id) mid
                                   FROM `store_record`
                                   WHERE `area_no` = #{warehouseNo}
                                     AND `sku` = #{sku}
                                   GROUP BY `area_no`,
                                            `sku`,
                                            `production_date`,
                                            `quality_date`,
                                            `batch`
                               ) srd
                                   INNER JOIN `store_record` sr ON srd.mid = sr.id
                          where sr.`store_quantity` > 0
                      ) tmp
                 group by tmp.area_no, tmp.sku, tmp.productionDate, tmp.qualityDate, tmp.batch
             ) tmp2
                 left join (
            select wpb.`warehouse_no`   area_no,
                   wpb.`sku`,
                   FROM_UNIXTIME(wpb.`produce_at` / 1000, '%Y-%m-%d') as 'produce_at', FROM_UNIXTIME(wpb.`shelf_life` / 1000, '%Y-%m-%d') as 'shelf_life', wcb.purchase_no,
                   sum(wcb.quantity) as costBatchQuantity
            from warehouse_cost_batch wcb
                     inner join warehouse_produce_batch wpb on wpb.id = wcb.produce_batch_id
            where wpb.warehouse_no = #{warehouseNo}
              and wpb.sku = #{sku}
            GROUP BY wpb.`warehouse_no`,
                     wpb.`sku`,
                     wpb.`produce_at`,
                     wpb.`shelf_life`,
                     wcb.purchase_no
        ) store on tmp2.area_no = store.area_no and tmp2.sku = store.sku
            and store.produce_at = tmp2.productionDate and store.shelf_life = tmp2.qualityDate and
                   store.purchase_no = tmp2.batch
        where store.area_no = #{warehouseNo}
          and store.sku = #{sku}
    </select>

    <select id="querySkuListByWarehouseNo" resultType="java.lang.String">
        select distinct sku
        from store_record
        where area_no = #{warehouseNo}
    </select>

    <select id="countSkuListByWarehouseNo" resultType="java.lang.Integer">
        select count(1)
        from store_record
        where area_no = #{warehouseNo}
    </select>

    <select id="listInProve"
            parameterType="net.summerfarm.wms.domain.StoreRecord.domainobject.query.QueryStoreRecordInProve"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select * from (select
        s.id, s.batch, s.sku, s.type,s.area_no areaNo,s.store_quantity quantity,s.quality_date
        qualityDate,s.production_date productionDate,s.lot_type lotType
        from
        (select *
        from store_record sr
        <where>
            sr.production_date is not null
            and sr.quality_date is not null
            <if test="warehouseNo != null">
                AND sr.area_no= #{warehouseNo}
            </if>
            <if test="sku != null">
                AND sr.sku= #{sku}
            </if>
            <if test="batch != null">
                and sr.batch = #{batch}
            </if>
            <if test="productionDateStart!=null">
                and sr.production_date >= #{productionDateStart}
            </if>
            <if test="productionDateEnd!=null">
                and sr.production_date &lt;= #{productionDateEnd}
            </if>
        </where>
        order by sr.id desc) s
        group by s.sku, s.batch, s.quality_date) t
        order by id desc
    </select>

    <select id="repairStoreRecordCostNull"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        select id              id,
               batch           batch,
               sku             sku,
               area_no         areaNo,
               store_quantity  quantity,
               quality_date
                               qualityDate,
               production_date productionDate
        from store_record
        where (cost is null or cost = 0)
          and create_time &gt;= #{startTime}
          and create_time &lt;= #{endTime}
    </select>

    <update id="updateCost">
        update store_record
        set cost = #{cost}
        where id = #{id}
    </update>

    <select id="selectListByBatch"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        SELECT
        sr.batch,
        sr.sku,
        sr.area_no areaNo,
        sr.quality_date qualityDate,
        sr.store_quantity storeQuantity,
        sr.cost,
        sr.production_date productionDate,
        sr.tenant_id tenantId
        FROM
        store_record sr
        INNER JOIN (
        SELECT
        max(id) id
        FROM
        store_record
        WHERE
        sku in
        <foreach collection="skuList" item="sku" open="(" separator="," close=")">
            #{sku}
        </foreach>
        AND area_no = #{warehouseNo}
        <if test="batch!=null and batch.length>0">
            and batch = #{batch}
        </if>
        GROUP BY
        sku,
        `batch` ,
        quality_date,
        production_date
        ) t ON sr.id = t.id
    </select>

    <select id="querySkuByAreaNoAndSku" resultType="java.lang.String">
        select distinct sku
        from store_record
        where area_no = #{areaNo}
        <if test="skuList !=null and skuList.size > 0">
            and sku in
            <foreach collection="skuList" open="(" close=")" item="sku" separator=",">#{sku}</foreach>
        </if>
    </select>

    <select id="selectStoreQuantityGtZeroByAreaNoAndSku" resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        SELECT  sr.area_no areaNo,
        sr.sku,
        sr.batch,
        sr.production_date productionDate,
        sr.quality_date qualityDate,
        sr.store_quantity storeQuantity,
        sr.`cost`
        FROM store_record sr
        INNER JOIN (
            SELECT max(id) id FROM store_record
            WHERE sku = #{sku}
            AND area_no = #{areaNo}
            GROUP BY `batch` , quality_date, production_date
        ) t ON sr.`id` = t.id
        WHERE sr.store_quantity > 0
    </select>

    <select id="selectBatchFirstInStoreInfo" parameterType="net.summerfarm.wms.domain.StoreRecord.param.query.BatchFirstInStoreInfoQueryParam"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.StoreRecordDO">
        SELECT  sr.area_no areaNo,
        sr.sku,
        sr.batch,
        sr.production_date productionDate,
        sr.quality_date qualityDate,
        sr.`type`,
        sr.update_time updateTime
        FROM store_record sr
        INNER JOIN (
        SELECT min(id) id FROM store_record
        WHERE sku = #{sku}
        AND area_no = #{warehouseNo}
        <if test="purchaseBatchList != null and purchaseBatchList.size() > 0">
            AND batch IN
            <foreach collection="purchaseBatchList" item="batch" open="(" close=")" separator=",">
                #{batch}
            </foreach>
        </if>
        <if test="productionDateList != null and productionDateList.size() > 0">
            AND `production_date` IN
            <foreach collection="productionDateList" item="productionDate" open="(" close=")" separator=",">
                #{productionDate}
            </foreach>
        </if>
        <if test="qualityDateList != null and qualityDateList.size() > 0">
            AND quality_date IN
            <foreach collection="qualityDateList" item="qualityDate" open="(" close=")" separator=",">
                #{qualityDate}
            </foreach>
        </if>
        GROUP BY `batch` , quality_date, production_date
        ) t ON sr.`id` = t.id
    </select>

    <select id="selectLastedPurchaseCost" resultType="java.math.BigDecimal">
        select
            `cost`
        from
            store_record
        where
            `type` = 11 <!-- 采购入库 -->
          and sku = #{sku,jdbcType=VARCHAR}
          and area_no = #{warehouseNo,jdbcType=INTEGER}
          and cost != 0
        order by id desc
        limit 1
    </select>

    <select id="sumStoreQuantityGroupByQualityDate" resultType="java.lang.Integer">
        /*FORCE_MASTER*/ select
        sum(sr.store_quantity)
        from store_record sr
        where sr.sku=#{sku} AND sr.area_no = #{warehouseNo}
        <if test="batch != null">
            and sr.batch= #{batch}
        </if>
        <choose>
            <when test="qualityDate == null">
                AND sr.quality_date is null
            </when>
            <otherwise>
                AND sr.quality_date = #{qualityDate}
            </otherwise>
        </choose>
        group by sr.sku, sr.batch, sr.quality_date, sr.area_no;
    </select>

</mapper>
