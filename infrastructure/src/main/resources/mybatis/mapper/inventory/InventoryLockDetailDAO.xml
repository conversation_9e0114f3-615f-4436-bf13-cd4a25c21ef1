<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.InventoryLockDetailDAO">

    <resultMap type="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.InventoryLockDetailDO" id="InventoryLockDetailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
        <result property="warehouseTenantId" column="warehouse_tenant_id" jdbcType="INTEGER"/>
        <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderTypeName" column="order_type_name" jdbcType="VARCHAR"/>
        <result property="lockQuantity" column="lock_quantity" jdbcType="INTEGER"/>
        <result property="remainQuantity" column="remain_quantity" jdbcType="INTEGER"/>
        <result property="releaseQuantity" column="release_quantity" jdbcType="INTEGER"/>
        <result property="reduceQuantity" column="reduce_quantity" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="orderSubNo" column="order_sub_no" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="InventoryLockDetailMap">
        select
          id, tenant_id, warehouse_no, warehouse_tenant_id, sku_code, order_no, order_type_name, lock_quantity, remain_quantity, release_quantity, reduce_quantity, creator, create_time, updater, update_time, delete_flag
            , order_sub_no
        from inventory_lock_detail
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="InventoryLockDetailMap">
        select
          id, tenant_id, warehouse_no, warehouse_tenant_id, sku_code, order_no, order_type_name, lock_quantity, remain_quantity, release_quantity, reduce_quantity, creator, create_time, updater, update_time, delete_flag
            , order_sub_no
        from inventory_lock_detail
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="warehouseTenantId != null">
                and warehouse_tenant_id = #{warehouseTenantId}
            </if>
            <if test="skuCode != null and skuCode != ''">
                and sku_code = #{skuCode}
            </if>
            <if test="skuCodeList != null">
                and sku_code in
                <foreach collection="skuCodeList" item="skuCode1" open="(" close=")" separator=",">
                    #{skuCode1}
                </foreach>
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="orderNoList != null">
                and order_no in
                <foreach collection="orderNoList" item="orderNo1" open="(" close=")" separator=",">
                    #{orderNo1}
                </foreach>
            </if>
            <if test="orderTypeName != null and orderTypeName != ''">
                and order_type_name = #{orderTypeName}
            </if>
            <if test="orderTypeNameList != null">
                and order_type_name in
                <foreach collection="orderTypeNameList" item="orderTypeName1" open="(" close=")" separator=",">
                    #{orderTypeName1}
                </foreach>
            </if>
            <if test="lockQuantity != null">
                and lock_quantity = #{lockQuantity}
            </if>
            <if test="remainQuantity != null">
                and remain_quantity = #{remainQuantity}
            </if>
            <if test="releaseQuantity != null">
                and release_quantity = #{releaseQuantity}
            </if>
            <if test="reduceQuantity != null">
                and reduce_quantity = #{reduceQuantity}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from inventory_lock_detail
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="warehouseNo != null">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="warehouseTenantId != null">
                and warehouse_tenant_id = #{warehouseTenantId}
            </if>
            <if test="skuCode != null and skuCode != ''">
                and sku_code = #{skuCode}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="orderTypeName != null and orderTypeName != ''">
                and order_type_name = #{orderTypeName}
            </if>
            <if test="lockQuantity != null">
                and lock_quantity = #{lockQuantity}
            </if>
            <if test="remainQuantity != null">
                and remain_quantity = #{remainQuantity}
            </if>
            <if test="releaseQuantity != null">
                and release_quantity = #{releaseQuantity}
            </if>
            <if test="reduceQuantity != null">
                and reduce_quantity = #{reduceQuantity}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="deleteFlag != null">
                and delete_flag = #{deleteFlag}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_lock_detail(tenant_id, warehouse_no, warehouse_tenant_id, sku_code, order_no, order_type_name, lock_quantity, remain_quantity, release_quantity, reduce_quantity, creator, create_time, updater, update_time, delete_flag)
        values (#{tenantId}, #{warehouseNo}, #{warehouseTenantId}, #{skuCode}, #{orderNo}, #{orderTypeName}, #{lockQuantity}, #{remainQuantity}, #{releaseQuantity}, #{reduceQuantity}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleteFlag})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_lock_detail(tenant_id, warehouse_no, warehouse_tenant_id, sku_code, order_no, order_type_name, lock_quantity, remain_quantity, release_quantity, reduce_quantity, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.tenantId}, #{entity.warehouseNo}, #{entity.warehouseTenantId}, #{entity.skuCode}, #{entity.orderNo}, #{entity.orderTypeName}, #{entity.lockQuantity}, #{entity.remainQuantity}, #{entity.releaseQuantity}, #{entity.reduceQuantity}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into inventory_lock_detail(tenant_id, warehouse_no, warehouse_tenant_id, sku_code, order_no, order_type_name, lock_quantity, remain_quantity, release_quantity, reduce_quantity, creator, create_time, updater, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.tenantId}, #{entity.warehouseNo}, #{entity.warehouseTenantId}, #{entity.skuCode}, #{entity.orderNo}, #{entity.orderTypeName}, #{entity.lockQuantity}, #{entity.remainQuantity}, #{entity.releaseQuantity}, #{entity.reduceQuantity}, #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
        tenant_id = values(tenant_id),
        warehouse_no = values(warehouse_no),
        warehouse_tenant_id = values(warehouse_tenant_id),
        sku_code = values(sku_code),
        order_no = values(order_no),
        order_type_name = values(order_type_name),
        lock_quantity = values(lock_quantity),
        remain_quantity = values(remain_quantity),
        release_quantity = values(release_quantity),
        reduce_quantity = values(reduce_quantity),
        creator = values(creator),
        create_time = values(create_time),
        updater = values(updater),
        update_time = values(update_time),
        delete_flag = values(delete_flag)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update inventory_lock_detail
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="warehouseTenantId != null">
                warehouse_tenant_id = #{warehouseTenantId},
            </if>
            <if test="skuCode != null and skuCode != ''">
                sku_code = #{skuCode},
            </if>
            <if test="orderNo != null and orderNo != ''">
                order_no = #{orderNo},
            </if>
            <if test="orderTypeName != null and orderTypeName != ''">
                order_type_name = #{orderTypeName},
            </if>
            <if test="lockQuantity != null">
                lock_quantity = #{lockQuantity},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity},
            </if>
            <if test="releaseQuantity != null">
                release_quantity = #{releaseQuantity},
            </if>
            <if test="reduceQuantity != null">
                reduce_quantity = #{reduceQuantity},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from inventory_lock_detail where id = #{id}
    </delete>

    <update id="updateForReleaseQuantity">
        update inventory_lock_detail
        <set>
            update_time = now(),
            updater = #{updater},
            remain_quantity = remain_quantity - #{releaseQuantityUpdate},
            release_quantity = release_quantity + #{releaseQuantityUpdate}
        </set>
        where id = #{id}
        and tenant_id = #{tenantId}
        and warehouse_no = #{warehouseNo}
        and sku_code = #{skuCode}
        and order_no = #{orderNo}
        and remain_quantity >= #{releaseQuantityUpdate}
    </update>

    <update id="updateForReduceQuantity">
        update inventory_lock_detail
        <set>
            update_time = now(),
            updater = #{updater},
            remain_quantity = remain_quantity - #{reduceQuantityUpdate},
            reduce_quantity = reduce_quantity + #{reduceQuantityUpdate}
        </set>
        where id = #{id}
        and tenant_id = #{tenantId}
        and warehouse_no = #{warehouseNo}
        and sku_code = #{skuCode}
        and order_no = #{orderNo}
        and remain_quantity >= #{reduceQuantityUpdate}
    </update>

    <update id="updateForSwitchWarehouse">
        update inventory_lock_detail
        <set>
            update_time = now(),
            updater = #{updater},
            warehouse_no = #{targetWarehouseNo},
        </set>
        where id = #{id}
        and warehouse_no = #{originWarehouseNo}
        and sku_code = #{skuCode}
    </update>
</mapper>

