<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.wms.infrastructure.dao.inventory.WmsBatchFrozenDAO">

    <select id="selectByDetail" resultType="java.lang.Integer">
        select ifnull(sum(lock_quantity), 0)
        from wms_batch_frozen
        where warehouse_no = #{areaNo}
          and sku = #{sku}
          and batch = #{batch}
          and quality_date = #{qualityDate}
          and status = 0;
    </select>

    <select id="listByAreaNoAndSku"
            resultType="net.summerfarm.wms.infrastructure.dao.inventory.dataobject.WmsBatchFrozenDO">
        select warehouse_no warehouseNo,
        sku sku,
        batch batch,
        quality_date qualityDate,
        lock_quantity lockQuantity
        from wms_batch_frozen
        where warehouse_no = #{warehouseNo}
        <if test="skus != null and skus.size() > 0">
            and sku in
            <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        and status = 0;
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.wms.manage.model.domain.WmsBatchFrozen" useGeneratedKeys="true">
        insert into wms_batch_frozen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="batch != null">
                batch,
            </if>
            <if test="lockQuantity != null">
                lock_quantity,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="typeId != null">
                type_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="batch != null">
                #{batch,jdbcType=VARCHAR},
            </if>
            <if test="lockQuantity != null">
                #{lockQuantity,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="typeId != null">
                #{typeId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="qualityDate != null">
                #{qualityDate,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateStatus">
        update wms_batch_frozen
        set status = 1
        where type = #{type}
          and type_id = #{typeId}
    </update>


</mapper>