<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>summerfarm-wms</artifactId>
        <groupId>net.summerfarm</groupId>
        <version>0.0.1</version>
    </parent>

    <groupId>net.summerfarm</groupId>
    <artifactId>wms-api</artifactId>
    <version>0.0.1</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>wms-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>net.manage.client</groupId>
            <artifactId>manage-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- validation -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
    </dependencies>

</project>
