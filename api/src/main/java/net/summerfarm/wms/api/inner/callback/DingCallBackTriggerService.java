package net.summerfarm.wms.api.inner.callback;

import net.summerfarm.wms.api.inner.callback.dto.DingTriggerEventReq;

/**
 * 钉钉事件处理器抽象接口
 */
public interface DingCallBackTriggerService {

    /**
     * 审批实例开始时触发
     */
    void start(DingTriggerEventReq result);

    /**
     * 审批终止时触发-发起人撤销审批单
     */
    void terminate(DingTriggerEventReq result);

    /**
     * 审批实例通过后触发
     */
    void agree(DingTriggerEventReq result);

    /**
     * 审批实例拒绝时触发
     */
    void refuse(DingTriggerEventReq result);

}
