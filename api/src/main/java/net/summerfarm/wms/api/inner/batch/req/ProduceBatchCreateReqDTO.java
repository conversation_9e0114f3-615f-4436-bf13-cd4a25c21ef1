package net.summerfarm.wms.api.inner.batch.req;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/10/24  16:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProduceBatchCreateReqDTO {
    /**
     * 生产批次id
     */
    private Long id;

    /**
     * sku
     */
    @NotNull(message = "sku不能为空")
    private String sku;

    /**
     * 仓库编号
     */
    @NotNull(message = "仓库编号不能为空")
    private Integer warehouseNo;

    /**
     * 保质期 时间戳
     */
    @NotNull(message = "保质期不能为空")
    private Long shelfLife;

    /**
     * 生产期 时间戳
     */
    @NotNull(message = "生产日期不能为空")
    private Long produceAt;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    /**
     * 成本批次信息
     */
    List<CostBatchReqDTO> costBatchReqDTOList;


    /**
     * 操作类型
     */
    private Integer operationType;


    /**
     * 操作人名称
     */
    private String operationName;

    /**
     * 来源id
     */
    private Long sourceId;

    /**
     * 备注
     */
    private String remake;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
}
