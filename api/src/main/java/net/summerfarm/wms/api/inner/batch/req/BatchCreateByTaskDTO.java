package net.summerfarm.wms.api.inner.batch.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> ct
 * create at:  2022/11/22  15:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchCreateByTaskDTO {


    /**
     * 来源id
     */
    private Long sourceId;

    /**
     * sku
     */
    @NotNull(message = "sku不能为空")
    private String sku;

    /**
     * 仓库编号
     */
    @NotNull(message = "仓库编号不能为空")
    private Integer warehouseNo;

    /**
     * 保质期 时间戳
     */
    @NotNull(message = "保质期不能为空")
    private Long shelfLife;

    /**
     * 生产期 时间戳
     */
    @NotNull(message = "生产日期不能为空")
    private Long produceAt;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    /**
     * 采购批次号
     */
    private String purchaseNo;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 操作人名称
     */
    private String operationId;

    /**
     * 备注
     */
    private String remake;

    /**
     * 成本批次类型
     * LotTypeEnum
     */
    private Integer lotType;


    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 操作人
     */
    private String operatorName;
}
