package net.summerfarm.wms.api.base;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BaseReq implements Serializable {
    Long tenantId;

    /**
     * 鲜沐为adminId，saas为authId
     */
    Long bizUserId;

    /**
     * 操作人名称
     */
    String userOperatorName;

    /**
     * 系统来源 0-内部 1-外部
     */
    Integer systemSource;
}
