package net.summerfarm.wms.api.h5.crosswarehouse;

import net.summerfarm.wms.api.h5.crosswarehouse.dto.CrossWarehouseSortSimpleDTO;

import java.util.List;

/**
 * 越库分拣明细明细查询
 * @author: xdc
 * @date: 2024/3/11
 **/
public interface CrossWarehouseSortQueryService {

    /**
     * 查询越库分拣明细简单信息
     *
     * @param psoNoList       ofc的批次号
     * @param saleOrderNoList 销售单号
     * @param skuCodeList     sku编码信息
     */
    List<CrossWarehouseSortSimpleDTO> findSimpleByUnique(List<String> psoNoList, List<String> saleOrderNoList,
                                                         List<String> skuCodeList, List<String> fulfillmentNoList);
}
