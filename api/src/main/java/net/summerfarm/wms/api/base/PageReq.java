package net.summerfarm.wms.api.base;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 分页请求参数
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PageReq implements Serializable {
    private static final long serialVersionUID = -6935983523966400366L;

    Integer pageSize;

    Integer pageNum;

    Boolean orderBy;

    String sortParam;
}
