package net.summerfarm.wms.api.inner.batch;

import net.summerfarm.wms.api.inner.batch.req.*;
import net.summerfarm.wms.api.inner.batch.res.BatchCreateByTaskResDTO;
import net.summerfarm.wms.api.inner.batch.res.ProduceBatchResDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/10/11  16:22
 */
public interface ProduceBatchInnerService {


    /**
     * 根据sku + produceAt(生产日期) + shelfLife(保质期)查询生产批次 + warehouseNO
     *
     * @param batchQueryReqDTO
     * @return
     */
    ProduceBatchResDTO queryProduceBatch(ProduceBatchQueryReqDTO batchQueryReqDTO);

    /**
     * 添加生产批次的接口
     *
     * @param produceBatchCreateReqDTO
     * @return
     */
    Long saveProduceBatch(ProduceBatchCreateReqDTO produceBatchCreateReqDTO);

    /**
     * 添加生产批次的接口-动态代理调用
     *
     * @param sku
     * @param warehouseNo
     * @param shelfLife
     * @param produceAt
     * @param operationName
     * @param operationType
     * @param purchaseNo
     * @param lotType
     * @param customCost
     * @return
     */
    Long saveProduceBatchForProxy(String sku, Integer warehouseNo,
                                  Long shelfLife, Long produceAt,
                                  String operationName, Integer operationType,
                                  String purchaseNo, Integer lotType,
                                  BigDecimal customCost, Long tenantId);


    /**
     * 更新生产批次数量 更新生产批次 id 和 生产日期 保质期
     *
     * @param produceBatchUpdateReqDTO
     */
    void updateProduceBatch(ProduceBatchUpdateReqDTO produceBatchUpdateReqDTO);

    void insertStoreRecord(ProduceBatchUpdateReqDTO updateProduceBatchBO);

    /**
     * 生产/成本批次库存调整
     *
     * @param fixReqDTO
     */
    void fixProduceCostBatch(ProduceBatchFixReqDTO fixReqDTO);

    /**
     * 成本批次库存调整
     *
     * @param updateProduceBatchBO
     */
    void fixCostBatch(ProduceBatchFixReqDTO updateProduceBatchBO);

    /**
     * 更新生产批次数量 更新生产批次 id 和 生产日期 保质期
     *
     * @param operationType
     * @param operationName
     * @param purchaseNo
     * @param produceBatchId
     * @param quantity
     * @param customCost
     */
    void updateProduceBatchForProxy(Integer operationType, String operationName,
                                    String purchaseNo, Long produceBatchId, Integer quantity, BigDecimal customCost,
                                    Long recordNo, Long tenantId);

    /**
     * 批量获生产批次信息
     *
     * @param produceBatchBO
     * @return
     */
    List<ProduceBatchResDTO> queryProduceBatchList(ProduceBatchQueryReqDTO produceBatchBO);

    List<ProduceBatchResDTO> queryProduceBatchWithPeriod(ProduceBatchQueryReqDTO produceBatchBO);

    /**
     * 通过生产批次id查询信息
     *
     * @param produceBatchId 生产批次id
     * @return 生产批次信息
     */
    ProduceBatchResDTO queryProduceBatchById(Long produceBatchId);

    /**
     * binlog监听更新数据库
     *
     * @param produceBatchBO
     */
    void binlogProduceBatch(ProduceBatchCreateReqDTO produceBatchBO);

    /**
     * 入库任务新增生产批次信息
     */

    List<BatchCreateByTaskResDTO> saveBatchList(List<BatchCreateByTaskDTO> batchCreateReqDTO);

    /**
     * 是否存在入库记录
     *
     * @param reqDTO r
     * @return boolean
     */
    Boolean existInStoreFlow(ProduceBatchQueryReqDTO reqDTO);

}
