package net.summerfarm.wms.api.inner.instore.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/29  13:35
 * 状态更新
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockStorageReqDTO {

    /**
     * 入库任务id
     */
    private Long stockStorageId;

    /**
     * 入库单id
     */
    private List<Long> inboundOrderIds;

    /**
     * 操作人id
     */
    private Long adminId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 是否为pda操作
     */
    private Boolean isPda;
}
