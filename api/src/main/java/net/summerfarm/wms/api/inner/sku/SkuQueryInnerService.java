package net.summerfarm.wms.api.inner.sku;

import net.summerfarm.wms.api.inner.sku.dto.SkuConditionReqDTO;
import net.summerfarm.wms.api.inner.sku.dto.SkuResDTO;

import java.util.List;

public interface SkuQueryInnerService {
    /**
     * 根据条件查询sku信息
     *
     * @param skuConditionReqDTO
     * @return
     */
    List<SkuResDTO> querySkuByCondition(SkuConditionReqDTO skuConditionReqDTO);

    /**
     * 根据条件查询sku信息（开启库位管理）
     * @param skuConditionBO
     * @return
     */
    List<SkuResDTO> querySkuByConditionForCabinet(SkuConditionReqDTO skuConditionBO);

    List<SkuResDTO> querySkuByConditionForOnSale(SkuConditionReqDTO skuConditionBO);
}
