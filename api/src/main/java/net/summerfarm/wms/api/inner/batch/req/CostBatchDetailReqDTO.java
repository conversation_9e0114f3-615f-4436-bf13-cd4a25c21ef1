package net.summerfarm.wms.api.inner.batch.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  15:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostBatchDetailReqDTO {

    private Long id;

    private LocalDate createTime;

    private LocalDate updateTime;

    /**
     * 成本类型 1.采购成本 2.自提成本 3 调拨运输成本
     * {link CostTypeEnum}
     */
    private Integer type;

    /**
     * 金额
     */
    private BigDecimal cost;

    /**
     * 批次成本id
     */
    private Long costBatchId;

    /**
     * 入库数量
     */
    private Integer quantity;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;
}
