package net.summerfarm.wms.api.h5.crosswarehouse.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 分配越库分拣明细详情
 * @author: xdc
 * @date: 2024/3/14
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllocationCrossWarehouseSortDetailInput implements Serializable {
    private static final long serialVersionUID = -4853624015360093018L;

    /**
     * 分配的sku
     */
    private String sku;

    /**
     * 分配数量
     */
    private Integer quantity;
}
