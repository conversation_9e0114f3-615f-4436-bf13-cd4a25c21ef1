package net.summerfarm.wms.api.inner.instore;

import net.summerfarm.wms.api.inner.instore.dto.InBoundOrderDetailInnerResDTO;
import net.summerfarm.wms.api.inner.instore.dto.InBoundOrderReqDTO;
import net.summerfarm.wms.api.inner.instore.dto.InBoundOrderInnerResDTO;

import java.util.List;

/**
 * 入库单查询
 *
 * <AUTHOR>
 */
public interface InBoundOrderInnerService {
    /**
     * 查询入库单
     * @param dto q
     * @return r
     */
    InBoundOrderInnerResDTO queryInBoundOrder(InBoundOrderReqDTO dto);

    /**
     * 入库单详情查询接口
     * @param dto q
     * @return r
     */
    List<InBoundOrderDetailInnerResDTO> queryInBoundOrderDetail(InBoundOrderReqDTO dto);
}
