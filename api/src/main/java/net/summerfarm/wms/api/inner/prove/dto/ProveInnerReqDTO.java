package net.summerfarm.wms.api.inner.prove.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 证明数据对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProveInnerReqDTO implements Serializable {
    private static final long serialVersionUID = 8305809727477913881L;
    Long id;

    String sku;

    Integer type;

    String sourceId;

    /**
     * 采购号
     */
    String purchaseNo;

    Long produceAt;

    Long shelfLife;

    /**
     * 监管仓证明
     */
    String supervisedWarehouseProof;

    /**
     * 检测结果
     */
    Integer detectionResult;

    /**
     * 抽样基数
     */
    BigDecimal samplingBase;

    /**
     * 抽样数
     */
    BigDecimal samples;

    /**
     * 抑制率
     */
    BigDecimal inhibitionRate;

    /**
     * 农药残留报告
     */
    String pesticideResidueReport;

    /**
     * 核酸检测报告
     */
    String nucleicAcidDetection;

    /**
     * 质检报告
     */
    String inspectionReport;

    /**
     * 报关证明
     */
    String customsDeclarationCertificate;

    /**
     * 操作人
     */
    String operator;
}
