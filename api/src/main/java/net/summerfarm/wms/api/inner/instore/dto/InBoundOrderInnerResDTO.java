package net.summerfarm.wms.api.inner.instore.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InBoundOrderInnerResDTO implements Serializable {
    private static final long serialVersionUID = -6520809942878054841L;
    /**
     * 入库单id
     */
    Long inBoundOrderId;

    /**
     * 入库任务id
     */
    Long inBoundTaskId;

    /**
     * 采购号
     */
    String purchaseNo;

    /**
     * 类型
     */
    Integer type;

    /**
     * 收货仓库
     */
    Long warehouseNo;

    /**
     * 操作人
     */
    String operator;

    /**
     * 入库时间
     */
    Long inBoundTime;


    /**
     * 收货容器
     */
    String receivingContainer;
}
