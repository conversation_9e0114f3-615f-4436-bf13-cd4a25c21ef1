package net.summerfarm.wms.api.inner.batch.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  15:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProduceBatchQueryReqDTO {
    /**
     * sku
     */
    private String sku;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 保质期 时间戳
     */
    private Long shelfLife;

    /**
     * 生产期 时间戳
     */
    private Long produceAt;

    /**
     * 数量
     */
    private Integer minQuantity;

    /**
     * sku 列表
     */
    private List<String> skuList;

    /**
     * 生产期 列表
     */
    private List<Long> produceAtList;
}
