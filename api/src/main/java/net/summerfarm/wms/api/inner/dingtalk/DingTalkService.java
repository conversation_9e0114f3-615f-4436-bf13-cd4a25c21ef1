package net.summerfarm.wms.api.inner.dingtalk;

/**
 * <AUTHOR> ct
 * create at:  2020/2/26  18:10
 * 钉钉
 */

public interface DingTalkService {

    /**
     * 创建审批流
     */
    void createProcessInstance(Long bizId, Long adminId, String sku, Long warehouseNo);

    /**
     * 发送盘盈盘亏预警提醒
     *
     * @param taskId 任务id
     * @param type   盘盈/盘亏
     * @param msg    消息
     */
    void sendCargoDamageMsg(Long taskId, String type, String msg, Integer auditId);
}

