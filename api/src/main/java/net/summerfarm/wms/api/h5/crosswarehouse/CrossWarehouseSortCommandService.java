package net.summerfarm.wms.api.h5.crosswarehouse;

import net.summerfarm.wms.api.h5.crosswarehouse.dto.CrossWarehouseSortInfoDTO;
import net.summerfarm.wms.api.h5.crosswarehouse.req.AllocationCrossWarehouseSortInput;
import net.summerfarm.wms.api.h5.crosswarehouse.req.CrossWarehouseSortAfterSaleCommandInput;

import java.util.List;

/**
 * 越库分拣明细信息
 *
 * @author: dongcheng
 * @date: 2023/10/8
 */
public interface CrossWarehouseSortCommandService {

    /**
     * 批量保存越库分拣明细
     *
     * @param crossWarehouseSortInfoList 越库分拣明细保存内容
     */
    void saveBatch(List<CrossWarehouseSortInfoDTO> crossWarehouseSortInfoList);

    /**
     * 退单售后
     *
     * @param input 退单售后明细列表
     */
    void afterSale(CrossWarehouseSortAfterSaleCommandInput input);

    /**
     * 分配越库分拣明细
     *
     * @param input 分配条件内容
     */
    void allocationCrossWarehouseSort(AllocationCrossWarehouseSortInput input);
}
