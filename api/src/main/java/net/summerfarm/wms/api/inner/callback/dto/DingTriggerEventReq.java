package net.summerfarm.wms.api.inner.callback.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 钉钉审批回调对象
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DingTriggerEventReq {

    /**
     * 业务数据id
     */
    private Long bizId;

    /**
     * 审批人id(admin.id或者是钉钉用户id)
     */
    private String handlerUserId;

    /**
     * 备注
     */
    private String remark;
}
