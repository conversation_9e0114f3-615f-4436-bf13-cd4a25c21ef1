package net.summerfarm.wms.api.inner.sku.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetBatchInventoryDTO;
import net.summerfarm.wms.api.h5.inventory.dto.res.cabinetBatchInventory.CabinetInventoryDTO;
import net.summerfarm.wms.api.inner.batch.res.ProduceBatchResDTO;
import java.util.List;
import java.util.Objects;

/**
 * sku信息对象
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SkuResDTO {

    Long id;

    String sku;

    /**
     * 生产批次
     */
    List<ProduceBatchResDTO> produceBatches;

    /**
     * 商品名称
     */
    String pdName;

    /**
     * 规格
     */
    String specification;

    /**
     * 商品类目
     */
    String category;

    /**
     * 存储区域
     */
    String temperature;

    /**
     * 包装
     */
    String packaging;

    /**
     * 库存数量
     */
    Integer stockNum;

    /**
     * 库位库存
     */
    List<CabinetInventoryDTO> cabinetInventoryDTOList;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SkuResDTO skuBO = (SkuResDTO) o;
        return Objects.equals(sku, skuBO.sku);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sku);
    }
}
