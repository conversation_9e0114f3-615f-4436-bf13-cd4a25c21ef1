package net.summerfarm.wms.api.inner.instore.dto;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 入库单详情数据对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InBoundOrderDetailInnerResDTO implements Serializable {
    private static final long serialVersionUID = 6775879319778646966L;
    /**
     * 详情id
     */
    Long id;

    /**
     * sku
     */
    String sku;

    /**
     * 商品名称
     */
    String pdName;

    /**
     * 规格
     */
    String specification;

    /**
     * 存储区域
     */
    String temperature;

    /**
     * 包装
     */
    String packaging;

    /**
     * 采购号
     */
    String purchaseNo;

    /**
     * 供应商
     */
    String supplier;

    /**
     * 入库数量
     */
    Integer stockNum;

    /**
     * 生产日期
     */
    Long produceAt;

    /**
     * 保质期
     */
    Long shelfLife;

    /**
     * 打印次数
     */
    Integer printNum;
}
