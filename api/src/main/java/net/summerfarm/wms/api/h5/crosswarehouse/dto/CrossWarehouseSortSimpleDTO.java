package net.summerfarm.wms.api.h5.crosswarehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.common.constant.Global;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @author: dongcheng
 * @date: 2023/10/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CrossWarehouseSortSimpleDTO implements Serializable {
    private static final long serialVersionUID = -8393703797959237517L;

    /**
     * ofc批次号
     */
    private String psoNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * 履约单号
     */
    private String fulfillmentNo;

    /**
     * 获取唯一键值
     *
     * @return 返回唯一键值
     */
    public String getUniqueKey() {
        String tmpPsoNo = StringUtils.isEmpty(psoNo) ? "" : psoNo;
        String tmpSku = StringUtils.isEmpty(sku) ? "" : sku;
        String tmpSaleOrderNo = StringUtils.isEmpty(saleOrderNo) ? "" : saleOrderNo;
        String tmpFulfillmentNo = StringUtils.isEmpty(fulfillmentNo) ? "" : fulfillmentNo;
        return tmpPsoNo + Global.CROSS_BAR + tmpSku + Global.CROSS_BAR + tmpSaleOrderNo +  Global.CROSS_BAR + tmpFulfillmentNo;
    }
}
