package net.summerfarm.wms.api.h5.crosswarehouse.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分配越库分拣明细请求
 * @author: xdc
 * @date: 2024/3/14
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllocationCrossWarehouseSortInput implements Serializable {
    private static final long serialVersionUID = 78673532738249540L;

    /**
     * 采购供应单号
     */
    private String psoNo;

    /**
     * 分配信息
     */
    private List<AllocationCrossWarehouseSortDetailInput> sortDetailList;

}
