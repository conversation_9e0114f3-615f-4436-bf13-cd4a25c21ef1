package net.summerfarm.wms.api.inner.batch.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wms.api.inner.batch.req.CostBatchReqDTO;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/14  14:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProduceBatchResDTO {

    /**
     * * 生产批次id
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 保质期 时间戳
     */
    private Long shelfLife;

    /**
     * 生产期 时间戳
     */
    private Long produceAt;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 采购单号
     */
    String purchaseNo;

    /**
     * 成本批次信息
     */
    List<CostBatchReqDTO> costBatches;
}
