package net.summerfarm.wms.api.inner.batch.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.Md5Util;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Base64;

/**
 * <AUTHOR> ct
 * create at:  2022/11/22  15:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class BatchCreateByTaskResDTO {

    /**
     * bizId
     */
    private Long bizId;

    /**
     * sku
     */
    private String sku;

    /**
     * 成本批次id
     */
    private Long costBatchId;
    /**
     * 保质期 时间戳
     */
    private Long shelfLife;

    /**
     * 生产期 时间戳
     */
    private Long produceAt;

    /**
     * 采购批次号
     */
    private String purchaseNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 金额
     */
    private BigDecimal cost;

    /**
     * 市场金额
     */
    private BigDecimal marketCost;

    public String duplicateKeyOnTask() {
        try {
            String duplicateKey = sku + WmsConstant.SPLIT + purchaseNo + WmsConstant.SPLIT + produceAt + WmsConstant.SPLIT + shelfLife;
            byte[] bytes = Md5Util.bit16(duplicateKey);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.info("加密异常", e);
        }
        return "";
    }
}
