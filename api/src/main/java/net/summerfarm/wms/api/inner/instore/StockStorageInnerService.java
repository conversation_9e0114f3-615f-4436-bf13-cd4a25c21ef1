package net.summerfarm.wms.api.inner.instore;

import net.summerfarm.wms.api.h5.instore.dto.req.StockStorageCommandDTO;

/**
 * <AUTHOR> ct
 * create at:  2022/11/26  17:45
 */
public interface StockStorageInnerService {

    /**
     * @param stockStorageCommandDTO
     */
    Long createStockStorage(StockStorageCommandDTO stockStorageCommandDTO);

    /**
     * 关闭任务
     */
    void closeStock(Long stockTaskId);

    /**
     * 取消任务
     * @param stockTaskId 任务id
     */
    void cancelStock(Long stockTaskId);

    /**
     * 完成
     * @param stockTaskId
     */
    void finishStock(Long stockTaskId);

    /**
     * 更新任务信息
     */
    void updateStock(StockStorageCommandDTO stockStorageCommandDTO);
}
