package net.summerfarm.wms.api.h5.stocktaking;

import net.summerfarm.wms.api.h5.stocktaking.dto.req.*;
import net.summerfarm.wms.api.h5.stocktaking.dto.res.SubmitStockTakingResp;

import java.util.Map;

/**
 * 盘点任务服务
 *
 * <AUTHOR>
 * @date 2022/09/14
 */
public interface StocktakingCommandService {
    /**
     * 创建盘点任务
     *
     * @param stockTakingCreateCommand 请求对象
     * @return 任务id
     */
    long createStockTaking(StockTakingCreateCommand stockTakingCreateCommand);

    long createStockTakingForOnSale(StockTakingCreateCommand stockTakingCreateCommand);

    /**
     * 提交盘点任务
     *
     * @param stockTakingCommitCommand 请求对象
     */
    void commitStockTaking(StockTakingCommitCommand stockTakingCommitCommand);

    /**
     * 添加盘点批次
     *
     * @param stockTakingBatchCommand 请求对象
     */
    Long addStockTakingBatch(StockTakingBatchCommand stockTakingBatchCommand);

    /**
     * 删除盘点批次
     *
     * @param stockTakingBatchCommand 请求对象
     */
    void deleteStockTakingBatch(StockTakingBatchDeleteCommand stockTakingBatchCommand);

    /**
     * 提交盘点任务
     *
     * <AUTHOR>
     * @date 2023/6/29 13:57
     * @param stockTakingSubmitCommand 库存盘点提交请求对象
     * @return java.lang.Long 盘点任务编码
     */
    SubmitStockTakingResp submitStockTaking(StockTakingSubmitCommand stockTakingSubmitCommand);

    /**
     * 取消盘点任务
     * @param stockTakingCancelCommand 盘点任务取消请求对象
     */
    void cancelStockTaking(StockTakingCancelCommand stockTakingCancelCommand);

    /**
     * 根据外部回告数据回写盘点明细
     * @param stockTakingCallbackCommand
     */
    void supplyStockTakingItemDetailByExternalCallback(StockTakingCallbackCommand stockTakingCallbackCommand);

    /**
     * 下发外部盘点通知
     * @param stockTakingId
     */
    void exeExternalStockTakingNotice(Long stockTakingId);

    /**
     * 下发外部盘点提交
     * @param stockTakingId
     */
    void exeExternalStockTakingCommit(Long stockTakingId);

    /**
     * 手动取消盘点单
     * @param stockTakingCancelManualCommand
     */
    void manualCancelStockTaking(StockTakingCancelManualCommand stockTakingCancelManualCommand);
}
