package net.summerfarm.wms.api.inner.skucode;

import net.summerfarm.wms.api.inner.skucode.dto.SkuBatchCodeReqDTO;

/**
 * <AUTHOR> ct
 * create at:  2021/12/13  15:16
 */
public interface SkuBatchCodeInnerService {
    /**
     * 生成条码
     * @param skuBatchCode
     */
    void createBatchCode(SkuBatchCodeReqDTO skuBatchCode);

    /**
     * 更新批次码
     */
    void updateBatchCode(SkuBatchCodeReqDTO skuBatchCode);

    /**
     * 生成条码，代理
     * @param skuCode
     * @param warehouseNo
     * @param purchaseNo
     * @param produceAt
     * @param shelfLife
     */
    void createBatchCodeForProxy(String skuCode, Long warehouseNo, String purchaseNo,
                                 Long produceAt, Long shelfLife);

    /**
     * 初始化降级批次、临保批次的批次类型
     */
    void initSkuCodeBatchType(boolean dryRun);
}
