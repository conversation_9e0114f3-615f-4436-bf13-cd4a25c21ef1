package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductQueryResDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.summerfarm.wms.domain.products.domainobject.Product;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 加工任务成品明细分页查询响应对象转换类
 * <AUTHOR>
 * @date 2023/02/15
 */
public interface ProcessingTaskProductQueryResDTOConverter {

    public static ProcessingTaskProductQueryResDTO convert(ProcessingTaskProduct processingTaskProduct,
                                                           Map<String, Product> productSpuMap) {
        if ( processingTaskProduct == null ) {
            return null;
        }

        Product product = productSpuMap.get(processingTaskProduct.getProductSkuCode());

        ProcessingTaskProductQueryResDTO processingTaskProductQueryResDTO = new ProcessingTaskProductQueryResDTO();

        if ( processingTaskProduct.getUpdateTime() != null ) {
            processingTaskProductQueryResDTO.setUpdateTime( new SimpleDateFormat( "yyyy-MM-dd" ).format( processingTaskProduct.getUpdateTime() ) );
        }
        processingTaskProductQueryResDTO.setMaterialSkuQuantity( processingTaskProduct.getMaterialSkuReceiveQuantity() );
        if ( processingTaskProduct.getCreateTime() != null ) {
            processingTaskProductQueryResDTO.setCreateTime( new SimpleDateFormat( "yyyy-MM-dd" ).format( processingTaskProduct.getCreateTime() ) );
        }
        processingTaskProductQueryResDTO.setMaterialFinishWeight( processingTaskProduct.getMaterialSkuReceiveWeight() );
        processingTaskProductQueryResDTO.setId( processingTaskProduct.getId() );
        processingTaskProductQueryResDTO.setWarehouseNo( processingTaskProduct.getWarehouseNo() );
        processingTaskProductQueryResDTO.setProcessingTaskCode( processingTaskProduct.getProcessingTaskCode() );
        processingTaskProductQueryResDTO.setProductSkuCode( processingTaskProduct.getProductSkuCode() );
        processingTaskProductQueryResDTO.setProductSkuSaasId(product == null ? null : product.getSaasSkuId());
        processingTaskProductQueryResDTO.setProductSkuName( processingTaskProduct.getProductSkuName() );
        processingTaskProductQueryResDTO.setProductSkuWeight( processingTaskProduct.getProductSkuWeight() );
        processingTaskProductQueryResDTO.setProductSkuUnit( processingTaskProduct.getProductSkuUnit() );
        processingTaskProductQueryResDTO.setProductSkuUnitDesc( processingTaskProduct.getProductSkuUnitDesc() );
        processingTaskProductQueryResDTO.setProductSkuNeedQuantity( processingTaskProduct.getProductSkuNeedQuantity() );
        processingTaskProductQueryResDTO.setProductSkuFinishQuantity( processingTaskProduct.getProductSkuFinishQuantity() );
        processingTaskProductQueryResDTO.setMaterialSkuCode( processingTaskProduct.getMaterialSkuCode() );
        processingTaskProductQueryResDTO.setMaterialSkuName( processingTaskProduct.getMaterialSkuName() );
        processingTaskProductQueryResDTO.setMaterialSkuWeight( processingTaskProduct.getMaterialSkuWeight() );
        processingTaskProductQueryResDTO.setMaterialSkuUnit( processingTaskProduct.getMaterialSkuUnit() );
        processingTaskProductQueryResDTO.setMaterialSkuReceiveQuantity( processingTaskProduct.getMaterialSkuReceiveQuantity() );
        processingTaskProductQueryResDTO.setMaterialSkuReceiveWeight( processingTaskProduct.getMaterialSkuReceiveWeight() );
        processingTaskProductQueryResDTO.setProductSkuSpecFinishWeight( processingTaskProduct.getProductSkuSpecFinishWeight() );
        processingTaskProductQueryResDTO.setProductSkuSpecFinishQuantity(
                (processingTaskProduct.getProductSkuWeight() == null ||
                        processingTaskProduct.getProductSkuWeight().compareTo(BigDecimal.ZERO) <= 0) ? 0 :
                processingTaskProduct.getProductSkuSpecFinishWeight().divide(
                        processingTaskProduct.getProductSkuWeight(), 0, RoundingMode.HALF_DOWN)
                        .intValue()
        );
        processingTaskProductQueryResDTO.setSpecLossWeight( processingTaskProduct.getSpecLossWeight() );
        processingTaskProductQueryResDTO.setWasteLossWeight( processingTaskProduct.getWasteLossWeight() );
        processingTaskProductQueryResDTO.setMaterialSkuRestoreQuantity( processingTaskProduct.getMaterialSkuRestoreQuantity() );
        processingTaskProductQueryResDTO.setCreator( processingTaskProduct.getCreator() );
        processingTaskProductQueryResDTO.setUpdater( processingTaskProduct.getUpdater() );
        processingTaskProductQueryResDTO.setDeleteFlag( processingTaskProduct.getDeleteFlag() );
        processingTaskProductQueryResDTO.setStatus( processingTaskProduct.getStatus() );
        processingTaskProductQueryResDTO.setMaterialSkuUnitDesc( processingTaskProduct.getMaterialSkuUnitDesc() );
        processingTaskProductQueryResDTO.setFinishRemark( processingTaskProduct.getFinishRemark() );
        processingTaskProductQueryResDTO.setMaterialModel( processingTaskProduct.getMaterialModel() );

        processingTaskProductQueryResDTO.setProductSkuRatioNum(processingTaskProduct.getProductSkuRatioNum());

        return processingTaskProductQueryResDTO;
    }

    public static List<ProcessingTaskProductQueryResDTO> convertList(List<ProcessingTaskProduct> processingTaskProductList, Map<String, Product> productSpuMap) {
        if ( processingTaskProductList == null ) {
            return null;
        }

        List<ProcessingTaskProductQueryResDTO> list = new ArrayList<ProcessingTaskProductQueryResDTO>( processingTaskProductList.size() );
        for ( ProcessingTaskProduct processingTaskProduct : processingTaskProductList ) {
            list.add( convert( processingTaskProduct, productSpuMap ) );
        }

        return list;
    }
}
