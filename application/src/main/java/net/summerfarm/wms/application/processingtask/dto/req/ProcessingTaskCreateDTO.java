package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class ProcessingTaskCreateDTO implements Serializable {

    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 库存仓编号
     */
    @NotNull(message = "仓库号不能为空")
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    private Integer type;

    /**
     * 加工任务成品
     */
    private List<ProcessingTaskProductDTO> processingTaskProductDTOList;


    /**
     * 加工任务成品明细
     */
    private List<ProcessingTaskProductSpecDTO> processingTaskProductSpecDTOList;


    /**
     * 加工任务成品订单明细
     */
    private List<ProcessingTaskProductSpecOrderDTO> processingTaskProductSpecOrderDTOList;



}
