package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessingTaskMaterialRestoreReqDTO implements Serializable {

    /**
     * 原料领料Id
     */
    private Long materialReceiveId;

    /**
     * 原料SKU
     */
    private String materialSkuCode;

    /**
     * 加工任务原料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 加工任务成品ID
     */
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 原料重量返还
     */
    private BigDecimal materialSkuRestoreWeight;
    /**
     * 原料数量返还
     */
    private Integer materialSkuRestoreQuantity;
}
