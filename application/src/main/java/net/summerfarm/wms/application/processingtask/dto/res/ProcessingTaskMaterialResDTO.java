package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProcessingTaskMaterialResDTO implements Serializable {

    /**
     * 物料领用记录
     */
    private List<ProcessingTaskMaterialReceiveResDTO> materialReceiveList;

    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 加工任务成品id
     */
    private Long processingTaskProductId;
    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 原料sku编号
     */
    private String materialSkuCode;
    /**
     * 原料skuId
     */
    private Long materialSkuSaasId;
    /**
     * 原料sku名称
     */
    private String materialSkuName;
    /**
     * 原料sku重量
     */
    private BigDecimal materialSkuWeight;
    /**
     * 原料sku单位
     */
    private String materialSkuUnit;
    /**
     * 原料sku使用数量
     */
    private Integer materialSkuQuantity;
    /**
     * 原料sku领料数量
     */
    private Integer materialSkuReceiveQuantity;
    /**
     * 原料sku领料重量
     */
    private BigDecimal materialSkuReceiveWeight;
    /**
     * 原料sku领料重量
     */
    private BigDecimal materialSkuTotalReceiveWeight;
    /**
     * 原料sku归还数量
     */
    private Integer materialSkuRestoreQuantity;
    /**
     * 废料损耗重量，人工填写
     */
    private BigDecimal wasteLossWeight;
    /**
     * 规格损耗重量，加工实重-加工数量*规格重量
     */
    private BigDecimal specLossWeight;
    /**
     * 原料SKU剩余重量，领料总重-加工实重-废料损耗
     */
    private BigDecimal materialSkuRemainWeight;
    /**
     * 原料sku单位描述-规格
     */
    private String materialSkuUnitDesc;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 原料sku比例数量
     */
    private Integer materialSkuRatioNum;

    /**
     * 原料sku还需数量 = 建议数量 - 已使用数量
     */
    private Integer materialSkuSuggestQuantity;

    /**
     * 原料sku已使用数量
     */
    private Integer materialSkuUsedQuantity;

    /**
     * 原料sku建议数量
     */
    private Integer materialSkuNeedQuantity;
}
