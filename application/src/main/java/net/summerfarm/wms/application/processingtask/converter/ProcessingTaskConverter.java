package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskQueryReqDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTask;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 加工任务entity转换类
 * <AUTHOR>
 * @date 2023/02/22
 */
@Mapper
public interface ProcessingTaskConverter {

    ProcessingTaskConverter INSTANCE = Mappers.getMapper(ProcessingTaskConverter.class);

    /**
     * req -> entity
     * @param reqDTO req
     * @return entity
     */
    ProcessingTask convert(ProcessingTaskQueryReqDTO reqDTO);
}
