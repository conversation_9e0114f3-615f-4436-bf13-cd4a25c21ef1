package net.summerfarm.wms.application.processingtask;

import com.aliyun.openservices.shade.org.apache.commons.lang3.math.NumberUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BigDecimalUtils;
import net.summerfarm.wms.annotation.OperatorAnnotation;
import net.summerfarm.wms.application.batch.service.SkuBatchCodeQueryService;
import net.summerfarm.wms.application.processingtask.dto.req.*;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductFinishCheckResDTO;
import net.summerfarm.wms.application.processingtask.service.ProcessingTaskProductCommandService;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductPrintResDTO;
import net.summerfarm.wms.application.processingtask.factory.ProcessingTaskProductFinishAggregateFactory;
import net.summerfarm.wms.application.processingtask.factory.ProcessingTaskProductSubmitAggregateFactory;
import net.summerfarm.wms.application.sku.enums.StorageLocation;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.IntegerUtil;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.batch.domainobject.CostBatch;
import net.summerfarm.wms.domain.batch.repository.CostBatchRepository;
import net.summerfarm.wms.domain.config.repository.ConfigRepository;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingTaskProductDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductFinishAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductSubmitAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskProductStatusEnum;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskTypeEnum;
import net.summerfarm.wms.domain.processingtask.repository.*;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.prove.domainobject.Prove;
import net.summerfarm.wms.domain.prove.repository.ProveQueryRepository;
import net.summerfarm.wms.domain.skucode.SkuBatchCodeRepository;
import net.summerfarm.wms.domain.skucode.domainobject.FindSkuCodeCd;
import net.summerfarm.wms.domain.skucode.domainobject.SkuBatchCode;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProcessingTaskProductCommandServiceImpl implements ProcessingTaskProductCommandService {

    @Autowired
    private ProcessingTaskProductDomainService processingTaskProductDomainService;

    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProcessingTaskProductRepository taskProductRepository;
    @Autowired
    private ProcessingTaskProductRecordRepository productRecordRepository;
    @Autowired
    private ProcessingTaskProductOrderRecordRepository productOrderRecordRepository;
    @Autowired
    private ProcessingTaskProductSubmitRecordRepository processingTaskProductSubmitRecordRepository;

    @Autowired
    private ProcessingTaskMaterialRepository taskMaterialRepository;
    @Autowired
    private ProcessingTaskMaterialReceiveRecordRepository processingTaskMaterialReceiveRecordRepository;

    @Autowired
    private SkuBatchCodeRepository skuBatchCodeRepository;
    @Autowired
    private ProductRepository productsRepository;
    @Autowired
    private CostBatchRepository costBatchRepository;
    @Autowired
    private ProveQueryRepository proveQueryRepository;
    @Autowired
    private ConfigRepository configRepository;

    @Autowired
    private SkuBatchCodeQueryService skuBatchCodeQueryService;

    private static final Integer MAX_PRINT_NUMBER = 99999;

    private static final String DEFAULT_ONLY_CODE = "00000";

    private static final String CODE_URL = "https://admin.summerfarm.net/dist/index.html?code=";

    @OperatorAnnotation
    @Override
    public CommonResult<Long> productSubmit(ProcessingTaskProductSubmitReqDTO reqDTO) {
        if (reqDTO == null || CollectionUtils.isEmpty(reqDTO.getProcessingSpecList()) ||
            reqDTO.getProcessingTaskCode() == null || reqDTO.getProcessingSpecList() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品提交请求参数缺失",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        reqDTO.setTenantId(LoginInfoThreadLocal.getTenantId());

        // 加工任务成品
        ProcessingTaskProduct processingTaskProduct = taskProductRepository.queryById(
                reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品不存在",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        if (ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品已加工完成",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(processingTaskProduct.getProcessingTaskCode());
        if (processingTask == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到加工任务",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 加工任务原料
        List<ProcessingTaskMaterial> materialList = taskMaterialRepository
                .listByProductId(processingTaskProduct.getProcessingTaskCode(),
                        processingTaskProduct.getId());
        // 未领料
        if (materialList.stream().anyMatch(s -> s.getMaterialSkuReceiveQuantity() <= 0)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "还未领料，请先完成领料",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 加工任务规格明细
        List<Long> specIdList = reqDTO.getProcessingSpecList().stream()
                        .map(ProcessingTaskProductSpecReqDTO::getProcessingTaskProductSpecId)
                        .distinct().collect(Collectors.toList());
        Map<Long, ProcessingTaskProductRecord> processingTaskProductRecordMap = productRecordRepository
                .mapByIdList(specIdList);

        // 提交数量校验
        Integer currentSubmitTotalQuantity = 0;
        for (ProcessingTaskProductSpecReqDTO specReqDTO : reqDTO.getProcessingSpecList()) {
            ProcessingTaskProductRecord productRecord = processingTaskProductRecordMap.get(
                    specReqDTO.getProcessingTaskProductSpecId());
            if (specReqDTO.getSubmitQuantity() > productRecord.getProductSkuSpecNeedQuantity() -
                    productRecord.getProductSkuSpecSubmitQuantity()){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品提交的数量超过剩余数量",
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            currentSubmitTotalQuantity = currentSubmitTotalQuantity + specReqDTO.getSubmitQuantity() *
                    productRecord.getProductSkuSpecWeight().divide(
                            processingTaskProduct.getProductSkuWeight(), 0, RoundingMode.HALF_DOWN).intValue();
        }
        // 商品组装-成品提交数量必须是加工比例的倍数
        if (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) &&
                currentSubmitTotalQuantity % processingTaskProduct.getProductSkuRatioNum() != 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "商品组装，成品提交的数量必须是加工比例的倍数",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 提交成品数量
        Integer commitProductNum = reqDTO.getProcessingSpecList().stream()
                .map(ProcessingTaskProductSpecReqDTO::getProductInventoryList)
                .flatMap(Collection::stream)
                .map(ProcessingTaskProductInventoryReqDTO::getSubmitQuantity)
                .reduce(Integer::sum)
                .orElse(0);
        if (commitProductNum <= 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品提交的数量不能为0",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 原料领取记录
        Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap =
                processingTaskMaterialReceiveRecordRepository.mapByProductIdsGroupMaterialId(
                        Arrays.asList(reqDTO.getProcessingTaskProductId()));

        // 判断提交成品数量是否超过原料上限
        for (ProcessingTaskMaterial material : materialList) {
            List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList =
                    materialReceiveRecordMap.get(material.getId());
            if (CollectionUtils.isEmpty(materialReceiveRecordList)){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未找到对应的物料领取记录:" + material.getMaterialSkuName(),
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            // 总领料数量
            Integer totalReceiveQuantity = materialReceiveRecordList.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuReceiveQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);

            // 总归还数量
            Integer totalRestoreQuantity = materialReceiveRecordList.stream()
                    .filter(s -> s.getMaterialSkuRestoreQuantity() != null)
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuRestoreQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);
            // 总废料损耗
            BigDecimal totalWasteLossWeight = materialReceiveRecordList.stream()
                    .filter(s -> s.getWasteLossWeight()!= null)
                    .map(ProcessingTaskMaterialReceiveRecord::getWasteLossWeight)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);

            // 可用原料重量 = 总领用 - 总归还- 总规格损耗
            BigDecimal ableMaterialWeight = BigDecimal.valueOf(totalReceiveQuantity - totalRestoreQuantity)
                    .multiply(material.getMaterialSkuWeight())
                    .subtract(totalWasteLossWeight);

            // 最大可用成品数量 = 可用原料重量 / 成品比例 * 原料比例
            Integer maxProductNum = ableMaterialWeight
                    .multiply(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()))
                    .divide(material.getMaterialSkuWeight(), 2, RoundingMode.DOWN)
                    .divide(BigDecimal.valueOf(material.getMaterialSkuRatioNum()), 0, RoundingMode.DOWN)
                    .intValue();

            // 成品已提交数量
            Integer committedProductNum = processingTaskProduct.getProductSkuFinishQuantity();
            if (currentSubmitTotalQuantity > maxProductNum - committedProductNum){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        material.getMaterialSkuName() +
                                "剩余的原料不足当前提交的成品数量，当前最大可提交成品数量:" + (maxProductNum - committedProductNum),
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
        }

        // 原料最新四证
        Prove materialProve = null;
        if (materialList.size() == 1) {
            String maxMaterialSkuPurchaseBatch = materialReceiveRecordMap.get(materialList.get(0).getId())
                    .stream()
                    .filter(materialReceiveRecord -> materialReceiveRecord.getMaterialSkuQuantity() > 0)
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuPurchaseBatch)
                    .max(String::compareTo)
                    .orElse(null);
            materialProve = proveQueryRepository.findBatchProve(
                    materialList.get(0).getMaterialSkuCode(), maxMaterialSkuPurchaseBatch);
        }

        // 加工任务订单规格明细
        List<ProcessingTaskProductOrderRecord> processingTaskProductRecordList = productOrderRecordRepository
                .listByProductId(reqDTO.getProcessingTaskProductId());
        processingTaskProductRecordList = Optional.ofNullable(processingTaskProductRecordList).orElse(Lists.newArrayList());

        // 获取成品成本
        BigDecimal productAvlBatchCost = getProductAvlBatchCost(processingTaskProduct, materialList,
                materialReceiveRecordMap);

        // 组装聚合根
        ProcessingTaskProductSubmitAggregate submitAggregate = ProcessingTaskProductSubmitAggregateFactory
                .newInstance(reqDTO, processingTaskProduct,
                        processingTaskProductRecordMap, processingTaskProductRecordList,
                        productAvlBatchCost, commitProductNum, materialProve);

        processingTaskProductDomainService.productSubmit(submitAggregate);
        return CommonResult.ok(1L);
    }

    /**
     * 获取成品成本
     * @param processingTaskProduct
     * @param materialList
     * @param materialReceiveRecordMap
     * @return
     */
    @NotNull
    private BigDecimal getProductAvlBatchCost(ProcessingTaskProduct processingTaskProduct,
                                              List<ProcessingTaskMaterial> materialList,
                                              Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap) {
        // 计算 sum(原料比例*原料成本)/sum(原料比例)
        BigDecimal productCost = BigDecimal.ZERO;

        for (ProcessingTaskMaterial processingTaskMaterial : materialList) {
            List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList =
                    materialReceiveRecordMap.get(processingTaskMaterial.getId());

            // 原料领料数量
            Map<String, Integer> receiveQuantityMap = materialReceiveRecordList.stream()
                    .collect(Collectors.toMap(
                            s -> s.getWarehouseNo() + "_" +
                            s.getMaterialSkuCode() + "_" +
                            s.getMaterialSkuPurchaseBatch() + "_" +
                            DateUtil.formatYmdDate(s.getMaterialSkuProductionDate()) + "_" +
                            DateUtil.formatYmdDate(s.getMaterialSkuQualityDate()) + "_",
                            s -> s.getMaterialSkuReceiveQuantity()
                                    - IntegerUtil.getValueDefault0(s.getMaterialSkuRestoreQuantity())
                                    - BigDecimalUtils.defaultValue(s.getWasteLossWeight(), BigDecimal.ZERO).divide(
                                            s.getMaterialSkuWeight(), 0, RoundingMode.HALF_DOWN).intValue(),
                            Integer::sum));

            // 原料领料所有数量
            Integer receiveQuantityAll = receiveQuantityMap.values().stream()
                    .reduce(Integer::sum)
                    .orElse(0);
            if (receiveQuantityAll == 0){
                log.warn("批次成本数量为0，物料：{}", processingTaskMaterial.getMaterialSkuCode());
                continue;
            }

            // 获取批次成本
            List<String> skuCodeList = materialReceiveRecordList.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuCode)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> purchaseBatchList = materialReceiveRecordList.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuPurchaseBatch)
                    .distinct()
                    .collect(Collectors.toList());
            List<LocalDate> productionDateList = materialReceiveRecordList.stream()
                    .map(s -> DateUtil.toLocalDate(s.getMaterialSkuProductionDate()))
                    .distinct()
                    .collect(Collectors.toList());
            List<LocalDate> qualityDateList = materialReceiveRecordList.stream()
                    .map(s -> DateUtil.toLocalDate(s.getMaterialSkuQualityDate()))
                    .distinct()
                    .collect(Collectors.toList());
            List<CostBatch> costBatchList = costBatchRepository.queryCostBatchByUnique(
                    Collections.singletonList(processingTaskProduct.getWarehouseNo()), skuCodeList,
                    purchaseBatchList, productionDateList, qualityDateList);
            costBatchList = costBatchList.stream()
                    .filter(s -> receiveQuantityMap.get(
                            s.getWarehouseNo() + "_" +
                                    s.getSku() + "_" +
                                    s.getPurchaseNo() + "_" +
                                    DateUtil.formatYmdDate(s.getProductionDate()) + "_" +
                                    DateUtil.formatYmdDate(s.getQualityDate()) + "_"
                    ) != null)
                    .filter(s -> s.getCost() != null && BigDecimal.ZERO.compareTo(s.getCost()) < 0)
                    .collect(Collectors.toList());
            Map<String, List<CostBatch>> costBatchMap = costBatchList.stream()
                    .collect(Collectors.groupingBy(CostBatch::getSku));

            List<CostBatch> costBatchListTmp = costBatchMap.get(processingTaskMaterial.getMaterialSkuCode());
            if (CollectionUtils.isEmpty(costBatchListTmp)){
                log.warn("批次成本不存在，物料：{}", processingTaskMaterial.getMaterialSkuCode());
                continue;
            }

            // 相同成本的只取一条
            Set<String> uniqueBatchCostSet = new HashSet<>();
            costBatchListTmp = costBatchListTmp.stream()
                   .filter(s -> {
                       String uniqueBatchCost = s.getWarehouseNo() + "_" +
                               s.getSku() + "_" +
                               s.getPurchaseNo() + "_" +
                               DateUtil.formatYmdDate(s.getProductionDate()) + "_" +
                               DateUtil.formatYmdDate(s.getQualityDate()) + "_" +
                               s.getCost();
                       boolean result = !uniqueBatchCostSet.contains(uniqueBatchCost);
                       uniqueBatchCostSet.add(uniqueBatchCost);
                       return  result;
                   })
                   .collect(Collectors.toList());

            // 原料成本
            BigDecimal maritalBatchCostAll = costBatchListTmp.stream()
                    .map(s -> {
                        Integer receiveQuantity = receiveQuantityMap.get(
                                s.getWarehouseNo() + "_" +
                                        s.getSku() + "_" +
                                        s.getPurchaseNo() + "_" +
                                        DateUtil.formatYmdDate(s.getProductionDate()) + "_" +
                                        DateUtil.formatYmdDate(s.getQualityDate()) + "_"
                        );
                        return s.getCost().multiply(BigDecimal.valueOf(receiveQuantity));
                    })
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            // 平均成本 = 每一成本 * 数量 / 所有总数
            BigDecimal maritalAvlBatchCost = maritalBatchCostAll.divide(
                    BigDecimal.valueOf(receiveQuantityAll), 8, RoundingMode.HALF_DOWN);

            // 成本 = 原料成本 * 原料比例 /成品比例
            productCost = productCost.add(maritalAvlBatchCost.multiply(
                    BigDecimal.valueOf(processingTaskMaterial.getMaterialSkuRatioNum())
                            .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 8, RoundingMode.UP)));
        }

        return productCost;
    }


    @Override
    public CommonResult<Long> productFinish(ProcessingTaskProductFinishReqDTO reqDTO) {
        if (reqDTO == null || reqDTO.getProcessingTaskProductId() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品提交请求参数缺失",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 加工任务成品
        ProcessingTaskProduct processingTaskProduct = taskProductRepository.queryById(
                reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品不存在",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        if (ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品已加工完成",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 查询加工任务
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(
                processingTaskProduct.getProcessingTaskCode());

        // 加工任务原料
        List<ProcessingTaskMaterial> materialList = taskMaterialRepository.listByProductId(
                processingTaskProduct.getProcessingTaskCode(), processingTaskProduct.getId()
        );

        // 原料领用列表
        Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap =
            processingTaskMaterialReceiveRecordRepository.mapByProductIdsGroupMaterialId(
                Arrays.asList(processingTaskProduct.getId()));
        // 原料校验 废料损耗
        if (materialReceiveRecordMap.values().stream()
                .flatMap(Collection::stream)
                .anyMatch(materialReceiveRecord ->
                                materialReceiveRecord.getWasteLossWeight() == null
                )){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "废料损耗还有未输入情况，请先进行填写",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 原料重量需要大于加工成品重量
        for (ProcessingTaskMaterial processingTaskMaterial : materialList) {
            // 所需原料
            BigDecimal totalMaterialNeedWeight = BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity())
                    .multiply(BigDecimal.valueOf(processingTaskMaterial.getMaterialSkuRatioNum()))
                    .multiply(processingTaskMaterial.getMaterialSkuWeight())
                    .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 2, RoundingMode.UP);
            Integer totalMaterialNeedQuantity = BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity())
                    .multiply(BigDecimal.valueOf(processingTaskMaterial.getMaterialSkuRatioNum()))
                    .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 0, RoundingMode.UP)
                    .intValue();

            // 使用原料
            Integer usedMaterialQuantity = processingTaskMaterial.getMaterialSkuReceiveQuantity() -
                    processingTaskMaterial.getMaterialSkuRestoreQuantity();
            BigDecimal usedMaterialWeight = BigDecimal.valueOf(usedMaterialQuantity).multiply(
                    processingTaskMaterial.getMaterialSkuWeight());

            // 可用重量 = 总领料重量 - 总归还重量 - 总废料损耗
            BigDecimal ableMaterialWeight =  usedMaterialWeight.subtract(processingTaskMaterial.getWasteLossWeight());

            // 成品用料比原料用料多
            if (totalMaterialNeedWeight.compareTo(ableMaterialWeight) > 0) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "成品完成数量大于领料数量，请确定领料操作是否完成，" + processingTaskMaterial.getMaterialSkuName() +
                                "，领料数量" + usedMaterialQuantity + "，所需数量:" + totalMaterialNeedQuantity +
                                "，还需领料:" + (totalMaterialNeedQuantity - usedMaterialQuantity) ,
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            // 组装需要恒等
            if (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType())){
                if (totalMaterialNeedWeight.compareTo(ableMaterialWeight) != 0) {
                    return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                            "领料数量过多，请进行归还，" + processingTaskMaterial.getMaterialSkuName() +
                                    "，领料数量:" + usedMaterialQuantity + "，所需数量:" + totalMaterialNeedQuantity +
                                    "，需要归还数量:" + (usedMaterialQuantity - totalMaterialNeedQuantity) ,
                            String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
                }
            }

            // 未领料
            if (processingTaskProduct.getProductSkuFinishQuantity() == 0 &&
                    !processingTaskMaterial.getMaterialSkuReceiveQuantity().equals(
                            processingTaskMaterial.getMaterialSkuRestoreQuantity())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "未加工成品，领用的原料请全部归还，" + processingTaskMaterial.getMaterialSkuName(),
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
        }

        ProcessingTaskProductFinishAggregate finishAggregate = ProcessingTaskProductFinishAggregateFactory
                .newInstance(reqDTO, processingTaskProduct, processingTask, materialList, materialReceiveRecordMap);

        processingTaskProductDomainService.productFinish(finishAggregate);

        return CommonResult.ok(1L);
    }

    @Override
    public CommonResult<ProcessingTaskProductFinishCheckResDTO> productFinishCheck(ProcessingTaskProductFinishReqDTO reqDTO) {
        if (reqDTO == null || reqDTO.getProcessingTaskProductId() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品提交请求参数缺失",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 加工任务成品
        ProcessingTaskProduct processingTaskProduct = taskProductRepository.queryById(
                reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品不存在",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        if (ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品已加工完成",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 查询加工任务
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(
                processingTaskProduct.getProcessingTaskCode());

        // 加工任务原料
        List<ProcessingTaskMaterial> materialList = taskMaterialRepository.listByProductId(
                processingTaskProduct.getProcessingTaskCode(), processingTaskProduct.getId()
        );

        // 原料领用列表
        Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap =
                processingTaskMaterialReceiveRecordRepository.mapByProductIdsGroupMaterialId(
                        Arrays.asList(processingTaskProduct.getId()));
        // 原料校验 废料损耗
        if (materialReceiveRecordMap.values().stream()
                .flatMap(Collection::stream)
                .anyMatch(materialReceiveRecord ->
                        materialReceiveRecord.getWasteLossWeight() == null
                )){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "废料损耗还有未输入情况，请先进行填写",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 原料重量需要大于加工成品重量
        for (ProcessingTaskMaterial processingTaskMaterial : materialList) {
            // 成品已加工所需原料数量
            BigDecimal totalMaterialNeedQuantityDecimal = BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity())
                    .multiply(BigDecimal.valueOf(processingTaskMaterial.getMaterialSkuRatioNum()))
                    .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 2, RoundingMode.UP);
            // 0位小数向上取整
            Integer needMaterialNum = totalMaterialNeedQuantityDecimal.setScale(0,
                    RoundingMode.UP).intValue();

            Integer wastLossQuantity = processingTaskMaterial.getWasteLossWeight()
                    .divide(processingTaskMaterial.getMaterialSkuWeight(), 2, RoundingMode.HALF_DOWN)
                    .intValue();
            Integer usedMaterialQuantity = processingTaskMaterial.getMaterialSkuReceiveQuantity() -
                    processingTaskMaterial.getMaterialSkuRestoreQuantity() - wastLossQuantity;

            if (needMaterialNum.compareTo(usedMaterialQuantity) > 0) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "成品完成数量大于领料数量，请确定领料操作是否完成，" + processingTaskMaterial.getMaterialSkuName() +
                                "，领料数量" + usedMaterialQuantity + "，所需数量:" + needMaterialNum +
                                "，还需领料:" + (needMaterialNum - usedMaterialQuantity) ,
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            if (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType())){
                if (needMaterialNum.compareTo(usedMaterialQuantity) != 0) {
                    return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                            "领料数量过多，请进行归还，" + processingTaskMaterial.getMaterialSkuName() +
                                    "，领料数量:" + usedMaterialQuantity + "，所需数量:" + needMaterialNum +
                                    "，需要归还数量:" + (usedMaterialQuantity - needMaterialNum) ,
                            String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
                }
            }

            if (processingTaskProduct.getProductSkuFinishQuantity() == 0 &&
                    !processingTaskMaterial.getMaterialSkuReceiveQuantity().equals(
                            processingTaskMaterial.getMaterialSkuRestoreQuantity())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "未加工成品，领用的原料请全部归还，" + processingTaskMaterial.getMaterialSkuName(),
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            if (needMaterialNum != 0 && BigDecimal.valueOf(usedMaterialQuantity)
                    .divide(BigDecimal.valueOf(needMaterialNum), 2, RoundingMode.HALF_DOWN)
                    .compareTo(BigDecimal.valueOf(1.03)) >= 0){
                return CommonResult.ok(ProcessingTaskProductFinishCheckResDTO.fail(
                        "原料剩余超过3%，若不操作原料返还，剩余原料将判定为规格损耗"));
            }
        }

        return CommonResult.ok(ProcessingTaskProductFinishCheckResDTO.success());
    }

    /**
     * 加工任务成品明细打印
     * @param reqDTO 请求对象
     * @return 响应对象
     */
    @Override
    public CommonResult<List<ProcessingTaskProductPrintResDTO>> print(ProcessingTaskProductPrintReqDTO reqDTO) {
        // 查询加工任务成品
        ProcessingTaskProduct processingTaskProduct = taskProductRepository.queryById(reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到当前加工任务成品",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(
                processingTaskProduct.getProcessingTaskCode());
        if (processingTask == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到当前加工任务",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 查询加工任务成品打印明细
        ProcessingTaskProductRecord processingTaskProductRecord = productRecordRepository.queryById(reqDTO.getProcessingTaskProductSpecId());
        // 查询当前加工任务成品提交明细
        List<ProcessingTaskProductSubmitRecord> recordList = processingTaskProductSubmitRecordRepository
                .queryAllByProcessingTaskProductId(reqDTO.getProcessingTaskProductId());
        if (CollectionUtils.isEmpty(recordList)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到当前加工任务成品提交记录",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 匹配加工任务成品明细和加工任务成品提交明细中加工规格相同的条目
        ProcessingTaskProductSubmitRecord record = null;
        for (ProcessingTaskProductSubmitRecord recordValue : recordList) {
            String key = processingTaskProductRecord.getProductSkuSpecWeight()
                    + processingTaskProductRecord.getProductSkuSpecUnit();
            String target = recordValue.getProductSkuSpecWeight() + recordValue.getProductSkuSpecUnit();
            if (target.equals(key)) {
                record = recordValue;
                break;
            }
        }
        // 没有匹配值
        if (Objects.isNull(record)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到当前加工任务成品明细的提交记录",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 查询当前成品的批次信息
        FindSkuCodeCd query = new FindSkuCodeCd();
        query.setSku(record.getProductSkuCode());
        query.setPurchaseNo(record.getProductSkuPurchaseBatch());
        query.setProductionDate(DateUtil.toLocalDate(record.getProductSkuProductionDate()));
        List<SkuBatchCode> skuBatchCodes = skuBatchCodeRepository.querySkuBatchCodeByCd(query);
        if (CollectionUtils.isEmpty(skuBatchCodes)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未查询到当前成品的批次码信息",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        SkuBatchCode skuBatchCode = skuBatchCodes.get(0);
        // 获取入库时间
        String day = Objects.nonNull(record.getCreateTime()) ?
                String.valueOf(DateUtil.toLocalDate(record.getCreateTime()).getDayOfMonth()) : "";
        // 校验打印数量
        Integer printNumber = processingTaskProductRecord.getProductSkuSpecPrintNumber();
        if (printNumber + reqDTO.getCanPrintNumber() > MAX_PRINT_NUMBER) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "超过最大打印次数",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 查询SKU信息
        Product productSpu = productsRepository.findProductFromGoodsCenter(processingTask.getWarehouseNo().longValue(),
                processingTaskProduct.getProductSkuCode());
        if (productSpu == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请求货品不存在",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        Integer storageLocation = Objects.isNull(productSpu.getTemperature()) ? 0 : productSpu.getTemperature();
        String storageLocationMsg = StorageLocation.getTypeById(storageLocation) + " " + day;

        String weight = skuBatchCodeQueryService.getWeight(productSpu.getSkuPdId(),
                productSpu.getSku());

        // 构建响应对象
        List<ProcessingTaskProductPrintResDTO> printData = new ArrayList<>();
        for (int i = 0; i < reqDTO.getCanPrintNumber(); i++) {
            printNumber++;
            ProcessingTaskProductPrintResDTO printRes = new ProcessingTaskProductPrintResDTO();
            printRes.setProductName(productSpu.getPdName());
            printRes.setProductionDate(DateUtil.formatYmdDate(skuBatchCode.getProductionDate()));
            printRes.setStorageLocation(storageLocationMsg);
            printRes.setWeight(weight != null ? weight : processingTaskProduct.getProductSkuUnitDesc());
            printRes.setProcessingSpecUnitDesc(record.getProductSkuSpecWeight() +
                    (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                            "KG" : processingTaskProduct.getProductSkuUnit()));
            String printNumberString = printNumber.toString();
            String onlyCode = skuBatchCode.getSkuBatchOnlyCode()
                    + DEFAULT_ONLY_CODE.substring(NumberUtils.INTEGER_ZERO, DEFAULT_ONLY_CODE.length()
                            - printNumberString.length()) + printNumber;
            String urlOnlyCode = "";
            String queryCodeUrl = configRepository.queryValueByKey(WmsConstant.CODE_URL_PREFIX);
            if (StringUtils.isBlank(queryCodeUrl)) {
                urlOnlyCode = CODE_URL + onlyCode;
            } else {
                urlOnlyCode = queryCodeUrl + onlyCode;
            }
            printRes.setOnlyCode(urlOnlyCode);
            printRes.setCode(onlyCode);
            printData.add(printRes);
        }
        // 更新打印次数
        skuBatchCodeRepository.addPrintNumber(skuBatchCode.getId(), reqDTO.getCanPrintNumber());
        processingTaskProductDomainService.updatePrintNumber(record.getId(), reqDTO.getProcessingTaskProductSpecId(), reqDTO.getCanPrintNumber());
        return CommonResult.ok(printData);
    }
}
