package net.summerfarm.wms.application.materialManage.service;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.external.input.WarehouseExternalRouteCommandInput;
import net.summerfarm.wms.application.external.service.WarehouseExternalRouteCommandService;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialTaskAssembler;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.enums.BooleanEnum;
import net.summerfarm.wms.domain.external.entity.WarehouseExternalRouteEntity;
import net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam;
import net.summerfarm.wms.domain.external.repository.WarehouseExternalRouteQueryRepository;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskDetailEntity;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialTaskDetailQueryRepository;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialTaskQueryRepository;
import net.summerfarm.wms.openapi.stockin.xm.req.StockInCreateNoticeReq;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCreateNoticeReq;
import net.xianmu.retry.core.annotation.XmRetryAnnotation;
import net.xianmu.retry.core.annotation.XmRetryBackOffPolicy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class WmsMaterialTaskExternalNoticeService {

    @Resource
    private WmsMaterialTaskQueryRepository materialTaskQueryRepository;
    @Resource
    private WmsMaterialTaskDetailQueryRepository materialTaskDetailQueryRepository;
    @Resource
    private WarehouseExternalRouteQueryRepository warehouseExternalRouteQueryRepository;
    @Resource
    private WarehouseExternalRouteCommandService warehouseExternalRouteCommandService;

    @XmRetryAnnotation(maxRetryTimes = 3,
            backOffPolicy = XmRetryBackOffPolicy.EXPONENTIAL_RANDOM,
            reThrowException = false,
            executeAfterTransactionCommit = true)
    public void exeExternalNotice(Long wmsMaterialTaskId) {
        if (null == wmsMaterialTaskId) {
            return;
        }
        WmsMaterialTaskEntity wmsMaterialTaskEntity = materialTaskQueryRepository.selectById(wmsMaterialTaskId);
        if (null == wmsMaterialTaskEntity) {
            return;
        }

        // 领用
        if (StockTaskTypeEnum.MATERIAL_TASK_RECEIVE.equalCode(wmsMaterialTaskEntity.getType())) {
            // 获取外部路由配置
            WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(
                    WarehouseExternalRouteQueryParam.builder()
                            .warehouseNo(wmsMaterialTaskEntity.getWarehouseNo())
                            .abilityCode(ExternalCodeConstant.STOCK_OUT_CREATE_NOTICE)
                            .orderType(wmsMaterialTaskEntity.getType())
                            .routeStatus(BooleanEnum.TRUE.getCode())
                            .build());
            // 对接下发
            if (null == warehouseExternalRoute) {
                log.warn("领用创建通知 未找到对应的外部路由配置 仓库编号:{} 出库类型:{}",
                        wmsMaterialTaskEntity.getWarehouseNo(), wmsMaterialTaskEntity.getType());
                return;
            }

            List<WmsMaterialTaskDetailEntity> list = materialTaskDetailQueryRepository
                    .listByTaskCode(wmsMaterialTaskEntity.getMaterialTaskCode());
            WarehouseExternalRouteCommandInput<StockOutCreateNoticeReq> warehouseExternalRouteCommandInput =
                    WmsMaterialTaskAssembler.buildStockOutCreateNoticeCommand(wmsMaterialTaskEntity, list,
                            warehouseExternalRoute);
            warehouseExternalRouteCommandService.externalRoute(warehouseExternalRouteCommandInput);
        }
        // 归还
        else if (StockTaskTypeEnum.MATERIAL_TASK_RESTORE.equalCode(wmsMaterialTaskEntity.getType())) {
            // 获取外部路由配置
            WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                    .warehouseNo(wmsMaterialTaskEntity.getWarehouseNo())
                    .abilityCode(ExternalCodeConstant.STOCK_IN_CREATE_NOTICE)
                    .orderType(wmsMaterialTaskEntity.getType())
                    .routeStatus(BooleanEnum.TRUE.getCode()).build());
            // 对接下发
            if (null == warehouseExternalRoute) {
                log.warn("当前仓库当前类型未查询到外部路由配置");
                return;
            }

            List<WmsMaterialTaskDetailEntity> list = materialTaskDetailQueryRepository
                    .listByTaskCode(wmsMaterialTaskEntity.getMaterialTaskCode());

            // 调用路由
            WarehouseExternalRouteCommandInput<StockInCreateNoticeReq> warehouseExternalRouteCommandInput =
                    WmsMaterialTaskAssembler.buildStockInCreateNoticeCommand(wmsMaterialTaskEntity, list, warehouseExternalRoute);
            warehouseExternalRouteCommandService.externalRoute(warehouseExternalRouteCommandInput);
        }
    }
}
