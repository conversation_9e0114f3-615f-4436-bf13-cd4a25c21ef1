package net.summerfarm.wms.application.processingtask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.BigDecimalUtils;
import net.summerfarm.wms.application.processingtask.service.ProcessingTaskMaterialQueryService;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskMaterialReceiveResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskMaterialResDTO;
import net.summerfarm.wms.application.processingtask.converter.ProcessingTaskMaterialReceiveResDTOConverter;
import net.summerfarm.wms.application.processingtask.converter.ProcessingTaskMaterialResDTOConverter;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.common.util.IntegerUtil;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import net.summerfarm.wms.domain.processingtask.repository.*;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProcessingTaskMaterialQueryServiceImpl implements ProcessingTaskMaterialQueryService {

    @Autowired
    private ProcessingTaskMaterialRepository taskMaterialRepository;
    @Autowired
    private ProcessingTaskMaterialReceiveRecordRepository materialReceiveRecordRepository;
    @Autowired
    private ProcessingTaskProductRepository taskProductRepository;
    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProductRepository productRepository;

    @Override
    public CommonResult<PageInfo<ProcessingTaskMaterialResDTO>> materialQueryPage(ProcessingTaskMaterialQueryReqDTO reqDTO) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();

        if (Objects.isNull(reqDTO.getPageIndex()) || Objects.isNull(reqDTO.getPageSize())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "请求参数错误");
        }

        if (reqDTO.getProcessingTaskProductId() == null ||
                reqDTO.getProcessingTaskCode() == null){
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "请求成品信息缺失");
        }

        // 查询成品
        ProcessingTaskProduct processingTaskProduct = taskProductRepository.queryById(
                reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "请求成品不存在");
        }

        //查询加工任务
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(reqDTO.getProcessingTaskCode());
        if (processingTask == null){
            throw new BizException("未查询到加工任务", ErrorCodeNew.PARAM_ERROR);
        }

        // 查询原料
        PageInfo<ProcessingTaskMaterial> pageInfoTemp = taskMaterialRepository.pageByProductId(
                reqDTO.getProcessingTaskCode(), reqDTO.getProcessingTaskProductId(), reqDTO.getProcessingMaterialSkuCode(),
                reqDTO.getPageIndex(), reqDTO.getPageSize()
        );

        List<ProcessingTaskMaterial> materialList = pageInfoTemp.getList();

        // 原料列表
        List<String> materialSkuCodeList = materialList
                .stream()
                .map(ProcessingTaskMaterial::getMaterialSkuCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Product> materialSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId, materialSkuCodeList);

        // 原料绑定领用记录
        List<Long> materialIdList = materialList.stream()
                .map(ProcessingTaskMaterial::getId)
                .distinct()
                .collect(Collectors.toList());
        List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordListTemp = materialReceiveRecordRepository
                .queryByMaterialIdList(materialIdList);
        Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveMap = materialReceiveRecordListTemp.stream()
                .collect(Collectors.groupingBy(
                        ProcessingTaskMaterialReceiveRecord::getProcessingTaskMaterialId));

        PageInfo<ProcessingTaskMaterialResDTO> pageInfo = new PageInfo<>();
        pageInfo.setTotal(pageInfoTemp.getTotal());
        pageInfo.setPages(pageInfoTemp.getPages());
        pageInfo.setList(ProcessingTaskMaterialResDTOConverter.convertList(
                materialList, materialSpuMap
        ));
        for (ProcessingTaskMaterialResDTO processingTaskMaterialResDTO : pageInfo.getList()) {
            List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList =
                    materialReceiveMap.get(processingTaskMaterialResDTO.getId());

            List<ProcessingTaskMaterialReceiveResDTO> processingTaskMaterialReceiveResDTOList =
                    ProcessingTaskMaterialReceiveResDTOConverter.INSTANCE.convertList(materialReceiveRecordList);
            if (!CollectionUtils.isEmpty(processingTaskMaterialReceiveResDTOList)) {
                processingTaskMaterialReceiveResDTOList.stream()
                        .filter(item -> item != null && null != item.getMaterialSkuRestoreQuantity())
                        .forEach(item -> item.setMaterialSkuRestoreCabinetCode(DefaultCabinetCodeEnum.JG.getCode()));
            }
            processingTaskMaterialResDTO.setMaterialReceiveList(processingTaskMaterialReceiveResDTOList == null ?
                    new ArrayList<>() : processingTaskMaterialReceiveResDTOList);

            // 成品已加工所需原料数量
            BigDecimal totalMaterialNeedQuantityDecimal = BigDecimal.valueOf(processingTaskProduct.getProductSkuNeedQuantity())
                    .multiply(BigDecimal.valueOf(processingTaskMaterialResDTO.getMaterialSkuRatioNum()))
                    .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 2, RoundingMode.UP);
            // 0位小数向上取整
            Integer totalMaterialNeedQuantity = totalMaterialNeedQuantityDecimal.setScale(0,
                    RoundingMode.UP).intValue();

            // 建议领料
            processingTaskMaterialResDTO.setMaterialSkuNeedQuantity(
                    totalMaterialNeedQuantity
            );
            // 已使用数量
            processingTaskMaterialResDTO.setMaterialSkuUsedQuantity(
                    IntegerUtil.getValueDefault0(processingTaskMaterialResDTO.getMaterialSkuReceiveQuantity())
                            - IntegerUtil.getValueDefault0(processingTaskMaterialResDTO.getMaterialSkuRestoreQuantity())
                            - BigDecimalUtils.defaultValue(processingTaskMaterialResDTO.getWasteLossWeight(), BigDecimal.ZERO)
                            .divide(processingTaskMaterialResDTO.getMaterialSkuWeight(), 0, RoundingMode.HALF_DOWN).intValue()
            );
            // 还需数量
            processingTaskMaterialResDTO.setMaterialSkuSuggestQuantity(
                    processingTaskMaterialResDTO.getMaterialSkuNeedQuantity() -
                            processingTaskMaterialResDTO.getMaterialSkuUsedQuantity()
            );

            for (ProcessingTaskMaterialReceiveResDTO processingTaskMaterialReceiveResDTO :
                    processingTaskMaterialResDTO.getMaterialReceiveList()) {
                // 当前总重量
                processingTaskMaterialReceiveResDTO.setMaterialSkuTotalWeight(
                        processingTaskMaterialReceiveResDTO.getMaterialSkuWeight().multiply(
                                BigDecimal.valueOf(
                                        processingTaskMaterialReceiveResDTO.getMaterialSkuQuantity()
                                )
                        )
                );

                // 领料总重量
                processingTaskMaterialReceiveResDTO.setMaterialSkuTotalReceiveWeight(
                        processingTaskMaterialReceiveResDTO.getMaterialSkuWeight().multiply(
                                BigDecimal.valueOf(
                                        processingTaskMaterialReceiveResDTO.getMaterialSkuReceiveQuantity()
                                )
                        )
                );

                // 剩余重量、剩余数量
                if (processingTaskMaterialReceiveResDTO.getMaterialSkuRemainWeight() != null) {
                    processingTaskMaterialReceiveResDTO.setMaterialSkuRemainQuantity(
                            processingTaskMaterialReceiveResDTO.getMaterialSkuRemainWeight().divide(
                                    processingTaskMaterialReceiveResDTO.getMaterialSkuWeight(), 0, RoundingMode.HALF_DOWN
                            ).intValue()
                    );
                }
            }
        }

        return CommonResult.ok(pageInfo);
    }

}
