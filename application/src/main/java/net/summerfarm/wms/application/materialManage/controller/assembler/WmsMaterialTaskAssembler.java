package net.summerfarm.wms.application.materialManage.controller.assembler;


import net.summerfarm.wms.application.external.input.WarehouseExternalRouteCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialTaskQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialTaskVO;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.external.entity.WarehouseExternalRouteEntity;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskDetailEntity;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskCommandParam;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskQueryParam;
import net.summerfarm.wms.openapi.stockin.xm.req.InboundTaskItemDTO;
import net.summerfarm.wms.openapi.stockin.xm.req.StockInCreateNoticeReq;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCreateItem;
import net.summerfarm.wms.openapi.stockout.xm.req.StockOutCreateNoticeReq;

import java.util.*;

/**
 *
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
public class WmsMaterialTaskAssembler {

    private WmsMaterialTaskAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static WmsMaterialTaskQueryParam toWmsMaterialTaskQueryParam(WmsMaterialTaskQueryInput wmsMaterialTaskQueryInput) {
        if (wmsMaterialTaskQueryInput == null) {
            return null;
        }
        WmsMaterialTaskQueryParam wmsMaterialTaskQueryParam = new WmsMaterialTaskQueryParam();
        wmsMaterialTaskQueryParam.setId(wmsMaterialTaskQueryInput.getId());
        wmsMaterialTaskQueryParam.setTenantId(wmsMaterialTaskQueryInput.getTenantId());
        wmsMaterialTaskQueryParam.setWarehouseNo(wmsMaterialTaskQueryInput.getWarehouseNo());
        wmsMaterialTaskQueryParam.setType(wmsMaterialTaskQueryInput.getType());
        wmsMaterialTaskQueryParam.setMaterialTaskCode(wmsMaterialTaskQueryInput.getMaterialTaskCode());
        wmsMaterialTaskQueryParam.setDestination(wmsMaterialTaskQueryInput.getDestination());
        wmsMaterialTaskQueryParam.setSourceType(wmsMaterialTaskQueryInput.getSourceType());
        wmsMaterialTaskQueryParam.setSourceId(wmsMaterialTaskQueryInput.getSourceId());
        wmsMaterialTaskQueryParam.setSourceCode(wmsMaterialTaskQueryInput.getSourceCode());
        wmsMaterialTaskQueryParam.setCreator(wmsMaterialTaskQueryInput.getCreator());
        wmsMaterialTaskQueryParam.setCreateTime(wmsMaterialTaskQueryInput.getCreateTime());
        wmsMaterialTaskQueryParam.setUpdater(wmsMaterialTaskQueryInput.getUpdater());
        wmsMaterialTaskQueryParam.setUpdateTime(wmsMaterialTaskQueryInput.getUpdateTime());
        wmsMaterialTaskQueryParam.setPageIndex(wmsMaterialTaskQueryInput.getPageIndex());
        wmsMaterialTaskQueryParam.setPageSize(wmsMaterialTaskQueryInput.getPageSize());
        return wmsMaterialTaskQueryParam;
    }





    public static WmsMaterialTaskCommandParam buildCreateParam(WmsMaterialTaskCommandInput wmsMaterialTaskCommandInput) {
        if (wmsMaterialTaskCommandInput == null) {
            return null;
        }
        WmsMaterialTaskCommandParam wmsMaterialTaskCommandParam = new WmsMaterialTaskCommandParam();
//        wmsMaterialTaskCommandParam.setId(wmsMaterialTaskCommandInput.getId());
        wmsMaterialTaskCommandParam.setTenantId(wmsMaterialTaskCommandInput.getTenantId());
        wmsMaterialTaskCommandParam.setWarehouseNo(wmsMaterialTaskCommandInput.getWarehouseNo());
        wmsMaterialTaskCommandParam.setType(wmsMaterialTaskCommandInput.getType());
//        wmsMaterialTaskCommandParam.setMaterialTaskCode(wmsMaterialTaskCommandInput.getMaterialTaskCode());
        wmsMaterialTaskCommandParam.setDestination(wmsMaterialTaskCommandInput.getDestination());
        wmsMaterialTaskCommandParam.setSourceType(wmsMaterialTaskCommandInput.getSourceType());
        wmsMaterialTaskCommandParam.setSourceId(wmsMaterialTaskCommandInput.getSourceId());
        wmsMaterialTaskCommandParam.setSourceCode(wmsMaterialTaskCommandInput.getSourceCode());
        wmsMaterialTaskCommandParam.setCreator(wmsMaterialTaskCommandInput.getCreator());
        wmsMaterialTaskCommandParam.setCreateTime(wmsMaterialTaskCommandInput.getCreateTime());
        wmsMaterialTaskCommandParam.setUpdater(wmsMaterialTaskCommandInput.getUpdater());
        wmsMaterialTaskCommandParam.setUpdateTime(wmsMaterialTaskCommandInput.getUpdateTime());
        return wmsMaterialTaskCommandParam;
    }


   public static WmsMaterialTaskVO toWmsMaterialTaskVO(WmsMaterialTaskEntity wmsMaterialTaskEntity) {
       if (wmsMaterialTaskEntity == null) {
            return null;
       }
       WmsMaterialTaskVO wmsMaterialTaskVO = new WmsMaterialTaskVO();
       wmsMaterialTaskVO.setId(wmsMaterialTaskEntity.getId());
       wmsMaterialTaskVO.setTenantId(wmsMaterialTaskEntity.getTenantId());
       wmsMaterialTaskVO.setWarehouseNo(wmsMaterialTaskEntity.getWarehouseNo());
       wmsMaterialTaskVO.setType(wmsMaterialTaskEntity.getType());
       wmsMaterialTaskVO.setMaterialTaskCode(wmsMaterialTaskEntity.getMaterialTaskCode());
       wmsMaterialTaskVO.setDestination(wmsMaterialTaskEntity.getDestination());
       wmsMaterialTaskVO.setSourceType(wmsMaterialTaskEntity.getSourceType());
       wmsMaterialTaskVO.setSourceId(wmsMaterialTaskEntity.getSourceId());
       wmsMaterialTaskVO.setSourceCode(wmsMaterialTaskEntity.getSourceCode());
       wmsMaterialTaskVO.setCreator(wmsMaterialTaskEntity.getCreator());
       wmsMaterialTaskVO.setCreateTime(wmsMaterialTaskEntity.getCreateTime());
       wmsMaterialTaskVO.setUpdater(wmsMaterialTaskEntity.getUpdater());
       wmsMaterialTaskVO.setUpdateTime(wmsMaterialTaskEntity.getUpdateTime());
       return wmsMaterialTaskVO;
   }

    public static WarehouseExternalRouteCommandInput<StockOutCreateNoticeReq> buildStockOutCreateNoticeCommand(
            WmsMaterialTaskEntity wmsMaterialTaskEntity,
            List<WmsMaterialTaskDetailEntity> list,
            WarehouseExternalRouteEntity warehouseExternalRoute) {

        WarehouseExternalRouteCommandInput<StockOutCreateNoticeReq> warehouseExternalRouteCommandInput = new WarehouseExternalRouteCommandInput<>();
        warehouseExternalRouteCommandInput.setIdempotentNo(wmsMaterialTaskEntity.getMaterialTaskCode());
        warehouseExternalRouteCommandInput.setWarehouseNo(wmsMaterialTaskEntity.getWarehouseNo());
        warehouseExternalRouteCommandInput.setTenantId(wmsMaterialTaskEntity.getTenantId());
        warehouseExternalRouteCommandInput.setWarehouseTenantId(warehouseExternalRoute.getWarehouseTenantId());
        warehouseExternalRouteCommandInput.setOrderType(wmsMaterialTaskEntity.getType());
        warehouseExternalRouteCommandInput.setAbilityCode(warehouseExternalRoute.getAbilityCode());
        warehouseExternalRouteCommandInput.setAbilityId(warehouseExternalRoute.getAbilityId());
        warehouseExternalRouteCommandInput.setExternalData(buildStockOutCreateNoticeReq(
                wmsMaterialTaskEntity, list));
        return warehouseExternalRouteCommandInput;
    }


    public static StockOutCreateNoticeReq buildStockOutCreateNoticeReq(
            WmsMaterialTaskEntity wmsMaterialTaskEntity,
            List<WmsMaterialTaskDetailEntity> list) {
        StockOutCreateNoticeReq stockOutCreateNoticeReq = new StockOutCreateNoticeReq();
        stockOutCreateNoticeReq.setIdempotentNo(wmsMaterialTaskEntity.getMaterialTaskCode());
        stockOutCreateNoticeReq.setStockOutNo(wmsMaterialTaskEntity.getMaterialTaskCode());
        stockOutCreateNoticeReq.setOutWarehouseNo(wmsMaterialTaskEntity.getWarehouseNo().toString());
        stockOutCreateNoticeReq.setOrderType(wmsMaterialTaskEntity.getType());
        stockOutCreateNoticeReq.setCreateTime(DateUtil.formatDate(new Date()));
        stockOutCreateNoticeReq.setExpectTime(DateUtil.formatDate(new Date()));
        List<StockOutCreateItem> stockOutCreateItems = new ArrayList<>();

        for (WmsMaterialTaskDetailEntity wmsMaterialTaskDetailEntity : list) {
            StockOutCreateItem stockOutCreateItem = new StockOutCreateItem();
            stockOutCreateItem.setSkuCode(wmsMaterialTaskDetailEntity.getMaterialSku());
            stockOutCreateItem.setPlanQuantity(wmsMaterialTaskDetailEntity.getQuantity());
            stockOutCreateItems.add(stockOutCreateItem);
        }

        stockOutCreateNoticeReq.setStockOutItems(stockOutCreateItems);
        return stockOutCreateNoticeReq;
    }

    public static WarehouseExternalRouteCommandInput<StockInCreateNoticeReq> buildStockInCreateNoticeCommand(
            WmsMaterialTaskEntity wmsMaterialTaskEntity,
            List<WmsMaterialTaskDetailEntity> list,
            WarehouseExternalRouteEntity warehouseExternalRoute) {
        WarehouseExternalRouteCommandInput<StockInCreateNoticeReq> warehouseExternalRouteCommandInput = new WarehouseExternalRouteCommandInput<>();
        warehouseExternalRouteCommandInput.setIdempotentNo(wmsMaterialTaskEntity.getMaterialTaskCode());
        warehouseExternalRouteCommandInput.setWarehouseNo(wmsMaterialTaskEntity.getWarehouseNo());
        warehouseExternalRouteCommandInput.setTenantId(wmsMaterialTaskEntity.getTenantId());
        warehouseExternalRouteCommandInput.setWarehouseTenantId(warehouseExternalRoute.getWarehouseTenantId());
        warehouseExternalRouteCommandInput.setOrderType(warehouseExternalRoute.getOrderType());
        warehouseExternalRouteCommandInput.setAbilityCode(warehouseExternalRoute.getAbilityCode());
        warehouseExternalRouteCommandInput.setAbilityId(warehouseExternalRoute.getAbilityId());
        warehouseExternalRouteCommandInput.setExternalData(buildbuildStockInCreateNoticeReq(wmsMaterialTaskEntity, list));
        return warehouseExternalRouteCommandInput;
    }

    public static StockInCreateNoticeReq buildbuildStockInCreateNoticeReq(
            WmsMaterialTaskEntity wmsMaterialTaskEntity,
            List<WmsMaterialTaskDetailEntity> list) {
        StockInCreateNoticeReq stockInCreateNoticeReq = new StockInCreateNoticeReq();
        stockInCreateNoticeReq.setIdempotentNo(wmsMaterialTaskEntity.getMaterialTaskCode());
        stockInCreateNoticeReq.setInWarehouseNo(wmsMaterialTaskEntity.getWarehouseNo());
        stockInCreateNoticeReq.setInboundTaskId(wmsMaterialTaskEntity.getMaterialTaskCode());
        stockInCreateNoticeReq.setBusinessNo(wmsMaterialTaskEntity.getMaterialTaskCode());
        stockInCreateNoticeReq.setInboundType(wmsMaterialTaskEntity.getType());
        stockInCreateNoticeReq.setExpectInboundTime(DateUtil.formatDate(new Date()));
        stockInCreateNoticeReq.setRemark(wmsMaterialTaskEntity.getRemark());

        List<InboundTaskItemDTO> detailList = new ArrayList<>();
        for (WmsMaterialTaskDetailEntity wmsMaterialTaskDetailEntity : list) {
            InboundTaskItemDTO inboundTaskItemDTO = new InboundTaskItemDTO();
            inboundTaskItemDTO.setSkuCode(wmsMaterialTaskDetailEntity.getMaterialSku());
            inboundTaskItemDTO.setSkuName(wmsMaterialTaskDetailEntity.getMaterialSkuName());
            inboundTaskItemDTO.setQuantity(wmsMaterialTaskDetailEntity.getQuantity());
            detailList.add(inboundTaskItemDTO);
        }
        stockInCreateNoticeReq.setItemList(detailList);

        return stockInCreateNoticeReq;
    }

}
