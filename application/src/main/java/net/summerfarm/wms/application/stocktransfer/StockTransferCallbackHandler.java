package net.summerfarm.wms.application.stocktransfer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.external.factory.StockTransferReqFactory;
import net.summerfarm.wms.application.openapi.provider.dto.StockTransferCallbackHandleDTO;
import net.summerfarm.wms.application.stockdamage.event.StockDamageCreateEvent;
import net.summerfarm.wms.application.stockdamage.event.StockDamageCreateItem;
import net.summerfarm.wms.application.stocktask.enums.StockTaskTypeEnum;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.dto.stocktransfer.OperateTransferCallbackDTO;
import net.summerfarm.wms.common.dto.stocktransfer.OperateTransferCallbackOutDTO;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.stockTransfer.*;
import net.summerfarm.wms.domain.stockTransfer.dto.TransferCallbackDTO;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferEntity;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemEntity;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemOpDetailEntity;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferItemOpEntity;
import net.summerfarm.wms.facade.wnc.WarehouseStorageFacade;
import net.summerfarm.wms.facade.wnc.dto.WarehouseStorageDTO;
import net.summerfarm.wms.initConfig.FeishuUrlConfig;
import net.summerfarm.wms.openapi.transfer.xm.req.*;
import net.xianmu.common.exception.BizException;
import net.xianmu.robot.feishu.FeishuBotUtil;
import net.xianmu.robot.feishu.dto.FeishuMultiElementsMsg;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/29 17:08
 * @Version 1.0
 */
@Component
@Slf4j
public class StockTransferCallbackHandler {

    @Autowired
    private StockTransferReqFactory stockTransferReqFactory;

    @Autowired
    private StockTransferQueryRepository stockTransferQueryRepository;

    @Autowired
    private StockTransferItemQueryRepository stockTransferItemQueryRepository;

    @Autowired
    private StockTransferItemOpQueryRepository stockTransferItemOpQueryRepository;

    @Autowired
    private StockTransferItemOpDetailQueryRepository stockTransferItemOpDetailQueryRepository;

    @Autowired
    private StockTransferCallbackBizService stockTransferCallbackBizService;

    @Autowired
    private MqProducer mqProducer;

    @Autowired
    private FeishuUrlConfig feishuUrlConfig;

    @Autowired
    private WarehouseStorageFacade warehouseStorageFacade;

    /**
     * 处理转换回调请求。
     * <p>
     * 此方法接收一个转换回调请求对象，并通过一系列处理步骤对这个请求进行加工和处理。
     * 主要包括以下几个步骤：
     * 1. 使用callbackDataFactory方法对传入的请求进行加工，转换为StockTransferCallbackHandleDTO对象。
     * 2. 调用callBackHandlePostProcessor方法对加工后的数据进行进一步处理。
     *
     * @param transferCallbackReq 转换回调请求对象，包含了回调请求的相关信息。
     */
    public void handle(TransferCallbackReq transferCallbackReq) {
        // 加工数据 将转换回调请求加工为处理DTO，准备进行后续处理。
        StockTransferCallbackHandleDTO stockTransferCallbackHandleDTO = this.callbackDataFactory(transferCallbackReq);

        if (!CollectionUtils.isEmpty(stockTransferCallbackHandleDTO.getCallBackNotInDbTransferInNumMap())){
            log.info("【三方回告的转换数据处理】转入效期有差异");
            this.transferInNotify(stockTransferCallbackHandleDTO.getCallBackNotInDbTransferInNumMap(), stockTransferCallbackHandleDTO.getStockTransferId());
            log.info("【三方回告的转换数据处理】转入效期有差异，处理结束。");
            return;
        }

        log.info("【三方回告的转换数据处理】 stockTransferCallbackHandleDTO >>> {}", JSON.toJSONString(stockTransferCallbackHandleDTO));
        // 处理数据
        this.callBackHandlePostProcessor(stockTransferCallbackHandleDTO);
    }

    /**
     * 回告数据处理工厂
     *
     * @param transferCallbackReq 回告数据
     * @return 交给后置处理器处理的数据
     */
    private StockTransferCallbackHandleDTO callbackDataFactory(TransferCallbackReq transferCallbackReq) {
        // 0. opDetail应该写入数据库的三方回告数量
        Map<Long, Integer> externalTransferInNumMap = new HashMap<>();
        // 1. 三方回告的转换入数量Map
        Map<String, Integer> callBackTransferInNumMap = this.calculateTransferInNum(transferCallbackReq.getStockTransferItems());
        // sku维度三方转入数量
        Map<String, Integer> externalTransferSkuInNumMap = transferCallbackReq.getStockTransferItems().stream().collect(Collectors.toMap(TransferItem::getTransferInSku, TransferItem::getTransferInQuantity, Integer::sum));
        //region 2. 加工鲜沐系统的转换数据
        StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(Long.valueOf(transferCallbackReq.getStockTransferNo()));
        // find from db
        List<StockTransferItemEntity> stockTransferItemEntities = stockTransferItemQueryRepository.listByStockTransferId(Long.valueOf(transferCallbackReq.getStockTransferNo()));
        // key: itemId
        Map<Long, StockTransferItemEntity> itemIdMapFromDb = stockTransferItemEntities.stream().collect(Collectors.toMap(StockTransferItemEntity::getId, Function.identity(), (k1, k2) -> k1));

        List<StockTransferItemOpEntity> stockTransferItemOpEntities = stockTransferItemOpQueryRepository.listByItemIdList(stockTransferItemEntities.stream().map(StockTransferItemEntity::getId).collect(Collectors.toList()));
        // key: itemOpId
        Map<Long, StockTransferItemOpEntity> opIdMapFromDb = stockTransferItemOpEntities.stream().collect(Collectors.toMap(StockTransferItemOpEntity::getId, Function.identity(), (k1, k2) -> k1));

        List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntities = stockTransferItemOpDetailQueryRepository.listByOpIdList(stockTransferItemOpEntities.stream().map(StockTransferItemOpEntity::getId).collect(Collectors.toList()));
        Map<String, List<StockTransferItemOpDetailEntity>> stockTransferItemOpDetailMapFromDb = new HashMap<>();
        for (StockTransferItemOpDetailEntity stockTransferItemOpDetailEntity : stockTransferItemOpDetailEntities) {
            StockTransferItemOpEntity op = opIdMapFromDb.get(stockTransferItemOpDetailEntity.getStockTransferItemOpId());
            StockTransferItemEntity item = itemIdMapFromDb.get(op.getStockTransferItemId());
            if (null == item){
                throw new BizException("库存转换任务明细不存在：{}" + JSON.toJSONString(item));
            }
            String uniqueKey = this.buildKey(op.getTransferOutSku(), item.getTransferInSku(), DateUtil.formatYmd(op.getProduceDate()), DateUtil.formatYmd(op.getShelfLife()));
            List<StockTransferItemOpDetailEntity> opDetailEntityList = stockTransferItemOpDetailMapFromDb.getOrDefault(uniqueKey, new ArrayList<>());
            opDetailEntityList.add(stockTransferItemOpDetailEntity);
            stockTransferItemOpDetailMapFromDb.put(uniqueKey, opDetailEntityList);
        }
        //endregion

        // 三方回告的，但是db里没有的效期 的转入数量map
        Map<String, Integer> callBackNotInDbTransferInNumMap = new HashMap<>();
        callBackTransferInNumMap.forEach((uniqueKey, callBackTransferInNum) -> {
            if (!stockTransferItemOpDetailMapFromDb.containsKey(uniqueKey)) {
                callBackNotInDbTransferInNumMap.put(uniqueKey, callBackTransferInNum);
            }
        });

        stockTransferItemOpDetailMapFromDb.forEach((uniqueKey, opDetailEntityList) -> {
            if (CollectionUtils.isEmpty(opDetailEntityList)){
                log.info("库存转换任务明细详情不存在：{}", uniqueKey);
                return;
            }

            for (int i = 0; i < opDetailEntityList.size(); i++) {
                StockTransferItemOpDetailEntity opDetail = opDetailEntityList.get(i);
                StockTransferItemOpEntity op = opIdMapFromDb.get(opDetail.getStockTransferItemOpId());
                if (op == null){
                    throw new BizException("库存转换任务op不存在：{}" + opDetail.getStockTransferItemOpId());
                }
                // 鲜沐转入数量
                int xianmuTransferInNum = calculate(opDetail.getTransferOutNum(), op.getTransferRatio()).intValue();
                // 三方回告数量 AKA doQuantity
                Integer callBackTransferInNum = callBackTransferInNumMap.getOrDefault(uniqueKey, 0);
                // 是最后一条
                boolean isLast = i == opDetailEntityList.size() - 1;
                if (!isLast){
                    // 不是最后一条
                    Integer externalTransferInNum = Math.min(xianmuTransferInNum, callBackTransferInNum);
                    // 记录三方回告数量，后面写入数据库
                    externalTransferInNumMap.put(opDetail.getId(), externalTransferInNum);
                    // 回写三方回告数量map
                    callBackTransferInNumMap.put(uniqueKey, callBackTransferInNum - externalTransferInNum);
                } else {
                    // 是最后一条
                    Integer externalTransferInNum = callBackTransferInNum;
                    // 记录三方回告数量，后面写入数据库
                    externalTransferInNumMap.put(opDetail.getId(), externalTransferInNum);
                    // 回写三方回告数量map
                    callBackTransferInNumMap.put(uniqueKey, callBackTransferInNum - externalTransferInNum);
                }
            }
        });

        log.info("三方回告数量map(即将写入db的数据为)：{}", JSON.toJSONString(externalTransferInNumMap));
        //endregion


        //region 3. 比对转出数量
        // 三方回告的转换出数量Map
        Map<String, Integer> callBackTransferOutNumMap = this.calculateTransferOutNum(transferCallbackReq.getStockTransferItems());
        // 鲜沐系统的转换出的数量Map
        // 应转换的数量 from DB
        TransferCreateNoticeReq stockTransferNoticeReq = stockTransferReqFactory.createStockTransferNoticeReq(stockTransferEntity);
        //endregion
        Map<String, Integer> shouldTransferOutNumMap = this.calculateTransferOutNum(stockTransferNoticeReq.getStockTransferItems());

        // 数据比较后 - 需要进行告警的转出sku的数量 Map
        Map<String, Integer> needAlarmOutSkuNum = new HashMap<>();
        for (Map.Entry<String, Integer> entry : callBackTransferOutNumMap.entrySet()) {
            String key = entry.getKey();
            Integer callBackTransferOutNum = entry.getValue();
            Integer shouldTransferOutNum = shouldTransferOutNumMap.getOrDefault(key, 0);
            int diffQuantity = callBackTransferOutNum - shouldTransferOutNum;
            if (diffQuantity == 0) {
                log.info("三方回告转出数量与系统数量一致：{}", key);
                continue;
            }
            needAlarmOutSkuNum.put(key, diffQuantity);
        }
        //endregion
        return new StockTransferCallbackHandleDTO(
                externalTransferInNumMap,
                externalTransferSkuInNumMap,
                needAlarmOutSkuNum,
                callBackNotInDbTransferInNumMap,
                Long.valueOf(transferCallbackReq.getStockTransferNo())
        );
    }

    /**
     * 转换回告数据后置处理器
     * @param stockTransferCallbackHandleDTO  处理三方转换回告的dto
     */
    public void callBackHandlePostProcessor(StockTransferCallbackHandleDTO stockTransferCallbackHandleDTO) {
        // 先把分配好的三方回告数量写入数据库
        stockTransferCallbackBizService.recordExternalCallbackNum(stockTransferCallbackHandleDTO.getExternalTransferInNumMap());
        // 自动货损
        this.handleDamage(stockTransferCallbackHandleDTO);
        // 自动仅转入
        stockTransferCallbackBizService.operateStockTransferInItemOnly(new TransferCallbackDTO(stockTransferCallbackHandleDTO.getStockTransferId()));
        // 转出的告警
        this.transferOutSkuNotify(stockTransferCallbackHandleDTO);
    }

    private void handleDamage(StockTransferCallbackHandleDTO stockTransferCallbackHandleDTO){
        Long stockTransferId = stockTransferCallbackHandleDTO.getStockTransferId();
        log.info("开始操作「自动货损」行为:{}", stockTransferId);
        StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(stockTransferId);
        List<StockDamageCreateItem> stockDamageCreateItems = new ArrayList<>();
        // find from db
        List<StockTransferItemEntity> stockTransferItemEntities = stockTransferItemQueryRepository.listByStockTransferId(stockTransferId);
        // key: inSkuCode
        Map<String, StockTransferItemEntity> stockTransferItemMapFromDb = stockTransferItemEntities.stream().collect(Collectors.toMap(StockTransferItemEntity::getTransferInSku, Function.identity(), (k1, k2) -> k1));
        List<StockTransferItemOpEntity> stockTransferItemOpEntities = stockTransferItemOpQueryRepository.listByItemIdList(stockTransferItemEntities.stream().map(StockTransferItemEntity::getId).collect(Collectors.toList()));
        // key: itemId
        Map<Long, List<StockTransferItemOpEntity>> stockTransferItemOpMapFromDb = stockTransferItemOpEntities.stream().collect(Collectors.groupingBy(StockTransferItemOpEntity::getStockTransferItemId));
        // key: itemOpId
        List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntities = stockTransferItemOpDetailQueryRepository.listByOpIdList(stockTransferItemOpEntities.stream().map(StockTransferItemOpEntity::getId).collect(Collectors.toList()));
        Map<Long, List<StockTransferItemOpDetailEntity>> stockTransferItemOpDetailMapFromDb = stockTransferItemOpDetailEntities.stream().collect(Collectors.groupingBy(StockTransferItemOpDetailEntity::getStockTransferItemOpId));
        // 获取忽略处理转入sku列表
        List<String> ignoreSku = getIgnoreSkuForExternalCallbackHandle(stockTransferItemMapFromDb, stockTransferItemOpMapFromDb, stockTransferItemOpDetailMapFromDb, stockTransferCallbackHandleDTO.getExternalTransferSkuInNumMap());

        stockTransferItemMapFromDb.forEach((inSkuCode, stockTransferItemEntity) -> {
            if (ignoreSku.contains(inSkuCode)) {
                log.info("忽略处理转入sku：{}", inSkuCode);
                return;
            }
            if (null == stockTransferItemEntity){
                log.info("库存转换任务明细不存在：{}", inSkuCode);
                return;
            }
            List<StockTransferItemOpEntity> stockTransferItemOpEntityList = stockTransferItemOpMapFromDb.get(stockTransferItemEntity.getId());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(stockTransferItemOpEntityList)){
                log.info("库存转换任务OP不存在：{}", inSkuCode);
                return;
            }
            stockTransferItemOpEntityList.forEach(op -> {
                List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntityList = stockTransferItemOpDetailMapFromDb.get(op.getId());
                for (StockTransferItemOpDetailEntity opDetail : stockTransferItemOpDetailEntityList) {
                    int num = calculate(opDetail.getTransferOutNum(), op.getTransferRatio()).intValue();
                    if (opDetail.getExternalTransferInNum() >= num){
                        log.info("三方回告转入数量 {} 大于等于 鲜沐系统操作的转入数量 {} ,不执行货损。 opDetail >>> {}", opDetail.getExternalTransferInNum(), num, JSON.toJSONString(opDetail));
                        continue;
                    }
                    // 鲜沐转入多出来的差异数量，我们要进行自动货损
                    int needDamageQuantity = num - opDetail.getExternalTransferInNum();
                    StockDamageCreateItem stockDamageCreateItem = new StockDamageCreateItem();
                    stockDamageCreateItem.setSku(inSkuCode);
                    stockDamageCreateItem.setBatchNo(opDetail.getTransferInBatch());
                    stockDamageCreateItem.setProductDate(DateUtil.toLocalDate(op.getProduceDate()));
                    stockDamageCreateItem.setQualityDate(DateUtil.toLocalDate(op.getShelfLife()));
                    stockDamageCreateItem.setQuantity(needDamageQuantity);
                    stockDamageCreateItems.add(stockDamageCreateItem);
                }
            });
        });
        StockDamageCreateEvent stockDamageCreateEvent = new StockDamageCreateEvent();
        stockDamageCreateEvent.setIdempotentNo(stockTransferEntity.getId().toString());
        stockDamageCreateEvent.setWarehouseNo(stockTransferEntity.getWarehouseNo().intValue());
        stockDamageCreateEvent.setTenantId(WmsConstant.XIANMU_TENANT_ID);
        stockDamageCreateEvent.setDamageTaskType(StockTaskTypeEnum.TRANSFER_DAMAGE_OUT.getId());
        stockDamageCreateEvent.setStockTransferId(stockTransferId);
        stockDamageCreateEvent.setStockDamageCreateItems(stockDamageCreateItems);
        // 发送货损创建消息
        if (!CollectionUtils.isEmpty(stockDamageCreateItems)) {
            mqProducer.send(Global.MQ_TOPIC, WmsConstant.STOCK_DAMAGE_CREATE, stockDamageCreateEvent);
        }
    }

    private void handleTransferOnlyIn(StockTransferCallbackHandleDTO stockTransferCallbackHandleDTO){
//        if (CollectionUtils.isEmpty(stockTransferCallbackHandleDTO.getNeedTransferOnlyInNum())){
//            log.info("handleTransferOnlyIn: needTransferOnlyInNum is empty");
//            return;
//        }
//        StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(stockTransferCallbackHandleDTO.getStockTransferId());
//        if (null == stockTransferEntity){
//            throw new BizException("未找到对应的库存转换单");
//        }
//        List<OperateTransferCallbackDTO> operateTransferCallbackDTOList = new ArrayList<>();
//        stockTransferCallbackHandleDTO.getNeedTransferOnlyInNum().forEach((key, value) -> {
//            operateTransferCallbackDTOList.add(OperateTransferCallbackDTO.createByUniqueKey(key, value));
//        });
//        if (CollectionUtils.isEmpty(operateTransferCallbackDTOList)){
//            return;
//        }
//        // find from db
//        List<StockTransferItemEntity> stockTransferItemEntities = stockTransferItemQueryRepository.listByStockTransferId(stockTransferCallbackHandleDTO.getStockTransferId());
//        Map<String, StockTransferItemEntity> stockTransferItemMapFromDb = stockTransferItemEntities.stream().collect(Collectors.toMap(StockTransferItemEntity::getTransferInSku, Function.identity(), (k1, k2) -> k1));
//        List<StockTransferItemOpEntity> stockTransferItemOpEntities = stockTransferItemOpQueryRepository.listByItemIdList(stockTransferItemEntities.stream().map(StockTransferItemEntity::getId).collect(Collectors.toList()));
//        Map<Long, List<StockTransferItemOpEntity>> stockTransferItemOpMapFromDb = stockTransferItemOpEntities.stream().collect(Collectors.groupingBy(StockTransferItemOpEntity::getStockTransferItemId));
//        List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntities = stockTransferItemOpDetailQueryRepository.listByOpIdList(stockTransferItemOpEntities.stream().map(StockTransferItemOpEntity::getId).collect(Collectors.toList()));
//        Map<Long, List<StockTransferItemOpDetailEntity>> stockTransferItemOpDetailMapFromDb = stockTransferItemOpDetailEntities.stream().collect(Collectors.groupingBy(StockTransferItemOpDetailEntity::getStockTransferItemOpId));
//
//
//        // 组装 仅转入 数据
//        List<StockTransferItemOpBO> onlyInData = new ArrayList<>();
//        Map<String, List<OperateTransferCallbackDTO>> transferOnlyInOpMap = operateTransferCallbackDTOList.stream().collect(Collectors.groupingBy(OperateTransferCallbackDTO::getTransferInSku));
//        transferOnlyInOpMap.forEach((transferInSku, value) -> {
//            if (CollectionUtils.isEmpty(value)){
//                return;
//            }
//            StockTransferItemEntity stockTransferItemEntity = stockTransferItemMapFromDb.get(transferInSku);
//            if (null == stockTransferItemEntity){
//                log.error("can not find stockTransferItemEntity by transferInSku:{} \n", transferInSku);
//                return;
//            }
//            Long transferItemId = stockTransferItemEntity.getId();
//
//            List<StockTransferItemOpEntity> stockTransferItemOpListFromDb = stockTransferItemOpMapFromDb.get(transferItemId);
//            if (CollectionUtils.isEmpty(stockTransferItemOpListFromDb)){
//                log.error("can not find stockTransferItemOpListFromDb by transferItemId:{} \n", transferItemId);
//                return;
//            }
//            Map<String, StockTransferItemOpEntity> outSkuAndOpMap = stockTransferItemOpListFromDb.stream().collect(Collectors.toMap(StockTransferItemOpEntity::getTransferOutSku, Function.identity(), (k1, k2) -> k1));
//            Map<String, List<OperateTransferCallbackDTO>> transferOnlyInOpOutSkuMap = value.stream().collect(Collectors.groupingBy(OperateTransferCallbackDTO::getTransferOutSku));
//            for (Map.Entry<String, List<OperateTransferCallbackDTO>> entry : transferOnlyInOpOutSkuMap.entrySet()) {
//                String transferOutSku = entry.getKey();
//                List<OperateTransferCallbackDTO> opList = entry.getValue();
//                StockTransferItemOpEntity stockTransferItemOpEntity = outSkuAndOpMap.get(transferOutSku);
//                if (null == stockTransferItemOpEntity) {
//                    log.error("can not find stockTransferItemOpEntity by transferOutSku:{} \n", transferOutSku);
//                    continue;
//                }
//                List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailFromDb = stockTransferItemOpDetailMapFromDb.get(stockTransferItemOpEntity.getId());
//                if (CollectionUtils.isEmpty(stockTransferItemOpDetailFromDb)){
//                    log.error("can not find stockTransferItemOpDetailFromDb by stockTransferItemOpEntity:{} \n", stockTransferItemOpEntity);
//                    continue;
//                }
//
//                StockTransferItemOpBO stockTransferItemOpBO = new StockTransferItemOpBO();
//                stockTransferItemOpBO.setWarehouseNo(stockTransferEntity.getWarehouseNo());
//                stockTransferItemOpBO.setStockTransferId(stockTransferEntity.getId());
//                stockTransferItemOpBO.setStockTransferItemId(transferItemId);
//                stockTransferItemOpBO.setType(TransferOpEnum.ONE_WAY.getCode());
//                stockTransferItemOpBO.setTransferRatio(stockTransferItemOpEntity.getTransferRatio());
//                stockTransferItemOpBO.setTransferOutSku(transferOutSku);
//                stockTransferItemOpBO.setTransferInSku(transferInSku);
//
//                // 组装转入参数
//                List<TransferOnlyInInfoBO> transferOnlyInInfos = new ArrayList<>();
//                opList.forEach(transferOnlyInInfo -> {
//                    transferOnlyInInfos.add(new TransferOnlyInInfoBO(
//                            transferOnlyInInfo.getTransferInQuantity(),
//                            DateUtil.parseLocalDateStringToLong(transferOnlyInInfo.getTransferInProduceDate()),
//                            DateUtil.parseLocalDateStringToLong(transferOnlyInInfo.getTransferInExpireDate())
//                    ));
//                });
//                stockTransferItemOpBO.setTransferOnlyInInfos(transferOnlyInInfos);
//                stockTransferItemOpBO.setOperator(WmsConstant.SYSTEM);
//
//                // 组装转出参数
//                StockTransferItemOpDetailEntity opDetailFromDb = stockTransferItemOpDetailFromDb.get(0);
//                TransferOutInfo transferOutInfo = new TransferOutInfo();
//                transferOutInfo.setTransferOutGoodsName("");
//                transferOutInfo.setTransferOutSku(transferOutSku);
//                transferOutInfo.setTransferOutBatch(opDetailFromDb.getTransferOutBatch());
//                transferOutInfo.setTransferOutCabinetNo(opDetailFromDb.getTransferOutCabinet());
//                transferOutInfo.setTransferOutNum(0L);
//                transferOutInfo.setProduceTime(opDetailFromDb.getProduceAt());
//                transferOutInfo.setShelfLife(opDetailFromDb.getShelfLife());
//                stockTransferItemOpBO.setTransferOutInfos(Collections.singletonList(transferOutInfo));
//                onlyInData.add(stockTransferItemOpBO);
//            }
//        });
//
//        // 执行仅转入
//        for (StockTransferItemOpBO onlyInDatum : onlyInData) {
//           stockTransferBizService.operateStockTransferInItemOnly(null);
//        }
    }

    /**
     * 三方转换回告告警
     * @param stockTransferCallbackHandleDTO 三方转换回告处理信息
     */
    private void transferOutSkuNotify(StockTransferCallbackHandleDTO stockTransferCallbackHandleDTO) {
        Long stockTransferId = stockTransferCallbackHandleDTO.getStockTransferId();
        log.info("开始操作「三方转换回告告警」行为:{}", stockTransferId);
        String warehouseName = "";
        try {
            StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(stockTransferId);
            WarehouseStorageDTO warehouseStorageDTO = warehouseStorageFacade.queryWarehouseStorage(stockTransferEntity.getWarehouseNo().intValue());
            warehouseName = warehouseStorageDTO.getWarehouseName();
        } catch (Exception e) {
            log.error("获取库存仓名称异常 >>> {}", stockTransferId, e);
        }
        if (CollectionUtils.isEmpty(stockTransferCallbackHandleDTO.getNeedAlarmOutSkuNum())){
            log.info("stockTransferCallbackHandleDTO.getNeedAlarmOutSkuNum is empty");
            return;
        }
        List<OperateTransferCallbackOutDTO> needAlarmDataList = new ArrayList<>();
        stockTransferCallbackHandleDTO.getNeedAlarmOutSkuNum().forEach((key, value) -> {
            needAlarmDataList.add(OperateTransferCallbackOutDTO.createByUniqueKey(key, value));
        });
        if (CollectionUtils.isEmpty(needAlarmDataList)){
            return;
        }
        List<Object> messageList = new ArrayList<>();
        List<String> firstLineMessage = new ArrayList<>();
        firstLineMessage.add("sku编码");
        firstLineMessage.add("生产日期");
        firstLineMessage.add("止保日期");
        firstLineMessage.add("三方转出差异数量");
        messageList.add(firstLineMessage);
        for (OperateTransferCallbackOutDTO operateTransferCallbackOutDTO : needAlarmDataList) {
            List<String> lineMessage = new ArrayList<>();
            lineMessage.add(operateTransferCallbackOutDTO.getTransferOutSku());
            lineMessage.add(operateTransferCallbackOutDTO.getTransferOutProduceDate());
            lineMessage.add(operateTransferCallbackOutDTO.getTransferOutExpireDate());
            lineMessage.add(operateTransferCallbackOutDTO.getTransferOutQuantity().toString());
            messageList.add(lineMessage);
        }
        FeishuBotUtil.sendMultiElementsMsgAndAtAll(
                feishuUrlConfig.getExternalCallbackInventoryAlarmFeiShuUrl(),
                new FeishuMultiElementsMsg(messageList, "【三方转换任务回告异常】" + warehouseName + " >>> 转换任务编号：" + stockTransferId, "red"));
    }

    /**
     * 转入效期有差异告警
     * @param callBackNotInDbTransferInNumMap  三方回告的，但是db里没有的效期 的转入数量map
     * @param stockTransferId 转换任务id
     */
    private void transferInNotify(Map<String, Integer> callBackNotInDbTransferInNumMap, Long stockTransferId) {
        log.info("开始进行「三方转换回告转入效期差异告警」行为:{}", stockTransferId);
        String warehouseName = "";
        try {
            StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(stockTransferId);
            WarehouseStorageDTO warehouseStorageDTO = warehouseStorageFacade.queryWarehouseStorage(stockTransferEntity.getWarehouseNo().intValue());
            warehouseName = warehouseStorageDTO.getWarehouseName();
        } catch (Exception e) {
            log.error("获取库存仓名称异常 >>> {}", stockTransferId, e);
        }
        if (CollectionUtils.isEmpty(callBackNotInDbTransferInNumMap)){
            log.info("stockTransferCallbackHandleDTO.callBackNotInDbTransferInNumMap is empty");
            return;
        }
        List<OperateTransferCallbackDTO> needAlarmDataList = new ArrayList<>();
        callBackNotInDbTransferInNumMap.forEach((key, value) -> {
            needAlarmDataList.add(OperateTransferCallbackDTO.createByUniqueKey(key, value));
        });
        if (CollectionUtils.isEmpty(needAlarmDataList)){
            return;
        }
        List<Object> messageList = new ArrayList<>();
        List<String> firstLineMessage = new ArrayList<>();
        firstLineMessage.add("转出sku");
        firstLineMessage.add("转入sku");
        firstLineMessage.add("生产日期");
        firstLineMessage.add("止保日期");
        firstLineMessage.add("三方转入数量");
        messageList.add(firstLineMessage);
        for (OperateTransferCallbackDTO operateTransferCallbackDTO : needAlarmDataList) {
            List<String> lineMessage = new ArrayList<>();
            lineMessage.add(operateTransferCallbackDTO.getTransferOutSku());
            lineMessage.add(operateTransferCallbackDTO.getTransferInSku());
            lineMessage.add(operateTransferCallbackDTO.getTransferInProduceDate());
            lineMessage.add(operateTransferCallbackDTO.getTransferInExpireDate());
            lineMessage.add(operateTransferCallbackDTO.getTransferInQuantity().toString());
            messageList.add(lineMessage);
        }
        FeishuBotUtil.sendMultiElementsMsgAndAtAll(
                feishuUrlConfig.getExternalCallbackInventoryAlarmFeiShuUrl(),
                new FeishuMultiElementsMsg(messageList, "【三方转换任务回告异常 - 回告的转入效期 不是 鲜沐操作的效期】" + warehouseName + " >>> 转换任务编号：" + stockTransferId, "red"));
    }

    /**
     * 获取忽略处理转入sku（内部转入数量与外部转入数量一致则不需再处理）
     * @param stockTransferItemMapFromDb
     * @param stockTransferItemOpMapFromDb
     * @param stockTransferItemOpDetailMapFromDb
     * @param externalTransferSkuInNumMap
     * @return
     */
    private List<String> getIgnoreSkuForExternalCallbackHandle(Map<String, StockTransferItemEntity> stockTransferItemMapFromDb,
                                                       Map<Long, List<StockTransferItemOpEntity>> stockTransferItemOpMapFromDb,
                                                       Map<Long, List<StockTransferItemOpDetailEntity>> stockTransferItemOpDetailMapFromDb,
                                                       Map<String, Integer> externalTransferSkuInNumMap) {
        List<String> ignoreSkuList = new ArrayList<>();
        Map<String, Integer> internalTransferSkuInNumMap = new HashMap<>();
        stockTransferItemMapFromDb.forEach((inSkuCode, stockTransferItemEntity) -> {
            if (null == stockTransferItemEntity){
                log.info("库存转换任务明细不存在：{}", inSkuCode);
                return;
            }
            List<StockTransferItemOpEntity> stockTransferItemOpEntityList = stockTransferItemOpMapFromDb.get(stockTransferItemEntity.getId());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(stockTransferItemOpEntityList)){
                log.info("库存转换任务OP不存在：{}", inSkuCode);
                return;
            }
            stockTransferItemOpEntityList.forEach(op -> {
                List<StockTransferItemOpDetailEntity> stockTransferItemOpDetailEntityList = stockTransferItemOpDetailMapFromDb.get(op.getId());
                for (StockTransferItemOpDetailEntity opDetail : stockTransferItemOpDetailEntityList) {
                    int num = calculate(opDetail.getTransferOutNum(), op.getTransferRatio()).intValue();
                    internalTransferSkuInNumMap.merge(inSkuCode, num, Integer::sum);
                }
            });
        });
        externalTransferSkuInNumMap.forEach((inSkuCode, externalQuantity) -> {
            Integer internalQuantity = internalTransferSkuInNumMap.get(inSkuCode);
            if (null == internalQuantity) {
                return;
            }
            // 内部转入数量与外部转入数量一致则不需再处理
            if (externalQuantity.equals(internalQuantity)) {
                ignoreSkuList.add(inSkuCode);
            }
        });
        log.info("获取忽略处理转入sku列表：{}", JSON.toJSONString(ignoreSkuList));
        return ignoreSkuList;
    }


    private Map<String, Integer> calculateTransferInNum(List<TransferItem> stockTransferItems) {
        Map<String, Integer> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(stockTransferItems)) {
            return resultMap;
        }
        for (TransferItem stockTransferItem : stockTransferItems) {
            List<TransferInItemDetail> transferInItemDetails = stockTransferItem.getTransferInItemDetails();
            if (CollectionUtils.isEmpty(transferInItemDetails)) {
                continue;
            }
            for (TransferInItemDetail transferInItemDetail : transferInItemDetails) {
                String uniqueKey = this.buildKey(stockTransferItem.getTransferOutSku(), stockTransferItem.getTransferInSku(), transferInItemDetail.getTransferInProductDate(), transferInItemDetail.getTransferInExpireDate());
                resultMap.put(
                        uniqueKey,
                        resultMap.getOrDefault(uniqueKey, 0) + transferInItemDetail.getTransferInQuantity()
                );
            }
        }
        return resultMap;
    }

    private Map<String, Integer> calculateTransferOutNum(List<TransferItem> stockTransferItems) {
        Map<String, Integer> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(stockTransferItems)) {
            return resultMap;
        }
        for (TransferItem stockTransferItem : stockTransferItems) {
            List<TransferOutItemDetail> transferOutItemDetails = stockTransferItem.getTransferOutItemDetails();
            if (CollectionUtils.isEmpty(transferOutItemDetails)) {
                continue;
            }
            for (TransferOutItemDetail transferOutItemDetail : transferOutItemDetails) {
                String uniqueKey = this.buildKey(stockTransferItem.getTransferOutSku(), transferOutItemDetail.getTransferOutProductDate(), transferOutItemDetail.getTransferOutExpireDate());
                resultMap.put(
                        uniqueKey,
                        resultMap.getOrDefault(uniqueKey, 0) + transferOutItemDetail.getTransferOutQuantity()
                );
            }
        }
        return resultMap;
    }

    /**
     * 构建一个唯一的关键字，用于标识特定的库存流转事件。
     * <p>
     * 关键字由四种信息组合而成，分别是：出库SKU代码、入库SKU代码、生产日期和过期日期。
     * 这种组合方式可以确保关键字的唯一性，并且能够快速定位到具体的库存流转事件。
     *
     * @param outSkuCode  出库商品的SKU代码，用于标识出库的商品。
     * @param inSkuCode   入库商品的SKU代码，用于标识入库的商品。
     * @param productDate 商品的生产日期，用于确保商品在有效期内。
     * @param expireDate  商品的过期日期，用于确保商品未过期。
     * @return 返回由四种信息组合而成的关键字，格式为"出库SKU#入库SKU#生产日期#过期日期"。
     */
    private String buildKey(String outSkuCode, String inSkuCode, String productDate, String expireDate) {
        // 使用"#"符号连接四种信息，构建唯一的关键字。
        return outSkuCode + "#" + inSkuCode + "#" + productDate + "#" + expireDate;
    }

    private String buildKey(String outSkuCode, String productDate, String expireDate) {
        // 使用"#"符号连接四种信息，构建唯一的关键字。
        return outSkuCode + "#" + productDate + "#" + expireDate;
    }

    private Long calculate(Long transferOutNum, String transferRatio) {
        String[] split = transferRatio.split(":");
        Double num = transferOutNum * Double.parseDouble(split[1]) / Double.parseDouble(split[0]);
        return num.longValue();
    }
}
