package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class ProcessingTaskMaterialReceiveReqDTO implements Serializable {

    /**
     * 加工任务成品ID
     */
    @NotNull(message = "加工任务成品ID不能为空")
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 原料SKU
     */
    private String materialSkuCode;

    /**
     * 原料领用数量
     */
    @NotNull(message = "原料领用数量和不能为空")
    private Integer materialSkuReceiveQuantity;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    private Long tenantId;

    /**
     * 领用批次库存信息
     */
    private List<ProcessingTaskMaterialInventoryReqDTO> materialReceiveList;
}
