package net.summerfarm.wms.application.skucodetrace.assmbler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.skucodetrace.inbound.vo.TraceBatchPrintVO;
import net.summerfarm.wms.application.skucodetrace.input.SkuBatchCodeTraceQuery;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceQueryParam;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class SkuBatchCodeTraceAssembler {


    public static SkuBatchCodeTraceQueryParam assembleQuery(SkuBatchCodeTraceQuery query) {
        if (query == null){
            return null;
        }

        SkuBatchCodeTraceQueryParam param = new SkuBatchCodeTraceQueryParam();

        // Copy properties from query to param
        param.setPageIndex(query.getPageIndex());
        param.setPageSize(query.getPageSize());
        param.setBeginStockTaskStorageSeq(query.getBeginStockTaskStorageSeq());
        param.setEndStockTaskStorageSeq(query.getEndStockTaskStorageSeq());

        param.setSkuBatchOnlyCode(query.getSkuBatchTraceCode());

        param.setSku(query.getSku());
        param.setPdId(query.getPdId());
        param.setStockTaskStorageId(query.getStockTaskStorageId());
        param.setPurchaseNo(query.getPurchaseNo());
        param.setWarehouseNo(query.getWarehouseNo());
//        param.setSkuQuantity(null);
        param.setPsoNo(query.getPsoNo());
        param.setOrderNo(query.getOrderNo());
        param.setFulfillmentNo(query.getFulfillmentNo());
        param.setStoreNo(query.getStoreNo());
        param.setDeliveryTime(query.getDeliveryTime());
        param.setMerchantNameLike(query.getMerchantNameLike());
        param.setBuyerNameLike(query.getBuyerNameLike());

        param.setState(query.getState());
        param.setStateList(query.getStateList());
        param.setCreateDateStart(query.getCreateDateStart());
        param.setCreateDateEnd(query.getCreateDateEnd());
        param.setLabelType(query.getLabelType());
        return param;
    }

    /**
     * 转换vo列表
     *
     * @param list
     * @param storeNameMap
     * @param goodsInfoMap
     * @return
     */
    public static List<TraceBatchPrintVO> assembleVOList(List<SkuBatchCodeTraceEntity> list,
                                                         Map<Integer, String> storeNameMap,
                                                         Map<String, GoodsInfoDTO> goodsInfoMap) {
        List<TraceBatchPrintVO> voList = new ArrayList<>();
        for (SkuBatchCodeTraceEntity entity : list) {
            String storeName = storeNameMap == null ? null : storeNameMap.get(entity.getStoreNo());
            GoodsInfoDTO goodsInfoDTO = goodsInfoMap == null ? null : goodsInfoMap.get(entity.getSku());
            TraceBatchPrintVO vo = assembleVO(entity, storeName, goodsInfoDTO);

            voList.add(vo);
        }
        return voList;
    }

    public static List<TraceBatchPrintVO> assembleVOList(List<SkuBatchCodeTraceEntity> list,
                                                         Map<Integer, String> storeNameMap,
                                                         Map<String, GoodsInfoDTO> goodsInfoMap,
                                                         Map<String, LocalDate> merchantDeliveryTimeFirstMap,
                                                         Map<String, LocalDate> merchantDeliveryTimeRecentlyMap,
                                                         Integer deliveryTimeStatisticPeriod) {
        List<TraceBatchPrintVO> voList = new ArrayList<>();
        for (SkuBatchCodeTraceEntity entity : list) {
            String storeName = storeNameMap == null ? null : storeNameMap.get(entity.getStoreNo());
            GoodsInfoDTO goodsInfoDTO = goodsInfoMap == null ? null : goodsInfoMap.get(entity.getSku());
            LocalDate firstDeliveryTime = merchantDeliveryTimeFirstMap.get(entity.getMerchantId());
            LocalDate recentlyDeliveryTime = merchantDeliveryTimeRecentlyMap.get(entity.getMerchantId() + "_" + entity.getDeliveryTime());

            TraceBatchPrintVO vo = assembleVO(entity, storeName, goodsInfoDTO, firstDeliveryTime, recentlyDeliveryTime, deliveryTimeStatisticPeriod);

            voList.add(vo);
        }
        return voList;
    }

    public static TraceBatchPrintVO assembleVO(SkuBatchCodeTraceEntity entity, String storeName, GoodsInfoDTO goodsInfoDTO) {
        TraceBatchPrintVO vo = new TraceBatchPrintVO();

        // Copy properties from entity to vo
        vo.setStockTaskStorageSeq(entity.getStockTaskStorageSeq());
        vo.setMerchantName(entity.getMerchantName());
        vo.setSkuBatchTraceCode(entity.getSkuBatchTraceCode());
        vo.setSku(entity.getSku());
        vo.setPdId(entity.getPdId());
        vo.setPdName(entity.getPdName());
        vo.setSpecification(entity.getSpecification());
        vo.setBatchDate(entity.getBatchDate());
        vo.setSkuPlanReceiptCount(entity.getSkuPlanReceiptCount());
        vo.setSkuCurrentSeq(entity.getSkuCurrentSeq());
        vo.setMerchantSkuTotalNum(entity.getMerchantSkuTotalNum());
        vo.setBuyerName(entity.getBuyerName());
        vo.setPathCode(entity.getPathCode());

        vo.setPsoNo(entity.getPsoNo());
        vo.setOrderNo(entity.getOrderNo());
        vo.setFulfillmentNo(entity.getFulfillmentNo());

        vo.setPathSequence(entity.getPathSequence());
        vo.setStockTaskStorageId(entity.getStockTaskStorageId());
        vo.setWeight(entity.getWeight());
        vo.setWeightPerson(entity.getWeightPerson());
        vo.setWeightTime(entity.getWeightTime());
        vo.setState(entity.getState());
        vo.setCreateTime(entity.getCreateTime());
        vo.setStoreNo(entity.getStoreNo());

        vo.setStoreName(storeName);
        vo.setStorageLocation(goodsInfoDTO != null ? goodsInfoDTO.getStorageLocation() : null);
        vo.setGrossWeight(goodsInfoDTO != null ? goodsInfoDTO.getWeight() : null);

        return vo;
    }

    public static TraceBatchPrintVO assembleVO(SkuBatchCodeTraceEntity entity, String storeName, GoodsInfoDTO goodsInfoDTO, LocalDate firstDeliveryTime, LocalDate recentlyDeliveryTime, Integer deliveryTimeStatisticPeriod) {
        TraceBatchPrintVO vo = new TraceBatchPrintVO();

        // Copy properties from entity to vo
        vo.setStockTaskStorageSeq(entity.getStockTaskStorageSeq());
        vo.setMerchantName(entity.getMerchantName());
        vo.setSkuBatchTraceCode(entity.getSkuBatchTraceCode());
        vo.setSku(entity.getSku());
        vo.setPdId(entity.getPdId());
        vo.setPdName(entity.getPdName());
        vo.setSpecification(entity.getSpecification());
        vo.setBatchDate(entity.getBatchDate());
        vo.setSkuPlanReceiptCount(entity.getSkuPlanReceiptCount());
        vo.setSkuCurrentSeq(entity.getSkuCurrentSeq());
        vo.setMerchantSkuTotalNum(entity.getMerchantSkuTotalNum());
        vo.setBuyerName(entity.getBuyerName());
        vo.setPathCode(entity.getPathCode());

        vo.setPsoNo(entity.getPsoNo());
        vo.setOrderNo(entity.getOrderNo());
        vo.setFulfillmentNo(entity.getFulfillmentNo());

        vo.setPathSequence(entity.getPathSequence());
        vo.setStockTaskStorageId(entity.getStockTaskStorageId());
        vo.setWeight(entity.getWeight());
        vo.setWeightPerson(entity.getWeightPerson());
        vo.setWeightTime(entity.getWeightTime());
        vo.setState(entity.getState());
        vo.setCreateTime(entity.getCreateTime());
        vo.setStoreNo(entity.getStoreNo());
        vo.setLabelType(entity.getLabelType());

        vo.setStoreName(storeName);
        vo.setStorageLocation(goodsInfoDTO != null ? goodsInfoDTO.getStorageLocation() : null);
        vo.setGrossWeight(goodsInfoDTO != null ? goodsInfoDTO.getWeight() : null);

        // 无第一次配送时间认定新客，打标近期未配送过
        if (null == firstDeliveryTime) {
            vo.setDeliveredRecentlyOption(false);
            log.info("无第一次配送时间认定新客，商户id：{}，商户名称：{}，溯源表id：{}", entity.getMerchantId(), entity.getMerchantName(), entity.getId());
            return vo;
        } else {
            // 第一次配送时间到本次配送时间相差天数（包含本次配送日需加一天）
            Long firstDeliveryTimeBetweenDay = DateUtil.getBetweenDay(firstDeliveryTime, entity.getDeliveryTime()) + 1L;
            // 第一次配送时间到本次配送时间相差天数<=配送统计周期口径（默认28天内），认定新客，打标近期未配送过
            if (firstDeliveryTimeBetweenDay <= deliveryTimeStatisticPeriod) {
                vo.setDeliveredRecentlyOption(false);
                log.info("第一次配送时间【{}】到本次配送时间【{}】相差天数【{}】<=配送统计周期口径【{}】，商户id：{}，商户名称：{}，溯源表id：{}", firstDeliveryTime, entity.getDeliveryTime(), firstDeliveryTimeBetweenDay, deliveryTimeStatisticPeriod, entity.getMerchantId(), entity.getMerchantName(), entity.getId());
                return vo;
            }
            if (null == recentlyDeliveryTime) {
                vo.setDeliveredRecentlyOption(false);
                log.info("最近无配送日期，商户id：{}，商户名称：{}，溯源表id：{}", entity.getMerchantId(), entity.getMerchantName(), entity.getId());
                return vo;
            }
            // 最近一次配送到本次配送相差天数
            Long recentlyDeliveryTimeBetweenDay = DateUtil.getBetweenDay(recentlyDeliveryTime, entity.getDeliveryTime());
            // 近期配送时间到本次配送时间相差天数>=配送统计周期口径（默认28天内），认定流失客户，打标近期未配送过
            if (recentlyDeliveryTimeBetweenDay >= deliveryTimeStatisticPeriod) {
                vo.setDeliveredRecentlyOption(false);
                log.info("近期配送时间【{}】到本次配送时间【{}】相差天数【{}】>=配送统计周期口径【{}】，商户id：{}，商户名称：{}，溯源表id：{}", recentlyDeliveryTime, entity.getDeliveryTime(), recentlyDeliveryTimeBetweenDay, deliveryTimeStatisticPeriod, entity.getMerchantId(), entity.getMerchantName(), entity.getId());
                return vo;
            } else {
                log.info("未命中近期未配送打标规则，，商户id：{}，商户名称：{}，溯源表id：{}", entity.getMerchantId(), entity.getMerchantName(), entity.getId());
                vo.setDeliveredRecentlyOption(true);
            }
        }


        return vo;
    }

}
