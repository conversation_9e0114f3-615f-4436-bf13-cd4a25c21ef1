package net.summerfarm.wms.application.skucodetrace.listener;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.skucodetrace.service.SkuBatchCodeTraceCommandService;
import net.summerfarm.wms.application.acl.mq.msg.SkuBatchCodeTraceMsg;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.RedisUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Description: 溯源批次码生成<br/>
 * date: 2024/8/8 17:17<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
@MqListener(topic = Global.STOCK_TASK,
        tag = WmsConstant.SKU_BATCH_CODE_TRACE_CREATE_TAG,
        consumerGroup = Global.MQ_GROUP,
        maxReconsumeTimes = 5)
public class SkuBatchCodeTraceCreateConsumer extends AbstractMqListener<SkuBatchCodeTraceMsg> {

    @Resource
    private SkuBatchCodeTraceCommandService skuBatchCodeTraceCommandService;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void process(SkuBatchCodeTraceMsg skuBatchCodeTraceMsg) {
        Long stockStorageTaskId = skuBatchCodeTraceMsg.getStockStorageTaskId();
        if(stockStorageTaskId == null){
            throw new BizException("入库任务Id不能为空");
        }
        try {
            String lockKey = "WMS:skuBatchCodeTraceCreate:" + stockStorageTaskId;
            redisUtil.executeCallableByJustTryOneTime(lockKey,2L, TimeUnit.HOURS,()->{
                skuBatchCodeTraceCommandService.createBatchCodeAndTrace(stockStorageTaskId);
                return 1;
            });
        } catch (Exception ex) {
            throw ex;
        }
    }
}
