package net.summerfarm.wms.application.processingtask;

import net.summerfarm.goods.client.enums.SubAgentTypeEnum;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingMaterialConfigUpsetReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigUpsetResDTO;
import net.summerfarm.wms.application.processingtask.service.ProcessingConfigCommandService;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingConfigUpsetReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigProductSkuSpecDTO;
import net.summerfarm.wms.application.processingtask.factory.ProcessingConfigAggregateFactory;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskTypeEnum;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingConfigDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingConfigUpdateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingConfigRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ProcessingConfigCommandServiceImpl implements ProcessingConfigCommandService {
    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ProcessingConfigRepository processingConfigRepository;
    @Autowired
    private ProcessingConfigDomainService processingConfigDomainService;

    @Override
    public CommonResult<ProcessingConfigUpsetResDTO> upsert(ProcessingConfigUpsetReqDTO reqDTO) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();
        if (tenantId == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"登录失效，请刷新后重试");
        }

        // region check
        if (reqDTO == null || reqDTO.getProductSkuWeight() == null ||
                CollectionUtils.isEmpty(reqDTO.getProcessingMaterialConfigList())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "参数有误", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        ProcessingTaskTypeEnum processingTaskTypeEnum = ProcessingTaskTypeEnum.getTypeByValue(reqDTO.getType());
        if (processingTaskTypeEnum == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "加工类型参数有误", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 原料倍数检查
        for (ProcessingMaterialConfigUpsetReqDTO materialConfigUpsetReqDTO : reqDTO.getProcessingMaterialConfigList()) {
            if (materialConfigUpsetReqDTO.getMaterialSkuRatioNum() == null){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "输入的原料比例为空", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            if (materialConfigUpsetReqDTO.getMaterialSkuWeight() == null){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "输入的原料重量为空", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            // 组拆装
            if (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(reqDTO.getType())) {
                continue;
            }

            // 非组装需要单位一致
            if (reqDTO.getProductSkuUnit() == null ||
                    !reqDTO.getProductSkuUnit().equals(materialConfigUpsetReqDTO.getMaterialSkuUnit())){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "输入的原料和成品单位不一致", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
        }

        // 非组装校验重量
        if (!ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(reqDTO.getType())) {
            BigDecimal totalMaterialRatioWeight = reqDTO.getProcessingMaterialConfigList().stream()
                    .map(s -> s.getMaterialSkuWeight().multiply(
                            BigDecimal.valueOf(s.getMaterialSkuRatioNum())))
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            if (totalMaterialRatioWeight.compareTo(reqDTO.getProductSkuWeight().multiply(
                        BigDecimal.valueOf(reqDTO.getProductSkuRatioNum()))) != 0 &&
                    !reqDTO.getSkipRatioAndWeightCheck()) {
                return CommonResult.ok(ProcessingConfigUpsetResDTO.failByRatioAndRatio(
                        "原料重量*比例数量≠成品重量*比例数量，是否确认继续\n" +
                                "原料总重：" + totalMaterialRatioWeight + reqDTO.getProcessingMaterialConfigList().get(0).getMaterialSkuUnit() +
                                "；成品总重：" + reqDTO.getProductSkuWeight().multiply(BigDecimal.valueOf(reqDTO.getProductSkuRatioNum()))  + reqDTO.getProductSkuUnit()));
            }
        }

        // 加工比例与倍数的比较
        if (!CollectionUtils.isEmpty(reqDTO.getProductSkuSpecList())) {
            for (ProcessingConfigProductSkuSpecDTO processingConfigProductSkuSpecDTO : reqDTO.getProductSkuSpecList()) {
                BigDecimal divide = processingConfigProductSkuSpecDTO.getProductSkuSpecWeight()
                        .divide(reqDTO.getProductSkuWeight(), 2, RoundingMode.DOWN);
                if (divide.compareTo(BigDecimal.valueOf(divide.intValue())) != 0) {
                    return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                            "输入的成品规格重量必须是成品重量的倍数", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
                }
            }
        }

        Product productSpu = null;
        Map<String, Product> materialSpuMap = new java.util.HashMap<>();
        Map<Long, Product> materialSpuIdMap = new java.util.HashMap<>();
        if (WmsConstant.XIANMU_TENANT_ID.equals(tenantId)){
            productSpu = productRepository.findProductFromGoodsCenter(
                    reqDTO.getWarehouseNo().longValue(),reqDTO.getProductSkuCode());
            if (productSpu == null){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "请求成品不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
            List<String> materialSkuCodeList = reqDTO.getProcessingMaterialConfigList().stream()
                    .map(ProcessingMaterialConfigUpsetReqDTO::getMaterialSkuCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            materialSpuMap = productRepository
                    .mapProductsBySkusOnlyGoods(reqDTO.getWarehouseNo().longValue(), materialSkuCodeList);
            if (materialSkuCodeList.size() != materialSpuMap.values().size()){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "请求原料不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
        } else {
            productSpu = productRepository.findProductFromGoodsCenter(reqDTO.getWarehouseNo().longValue(), null ,
                    reqDTO.getProductSkuSaasId());
            if (productSpu == null){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "请求成品不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
            reqDTO.setProductSkuCode(productSpu.getSku());
            List<Long> materialSkuSaasIdList = reqDTO.getProcessingMaterialConfigList().stream()
                    .map(ProcessingMaterialConfigUpsetReqDTO::getMaterialSkuSaasId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            materialSpuIdMap = productRepository
                    .mapProductsBySaasSkuIdOnlyGoods(reqDTO.getWarehouseNo().longValue(), materialSkuSaasIdList);
            if (materialSkuSaasIdList.size() != materialSpuIdMap.values().size()){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "请求原料不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
            for (ProcessingMaterialConfigUpsetReqDTO materialConfigUpsetReqDTO : reqDTO.getProcessingMaterialConfigList()) {
                Product materialSpu = materialSpuIdMap.get(materialConfigUpsetReqDTO.getMaterialSkuSaasId());
                if (materialSpu == null){
                    return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                            "请求原料不存在" + materialConfigUpsetReqDTO.getMaterialSkuSaasId(),
                            String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
                }
                materialConfigUpsetReqDTO.setMaterialSkuCode(materialSpu.getSku());
            }
        }

        // 货品属性校验，代销入仓品只能和代销入仓品转换
        for (Product materialSpu : materialSpuMap.values()) {
            if ((SubAgentTypeEnum.CONSIGNMENT_IN_WAREHOUSING.getType().equals(materialSpu.getSubType())
                    || SubAgentTypeEnum.CONSIGNMENT_IN_WAREHOUSING.getType().equals(productSpu.getSubType()))
                    && !materialSpu.getSubType().equals(productSpu.getSubType())) {
                String errorMsg = "原料sku：" + materialSpu.getSku() + "【" + SubAgentTypeEnum.getDescByType(materialSpu.getSubType()) + "】" +
                        "成品sku：" + reqDTO.getProductSkuCode() + "【" + SubAgentTypeEnum.getDescByType(productSpu.getSubType()) + "】" +
                        "代销入仓品不可与其他商品性质互转";
                throw new net.xianmu.common.exception.BizException(errorMsg);
            }
        }

        // endregion check

        // region update
        if (reqDTO.getId() != null){
            ProcessingConfig queryProcessingConfig = processingConfigRepository.queryById(reqDTO.getId());
            if (queryProcessingConfig == null){
                throw new BizException("加工配置不存在，请稍后重试", ErrorCodeNew.PARAM_ERROR);
            }

            ProcessingConfigUpdateAggregate updateAggregate = ProcessingConfigAggregateFactory.newUpdateInstance(
                    tenantId,
                    queryProcessingConfig, reqDTO, materialSpuMap, materialSpuIdMap, productSpu
            );
            processingConfigDomainService.update(updateAggregate);
            return CommonResult.ok(ProcessingConfigUpsetResDTO.success());
        }
        // endregion

        // region create
        // 校验是否创建
        List<ProcessingConfig> processingConfigList = processingConfigRepository.listByWarehouseAndProductSkuCodeAndType(
                reqDTO.getWarehouseNo(), reqDTO.getProductSkuCode(), processingTaskTypeEnum.getValue());
        if (!CollectionUtils.isEmpty(processingConfigList)){
            throw new BizException("请求加工配置已存在，请刷新搜索记录进行更新", ErrorCodeNew.PARAM_ERROR);
        }

        ProcessingConfigCreateAggregate createAggregate = ProcessingConfigAggregateFactory.newCreateInstance(
                tenantId,
                reqDTO, materialSpuMap, materialSpuIdMap, productSpu
        );
        Long id = processingConfigDomainService.create(createAggregate);
        return CommonResult.ok(ProcessingConfigUpsetResDTO.success());
        // endregion
    }

    /**
     * 作废
     *
     * @param processingConfigId 加工规则编码
     * @return 操作结果
     */
    @Override
    public Long invalid(Long processingConfigId) {
        // 查询校验要作废的加工规则是否存在
        ProcessingConfig processingConfig = processingConfigRepository.queryById(processingConfigId);
        if (Objects.isNull(processingConfig)){
            throw new BizException("加工配置不存在，作废失败", ErrorCodeNew.PARAM_ERROR);
        }

        // 存在加工中任务
//        if (processingTaskProductRepository.countProcessingByWarehouseAndSkuCode(processingConfig.getWarehouseNo(),
//                processingConfig.getProductSkuCode()) > 0){
//            throw new BizException(ErrorCodeNew.PARAM_ERROR.getCode(), "存在sku加工中的任务，请稍后进行作废");
//        }
        String operatorName = LoginInfoThreadLocal.getCurrentUserName();
        return processingConfigDomainService.invalid(processingConfig.getId(), operatorName);
    }
}
