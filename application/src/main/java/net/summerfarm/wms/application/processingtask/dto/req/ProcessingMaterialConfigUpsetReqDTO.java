package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessingMaterialConfigUpsetReqDTO implements Serializable {

    /**
     * 原料sku
     */
    private String materialSkuCode;

    /**
     * 原料skuId
     */
    private Long materialSkuSaasId;

    /**
     * 原料sku名称
     */
    private String materialSkuName;

    /**
     * 原料重量
     */
    private BigDecimal materialSkuWeight;

    /**
     * 原料sku单位
     */
    private String materialSkuUnit;

    /**
     * 原料sku单位描述
     */
    private String materialSkuUnitDesc;

    /**
     * 原料sku比例数量
     */
    private Integer materialSkuRatioNum;
}
