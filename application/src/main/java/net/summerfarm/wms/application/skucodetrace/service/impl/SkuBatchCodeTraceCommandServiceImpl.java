package net.summerfarm.wms.application.skucodetrace.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.batch.service.SkuBatchCodeCommandService;
import net.summerfarm.wms.application.skucodetrace.assmbler.SkuBatchCodeTraceAssembler;
import net.summerfarm.wms.application.skucodetrace.inbound.input.command.FulfillmentOrderInfoInput;
import net.summerfarm.wms.application.skucodetrace.inbound.input.command.ThresholdUpdateInput;
import net.summerfarm.wms.application.skucodetrace.inbound.vo.ThresholdUpdateResultVO;
import net.summerfarm.wms.application.skucodetrace.service.SkuBatchCodeTraceCommandService;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.instore.domainobject.StockStorageItem;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.enums.StockStoragePurchaseModeEnums;
import net.summerfarm.wms.domain.instore.repository.StockStorageItemRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.skucode.SkuBatchCodeRepository;
import net.summerfarm.wms.domain.skucode.domainobject.FindSkuCodeCd;
import net.summerfarm.wms.domain.skucode.domainobject.SkuBatchCode;
import net.summerfarm.wms.domain.skucode.enums.SkuBatchCodeBizTypeEnums;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.enums.SkuBatchCodeTraceEnums;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceCreateCommandParam;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceCommandRepository;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceQueryRepository;
import net.summerfarm.wms.domain.skucodetrace.service.SkuBatchCodeTraceCommandDomainService;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.wms.initConfig.SkuBatchCodePrintConfig;
import net.summerfarm.wms.initConfig.SkuBatchCodeTraceConfig;
import net.summerfarm.wms.instore.enums.StockStorageTypeEnums;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 唯一溯源码操作服务<br/>
 * date: 2024/8/7 14:36<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class SkuBatchCodeTraceCommandServiceImpl implements SkuBatchCodeTraceCommandService {

    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    private StockStorageItemRepository stockStorageItemRepository;
    @Resource
    private SkuBatchCodeCommandService skuBatchCodeCommandService;
    @Resource
    private SkuBatchCodeTraceCommandDomainService skuBatchCodeTraceCommandDomainService;
    @Resource
    private SkuBatchCodeTraceQueryRepository skuBatchCodeTraceQueryRepository;
    @Resource
    private SkuBatchCodeRepository skuBatchCodeRepository;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private WarehouseStorageRepository warehouseStorageRepository;
    @Resource
    private SkuBatchCodePrintConfig skullBatchCodePrintConfig;
    @Resource
    private SkuBatchCodeTraceConfig skuBatchCodeTraceConfig;
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createBatchCodeAndTrace(Long stockStorageTaskId) {
        if(stockStorageTaskId == null){
            throw new BizException("入库任务Id不能为空");
        }
        // 查询入库任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockStorageTaskId);
        if(stockTaskStorage == null){
            throw new BizException("入库任务不存在");
        }
        if(!Objects.equals(stockTaskStorage.getType(), StockStorageTypeEnums.PURCHASE_IN.getId())){
            log.error("非采购入库类型不支持自动生成溯源码,入库任务Id:{}",stockStorageTaskId,new BizException("非采购入库类型，不支持自动生成溯源码"));
            return;
        }
        // 入库相关明细
        List<StockStorageItem> stockStorageItems = stockStorageItemRepository.queryStockStorageItem(stockStorageTaskId);
        if(CollectionUtils.isEmpty(stockStorageItems)){
            log.error("入库明细为空,入库任务Id:{}",stockStorageTaskId,new BizException("入库明细为空"));
            return;
        }

        Integer purchaseMode = stockTaskStorage.getPurchaseMode();

        List<Integer> couldPurchaseModeList = Arrays.asList(StockStoragePurchaseModeEnums.POP_PURCHASE.getCode(), StockStoragePurchaseModeEnums.POP_PURCHASE_T2.getCode());
        if(!couldPurchaseModeList.contains(purchaseMode)){
            log.info("非采购入库类型不支持自动生成溯源码,入库任务Id:{}",stockStorageTaskId,new BizException("非采购入库类型，不支持自动生成溯源码"));
            return;
        }

        // 根据入库明细创建批次码
        Map<Long, SkuBatchCode> stockStorageItemId2BatchCodeMap = this.skuBatchCodeCreate(stockTaskStorage, stockStorageItems);
        // 创建溯源码
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntities = this.createCodeTrace(stockTaskStorage, stockStorageItems, stockStorageItemId2BatchCodeMap);

        if(Objects.equals(purchaseMode, StockStoragePurchaseModeEnums.POP_PURCHASE.getCode())){
            // POP 采购入库
            skuBatchCodeTraceEntities.forEach(skuBatchCodeTrace -> {
                skuBatchCodeTrace.setLabelType(SkuBatchCodeTraceEnums.LabelType.POP.getValue());
            });
            // 初始化溯源码的订单信息
            skuBatchCodeTraceCommandDomainService.updateCodeTraceOrderInfo(skuBatchCodeTraceEntities);
        }
        if(Objects.equals(purchaseMode, StockStoragePurchaseModeEnums.POP_PURCHASE_T2.getCode())){
            // POP T+2
            skuBatchCodeTraceEntities.forEach(skuBatchCodeTrace -> {
                skuBatchCodeTrace.setLabelType(SkuBatchCodeTraceEnums.LabelType.POP_T2.getValue());
            });
            skuBatchCodeTraceCommandDomainService.updateCodeTraceStoreNoInfo(skuBatchCodeTraceEntities);
        }
    }

    /**
     * 创建溯源码
     * @param popStockTaskStorage 入库任务
     * @param popStockStorageItems 入库任务明细
     * @param stockStorageItemId2BatchCodeMap 批次码信息
     */
    private List<SkuBatchCodeTraceEntity> createCodeTrace(StockTaskStorage popStockTaskStorage, List<StockStorageItem> popStockStorageItems, Map<Long, SkuBatchCode> stockStorageItemId2BatchCodeMap) {
        Map<Long, StockStorageItem> id2StockStorageItemMap = popStockStorageItems.stream().collect(Collectors.toMap(StockStorageItem::getId, Function.identity()));
        List<SkuBatchCodeTraceCreateCommandParam> params = new ArrayList<>();
        List<String> notNeedCreateSkus = skuBatchCodeTraceConfig.getNotNeedCreateSkus();
        id2StockStorageItemMap.forEach((stockStorageItemId, stockStorageItem) -> {
            SkuBatchCode skuBatchCode = stockStorageItemId2BatchCodeMap.get(stockStorageItemId);
            if(notNeedCreateSkus.contains(stockStorageItem.getSku())){
                log.info("商品编码:{} 不需要创建溯源码",stockStorageItem.getSku());
                return;
            }
            SkuBatchCodeTraceCreateCommandParam param = SkuBatchCodeTraceCreateCommandParam.builder()
                    .sku(stockStorageItem.getSku())
                    .skuBatchOnlyCode(skuBatchCode.getSkuBatchOnlyCode())
                    .stockTaskStorageId(popStockTaskStorage.getId())
                    .batchDate(LocalDate.now())
                    .purchaseNo(popStockTaskStorage.getSourceId())
                    .warehouseNo(popStockTaskStorage.getInWarehouseNo())
                    .skuQuantity(stockStorageItem.getQuantity())
                    .psoNo(popStockTaskStorage.getPsoNo())
                    .deliveryTime(popStockTaskStorage.getExpectTime().toLocalDate())
                    .build();
            params.add(param);
        });

        // 溯源码创建 初始化
        return skuBatchCodeTraceCommandDomainService.skuBatchCodeTraceCreate(params);
    }

    /**
     * 创建批次码
     * @param popStockTaskStorage 入库任务
     * @param popStockStorageItems 入库任务明细
     * @return 入库任务明细ID 对应的 批次码
     */
    private Map<Long, SkuBatchCode> skuBatchCodeCreate(StockTaskStorage popStockTaskStorage, List<StockStorageItem> popStockStorageItems) {
        Map<Long, SkuBatchCode> stockStorageItemId2BatchCodeMap = new HashMap<>();
        Long stockStorageTaskId = popStockTaskStorage.getId();
        for (StockStorageItem popStockStorageItem : popStockStorageItems) {
            Long stockStorageItemId = popStockStorageItem.getId();
            String sku = popStockStorageItem.getSku();
            String purchaseNo = popStockTaskStorage.getSourceId();
            SkuBatchCode skuBatchCode = new SkuBatchCode(sku, purchaseNo, LocalDate.now());
            skuBatchCode.setBizType(SkuBatchCodeBizTypeEnums.IN_STORE_TASK.getCode());
            skuBatchCode.setBizId(stockStorageTaskId);

            skuBatchCodeCommandService.createBatchCode(skuBatchCode);

            if(StringUtils.isBlank(skuBatchCode.getSkuBatchOnlyCode())){
                FindSkuCodeCd findSkuCodeCd = FindSkuCodeCd.builder()
                        .bizId(skuBatchCode.getBizId())
                        .bizType(skuBatchCode.getBizType())
                        .sku(skuBatchCode.getSku())
                        .purchaseNo(skuBatchCode.getPurchaseNo())
                        .build();
                //遍历处理 已经生成的数据过滤掉
                List<SkuBatchCode> skuBatchCodeList = skuBatchCodeRepository.querySkuBatchCodeByCd(findSkuCodeCd);
                if(!CollectionUtils.isEmpty(skuBatchCodeList)){
                    skuBatchCode.setSkuBatchOnlyCode(skuBatchCodeList.get(0).getSkuBatchOnlyCode());
                }else{
                    BizException bizException = new BizException("唯一溯源码操作服务-唯一批次码为空:stockStorageTaskId:" + stockStorageTaskId + ",sku:" + sku);
                    log.error("唯一溯源码操作服务-唯一批次码为空:stockStorageTaskId:{},sku:{}" , stockStorageTaskId, sku,bizException);
                    throw bizException;
                }
            }
            stockStorageItemId2BatchCodeMap.put(stockStorageItemId, skuBatchCode);
        }
        return stockStorageItemId2BatchCodeMap;
    }

    @Override
    public CommonResult<ThresholdUpdateResultVO> upsetWeight(ThresholdUpdateInput input) {
        if (input == null || StringUtils.isEmpty(input.getSkuBatchTraceCode())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "批次唯一溯源码不能为空");
        }

        if(input.getWeight().compareTo(BigDecimal.ZERO) < 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "批次唯一溯源码重量不能小于0");
        }
        SkuBatchCodeTraceEntity skuBatchCodeTraceEntity = skuBatchCodeTraceQueryRepository.findBySkuBatchTraceCode(
                input.getSkuBatchTraceCode());
        if (skuBatchCodeTraceEntity == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求溯源码不存在");
        }


        // 阈值校验
        if (input.getSkipThresholdCheck() == null || !input.getSkipThresholdCheck()) {
            WarehouseStorageCenterEntity warehouseStorageCenter = warehouseStorageRepository
                    .selectByWarehouseNo(skuBatchCodeTraceEntity.getWarehouseNo());
            GoodsInfoDTO goodsInfoDTO = goodsReadFacade.findGoodsInfoBySkuList(
                    warehouseStorageCenter.getTenantId(), skuBatchCodeTraceEntity.getSku());
            BigDecimal standardWeight = goodsInfoDTO.getWeight();
            if (standardWeight != null && input.getWeight() != null) {
                BigDecimal differenceRatio = calculateWeightDifferenceRatio(input.getWeight(), standardWeight);

                // 阈值校验
                if (differenceRatio.compareTo(skullBatchCodePrintConfig.getWeightThresholdRatio()) > 0) {
                    ThresholdUpdateResultVO thresholdUpdateResultVO = ThresholdUpdateResultVO.builder()
                            .currentThresholdBlock(true)
                            .msg("重量不能小于标准重量")
                            .skuBatchTraceCode(input.getSkuBatchTraceCode())
                            .traceBatchPrintVO(SkuBatchCodeTraceAssembler.assembleVO(
                                    skuBatchCodeTraceEntity, null, null))
                            .build();
                    return CommonResult.ok(thresholdUpdateResultVO);
                }
            }
        }

        //记录重量
        LocalDateTime weightDateTime = LocalDateTime.now();
        skuBatchCodeTraceCommandDomainService.updateCodeTraceWeight(
                skuBatchCodeTraceEntity,
                input.getWeight(),
                LoginInfoThreadLocal.getCurrentUserName(),
                weightDateTime
        );

        skuBatchCodeTraceEntity.setWeight(skuBatchCodeTraceEntity.getWeight());
        skuBatchCodeTraceEntity.setWeightTime(weightDateTime);
        skuBatchCodeTraceEntity.setWeightPerson(LoginInfoThreadLocal.getCurrentUserName());
        ThresholdUpdateResultVO thresholdUpdateResultVO = ThresholdUpdateResultVO.builder()
                .currentThresholdBlock(false)
                .msg("")
                .skuBatchTraceCode(input.getSkuBatchTraceCode())
                .traceBatchPrintVO(SkuBatchCodeTraceAssembler.assembleVO(
                        skuBatchCodeTraceEntity, null, null))
                .build();
        return CommonResult.ok(thresholdUpdateResultVO);
    }

    /**
     * 计算相差绝对值的所占比例
     * @param weight1
     * @param weight2
     * @return
     */
    private BigDecimal calculateWeightDifferenceRatio(BigDecimal weight1, BigDecimal weight2) {
        if (weight1 == null || weight2 == null){
            throw new BizException("参数错误为空");
        }

        // 计算两个重量的绝对差值
        BigDecimal absoluteDifference = weight1.subtract(weight2).abs();

        // 计算绝对差值占第二个重量的比例
        BigDecimal differenceRatio = absoluteDifference.divide(weight2, 2, BigDecimal.ROUND_HALF_UP);

        return differenceRatio;
    }

    @Override
    public void updateSkuBatchCodeTraceFulfillmentOrderInfo(List<FulfillmentOrderInfoInput> reqFulOrderInputList) {
        if(CollectionUtils.isEmpty(reqFulOrderInputList)){
            return;
        }
        List<String> skuBatchTraceCodeList = reqFulOrderInputList.stream().map(FulfillmentOrderInfoInput::getSkuBatchTraceCode).collect(Collectors.toList());
        List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntities = skuBatchCodeTraceQueryRepository.findListBySkuBatchTraceCode(skuBatchTraceCodeList);
        if(CollectionUtils.isEmpty(skuBatchCodeTraceEntities)){
            log.info("唯一溯源码操作服务-更新唯一批次码信息-唯一批次码不存在:{}" , skuBatchTraceCodeList);
            return;
        }

        // 过滤POPT2标签溯源码
        skuBatchCodeTraceEntities = skuBatchCodeTraceEntities.stream()
                .filter(e -> Objects.equals(e.getLabelType(), SkuBatchCodeTraceEnums.LabelType.POP_T2.getValue()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuBatchCodeTraceEntities)){
            log.info("唯一溯源码操作服务-更新唯一批次码信息-POPT2标签溯源码不存在:{}" , skuBatchTraceCodeList);
            return;
        }

        Map<String, FulfillmentOrderInfoInput> reqSkuBatchTraceCodeMap = reqFulOrderInputList.stream().collect(Collectors.toMap(FulfillmentOrderInfoInput::getSkuBatchTraceCode, Function.identity(), (o, n) -> o));

        for (SkuBatchCodeTraceEntity currentSkuBatchCodeTraceEntity : skuBatchCodeTraceEntities) {
            FulfillmentOrderInfoInput reqFulfillmentOrderInfoInput = reqSkuBatchTraceCodeMap.get(currentSkuBatchCodeTraceEntity.getSkuBatchTraceCode());
            if(reqFulfillmentOrderInfoInput == null){
                log.info("唯一溯源码操作服务-更新唯一批次码信息-请求参数不存在:{}" , currentSkuBatchCodeTraceEntity.getSkuBatchTraceCode());
                continue;
            }
            currentSkuBatchCodeTraceEntity.setOrderNo(reqFulfillmentOrderInfoInput.getOrderNo());
            currentSkuBatchCodeTraceEntity.setFulfillmentNo(reqFulfillmentOrderInfoInput.getFulfillmentNo());
            currentSkuBatchCodeTraceEntity.setMerchantName(reqFulfillmentOrderInfoInput.getMerchantName());
            currentSkuBatchCodeTraceEntity.setMerchantId(reqFulfillmentOrderInfoInput.getMerchantId());
            currentSkuBatchCodeTraceEntity.setDeliveryTime(reqFulfillmentOrderInfoInput.getDeliveryTime());
            currentSkuBatchCodeTraceEntity.setFulfillmentWay(reqFulfillmentOrderInfoInput.getFulfillmentWay());
            currentSkuBatchCodeTraceEntity.setPathCode(reqFulfillmentOrderInfoInput.getPathCode());
            currentSkuBatchCodeTraceEntity.setPathSequence(reqFulfillmentOrderInfoInput.getPathSequence());
            currentSkuBatchCodeTraceEntity.setContactId(reqFulfillmentOrderInfoInput.getContactId());
        }
        skuBatchCodeTraceCommandDomainService.updateSkuBatchCodeTraceFulfillmentOrderInfo(skuBatchCodeTraceEntities);

    }
}
