package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProcessingConfigUpsetResDTO implements Serializable {

    /**
     * 是否成功
     */
    private Boolean isSuccess;


    /**
     * 失败编码
     * 100, 原料重量*比例数量小于/大于成品重量
     */
    private String failCode;

    /**
     * 失败原因
     */
    private String failMsg;

    public static ProcessingConfigUpsetResDTO success(){
        ProcessingConfigUpsetResDTO resDTO = new ProcessingConfigUpsetResDTO();
        resDTO.setIsSuccess(true);
        return resDTO;
    }

    public static ProcessingConfigUpsetResDTO failByRatioAndRatio(String failMsg){
        ProcessingConfigUpsetResDTO resDTO = new ProcessingConfigUpsetResDTO();
        resDTO.setIsSuccess(false);
        resDTO.setFailCode("100");
        resDTO.setFailMsg(failMsg);
        return resDTO;
    }
}
