package net.summerfarm.wms.application.stocktransfer;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.inner.stocktransfer.TransferTaskHandler;
import net.summerfarm.wms.application.external.event.WarehouseExternalRouteStockTransferEvent;
import net.summerfarm.wms.common.constant.ExternalCodeConstant;
import net.summerfarm.wms.common.enums.BooleanEnum;
import net.summerfarm.wms.domain.external.entity.WarehouseExternalRouteEntity;
import net.summerfarm.wms.domain.external.param.WarehouseExternalRouteQueryParam;
import net.summerfarm.wms.domain.external.repository.WarehouseExternalRouteQueryRepository;
import net.summerfarm.wms.domain.stockTransfer.StockTransferQueryRepository;
import net.summerfarm.wms.domain.stockTransfer.entity.StockTransferEntity;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static net.summerfarm.wms.common.constant.WmsConstant.EXTERNAL_ROUTE_STOCK_TRANSFER_INVOKE;
import static net.summerfarm.wms.common.constant.WmsConstant.MQ_TOPIC;

/**
 * <AUTHOR>
 * @Date 2024/7/29 14:31
 * @Version 1.0
 */
@Slf4j
@Component
public class TransferTaskHandlerImpl implements TransferTaskHandler {
    @Autowired
    private MqProducer mqProducer;

    @Autowired
    private StockTransferQueryRepository stockTransferQueryRepository;

    @Autowired
    private WarehouseExternalRouteQueryRepository warehouseExternalRouteQueryRepository;

    public void finishPostProcessor(Long stockTransferId){
        try {
            StockTransferEntity stockTransferEntity = stockTransferQueryRepository.selectById(stockTransferId);
            if (stockTransferEntity == null){
                throw new net.xianmu.common.exception.BizException("未查询到转换任务，完成转换任务失败");
            }
            if (null == stockTransferEntity.getWarehouseNo()){
                throw new net.xianmu.common.exception.BizException("转换任务的仓库号为空");
            }
            // 获取外部路由配置
            WarehouseExternalRouteEntity warehouseExternalRoute = warehouseExternalRouteQueryRepository.findOne(WarehouseExternalRouteQueryParam.builder()
                    .warehouseNo(stockTransferEntity.getWarehouseNo().intValue())
                    .abilityCode(ExternalCodeConstant.STOCK_TRANSFER_CREATE_NOTICE)
                    .orderType(stockTransferEntity.getTransferDimension())
                    .routeStatus(BooleanEnum.TRUE.getCode()).build());
            // 对接下发
            if (null == warehouseExternalRoute) {
                log.info("当前仓库 " + stockTransferEntity.getWarehouseNo() + " ->【转换下发】能力点 未查询到外部路由配置");
                return;
            }
            WarehouseExternalRouteStockTransferEvent warehouseExternalRouteStockTransferEvent = new WarehouseExternalRouteStockTransferEvent();
            warehouseExternalRouteStockTransferEvent.setIdempotentNo(stockTransferId.toString());
            warehouseExternalRouteStockTransferEvent.setWarehouseExternalRouteId(warehouseExternalRoute.getId());
            warehouseExternalRouteStockTransferEvent.setStockTransferId(stockTransferId);
            mqProducer.send(MQ_TOPIC, EXTERNAL_ROUTE_STOCK_TRANSFER_INVOKE, warehouseExternalRouteStockTransferEvent);
        } catch (Exception e) {
            log.error("完成转换后置发送消息异常 {}", stockTransferId, e);
        }
    }
}
