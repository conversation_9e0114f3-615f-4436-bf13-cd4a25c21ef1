package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 加工任务分页请求对象
 * <AUTHOR>
 * @date 2023/02/20
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingTaskQueryReqDTO {

    /**
     * 库存仓编码
     */
    private Integer warehouseNo;

    /**
     * 加工任务类型
     */
    private Integer type;

    /**
     * 加工任务装填
     */
    private Integer status;

    /**
     * 成品SKU名称
     */
    private String productSkuName;

    /**
     * 成品SKU
     */
    private String productSkuCode;

    /**
     * 成品SKUId
     */
    private Long productSkuSaasId;

    /**
     * 原料sku名称
     */
    private String materialSkuName;

    /**
     * 原料sku编号
     */
    private String materialSkuCode;

    /**
     * 原料skuid
     */
    private Long materialSkuSaasId;

    /**
     * 来源ID
     */
    private String sourceId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;

}
