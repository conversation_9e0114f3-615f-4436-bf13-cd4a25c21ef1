package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;

@Data
public class ProcessingTaskProductFinishCheckResDTO {

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * 提示信息
     */
    private String message;

    public static ProcessingTaskProductFinishCheckResDTO success() {
        ProcessingTaskProductFinishCheckResDTO resDTO = new ProcessingTaskProductFinishCheckResDTO();
        resDTO.setIsSuccess(true);
        return resDTO;
    }

    public static ProcessingTaskProductFinishCheckResDTO fail(String message) {
        ProcessingTaskProductFinishCheckResDTO resDTO = new ProcessingTaskProductFinishCheckResDTO();
        resDTO.setIsSuccess(false);
        resDTO.setMessage(message);
        return resDTO;
    }
}
