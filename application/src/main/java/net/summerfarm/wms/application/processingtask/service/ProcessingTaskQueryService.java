package net.summerfarm.wms.application.processingtask.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskQueryResDTO;

/**
 * <AUTHOR>
 * @date 2023/02/12
 */
public interface ProcessingTaskQueryService {

    /**
     * 加工任务分页查询
     * @param reqDTO 请求query对象
     * @return 分页查询结果
     */
    PageInfo<ProcessingTaskQueryResDTO> page(ProcessingTaskQueryReqDTO reqDTO);

    /**
     * 加工任务详情
     * @param processingTaskCode 加工任务编码
     * @return 详情数据
     */
    ProcessingTaskQueryResDTO detail(String processingTaskCode);
}
