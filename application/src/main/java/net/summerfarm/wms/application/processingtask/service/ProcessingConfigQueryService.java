package net.summerfarm.wms.application.processingtask.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingConfigQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigQueryResDTO;

public interface ProcessingConfigQueryService {

    /**
     * 分页查询加工规则
     * @param reqDTO query对象
     * @return 分页信息
     */
    PageInfo<ProcessingConfigQueryResDTO> page(ProcessingConfigQueryReqDTO reqDTO);

    /**
     * 加工规格详情接口
     * @param id 加工规格id
     * @return 加工规格res对象
     */
    ProcessingConfigQueryResDTO detail(Long id);
}
