package net.summerfarm.wms.application.materialManage.controller;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialTaskAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialTaskQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialTaskVO;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialTaskCommandService;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialTaskQueryService;
import net.summerfarm.wms.common.converter.PageInfoConverter;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;

import javax.validation.Valid;
import java.time.LocalDateTime;


/**
 * @Title 物料任务
 * @Description 物料任务功能模块
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 */
@RestController
@RequestMapping(value="/wmsMaterialTask")
public class WmsMaterialTaskController{

	@Autowired
	private WmsMaterialTaskCommandService wmsMaterialTaskCommandService;
	@Autowired
	private WmsMaterialTaskQueryService wmsMaterialTaskQueryService;


	/**
	 * @Description 物料任务列表
	 * @return WmsMaterialTaskVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<WmsMaterialTaskVO>> getPage(@RequestBody WmsMaterialTaskQueryInput input){
		PageInfo<WmsMaterialTaskEntity> page = wmsMaterialTaskQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, WmsMaterialTaskAssembler::toWmsMaterialTaskVO));
	}

	/**
	* @Description 获取详情
	* @return WmsMaterialTaskVO
	*/
	@PostMapping(value = "/query/detail/{id}")
	public CommonResult<WmsMaterialTaskVO> detail(@PathVariable Long id){
		return CommonResult.ok(WmsMaterialTaskAssembler.toWmsMaterialTaskVO(wmsMaterialTaskQueryService.getDetail(id)));
	}


	/**
	 * 物料领用
	 * @return WmsMaterialTaskVO
	 */
	@PostMapping(value = "/upsert/receive")
	public CommonResult<WmsMaterialTaskVO> receive(@RequestBody @Valid WmsMaterialTaskCommandInput input) {
		input.setTenantId(LoginInfoThreadLocal.getTenantId());
		input.setCreator(LoginInfoThreadLocal.getCurrentUserName());
		input.setCreateTime(LocalDateTime.now());
		input.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
		input.setUpdateTime(LocalDateTime.now());
		return wmsMaterialTaskCommandService.receive(input);
	}


	/**
	 * 物料归还
	 * @return WmsMaterialTaskVO
	 */
	@PostMapping(value = "/upsert/restore")
	public CommonResult<WmsMaterialTaskVO> restore(@RequestBody @Valid WmsMaterialTaskCommandInput input) {
		input.setTenantId(LoginInfoThreadLocal.getTenantId());
		input.setCreator(LoginInfoThreadLocal.getCurrentUserName());
		input.setCreateTime(LocalDateTime.now());
		input.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
		input.setUpdateTime(LocalDateTime.now());
		return wmsMaterialTaskCommandService.restore(input);
	}
}

