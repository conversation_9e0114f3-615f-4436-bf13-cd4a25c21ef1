package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 加工任务成品分页查询响应DTO
 * <AUTHOR>
 * @date 2023/02/15
 */
@Data
public class ProcessingTaskProductQueryResDTO {

    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 成品skuId
     */
    private Long productSkuSaasId;
    /**
     * 成品sku名称
     */
    private String productSkuName;
    /**
     * 成品sku重量
     */
    private BigDecimal productSkuWeight;
    /**
     * 成品sku单位
     */
    private String productSkuUnit;
    /**
     * 成品sku规格描述
     */
    private String productSkuUnitDesc;
    /**
     * 成品sku所需数量
     */
    private Integer productSkuNeedQuantity;
    /**
     * 成品sku生产数量
     */
    private Integer productSkuFinishQuantity;
    /**
     * 原料sku
     */
    @Deprecated
    private String materialSkuCode;
    /**
     * 原料sku名称
     */
    @Deprecated
    private String materialSkuName;
    /**
     * 原料sku重量
     */
    @Deprecated
    private BigDecimal materialSkuWeight;
    /**
     * 原料sku单位
     */
    @Deprecated
    private String materialSkuUnit;
    /**
     * 原料sku领料数量
     */
    @Deprecated
    private Integer materialSkuReceiveQuantity;
    /**
     * 原料sku领料总重
     */
    @Deprecated
    private BigDecimal materialSkuReceiveWeight;
    /**
     * 原料sku领料数量
     */
    @Deprecated
    private Integer materialSkuQuantity;
    /**
     * 原料sku领料总重
     */
    @Deprecated
    private BigDecimal materialFinishWeight;
    /**
     * 成品总重
     */
    private BigDecimal productSkuSpecFinishWeight;
    /**
     * 成品总重
     */
    private Integer productSkuSpecFinishQuantity;
    /**
     * 规格损耗重量
     */
    private BigDecimal specLossWeight;
    /**
     * 废料损耗重量
     */
    private BigDecimal wasteLossWeight;
    /**
     * 原料sku归还数量
     */
    private Integer materialSkuRestoreQuantity;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;

    /**
     * 任务状态：0未加工、1已加工
     */
    private Integer status;
    /**
     * 规格列表
     */
    List<ProcessingTaskProductSpecDTO> productSpecList;

    /**
     * 原料sku单位描述-规格
     */
    @Deprecated
    private String materialSkuUnitDesc;

    /**
     * 完成备注
     */
    private String finishRemark;

    /**
     * 需要备注
     */
    private Boolean needRemark;

    /**
     * 成品SKU库位
     */
    private String productSkuCabinetCode;

    /**
     * 原料模式，0-单原料，1-多原料
     */
    private Integer materialModel;

    /**
     * 多原料信息
     */
    private List<ProcessingTaskMaterialResDTO> processingTaskMaterialList;

    /**
     * 成品sku比例数量
     */
    private Integer productSkuRatioNum;
}
