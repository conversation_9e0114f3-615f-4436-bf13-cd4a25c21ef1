package net.summerfarm.wms.application.materialManage.service;


import net.summerfarm.wms.api.h5.filedown.resp.FileDownResp;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialBindingQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingExportVO;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.xianmu.common.result.CommonResult;

/**
 * @date 2025-03-18 15:49:28
 * @version 1.0
 */
public interface WmsMaterialBindingCommandService {

    /**
     * @return WmsMaterialBindingEntity
     * @description: 新增
     **/
    CommonResult<WmsMaterialBindingVO> insert(WmsMaterialBindingCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

}