package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProcessingTaskMaterialInventoryReqDTO implements Serializable {

    /**
     * 原料SKU编码
     */
    @NotNull(message = "原料领用编码不能为空")
    private String materialSkuCode;

    /**
     * 原料采购批次
     */
//    @NotNull(message = "原料领用采购批次不能为空")
    private String materialSkuPurchaseBatch;

    /**
     * 原料生产日期
     */
    @NotNull(message = "原料领用生产不能为空")
    private String materialSkuPurchaseBatchProductionDate;

    /**
     * 原料保质期
     */
    @NotNull(message = "原料领用日期不能为空")
    private String materialSkuPurchaseBatchQualityDate;

    /**
     * 原料接收数量
     */
    @NotNull(message = "原料领用数量不能为空")
    private Integer materialSkuReceiveQuantity;

    /**
     * 原料库位id
     */
    private Integer materialSkuCabinetId;

    /**
     * 原料库位编码
     */
    private String materialSkuCabinetCode;

}
