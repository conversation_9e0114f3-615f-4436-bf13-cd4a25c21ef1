package net.summerfarm.wms.application.processingtask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.processingtask.service.ProcessingTaskQueryService;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskQueryResDTO;
import net.summerfarm.wms.application.processingtask.converter.ProcessingTaskQueryResDTOConverter;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTask;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskTypeEnum;
import net.summerfarm.wms.domain.processingtask.domainobject.param.ProcessingTaskQueryParam;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProcessingTaskQueryServiceImpl implements ProcessingTaskQueryService {

    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private WarehouseStorageRepository warehouseStorageRepository;

    /**
     * 加工任务分页查询
     * @param reqDTO 请求query对象
     * @return 分页查询结果
     */
    @Override
    public PageInfo<ProcessingTaskQueryResDTO> page(ProcessingTaskQueryReqDTO reqDTO) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();
        if (reqDTO.getMaterialSkuSaasId() != null){
            Product product = productRepository.findProductFromGoodsCenter(null,
                    tenantId,
                    reqDTO.getMaterialSkuSaasId());
            if (product == null) {
                PageInfo<ProcessingTaskQueryResDTO> result = new PageInfo<>();
                result.setTotal(0);
                result.setPages(result.getPages());
                result.setList(new ArrayList<>());
                return result;
            }
            reqDTO.setMaterialSkuCode(product.getSku());
        }
        if (reqDTO.getProductSkuSaasId() != null){
            Product product = productRepository.findProductFromGoodsCenter(null,
                    tenantId,
                    reqDTO.getProductSkuSaasId());
            if (product == null) {
                PageInfo<ProcessingTaskQueryResDTO> result = new PageInfo<>();
                result.setTotal(0);
                result.setPages(result.getPages());
                result.setList(new ArrayList<>());
                return result;
            }
            reqDTO.setProductSkuCode(product.getSku());
        }

        ProcessingTaskQueryParam queryParam = ProcessingTaskQueryParam.builder()
                .warehouseNo(reqDTO.getWarehouseNo())
                .type(reqDTO.getType())
                .typeList(Arrays.asList(ProcessingTaskTypeEnum.SKU_PROCESSING.getValue(), ProcessingTaskTypeEnum.SKU_ASSEMBLY.getValue()))
                .status(reqDTO.getStatus())
                .productSkuName(reqDTO.getProductSkuName())
                .productSkuCode(reqDTO.getProductSkuCode())
                .materialSkuName(reqDTO.getMaterialSkuName())
                .materialSkuCode(reqDTO.getMaterialSkuCode())
                .processingTaskCode(reqDTO.getProcessingTaskCode())
                .build();

        PageInfo<ProcessingTask> pageInfo = processingTaskRepository.queryAllByLimit(
                queryParam, reqDTO.getPageIndex(), reqDTO.getPageSize());

        List<ProcessingTaskQueryResDTO> list = ProcessingTaskQueryResDTOConverter.INSTANCE.convertList(pageInfo.getList());

        List<Integer> warehouseNoList = list.stream()
                .map(ProcessingTaskQueryResDTO::getWarehouseNo)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, WarehouseStorageCenterEntity> warehouseStorageCenterEntityMap =
                warehouseStorageRepository.mapByWarehouseNoListByWarehouseNo(warehouseNoList);

        for (ProcessingTaskQueryResDTO resDTO : list) {
            WarehouseStorageCenterEntity warehouseStorageCenterEntity =
                    warehouseStorageCenterEntityMap.get(resDTO.getWarehouseNo());
            resDTO.setWarehouseName(warehouseStorageCenterEntity != null ?
                    warehouseStorageCenterEntity.getWarehouseName() : resDTO.getWarehouseName());
        }

        PageInfo<ProcessingTaskQueryResDTO> result = new PageInfo<>();
        result.setPages(pageInfo.getPages());
        result.setTotal(pageInfo.getTotal());
        result.setList(list);
        return result;
    }

    /**
     * 加工任务详情
     * @param processingTaskCode 加工任务编码
     * @return 详情数据
     */
    @Override
    public ProcessingTaskQueryResDTO detail(String processingTaskCode) {
        if (Objects.isNull(processingTaskCode)) {
            return null;
        }
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(processingTaskCode);
        ProcessingTaskQueryResDTO resDTO = ProcessingTaskQueryResDTOConverter.INSTANCE.convert(processingTask);
        if (resDTO == null){
            return null;
        }
        WarehouseStorageCenterEntity warehouseStorageCenter =
                warehouseStorageRepository.selectByWarehouseNo(resDTO.getWarehouseNo());
        resDTO.setWarehouseName(warehouseStorageCenter.getWarehouseName());
        return resDTO;
    }
}
