package net.summerfarm.wms.application.processingtask.factory;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.processingtask.dto.req.*;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskTypeEnum;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.util.NumberUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.SkuSpec;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ProcessingTaskCreateDTOFactory {

    /**
     * 新建实例
     *
     * @param reqDTOList
     * @return
     */
    public static ProcessingTaskCreateDTO newInstance(
            List<ProcessingTaskOrderCreateReqDTO> reqDTOList,
            Map<String, List<SkuSpec>> mapSkuSpec) {
        if (CollectionUtils.isEmpty(reqDTOList)) {
            return null;
        }

        ProcessingTaskCreateDTO processingTaskCreateDTO = new ProcessingTaskCreateDTO();
        processingTaskCreateDTO.setWarehouseNo(reqDTOList.get(0).getWarehouseNo());
        processingTaskCreateDTO.setProcessingTaskCode(reqDTOList.get(0).getProcessingTaskCode());
        Integer type = reqDTOList.get(0).getType();
        processingTaskCreateDTO.setType(type);

        buildProductDTOList(reqDTOList, processingTaskCreateDTO);

        if (Objects.equals(type, ProcessingTaskTypeEnum.ORDER_PROCESSING.getValue())){
            buildProductSpecOrderDTOList(reqDTOList, mapSkuSpec, processingTaskCreateDTO);
            buildProductSpecDTOList(processingTaskCreateDTO);
        }else {
            //成品订单规格
            processingTaskCreateDTO.setProcessingTaskProductSpecOrderDTOList(Lists.newArrayList());
            // 成品规格明细
            buildProductSpecDTOList(reqDTOList, mapSkuSpec, processingTaskCreateDTO);
        }

        return processingTaskCreateDTO;
    }

    private static void buildProductSpecDTOList(List<ProcessingTaskOrderCreateReqDTO> reqDTOList, Map<String, List<SkuSpec>> mapSkuSpec, ProcessingTaskCreateDTO processingTaskCreateDTO) {
        Map<String, ProcessingTaskProductSpecDTO> specDTOMap = new HashMap<>();
        for (ProcessingTaskOrderCreateReqDTO reqDTO : reqDTOList) {
            // 匹配的成品规格列表
            List<SkuSpec> skuSpecList = mapSkuSpec.get(reqDTO.getProductSkuCode());
            if (CollectionUtils.isEmpty(skuSpecList)) {
                throw new BizException("产品规格不存在:" + reqDTO.getProductSkuInput(), ErrorCodeNew.PARAM_ERROR);
            }
            SkuSpec skuSpecTemp = skuSpecList.get(0);
            BigDecimal productSkuSpecWeight = skuSpecTemp.getProductSkuSpecWeight();

            ProcessingTaskProductSpecDTO specDTO = specDTOMap.get(
                    reqDTO.getProductSkuCode() + "_" + productSkuSpecWeight);
            if (specDTO != null) {
                specDTO.setProductSkuSpecNeedQuantity(
                        specDTO.getProductSkuSpecNeedQuantity() +
                                reqDTO.getProductSkuNeedQuantity()
                );
                specDTO.setProductSkuNeedQuantity(
                        specDTO.getProductSkuNeedQuantity() +
                                reqDTO.getProductSkuNeedQuantity());
                continue;
            }
            specDTO = new ProcessingTaskProductSpecDTO();
            specDTO.setProductSkuCode(skuSpecTemp.getProductSkuCode());
            specDTO.setProductSkuName(skuSpecTemp.getProductSkuName());

            specDTO.setProductSkuSpecWeight(skuSpecTemp.getProductSkuSpecWeight());
            specDTO.setProductSkuSpecUnit(skuSpecTemp.getProductSkuSpecUnit());

            specDTO.setProductSkuNeedQuantity(reqDTO.getProductSkuNeedQuantity());
            specDTO.setProductSkuSpecNeedQuantity(reqDTO.getProductSkuNeedQuantity());

            specDTOMap.put(skuSpecTemp.getProductSkuCode() + "_" + skuSpecTemp.getProductSkuSpecWeight(), specDTO);
        }
        processingTaskCreateDTO.setProcessingTaskProductSpecDTOList(
                specDTOMap.values().stream().collect(Collectors.toList()));
    }

    private static void buildProductSpecDTOList(ProcessingTaskCreateDTO processingTaskCreateDTO) {
        // 成品规格明细
        Map<String, ProcessingTaskProductSpecDTO> specDTOMap = new HashMap<>();
        for (ProcessingTaskProductSpecOrderDTO specOrderDTO :
                processingTaskCreateDTO.getProcessingTaskProductSpecOrderDTOList()) {

            ProcessingTaskProductSpecDTO specDTO = specDTOMap.get(
                    specOrderDTO.getProductSkuCode() + "_" + specOrderDTO.getProductSkuSpecWeight());
            if (specDTO != null) {
                specDTO.setProductSkuSpecNeedQuantity(
                        specDTO.getProductSkuSpecNeedQuantity() +
                                specOrderDTO.getProductSkuSpecNeedQuantity()
                );
                specDTO.setProductSkuNeedQuantity(
                        specDTO.getProductSkuNeedQuantity() +
                                specOrderDTO.getProductSkuNeedQuantity());
                continue;
            }

            specDTO = new ProcessingTaskProductSpecDTO();
            specDTO.setProductSkuCode(specOrderDTO.getProductSkuCode());
            specDTO.setProductSkuName(specOrderDTO.getProductSkuName());

            specDTO.setProductSkuSpecWeight(specOrderDTO.getProductSkuSpecWeight());
            specDTO.setProductSkuSpecUnit(specOrderDTO.getProductSkuSpecUnit());

            specDTO.setProductSkuNeedQuantity(specOrderDTO.getProductSkuNeedQuantity());
            specDTO.setProductSkuSpecNeedQuantity(specOrderDTO.getProductSkuSpecNeedQuantity());

            specDTOMap.put(specOrderDTO.getProductSkuCode() + "_" + specOrderDTO.getProductSkuSpecWeight(), specDTO);
        }
        processingTaskCreateDTO.setProcessingTaskProductSpecDTOList(
                specDTOMap.values().stream().collect(Collectors.toList()));
    }

    private static void buildProductDTOList(List<ProcessingTaskOrderCreateReqDTO> reqDTOList, ProcessingTaskCreateDTO processingTaskCreateDTO) {
        // region 成品
        Map<String, ProcessingTaskProductDTO> productDTOMap = new HashMap<>();
        for (ProcessingTaskOrderCreateReqDTO reqDTO : reqDTOList) {
            ProcessingTaskProductDTO productDTO = productDTOMap.get(reqDTO.getProductSkuCode());
            if (productDTO != null) {
                productDTO.setProductSkuNeedQuantity(
                        productDTO.getProductSkuNeedQuantity() +
                                reqDTO.getProductSkuNeedQuantity()
                );
                continue;
            }

            productDTO = new ProcessingTaskProductDTO();
            productDTO.setProductSkuCode(reqDTO.getProductSkuCode());
            productDTO.setProductSkuNeedQuantity(reqDTO.getProductSkuNeedQuantity());

            productDTOMap.put(reqDTO.getProductSkuCode(), productDTO);
        }
        // 成品
        processingTaskCreateDTO.setProcessingTaskProductDTOList(
                productDTOMap.values().stream().collect(Collectors.toList()));
        // endregion
    }

    private static void buildProductSpecOrderDTOList(List<ProcessingTaskOrderCreateReqDTO> reqDTOList,
                                                     Map<String, List<SkuSpec>> mapSkuSpec,
                                                     ProcessingTaskCreateDTO processingTaskCreateDTO) {
        // region 成品订单规格
        List<ProcessingTaskProductSpecOrderDTO> specOrderDTOList = new ArrayList<>();
        for (ProcessingTaskOrderCreateReqDTO reqDTO : reqDTOList) {
            // 匹配的成品规格列表
            List<SkuSpec> skuSpecList = mapSkuSpec.get(reqDTO.getProductSkuCode());
            if (CollectionUtils.isEmpty(skuSpecList)){
                throw new BizException("产品规格不存在:" + reqDTO.getProductSkuCode(), ErrorCodeNew.PARAM_ERROR);
            }
            SkuSpec skuSpecTemp = skuSpecList.get(0);
            BigDecimal productSkuWeight =  skuSpecTemp.getProductSkuWeight();

            // 需求总数
            BigDecimal needTotal = BigDecimal.valueOf(reqDTO.getProductSkuNeedQuantity())
                    .multiply(productSkuWeight);

            // region 成品订单明细
            List<BigDecimal> specNumberList = skuSpecList.stream()
                    .map(SkuSpec::getProductSkuSpecWeight)
                    .distinct()
                    .collect(Collectors.toList());
            Map<BigDecimal, SkuSpec> skuSpecWeightMap = skuSpecList.stream().collect(Collectors.toMap(
                    SkuSpec::getProductSkuSpecWeight, Function.identity(), (a, b) -> a
            ));
            Map<BigDecimal, Integer> distributionMap = NumberUtil.distributionMap(specNumberList, needTotal);

            for (Map.Entry<BigDecimal, Integer> bigDecimalIntegerEntry : distributionMap.entrySet()) {
                BigDecimal specWeight = bigDecimalIntegerEntry.getKey();
                Integer specNeedQuantity = bigDecimalIntegerEntry.getValue();

                SkuSpec skuSpec = skuSpecWeightMap.get(specWeight);


                ProcessingTaskProductSpecOrderDTO processingTaskProductDTO = new ProcessingTaskProductSpecOrderDTO();
                processingTaskProductDTO.setSourceId(reqDTO.getSourceId());
                processingTaskProductDTO.setProductSkuCode(skuSpec.getProductSkuCode());
                processingTaskProductDTO.setProductSkuName(skuSpec.getProductSkuName());

                processingTaskProductDTO.setProductSkuNeedQuantity(
                        BigDecimal.valueOf(specNeedQuantity).multiply(specWeight)
                                .divide(productSkuWeight,  0, RoundingMode.HALF_DOWN)
                                .intValue()
                );
                processingTaskProductDTO.setProductSkuSpecNeedQuantity(specNeedQuantity);
                processingTaskProductDTO.setProductSkuSpecWeight(specWeight);
                processingTaskProductDTO.setProductSkuSpecUnit(skuSpec.getProductSkuSpecUnit());

                specOrderDTOList.add(processingTaskProductDTO);
            }
        }
        // 成品订单规格
        processingTaskCreateDTO.setProcessingTaskProductSpecOrderDTOList(specOrderDTOList);
        // endregion
    }

}
