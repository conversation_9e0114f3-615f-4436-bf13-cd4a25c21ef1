package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessingTaskProductSpecDTO implements Serializable {

    /**
     * 成品sku编码
     */
    private String productSkuCode;

    /**
     * 成品sku需要数量
     */
    private Integer productSkuNeedQuantity;
    /**
     * 成品sku规格预计加工数
     */
    private Integer productSkuSpecNeedQuantity;

    /**
     * 成品sku名称
     */
    private String productSkuName;
    /**
     * 成品sku规格重量
     */
    private BigDecimal productSkuSpecWeight;
    /**
     * 成品sku规格单位
     */
    private String productSkuSpecUnit;
}
