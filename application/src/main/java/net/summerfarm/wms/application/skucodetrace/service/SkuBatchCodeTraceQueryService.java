package net.summerfarm.wms.application.skucodetrace.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.skucodetrace.input.SkuBatchCodeTraceQuery;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Description: 唯一溯源码查询服务<br/>
 * date: 2024/8/7 14:34<br/>
 *
 * <AUTHOR> />
 */
public interface SkuBatchCodeTraceQueryService {
    /**
     * 批量打印查询
     * @param query 查询
     */
    PageInfo<SkuBatchCodeTraceEntity> batchPrint(SkuBatchCodeTraceQuery query);

    /**
     * 唯一溯源码查询
     * @param query 查询
     */
    SkuBatchCodeTraceEntity traceOnlyQuery(SkuBatchCodeTraceQuery query);

    /**
     * 商户最近一次配送时间
     * @param skuBatchCodeTraceEntityList
     * @return
     */
    Map<String, LocalDate> findMerchantDeliveryTimeRecently(List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList);

    /**
     * 商户第一次配送时间
     * @param skuBatchCodeTraceEntityList
     * @return
     */
    Map<String, LocalDate> findMerchantDeliveryTimeFirst(List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList);
}
