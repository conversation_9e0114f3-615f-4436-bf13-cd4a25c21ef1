package net.summerfarm.wms.application.materialManage.controller.assembler;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingDetailCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialBindingDetailQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingDetailVO;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingDetailCommandParam;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingDetailQueryParam;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.xianmu.common.exception.BizException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Slf4j
public class WmsMaterialBindingDetailAssembler {

    private WmsMaterialBindingDetailAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static WmsMaterialBindingDetailCommandParam buildCreateParam(WmsMaterialBindingCommandInput input,
                                                                        WmsMaterialBindingDetailCommandInput wmsMaterialBindingDetailCommandInput,
                                                                        Map<String, GoodsInfoDTO> goodsInfoDTOMap,
                                                                        Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap) {
        if (wmsMaterialBindingDetailCommandInput == null) {
            return null;
        }

        GoodsInfoDTO goodsInfoDTO = goodsInfoDTOMap.get(input.getSku());
        if (goodsInfoDTO == null){
            log.error("请求货品不存在 sku:{}", input.getSku());
            throw new BizException("请求货品不存在");
        }

        GoodsInfoDTO materialGoodsInfoDTO = materialGoodsInfoDTOMap.get(wmsMaterialBindingDetailCommandInput.getMaterialSku());
        if (materialGoodsInfoDTO == null){
            log.error("请求物料不存在 sku:{}", wmsMaterialBindingDetailCommandInput.getMaterialSku());
            throw new BizException("请求物料不存在");
        }


        WmsMaterialBindingDetailCommandParam wmsMaterialBindingDetailCommandParam = new WmsMaterialBindingDetailCommandParam();
//        wmsMaterialBindingDetailCommandParam.setId(wmsMaterialBindingDetailCommandInput.getId());
//        wmsMaterialBindingDetailCommandParam.setMaterialBingingId();
        wmsMaterialBindingDetailCommandParam.setTenantId(input.getTenantId());
        wmsMaterialBindingDetailCommandParam.setWarehouseNo(input.getWarehouseNo());
        wmsMaterialBindingDetailCommandParam.setSku(input.getSku());
        wmsMaterialBindingDetailCommandParam.setSkuSaasId(goodsInfoDTO != null ?
                goodsInfoDTO.getSkuId() : input.getSkuSaasId());
        wmsMaterialBindingDetailCommandParam.setMaterialSku(wmsMaterialBindingDetailCommandInput.getMaterialSku());
        wmsMaterialBindingDetailCommandParam.setMaterialSkuSaasId(materialGoodsInfoDTO != null ?
                materialGoodsInfoDTO.getSkuId() : wmsMaterialBindingDetailCommandInput.getMaterialSkuSaasId());
        wmsMaterialBindingDetailCommandParam.setMaterialSkuName(materialGoodsInfoDTO != null ?
                materialGoodsInfoDTO.getTitle() : "");
        wmsMaterialBindingDetailCommandParam.setMaterialSkuRatio(wmsMaterialBindingDetailCommandInput.getMaterialSkuRatio());
        wmsMaterialBindingDetailCommandParam.setReuse(wmsMaterialBindingDetailCommandInput.getReuse());
        wmsMaterialBindingDetailCommandParam.setAutoOutbound(wmsMaterialBindingDetailCommandInput.getAutoOutbound());
        wmsMaterialBindingDetailCommandParam.setCreator(input.getCreator());
        wmsMaterialBindingDetailCommandParam.setCreateTime(input.getCreateTime());
        wmsMaterialBindingDetailCommandParam.setUpdater(input.getUpdater());
        wmsMaterialBindingDetailCommandParam.setUpdateTime(input.getUpdateTime());
        wmsMaterialBindingDetailCommandParam.setDeleteFlag(DeleteFlagEnum.NO.getValue());
        wmsMaterialBindingDetailCommandParam.setMaterialBingingId(wmsMaterialBindingDetailCommandInput.getMaterialBingingId());
        return wmsMaterialBindingDetailCommandParam;
    }


// ------------------------------- response ----------------------------

    public static List<WmsMaterialBindingDetailVO> toWmsMaterialBindingDetailVOList(List<WmsMaterialBindingDetailEntity> wmsMaterialBindingDetailEntityList,
                                                                                    Map<String, GoodsInfoDTO> materialSkuGoodsInfoDTOMap) {
        if (wmsMaterialBindingDetailEntityList == null) {
            return Collections.emptyList();
        }
        List<WmsMaterialBindingDetailVO> wmsMaterialBindingDetailVOList = new ArrayList<>();
        for (WmsMaterialBindingDetailEntity wmsMaterialBindingDetailEntity : wmsMaterialBindingDetailEntityList) {
            wmsMaterialBindingDetailVOList.add(toWmsMaterialBindingDetailVO(wmsMaterialBindingDetailEntity, materialSkuGoodsInfoDTOMap));
        }
        return wmsMaterialBindingDetailVOList;
}


   public static WmsMaterialBindingDetailVO toWmsMaterialBindingDetailVO(WmsMaterialBindingDetailEntity wmsMaterialBindingDetailEntity,
                                                                         Map<String, GoodsInfoDTO> materialSkuGoodsInfoDTOMap) {
       if (wmsMaterialBindingDetailEntity == null) {
            return null;
       }

       GoodsInfoDTO materialSkuGoodsInfoDTO =
               materialSkuGoodsInfoDTOMap.get(wmsMaterialBindingDetailEntity.getMaterialSku());


       WmsMaterialBindingDetailVO wmsMaterialBindingDetailVO = new WmsMaterialBindingDetailVO();
       wmsMaterialBindingDetailVO.setId(wmsMaterialBindingDetailEntity.getId());
       wmsMaterialBindingDetailVO.setTenantId(wmsMaterialBindingDetailEntity.getTenantId());
       wmsMaterialBindingDetailVO.setWarehouseNo(wmsMaterialBindingDetailEntity.getWarehouseNo());
       wmsMaterialBindingDetailVO.setSku(wmsMaterialBindingDetailEntity.getSku());
       wmsMaterialBindingDetailVO.setSkuSaasId(wmsMaterialBindingDetailEntity.getSkuSaasId());
       wmsMaterialBindingDetailVO.setMaterialSku(wmsMaterialBindingDetailEntity.getMaterialSku());
       wmsMaterialBindingDetailVO.setMaterialSkuSaasId(wmsMaterialBindingDetailEntity.getMaterialSkuSaasId());
       wmsMaterialBindingDetailVO.setMaterialSkuRatio(wmsMaterialBindingDetailEntity.getMaterialSkuRatio());
       wmsMaterialBindingDetailVO.setMaterialSkuName(materialSkuGoodsInfoDTO != null
               ? materialSkuGoodsInfoDTO.getTitle() : wmsMaterialBindingDetailEntity.getMaterialSkuName());
       wmsMaterialBindingDetailVO.setMaterialSkuSpecification(materialSkuGoodsInfoDTO != null ?
               materialSkuGoodsInfoDTO.getSpecification() : "");
       wmsMaterialBindingDetailVO.setMaterialSkuSubType(materialSkuGoodsInfoDTO != null ?
               materialSkuGoodsInfoDTO.getSubAgentType() : null);
       if (materialSkuGoodsInfoDTO != null) {
           wmsMaterialBindingDetailVO.setReuse(
                   materialSkuGoodsInfoDTO.getPropertyValueByEnum(ProductsPropertyEnum.REUSE));
           wmsMaterialBindingDetailVO.setAutoOutbound(
                   materialSkuGoodsInfoDTO.getPropertyValueByEnum(ProductsPropertyEnum.AUTO_OUTBOUND));
       }

       wmsMaterialBindingDetailVO.setCreator(wmsMaterialBindingDetailEntity.getCreator());
       wmsMaterialBindingDetailVO.setCreateTime(wmsMaterialBindingDetailEntity.getCreateTime());
       wmsMaterialBindingDetailVO.setUpdater(wmsMaterialBindingDetailEntity.getUpdater());
       wmsMaterialBindingDetailVO.setUpdateTime(wmsMaterialBindingDetailEntity.getUpdateTime());
       wmsMaterialBindingDetailVO.setDeleteFlag(wmsMaterialBindingDetailEntity.getDeleteFlag());
       wmsMaterialBindingDetailVO.setMaterialBingingId(wmsMaterialBindingDetailEntity.getMaterialBingingId());
       return wmsMaterialBindingDetailVO;
   }

}
