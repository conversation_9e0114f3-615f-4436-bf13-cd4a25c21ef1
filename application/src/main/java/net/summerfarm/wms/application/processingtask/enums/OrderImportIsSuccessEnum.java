package net.summerfarm.wms.application.processingtask.enums;

public enum OrderImportIsSuccessEnum {


    ALL_FAIL(0, "全部失败"),

    ALL_SUCCESS(1, "全部成功"),

    PART_SUCCUESS(2, "部分成功"),

    ;

    private final int value;

    private final String description;

    OrderImportIsSuccessEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }


    public boolean equalsCode(Integer input){
        return Integer.valueOf(this.value).equals(input);
    }
}
