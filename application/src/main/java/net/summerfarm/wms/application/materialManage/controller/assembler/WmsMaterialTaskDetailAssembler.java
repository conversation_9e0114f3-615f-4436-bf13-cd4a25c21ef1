package net.summerfarm.wms.application.materialManage.controller.assembler;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.enums.ProductsPropertyEnum;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskDetailCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialTaskDetailQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialTaskDetailVO;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskDetailEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskDetailCommandParam;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskDetailQueryParam;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.xianmu.common.exception.BizException;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025-03-18 15:49:27
 * @version 1.0
 *
 */
@Slf4j
public class WmsMaterialTaskDetailAssembler {

    private WmsMaterialTaskDetailAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------

    public static WmsMaterialTaskDetailCommandParam buildCreateParam(WmsMaterialTaskCommandInput wmsMaterialTaskCommandInput,
                                                                     WmsMaterialTaskDetailCommandInput wmsMaterialTaskDetailCommandInput,
                                                                     Map<String, GoodsInfoDTO> goodsInfoDTOMap) {
        if (wmsMaterialTaskCommandInput == null || wmsMaterialTaskDetailCommandInput == null) {
            return null;
        }

        GoodsInfoDTO materialGoodsInfoDTO = goodsInfoDTOMap.get(wmsMaterialTaskDetailCommandInput.getMaterialSku());
        if (materialGoodsInfoDTO == null){
            throw new BizException("商品信息不存在");
        }

        WmsMaterialTaskDetailCommandParam wmsMaterialTaskDetailCommandParam = new WmsMaterialTaskDetailCommandParam();
//        wmsMaterialTaskDetailCommandParam.setId(wmsMaterialTaskDetailCommandInput.getId());
        wmsMaterialTaskDetailCommandParam.setTenantId(wmsMaterialTaskCommandInput.getTenantId());
        wmsMaterialTaskDetailCommandParam.setWarehouseNo(wmsMaterialTaskCommandInput.getWarehouseNo());
        wmsMaterialTaskDetailCommandParam.setType(wmsMaterialTaskCommandInput.getType());
//        wmsMaterialTaskDetailCommandParam.setMaterialTaskCode(wmsMaterialTaskDetailCommandInput.getMaterialTaskCode());
        wmsMaterialTaskDetailCommandParam.setMaterialSku(wmsMaterialTaskDetailCommandInput.getMaterialSku());
        wmsMaterialTaskDetailCommandParam.setMaterialSkuSaasId(materialGoodsInfoDTO != null ?
                materialGoodsInfoDTO.getSkuId() : wmsMaterialTaskDetailCommandInput.getMaterialSkuSaasId());
        wmsMaterialTaskDetailCommandParam.setMaterialSkuName(materialGoodsInfoDTO != null ?
                materialGoodsInfoDTO.getTitle() :  "");
        wmsMaterialTaskDetailCommandParam.setQuantity(wmsMaterialTaskDetailCommandInput.getQuantity());
        wmsMaterialTaskDetailCommandParam.setCreator(wmsMaterialTaskCommandInput.getCreator());
        wmsMaterialTaskDetailCommandParam.setCreateTime(wmsMaterialTaskCommandInput.getCreateTime());
        wmsMaterialTaskDetailCommandParam.setUpdater(wmsMaterialTaskCommandInput.getUpdater());
        wmsMaterialTaskDetailCommandParam.setUpdateTime(wmsMaterialTaskCommandInput.getUpdateTime());
        return wmsMaterialTaskDetailCommandParam;
    }

// ------------------------------- response ----------------------------

}
