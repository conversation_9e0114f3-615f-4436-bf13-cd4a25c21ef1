package net.summerfarm.wms.application.processingtask.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskCreateDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductSpecDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductSpecOrderDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingMaterialModelTypeEnum;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskStatusEnum;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class ProcessingTaskCreateAggregateFactory {

    /**
     * 创建实例
     *
     * @param processingTaskCreateDTO
     * @param mapProcessingConfig
     * @param materialConfigGroup
     * @return
     */
    public static ProcessingTaskCreateAggregate newInstance(ProcessingTaskCreateDTO processingTaskCreateDTO,
                                                            Map<String, List<ProcessingConfig>> mapProcessingConfig,
                                                            Map<Long, List<ProcessingMaterialConfigEntity>> materialConfigGroup) {
        ProcessingTaskCreateAggregate aggregate = new ProcessingTaskCreateAggregate();

        aggregate.setProcessingTask(newProcessingTask(processingTaskCreateDTO));

        aggregate.setProcessingTaskProductList(newProcessingTaskProductList(
                processingTaskCreateDTO, mapProcessingConfig));

        aggregate.setProcessingTaskProductOrderRecordList(newProcessingTaskProductOrderRecordList(
                processingTaskCreateDTO, mapProcessingConfig));

        aggregate.setProcessingTaskProductRecordList(newProcessingTaskProductRecordList(
                processingTaskCreateDTO, mapProcessingConfig));

        aggregate.setProcessingTaskMaterialList(newProcessingTaskMaterialList(
                processingTaskCreateDTO, mapProcessingConfig, materialConfigGroup));


        return aggregate;
    }

    private static List<ProcessingTaskMaterial> newProcessingTaskMaterialList(
            ProcessingTaskCreateDTO processingTaskCreateDTO,
            Map<String, List<ProcessingConfig>> mapProcessingConfig,
            Map<Long, List<ProcessingMaterialConfigEntity>> materialConfigGroup) {
        List<ProcessingTaskMaterial> result = new ArrayList<>();

        for (ProcessingTaskProductDTO dto : processingTaskCreateDTO.getProcessingTaskProductDTOList()) {
            List<ProcessingConfig> processingConfigList = mapProcessingConfig.get(dto.getProductSkuCode());
            for (ProcessingConfig processingConfig : processingConfigList) {
                List<ProcessingMaterialConfigEntity> materialConfigEntityList =
                        materialConfigGroup.get(processingConfig.getId());
                if (CollectionUtils.isEmpty(materialConfigEntityList)){
                    throw new BizException("创建加工任务多原料配置不存在:" + processingConfig.getId());
                }
                for (ProcessingMaterialConfigEntity materialConfigEntity : materialConfigEntityList) {
                    ProcessingTaskMaterial processingTaskMaterial = new ProcessingTaskMaterial();

                    processingTaskMaterial.setWarehouseNo(processingTaskCreateDTO.getWarehouseNo());
                    processingTaskMaterial.setProcessingTaskCode(processingTaskCreateDTO.getProcessingTaskCode());

                    processingTaskMaterial.setProductSkuCode(processingConfig.getProductSkuCode());

                    processingTaskMaterial.setMaterialSkuCode(materialConfigEntity.getMaterialSkuCode());
                    processingTaskMaterial.setMaterialSkuName(materialConfigEntity.getMaterialSkuName());
                    processingTaskMaterial.setMaterialSkuWeight(materialConfigEntity.getMaterialSkuWeight());
                    processingTaskMaterial.setMaterialSkuUnit(materialConfigEntity.getMaterialSkuUnit());
                    processingTaskMaterial.setMaterialSkuQuantity(0);
                    processingTaskMaterial.setMaterialSkuReceiveQuantity(0);
                    processingTaskMaterial.setMaterialSkuReceiveWeight(BigDecimal.ZERO);
                    processingTaskMaterial.setMaterialSkuRestoreQuantity(0);

                    processingTaskMaterial.setWasteLossWeight(BigDecimal.ZERO);
                    processingTaskMaterial.setSpecLossWeight(BigDecimal.ZERO);
                    processingTaskMaterial.setMaterialSkuRemainWeight(BigDecimal.ZERO);
                    processingTaskMaterial.setMaterialSkuUnitDesc(materialConfigEntity.getMaterialSkuUnitDesc());

                    processingTaskMaterial.setMaterialSkuRatioNum(materialConfigEntity.getMaterialSkuRatioNum());

                    processingTaskMaterial.setCreator(LoginInfoThreadLocal.getCurrentUserName());
                    processingTaskMaterial.setCreateTime(new Date());
                    processingTaskMaterial.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
                    processingTaskMaterial.setUpdateTime(new Date());
                    processingTaskMaterial.setDeleteFlag(DeleteFlagEnum.NO.getValue());

                    result.add(processingTaskMaterial);
                }
            }
        }

        return result;
    }

    private static List<ProcessingTaskProduct> newProcessingTaskProductList(
            ProcessingTaskCreateDTO processingTaskCreateDTO,
            Map<String, List<ProcessingConfig>> mapProcessingConfig) {
        List<ProcessingTaskProduct> result = new ArrayList<>();

        for (ProcessingTaskProductDTO dto : processingTaskCreateDTO.getProcessingTaskProductDTOList()) {
            List<ProcessingConfig> processingConfigList = mapProcessingConfig.get(dto.getProductSkuCode());
            for (ProcessingConfig processingConfig : processingConfigList) {
                ProcessingTaskProduct processingTaskProduct = new ProcessingTaskProduct();

                processingTaskProduct.setWarehouseNo(processingTaskCreateDTO.getWarehouseNo());
                processingTaskProduct.setProcessingTaskCode(processingTaskCreateDTO.getProcessingTaskCode());

                processingTaskProduct.setMaterialSkuCode(processingConfig.getMaterialSkuCode());
                processingTaskProduct.setMaterialSkuName(processingConfig.getMaterialSkuName());
                processingTaskProduct.setMaterialSkuWeight(processingConfig.getMaterialSkuWeight());
                processingTaskProduct.setMaterialSkuUnit(processingConfig.getMaterialSkuUnit());
                processingTaskProduct.setMaterialSkuUnitDesc(processingConfig.getMaterialSkuUnitDesc());
                processingTaskProduct.setMaterialSkuReceiveQuantity(0);
                processingTaskProduct.setMaterialSkuRestoreQuantity(0);
                processingTaskProduct.setMaterialSkuReceiveWeight(BigDecimal.ZERO);

                processingTaskProduct.setProductSkuCode(processingConfig.getProductSkuCode());
                processingTaskProduct.setProductSkuName(processingConfig.getProductSkuName());
                processingTaskProduct.setProductSkuWeight(processingConfig.getProductSkuWeight());
                processingTaskProduct.setProductSkuUnit(processingConfig.getProductSkuUnit());
                processingTaskProduct.setProductSkuUnitDesc(processingConfig.getProductSkuUnitDesc());
                processingTaskProduct.setProductSkuNeedQuantity(dto.getProductSkuNeedQuantity());
                processingTaskProduct.setProductSkuFinishQuantity(0);
                processingTaskProduct.setProductSkuSpecFinishWeight(BigDecimal.ZERO);

                processingTaskProduct.setWasteLossWeight(BigDecimal.ZERO);
                processingTaskProduct.setSpecLossWeight(BigDecimal.ZERO);

                processingTaskProduct.setCreator(LoginInfoThreadLocal.getCurrentUserName());
                processingTaskProduct.setCreateTime(new Date());
                processingTaskProduct.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
                processingTaskProduct.setUpdateTime(new Date());
                processingTaskProduct.setDeleteFlag(DeleteFlagEnum.NO.getValue());

                processingTaskProduct.setMaterialModel(ProcessingMaterialModelTypeEnum.MULTI_MATERIAL.getValue());

                processingTaskProduct.setProductSkuRatioNum(processingConfig.getProductSkuRatioNum());

                result.add(processingTaskProduct);
            }
        }

        return result;
    }

    private static List<ProcessingTaskProductRecord> newProcessingTaskProductRecordList(
            ProcessingTaskCreateDTO processingTaskCreateDTO,
            Map<String, List<ProcessingConfig>> mapProcessingConfig) {
        List<ProcessingTaskProductRecord> result = new ArrayList<>();

        for (ProcessingTaskProductSpecDTO dto : processingTaskCreateDTO.getProcessingTaskProductSpecDTOList()) {
            List<ProcessingConfig> processingConfigList = mapProcessingConfig.get(dto.getProductSkuCode());
            if (CollectionUtils.isEmpty(processingConfigList)){
                String errorMsg = String.format("sku加工配置不存在: %s", dto.getProductSkuCode());
                log.error(errorMsg);
                throw new BizException(ErrorCode.GOODS_NOT_EXIST.getCode(), errorMsg);
            }
            ProcessingConfig processingConfig = processingConfigList.get(0);


            ProcessingTaskProductRecord processingTaskProduct = new ProcessingTaskProductRecord();

            processingTaskProduct.setWarehouseNo(processingTaskCreateDTO.getWarehouseNo());
            processingTaskProduct.setProcessingTaskCode(processingTaskCreateDTO.getProcessingTaskCode());

//                processingTaskProduct.setProcessingTaskProductId();

            processingTaskProduct.setProductSkuCode(processingConfig.getProductSkuCode());
            processingTaskProduct.setProductSkuName(processingConfig.getProductSkuName());
            processingTaskProduct.setProductSkuNeedQuantity(dto.getProductSkuNeedQuantity());

            processingTaskProduct.setProductSkuSpecWeight(dto.getProductSkuSpecWeight());
            processingTaskProduct.setProductSkuSpecUnit(dto.getProductSkuSpecUnit());
            processingTaskProduct.setProductSkuSpecNeedQuantity(dto.getProductSkuSpecNeedQuantity());
            processingTaskProduct.setProductSkuSpecSubmitQuantity(0);
            processingTaskProduct.setProductSkuSpecSubmitWeight(BigDecimal.ZERO);
            processingTaskProduct.setProductSkuSpecPrintNumber(0);

            processingTaskProduct.setCreator(LoginInfoThreadLocal.getCurrentUserName());
            processingTaskProduct.setCreateTime(new Date());
            processingTaskProduct.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
            processingTaskProduct.setUpdateTime(new Date());
            processingTaskProduct.setDeleteFlag(DeleteFlagEnum.NO.getValue());

            result.add(processingTaskProduct);
        }
        return result;
    }


    private static List<ProcessingTaskProductOrderRecord> newProcessingTaskProductOrderRecordList(
            ProcessingTaskCreateDTO processingTaskCreateDTO,
            Map<String, List<ProcessingConfig>> mapProcessingConfig) {
        List<ProcessingTaskProductOrderRecord> result = new ArrayList<>();

        for (ProcessingTaskProductSpecOrderDTO dto : processingTaskCreateDTO.getProcessingTaskProductSpecOrderDTOList()) {
            List<ProcessingConfig> processingConfigList = mapProcessingConfig.get(dto.getProductSkuCode());
            if (CollectionUtils.isEmpty(processingConfigList)){
                String errorMsg = String.format("sku加工配置不存在: %s", dto.getProductSkuCode());
                log.error(errorMsg);
                throw new BizException(ErrorCode.GOODS_NOT_EXIST.getCode(), errorMsg);
            }
            ProcessingConfig processingConfig = processingConfigList.get(0);

            ProcessingTaskProductOrderRecord processingTaskProduct = new ProcessingTaskProductOrderRecord();

            processingTaskProduct.setWarehouseNo(processingTaskCreateDTO.getWarehouseNo());
            processingTaskProduct.setProcessingTaskCode(processingTaskCreateDTO.getProcessingTaskCode());

//                processingTaskProduct.setProcessingTaskProductId();

            processingTaskProduct.setSourceId(dto.getSourceId());

            processingTaskProduct.setProductSkuCode(processingConfig.getProductSkuCode());
            processingTaskProduct.setProductSkuName(processingConfig.getProductSkuName());
            processingTaskProduct.setProductSkuNeedQuantity(dto.getProductSkuNeedQuantity());

            processingTaskProduct.setProductSkuSpecWeight(dto.getProductSkuSpecWeight());
            processingTaskProduct.setProductSkuSpecUnit(dto.getProductSkuSpecUnit());
            processingTaskProduct.setProductSkuSpecNeedQuantity(dto.getProductSkuSpecNeedQuantity());
            processingTaskProduct.setProductSkuSpecSubmitQuantity(0);
            processingTaskProduct.setProductSkuSpecSubmitWeight(BigDecimal.ZERO);
            processingTaskProduct.setProductSkuSpecPrintNumber(0);

            processingTaskProduct.setCreator(LoginInfoThreadLocal.getCurrentUserName());
            processingTaskProduct.setCreateTime(new Date());
            processingTaskProduct.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
            processingTaskProduct.setUpdateTime(new Date());
            processingTaskProduct.setDeleteFlag(DeleteFlagEnum.NO.getValue());

            result.add(processingTaskProduct);
        }
        return result;
    }

    private static ProcessingTask newProcessingTask(ProcessingTaskCreateDTO processingTaskCreateDTO) {
        ProcessingTask processingTask = new ProcessingTask();

        processingTask.setWarehouseNo(processingTaskCreateDTO.getWarehouseNo());
        processingTask.setProcessingTaskCode(processingTaskCreateDTO.getProcessingTaskCode());
        processingTask.setType(processingTaskCreateDTO.getType());
        processingTask.setStatus(ProcessingTaskStatusEnum.NOT_PROCESSING.getValue());

        processingTask.setFinishTime(null);
        processingTask.setFinishRemark(null);

        processingTask.setCreator(LoginInfoThreadLocal.getCurrentUserName());
        processingTask.setCreateTime(new Date());
        processingTask.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
        processingTask.setUpdateTime(new Date());
        processingTask.setDeleteFlag(DeleteFlagEnum.NO.getValue());

        return processingTask;
    }
}
