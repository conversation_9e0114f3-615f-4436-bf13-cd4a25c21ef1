package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigQueryResDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 加工规则响应对象转换类
 * <AUTHOR>
 * @date 2023/02/14
 */
public class ProcessingConfigQueryResDTOConverter {

    public static ProcessingConfigQueryResDTO convert(ProcessingConfig processingConfig) {
        if ( processingConfig == null ) {
            return null;
        }

        ProcessingConfigQueryResDTO processingConfigQueryResDTO = new ProcessingConfigQueryResDTO();

        if ( processingConfig.getCreateTime() != null ) {
            processingConfigQueryResDTO.setCreateTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( processingConfig.getCreateTime() ) );
        }
        if ( processingConfig.getUpdateTime() != null ) {
            processingConfigQueryResDTO.setUpdateTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( processingConfig.getUpdateTime() ) );
        }
        processingConfigQueryResDTO.setId( processingConfig.getId() );
        processingConfigQueryResDTO.setWarehouseNo( processingConfig.getWarehouseNo() );
        processingConfigQueryResDTO.setType( processingConfig.getType() );
        processingConfigQueryResDTO.setMaterialSkuCode( processingConfig.getMaterialSkuCode() );
        processingConfigQueryResDTO.setMaterialSkuName( processingConfig.getMaterialSkuName() );
        processingConfigQueryResDTO.setMaterialSkuWeight( processingConfig.getMaterialSkuWeight() );
        processingConfigQueryResDTO.setMaterialSkuUnit( processingConfig.getMaterialSkuUnit() );
        processingConfigQueryResDTO.setProductSkuCode( processingConfig.getProductSkuCode() );
        processingConfigQueryResDTO.setProductSkuName( processingConfig.getProductSkuName() );
        processingConfigQueryResDTO.setProductSkuWeight( processingConfig.getProductSkuWeight() );
        processingConfigQueryResDTO.setProductSkuUnit( processingConfig.getProductSkuUnit() );
        processingConfigQueryResDTO.setInvalid( processingConfig.getInvalid() );
        processingConfigQueryResDTO.setCreator( processingConfig.getCreator() );
        processingConfigQueryResDTO.setUpdater( processingConfig.getUpdater() );
        processingConfigQueryResDTO.setDeleteFlag( processingConfig.getDeleteFlag() );
        processingConfigQueryResDTO.setProcessingConfigCode( processingConfig.getProcessingConfigCode() );
        processingConfigQueryResDTO.setMaterialSkuUnitDesc( processingConfig.getMaterialSkuUnitDesc() );
        processingConfigQueryResDTO.setProductSkuUnitDesc( processingConfig.getProductSkuUnitDesc() );
        processingConfigQueryResDTO.setMaterialModel( processingConfig.getMaterialModel() );
        processingConfigQueryResDTO.setProductSkuRatioNum( processingConfig.getProductSkuRatioNum());

        return processingConfigQueryResDTO;
    }

    public static List<ProcessingConfigQueryResDTO> convertList(List<ProcessingConfig> processingConfigList) {
        if ( processingConfigList == null ) {
            return null;
        }

        List<ProcessingConfigQueryResDTO> list = new ArrayList<ProcessingConfigQueryResDTO>( processingConfigList.size() );
        for ( ProcessingConfig processingConfig : processingConfigList ) {
            list.add( convert( processingConfig ) );
        }

        return list;
    }
}
