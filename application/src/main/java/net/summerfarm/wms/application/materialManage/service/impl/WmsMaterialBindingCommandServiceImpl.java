package net.summerfarm.wms.application.materialManage.service.impl;


import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialBindingAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingDetailCommandInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialBindingCommandService;
import net.summerfarm.wms.application.materialManage.service.factory.WmsMaterialBindingCreateAggregateFactory;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialBindingCreateAggregate;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingCommandParam;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialBindingQueryRepository;
import net.summerfarm.wms.domain.materialManage.service.WmsMaterialBindingCommandDomainService;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class WmsMaterialBindingCommandServiceImpl implements WmsMaterialBindingCommandService {

    @Autowired
    private WmsMaterialBindingCommandDomainService wmsMaterialBindingCommandDomainService;
    @Autowired
    private WmsMaterialBindingQueryRepository wmsMaterialBindingQueryRepository;
    @Autowired
    private GoodsReadFacade goodsReadFacade;
    @Autowired
    private ProductRepository productRepository;

    @Override
    public CommonResult<WmsMaterialBindingVO> insert(WmsMaterialBindingCommandInput input) {
        if (input == null || CollectionUtils.isEmpty(input.getDetailCommandInputList())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求明细不存在");
        }
        if (input.getWarehouseNo() == null || StringUtils.isEmpty(input.getSku())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求参数为空");
        }
        if (input.getSku() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求sku参数为空");
        }
        // 校验请求明细是否重复
        Set<String> materialSkuSet = input.getDetailCommandInputList().stream()
               .map(WmsMaterialBindingDetailCommandInput::getMaterialSku)
               .filter(Objects::nonNull)
               .collect(Collectors.toSet());
        if (materialSkuSet.size()!= input.getDetailCommandInputList().size()){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求sku明细存在重复");
        }

        WmsMaterialBindingEntity wmsMaterialBindingEntity = wmsMaterialBindingQueryRepository.selectEnableByWnoAndSku(
                input.getWarehouseNo(), input.getSku()
        );
        if (wmsMaterialBindingEntity != null){
            // 作废之前的
            this.delete(wmsMaterialBindingEntity.getId());
        }

        List<String> materialSkuCodeList = input.getDetailCommandInputList().stream()
                .map(WmsMaterialBindingDetailCommandInput::getMaterialSku)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialSkuCodeList)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求明细不存在");
        }

        // 物料类目
        Long materialFirstCategory = productRepository.getMaterialFirstCategory();

        // 货品
        Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                input.getTenantId(), Collections.singletonList(input.getSku()));
        // 校验货品存在物料
        long skuCount = goodsInfoDTOMap.values().stream()
                .filter(s -> materialFirstCategory != null &&
                        materialFirstCategory.equals(s.getFirstCategoryId()))
                .count();
        if (skuCount > 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求货品存在物料");
        }

        // 物料
        Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                input.getTenantId(), materialSkuCodeList);
        // 获取下架的所有物料sku
        String offSaleMaterialSkuCode = materialGoodsInfoDTOMap.values().stream()
                .filter(s -> s.getUseFlag() != 1)
                .map(GoodsInfoDTO::getSku)
                .collect(Collectors.joining(","));
        if (StringUtils.hasText(offSaleMaterialSkuCode)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求物资存在下架的sku：" + offSaleMaterialSkuCode);
        }
        // 校验货品存在物料
        Long materialSkuCount = materialGoodsInfoDTOMap.values().stream()
                .filter(s -> materialFirstCategory != null &&
                        materialFirstCategory.equals(s.getFirstCategoryId()))
                .count();
        if (materialSkuCount.compareTo((long) materialGoodsInfoDTOMap.size()) != 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求物资sku存在非物料");
        }

        WmsMaterialBindingCreateAggregate param = WmsMaterialBindingCreateAggregateFactory.build(
                input, goodsInfoDTOMap, materialGoodsInfoDTOMap);
        WmsMaterialBindingEntity entity = wmsMaterialBindingCommandDomainService.insert(param);

        return CommonResult.ok(WmsMaterialBindingAssembler.toWmsMaterialBindingVO(entity,
                new HashMap<>(), new HashMap<>(), new HashMap<>(), new HashMap<>()));
    }


    @Override
    public int delete(Long id) {
        WmsMaterialBindingEntity entity = wmsMaterialBindingQueryRepository.selectById(id);
        if (entity == null){
            return 0;
        }

        WmsMaterialBindingCommandParam updateParam = new WmsMaterialBindingCommandParam();
        updateParam.setId(id);
        updateParam.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
        updateParam.setUpdateTime(LocalDateTime.now());
        return wmsMaterialBindingCommandDomainService.delete(updateParam);
    }

}