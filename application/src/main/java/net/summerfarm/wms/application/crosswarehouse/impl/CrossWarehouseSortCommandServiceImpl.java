package net.summerfarm.wms.application.crosswarehouse.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.crosswarehouse.CrossWarehouseSortCommandService;
import net.summerfarm.wms.api.h5.crosswarehouse.dto.CrossWarehouseSortInfoDTO;
import net.summerfarm.wms.api.h5.crosswarehouse.req.AllocationCrossWarehouseSortDetailInput;
import net.summerfarm.wms.api.h5.crosswarehouse.req.AllocationCrossWarehouseSortInput;
import net.summerfarm.wms.api.h5.crosswarehouse.req.CrossWarehouseSortAfterSaleCommandInput;
import net.summerfarm.wms.application.mission.bizobject.CrossWarehouseSortAllocationDTO;
import net.summerfarm.wms.application.mission.handler.CrossWarehouseAllocationHandler;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.config.repository.ConfigRepository;
import net.summerfarm.wms.domain.crosswarehouse.domainobject.CrossWarehouseSortInfo;
import net.summerfarm.wms.domain.crosswarehouse.repository.CrossWarehouseSortInfoRepository;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageStateEnum;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.mq.crosswarehouse.CrossWarehouseAfterSaleReturnEvent;
import net.xianmu.robot.feishu.FeishuBotUtil;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: dongcheng
 * @date: 2023/10/8
 */
@Service
@Slf4j
public class CrossWarehouseSortCommandServiceImpl implements CrossWarehouseSortCommandService {

    @Resource
    private CrossWarehouseSortInfoRepository crossWarehouseSortInfoRepository;
    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ConfigRepository configRepository;

    /**
     * 批量保存越库分拣明细
     *
     * @param crossWarehouseSortInfoList 越库分拣明细保存内容
     */
    @Override
    public void saveBatch(List<CrossWarehouseSortInfoDTO> crossWarehouseSortInfoList) {
        if (CollectionUtils.isEmpty(crossWarehouseSortInfoList)) {
            return;
        }

        List<CrossWarehouseSortInfo> collect = crossWarehouseSortInfoList.stream().map(it -> {
            return CrossWarehouseSortInfo.builder()
                    .sku(it.getSku())
                    .saleOrderNo(it.getSaleOrderNo())
                    .psoNo(it.getPsoNo())
                    .warehouseNo(it.getWarehouseNo())
                    .storeNo(it.getStoreNo())
                    .supplierId(it.getSupplierId())
                    .tenantId(it.getTenantId())
                    .totalQuantity(it.getQuantity())
                    .generateQuantity(0)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .initQuantity(it.getQuantity())
                    .shelveQuantity(0)
                    .fulfillmentNo(it.getFulfillmentNo())
                    .build();
        }).collect(Collectors.toList());
        // 批量保存
        crossWarehouseSortInfoRepository.batchSave(collect);
    }

    /**
     * 退单售后
     *
     * @param afterSaleCommandInput 退单售后明细列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterSale(CrossWarehouseSortAfterSaleCommandInput afterSaleCommandInput) {
        if (afterSaleCommandInput == null) {
            return;
        }

        // 查询对应的越库分拣明细
        List<CrossWarehouseSortInfo> crossWarehouseSortInfoList =
                crossWarehouseSortInfoRepository.findByFulfillmentNoAndPsoAndSku(afterSaleCommandInput.getFulfillmentNo(),
                        afterSaleCommandInput.getPsoNo(), afterSaleCommandInput.getSkuCode());

        if (CollectionUtils.isEmpty(crossWarehouseSortInfoList)) {
            return;
        }

        CrossWarehouseSortInfo crossWarehouseSortInfo = crossWarehouseSortInfoList.get(0);
        Long warehouseNo = crossWarehouseSortInfo.getWarehouseNo();

        // 获取入库任务
        StockTaskStorage stockTaskStorage = getStockTaskStorage(warehouseNo, afterSaleCommandInput.getPsoNo());

        // 入库任务不存在
        if (stockTaskStorage == null) {
            log.info("还没有生成入库任务: 退单没有关联的入库任务内容, 直接做数据更新");
            // 扣减对应的越库投线明细
            crossWarehouseSortInfoRepository.updateTotalQuantityById(crossWarehouseSortInfo.getId(),
                    crossWarehouseSortInfo.getTotalQuantity(),
                    afterSaleCommandInput.getQuantity());
            // 完成发送收到操作事务后发
            sendAfterSaleReturnMessage(afterSaleCommandInput);
            return;
        }

        // 处理退单操作
        handlerAfterSale(stockTaskStorage, crossWarehouseSortInfo, afterSaleCommandInput);

        // 完成发送收到操作事务后发
        sendAfterSaleReturnMessage(afterSaleCommandInput);
    }

    /**
     * 处理退单操作
     *
     * @param stockTaskStorage       入库任务
     * @param crossWarehouseSortInfo 越库分拣明细
     * @param afterSaleCommandInput  退单内容
     */
    private void handlerAfterSale(StockTaskStorage stockTaskStorage,
                                  CrossWarehouseSortInfo crossWarehouseSortInfo,
                                  CrossWarehouseSortAfterSaleCommandInput afterSaleCommandInput) {
        // 查询飞书告警机器人地址
        String url = configRepository.queryValueByKey("wms_after_sale_order_notice");
        // 操作对应的退单操作 越库入库类型
        if (Objects.equals(OperationType.CROSS_WAREHOUSE_IN.getId(), stockTaskStorage.getType())) {
            if (afterSaleCommandInput.getQuantity() > crossWarehouseSortInfo.getTotalQuantity()) {
                String txt = "非日配退单消息: " +
                        "退单数量大于现在可以操作的数量 业务单号:[" +
                        afterSaleCommandInput.getBusinessNo() + "]" +
                        "请及时查看: " + JSON.toJSONString(afterSaleCommandInput);
                FeishuBotUtil.sendTextMsgAndAtAll(url, txt);
                return;
            }

            // 扣减对应的越库投线明细
            crossWarehouseSortInfoRepository.updateTotalQuantityById(crossWarehouseSortInfo.getId(),
                    crossWarehouseSortInfo.getTotalQuantity(),
                    afterSaleCommandInput.getQuantity());

        } else if (Objects.equals(OperationType.PURCHASE_IN.getId(), stockTaskStorage.getType())) {
            // 查询是否完成收货
            if (Objects.equals(StockTaskStorageStateEnum.FINISH.getId(), stockTaskStorage.getState())) {
                // 完成收货
                String txt = "非日配退单消息: " +
                        "采购预提已经完成入库任务 业务单号:[" +
                        afterSaleCommandInput.getBusinessNo() + "]" +
                        "不做处理";
                FeishuBotUtil.sendTextMsgAndAtAll(url, txt);
                return;
            }

            crossWarehouseSortInfoRepository.updateTotalQuantityById(crossWarehouseSortInfo.getId(),
                    crossWarehouseSortInfo.getTotalQuantity(),
                    afterSaleCommandInput.getQuantity());
            // 判断剩余可分配数量是否大于等于当前退单数量
            // 大于等于直接扣减对应的 可分配总数量
            // 小于的话说明当前的订单已经分配完成 需要触发重新分配
            if (crossWarehouseSortInfo.getRemainingAllocationQuantity() >= afterSaleCommandInput.getQuantity()) {
                return;
            }
            // 触发重新分配
            reAllocationCrossWarehouseSort(afterSaleCommandInput.getPsoNo(), afterSaleCommandInput.getSkuCode());
        } else {
            log.error("业务单号对应的入库任务不在设计的业务范围内");
        }
    }

    /**
     * 退单触发重新分配
     *
     * @param psoNo   采购供应单号
     * @param skuCode sku列表
     */
    private void reAllocationCrossWarehouseSort(String psoNo, String skuCode) {
        // 按照查询越库分拣明细对应的 pso + sku
        List<String> skuList = Lists.newArrayList();
        skuList.add(skuCode);
        List<CrossWarehouseSortInfo> warehouseSortInfos = crossWarehouseSortInfoRepository.findByPsoNoAndSkuList(psoNo, skuList);

        // 重新分配
        List<CrossWarehouseSortAllocationDTO> sortAllocationDTOList = CrossWarehouseAllocationHandler.reAllocation(warehouseSortInfos);

        if (CollectionUtils.isEmpty(sortAllocationDTOList)) {
            log.info("退单触发重新分配 分配逻辑下来不需要做分配");
            return;
        }

        // 更新分配明细
        for (CrossWarehouseSortAllocationDTO allocationDTO : sortAllocationDTOList) {
            // 更新对应的分配信息
            crossWarehouseSortInfoRepository.updateQuantityById(allocationDTO.getId(),
                    allocationDTO.getAllocationCrossQuantity(),
                    allocationDTO.getAllocationShelveQuantity());
        }
    }

    /**
     * 分配越库分拣明细
     *
     * @param input 分配条件内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allocationCrossWarehouseSort(AllocationCrossWarehouseSortInput input) {

        List<AllocationCrossWarehouseSortDetailInput> sortDetailList = input.getSortDetailList();
        String psoNo = input.getPsoNo();
        List<String> skuList = sortDetailList.stream().map(AllocationCrossWarehouseSortDetailInput::getSku)
                .distinct().collect(Collectors.toList());
        // 按照查询越库分拣明细对应的 pso + sku
        List<CrossWarehouseSortInfo> warehouseSortInfos = crossWarehouseSortInfoRepository.findByPsoNoAndSkuList(psoNo, skuList);
        // 查询到的数据进行分组
        Map<String, List<CrossWarehouseSortInfo>> warehouseSortInfoListMap = warehouseSortInfos.stream().collect(Collectors.groupingBy(CrossWarehouseSortInfo::getSku));

        sortDetailList.forEach(sortDetail -> {
            List<CrossWarehouseSortInfo> warehouseSortInfoList = warehouseSortInfoListMap.get(sortDetail.getSku());
            // 待分配的数量
            Integer quantity = sortDetail.getQuantity();
            // 分配正常生成数量
            List<CrossWarehouseSortAllocationDTO> allocationList = CrossWarehouseAllocationHandler.allocation(quantity, warehouseSortInfoList);
            if (CollectionUtils.isEmpty(allocationList)) {
                log.info("预提分配分拣明细已经全部分配完成, 收货 -->> pso:{}, -->> sku:{}, -->> quantity:{}", psoNo, sortDetail.getSku(), quantity);
                return;
            }
            // 分配数量
            Integer generateQuantity = 0;
            for (CrossWarehouseSortAllocationDTO allocationDTO : allocationList) {
                // 更新对应的分拣明细内容
                generateQuantity = generateQuantity + allocationDTO.getAllAllocationQuantity();
                crossWarehouseSortInfoRepository.updateQuantityById(allocationDTO.getId(),
                        allocationDTO.getAllocationCrossQuantity(),
                        allocationDTO.getAllocationShelveQuantity());
            }
            if (quantity > generateQuantity) {
                log.info("预提分配分拣明细已经全部分配完成,还多出数量, 收货 -->> pso:{}, -->> sku:{}, -->> quantity:{}", psoNo, sortDetail.getSku(), quantity - generateQuantity);
            }
        });

    }


    /**
     * 获取对应的入库任务
     *
     * @return 返回入库任务
     */
    private StockTaskStorage getStockTaskStorage(Long warehouseNo, String psoNo) {
        // 查询对应的入库任务 采购入库和越库入库
        List<Integer> typeList = Lists.newArrayList();
        typeList.add(OperationType.PURCHASE_IN.getId());
        typeList.add(OperationType.CROSS_WAREHOUSE_IN.getId());
        List<StockTaskStorage> stockTaskStorageList
                = stockTaskStorageQueryRepository.queryStockTaskStorage(warehouseNo, typeList, psoNo);
        if (CollectionUtils.isEmpty(stockTaskStorageList)) {
            return null;
        }
        return stockTaskStorageList.get(0);
    }

    /**
     * 发送接受到消息回告
     *
     * @param afterSaleCommandInput 退单消息内容
     */
    private void sendAfterSaleReturnMessage(CrossWarehouseSortAfterSaleCommandInput afterSaleCommandInput) {
        if (afterSaleCommandInput == null) {
            return;
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CrossWarehouseAfterSaleReturnEvent event = CrossWarehouseAfterSaleReturnEvent.builder().businessNo(afterSaleCommandInput.getBusinessNo()).build();
                // 发送收到退单消息
                mqProducer.send("topic_wms_stock_task", "tag_wms_cross_refund_return", event);
            }
        });
    }
}
