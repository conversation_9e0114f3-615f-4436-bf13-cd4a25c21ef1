package net.summerfarm.wms.application.crosswarehouse.model;

import lombok.Data;

import java.io.Serializable;

/**
 * wms_cross_warehouse_sort_info 的备注信息
 * <AUTHOR>
 * @Date 2024/4/18 15:53
 * @Version 1.0
 */
@Data
public class CrossRemarkJson implements Serializable {
    private static final long serialVersionUID = 8024244814597652942L;

    /**
     * 旧城配仓编码
     */
    private Integer oldStoreNo;

    /**
     * 新城配仓编码
     */
    private Integer newStoreNo;

    /**
     * 旧仓库编码
     */
    private Long oldWarehouseNo;

    /**
     * 新仓库编码
     */
    private Long newWarehouseNo;

    /**
     * 备注添加时间
     */
    private Long addTime;
}
