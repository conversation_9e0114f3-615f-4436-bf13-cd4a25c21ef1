package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 成品SKU规格
 * <AUTHOR>
 * @date 2023/02/13
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingConfigProductSkuSpecDTO {

    /**
     * 成品SKU规格重量
     */
    private BigDecimal productSkuSpecWeight;

    /**
     * 成品SKU规格单位
     */
    private String productSkuSpecUnit;
}
