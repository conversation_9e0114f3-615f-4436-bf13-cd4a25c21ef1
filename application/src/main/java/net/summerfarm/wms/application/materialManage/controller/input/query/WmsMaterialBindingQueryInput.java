package net.summerfarm.wms.application.materialManage.controller.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Data
public class WmsMaterialBindingQueryInput extends BasePageInput implements Serializable{

	/**
	 * id
	 */
	private Long id;
	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * sku编码
	 */
	private String sku;

	/**
	 * skuSaasId
	 */
	private Long skuSaasId;

	/**
	 * sku名称
	 */
	private String skuName;

	/**
	 * 物料sku编码
	 */
	private String materialSku;

	/**
	 * 物料skuSaasId
	 */
	private Long materialSkuSaasId;

	/**
	 * 物料sku名称
	 */
	private String materialSkuName;

	/**
	 * 状态，0：无效，1：有效
	 */
	private Integer status;


	/**
	 * 重复利用， 否，是
	 */
	private String reuse;

	/**
	 * 自动出库，否，是
	 */
	private String autoOutbound;

	/**
	 * 绑定id
	 */
	private Long materialBingingId;
}