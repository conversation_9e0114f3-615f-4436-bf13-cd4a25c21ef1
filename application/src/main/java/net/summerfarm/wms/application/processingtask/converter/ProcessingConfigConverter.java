package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.req.ProcessingConfigQueryReqDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ProcessingConfigConverter {

    ProcessingConfigConverter INSTANCE = Mappers.getMapper(ProcessingConfigConverter.class);

    /**
     * query -> entity
     * @param query query对象
     * @return entity对象
     */
    ProcessingConfig convert(ProcessingConfigQueryReqDTO query);
}
