package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProcessingMaterialConfigResDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 加工配置id
     */
    private Long processingConfigId;

    /**
     * 原料sku
     */
    private String materialSkuCode;

    /**
     * 原料skuId
     */
    private Long materialSkuSaasId;

    /**
     * 原料skuPdId
     */
    private Long materialSkuPdId;

    /**
     * 原料sku名称
     */
    private String materialSkuName;

    /**
     * 原料重量
     */
    private BigDecimal materialSkuWeight;

    /**
     * 原料sku单位
     */
    private String materialSkuUnit;

    /**
     * 原料sku单位描述
     */
    private String materialSkuUnitDesc;

    /**
     * 原料sku比例数量
     */
    private Integer materialSkuRatioNum;

    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 成品skuId
     */
    private Long productSkuSaasId;
    /**
     * 成品sku PdId
     */
    private Long productSkuPdId;
    /**
     * 成品sku名称
     */
    private String productSkuName;

    /**
     * 成品sku重量
     */
    private BigDecimal productSkuWeight;

    /**
     * 成品sku单位
     */
    private String productSkuUnit;

    /**
     * 成品sku单位描述
     */
    private String productSkuUnitDesc;
}
