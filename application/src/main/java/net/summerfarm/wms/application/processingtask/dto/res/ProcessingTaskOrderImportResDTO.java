package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskOrderCreateReqDTO;

import java.io.Serializable;
import java.util.List;

@Data
public class ProcessingTaskOrderImportResDTO implements Serializable {

    /**
     * 是否成功, 0-全部失败， 1-全部成功，2-部分成功
     */
    private Integer isSuccess;


    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 失败文件地址
     */
    private String failFileAddress;

    /**
     * 失败原因
     */
    private String fileMsg;

    /**
     * 真实失败原因
     */
    private String realFailMsg;

    /**
     * 创建请求列表
     */
    private List<ProcessingTaskOrderCreateReqDTO> createReqDTOList;
}
