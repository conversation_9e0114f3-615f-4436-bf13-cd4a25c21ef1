package net.summerfarm.wms.application.processingtask.enums;

public enum ProcessingTaskCanFinishEnum {


    ALL_PROCESSED(1, "全部加工完成"),

    PART_PROCESSING(2, "填写原因完成"),

    NOT_PROCESSED(3, "未完成商品加工"),

    ;

    private final int value;

    private final String description;

    ProcessingTaskCanFinishEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public boolean equalsCode(Integer input){
        return Integer.valueOf(this.value).equals(input);
    }
}
