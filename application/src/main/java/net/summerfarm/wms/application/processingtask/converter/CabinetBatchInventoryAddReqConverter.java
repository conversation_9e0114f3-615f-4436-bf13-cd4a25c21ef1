package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryAddDetailReq;
import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryAddReq;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryAdd;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CabinetBatchInventoryAddReqConverter {

    CabinetBatchInventoryAddReqConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryAddReqConverter.class);

    CabinetBatchInventoryAddReq convert(CabinetBatchInventoryAdd cabinetBatchInventoryAdd);

    CabinetBatchInventoryAddReq convertSame(CabinetBatchInventoryAddReq cabinetBatchInventoryAddReq);

    CabinetBatchInventoryAddDetailReq convertSameDetail(CabinetBatchInventoryAddDetailReq detailReq);

}
