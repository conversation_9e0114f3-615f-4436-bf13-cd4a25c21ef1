package net.summerfarm.wms.application.materialManage.service.factory;

import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialTaskAssembler;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialTaskDetailAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialTaskDetailCommandInput;
import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialTaskReceiveAggregate;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskCommandParam;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialTaskDetailCommandParam;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class WmsMaterialTaskReceiveAggregateFactory {

    public static WmsMaterialTaskReceiveAggregate build(WmsMaterialTaskCommandInput input,
                                                        Map<String, GoodsInfoDTO> goodsInfoDTOMap) {
        WmsMaterialTaskReceiveAggregate wmsMaterialTaskReceiveAggregate = new WmsMaterialTaskReceiveAggregate();

        WmsMaterialTaskCommandParam taskCommandParam = WmsMaterialTaskAssembler.buildCreateParam(input);
        List<WmsMaterialTaskDetailCommandParam> taskDetailCommandParamList = new ArrayList<>();
        for (WmsMaterialTaskDetailCommandInput wmsMaterialTaskDetailCommandInput : input.getDetailList()) {
            WmsMaterialTaskDetailCommandParam detailCommandParam = WmsMaterialTaskDetailAssembler
                    .buildCreateParam(input, wmsMaterialTaskDetailCommandInput, goodsInfoDTOMap);
            if (detailCommandParam != null){
                taskDetailCommandParamList.add(detailCommandParam);
            }
        }
        wmsMaterialTaskReceiveAggregate.setTaskCommandParam(taskCommandParam);
        wmsMaterialTaskReceiveAggregate.setTaskDetailCommandParam(taskDetailCommandParamList);
        return wmsMaterialTaskReceiveAggregate;
    }
}
