package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 加工任务明细成品打印响应对象
 * <AUTHOR>
 * @date 2023/02/18
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingTaskProductPrintResDTO {

    /**
     * 二维码内容
     */
    private String onlyCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String weight;

    /**
     * 加工规格
     */
    private String processingSpecUnitDesc;

    /**
     * 温区
     */
    private String storageLocation;

    /**
     * 生产日期
     */
    private String productionDate;

    /**
     * 条码
     */
    private String code;
}
