package net.summerfarm.wms.application.materialManage.controller.assembler;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialBindingQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingDetailVO;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingExportVO;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity;
import net.summerfarm.wms.domain.materialManage.enums.MaterialBindingStatusEnum;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingCommandParam;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingQueryParam;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.xianmu.common.exception.BizException;

import java.util.*;

/**
 *
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Slf4j
public class WmsMaterialBindingAssembler {

    private WmsMaterialBindingAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static WmsMaterialBindingQueryParam toWmsMaterialBindingQueryParam(WmsMaterialBindingQueryInput wmsMaterialBindingQueryInput) {
        if (wmsMaterialBindingQueryInput == null) {
            return null;
        }
        WmsMaterialBindingQueryParam wmsMaterialBindingQueryParam = new WmsMaterialBindingQueryParam();
        wmsMaterialBindingQueryParam.setId(wmsMaterialBindingQueryInput.getId());
        wmsMaterialBindingQueryParam.setTenantId(wmsMaterialBindingQueryInput.getTenantId());
        wmsMaterialBindingQueryParam.setWarehouseNo(wmsMaterialBindingQueryInput.getWarehouseNo());
        wmsMaterialBindingQueryParam.setSku(wmsMaterialBindingQueryInput.getSku());
        wmsMaterialBindingQueryParam.setSkuSaasId(wmsMaterialBindingQueryInput.getSkuSaasId());
        wmsMaterialBindingQueryParam.setSkuName(wmsMaterialBindingQueryInput.getSkuName());
//        wmsMaterialBindingQueryParam.setSkuRatio(wmsMaterialBindingQueryInput.getSkuRatio());
        wmsMaterialBindingQueryParam.setStatus(wmsMaterialBindingQueryInput.getStatus());
//        wmsMaterialBindingQueryParam.setCreator(wmsMaterialBindingQueryInput.getCreator());
//        wmsMaterialBindingQueryParam.setCreateTime(wmsMaterialBindingQueryInput.getCreateTime());
//        wmsMaterialBindingQueryParam.setUpdater(wmsMaterialBindingQueryInput.getUpdater());
//        wmsMaterialBindingQueryParam.setUpdateTime(wmsMaterialBindingQueryInput.getUpdateTime());
//        wmsMaterialBindingQueryParam.setDeleteFlag(wmsMaterialBindingQueryInput.getDeleteFlag());
        wmsMaterialBindingQueryParam.setPageIndex(wmsMaterialBindingQueryInput.getPageIndex());
        wmsMaterialBindingQueryParam.setPageSize(wmsMaterialBindingQueryInput.getPageSize());

        wmsMaterialBindingQueryParam.setMaterialSku(wmsMaterialBindingQueryInput.getMaterialSku());
        wmsMaterialBindingQueryParam.setMaterialSkuSaasId(wmsMaterialBindingQueryInput.getMaterialSkuSaasId());
        wmsMaterialBindingQueryParam.setMaterialSkuName(wmsMaterialBindingQueryInput.getMaterialSkuName());

        return wmsMaterialBindingQueryParam;
    }





    public static WmsMaterialBindingCommandParam buildCreateParam(WmsMaterialBindingCommandInput wmsMaterialBindingCommandInput,
                                                                  Map<String, GoodsInfoDTO> goodsInfoDTOMap) {
        if (wmsMaterialBindingCommandInput == null) {
            return null;
        }

        GoodsInfoDTO goodsInfoDTO = goodsInfoDTOMap.get(wmsMaterialBindingCommandInput.getSku());
        if (goodsInfoDTO == null){
            log.error("请求货品不存在 sku:{}", wmsMaterialBindingCommandInput.getSku());
            throw new BizException("请求货品不存在");
        }

        WmsMaterialBindingCommandParam wmsMaterialBindingCommandParam = new WmsMaterialBindingCommandParam();
//        wmsMaterialBindingCommandParam.setId(wmsMaterialBindingCommandInput.getId());
        wmsMaterialBindingCommandParam.setTenantId(wmsMaterialBindingCommandInput.getTenantId());
        wmsMaterialBindingCommandParam.setWarehouseNo(wmsMaterialBindingCommandInput.getWarehouseNo());
        wmsMaterialBindingCommandParam.setSku(wmsMaterialBindingCommandInput.getSku());
        wmsMaterialBindingCommandParam.setSkuSaasId(goodsInfoDTO != null ?
                goodsInfoDTO.getSkuId() : wmsMaterialBindingCommandInput.getSkuSaasId());
        wmsMaterialBindingCommandParam.setSkuName(goodsInfoDTO != null ?
                goodsInfoDTO.getTitle() : "");
        wmsMaterialBindingCommandParam.setSkuRatio(wmsMaterialBindingCommandInput.getSkuRatio());
        wmsMaterialBindingCommandParam.setStatus(MaterialBindingStatusEnum.VALID.getCode());
        wmsMaterialBindingCommandParam.setCreator(wmsMaterialBindingCommandInput.getCreator());
        wmsMaterialBindingCommandParam.setCreateTime(wmsMaterialBindingCommandInput.getCreateTime());
        wmsMaterialBindingCommandParam.setUpdater(wmsMaterialBindingCommandInput.getUpdater());
        wmsMaterialBindingCommandParam.setUpdateTime(wmsMaterialBindingCommandInput.getUpdateTime());
        wmsMaterialBindingCommandParam.setDeleteFlag(DeleteFlagEnum.NO.getValue());
        return wmsMaterialBindingCommandParam;
    }

   public static WmsMaterialBindingVO toWmsMaterialBindingVO(WmsMaterialBindingEntity wmsMaterialBindingEntity,
                                                             Map<Long, List<WmsMaterialBindingDetailEntity>> detailEntityMap,
                                                             Map<Integer, WarehouseStorageCenterEntity> warehouseStorageCenterEntityMap,
                                                             Map<String, GoodsInfoDTO> skuGoodsInfoDTOMap,
                                                             Map<String, GoodsInfoDTO> materialSkuGoodsInfoDTOMap) {
       if (wmsMaterialBindingEntity == null) {
            return null;
       }

       WarehouseStorageCenterEntity warehouseStorageCenter =
               warehouseStorageCenterEntityMap.get(wmsMaterialBindingEntity.getWarehouseNo());

       GoodsInfoDTO skuGoodsInfoDTO = skuGoodsInfoDTOMap.get(wmsMaterialBindingEntity.getSku());

       List<WmsMaterialBindingDetailEntity> detailEntityList = detailEntityMap.get(wmsMaterialBindingEntity.getId());

       WmsMaterialBindingVO wmsMaterialBindingVO = new WmsMaterialBindingVO();
       wmsMaterialBindingVO.setId(wmsMaterialBindingEntity.getId());
       wmsMaterialBindingVO.setTenantId(wmsMaterialBindingEntity.getTenantId());
       wmsMaterialBindingVO.setWarehouseNo(wmsMaterialBindingEntity.getWarehouseNo());
       wmsMaterialBindingVO.setWarehouseName(warehouseStorageCenter != null ?
               warehouseStorageCenter.getWarehouseName() : "" + wmsMaterialBindingEntity.getWarehouseNo());
       wmsMaterialBindingVO.setSku(wmsMaterialBindingEntity.getSku());
       wmsMaterialBindingVO.setSkuSaasId(wmsMaterialBindingEntity.getSkuSaasId());
       wmsMaterialBindingVO.setSkuRatio(wmsMaterialBindingEntity.getSkuRatio());
       wmsMaterialBindingVO.setSkuName(skuGoodsInfoDTO != null ?
               skuGoodsInfoDTO.getTitle(): wmsMaterialBindingEntity.getSkuName());
       wmsMaterialBindingVO.setSkuSpecification(skuGoodsInfoDTO != null ?
               skuGoodsInfoDTO.getSpecification() : "");
       wmsMaterialBindingVO.setSkuSubType(skuGoodsInfoDTO != null ?
               skuGoodsInfoDTO.getSubAgentType() : null);
       wmsMaterialBindingVO.setStatus(wmsMaterialBindingEntity.getStatus());
       wmsMaterialBindingVO.setCreator(wmsMaterialBindingEntity.getCreator());
       wmsMaterialBindingVO.setCreateTime(wmsMaterialBindingEntity.getCreateTime());
       wmsMaterialBindingVO.setUpdater(wmsMaterialBindingEntity.getUpdater());
       wmsMaterialBindingVO.setUpdateTime(wmsMaterialBindingEntity.getUpdateTime());
       wmsMaterialBindingVO.setDeleteFlag(wmsMaterialBindingEntity.getDeleteFlag());

       wmsMaterialBindingVO.setDetailVOList(
               WmsMaterialBindingDetailAssembler.toWmsMaterialBindingDetailVOList(detailEntityList, materialSkuGoodsInfoDTOMap)
       );
       return wmsMaterialBindingVO;
   }

      public static List<WmsMaterialBindingExportVO> convert2Export(List<WmsMaterialBindingVO> list) {
        if (list == null) {
            return Collections.emptyList();
        }
        List<WmsMaterialBindingExportVO> exportList = new ArrayList<>();
        for (WmsMaterialBindingVO vo : list) {
            for (WmsMaterialBindingDetailVO detailVO : vo.getDetailVOList()) {
                WmsMaterialBindingExportVO exportVO = new WmsMaterialBindingExportVO();
                exportVO.setId(vo.getId());
                exportVO.setWarehouseName(vo.getWarehouseName());
                exportVO.setSku(vo.getSku());
                exportVO.setSkuName(vo.getSkuName());
                exportVO.setSkuSpecification(vo.getSkuSpecification());
                exportVO.setSkuRatio(vo.getSkuRatio());
                exportVO.setMaterialSku(detailVO.getMaterialSku());
                exportVO.setMaterialSkuName(detailVO.getMaterialSkuName());
                exportVO.setMaterialSkuSpecification(detailVO.getMaterialSkuSpecification());
                exportVO.setMaterialSkuRatio(detailVO.getMaterialSkuRatio());
                exportVO.setReuseDesc(detailVO.getReuse());
                exportVO.setAutoOutboundDesc(detailVO.getAutoOutbound());
                exportVO.setStatusDesc(MaterialBindingStatusEnum.getDescByCode(vo.getStatus()));

                exportList.add(exportVO);
            }
        }
        return exportList;
    }

}
