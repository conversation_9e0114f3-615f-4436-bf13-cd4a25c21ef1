package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Description: 加工任务导入请求
 * date: 2024/3/4 17:05
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingTaskImportReqDTO implements Serializable {

    /**
     * oss文件链接
     */
    @NotBlank(message = "上传文件 不可为空")
    private String fileAddress;

    /**
     * 仓库编码
     */
    @NotNull(message = "仓库编码 不可为空")
    private Integer warehouseNo;

    /**
     * 加工类型：1：订单加工，2：商品加工，3：商品组装
     */
    @NotNull(message = "加工类型 不可为空")
    private Integer type;

    private Long tenantId;
}
