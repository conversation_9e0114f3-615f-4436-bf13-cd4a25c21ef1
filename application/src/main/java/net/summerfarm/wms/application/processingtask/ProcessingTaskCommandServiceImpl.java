package net.summerfarm.wms.application.processingtask;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.wms.application.processingtask.dto.req.*;
import net.summerfarm.wms.application.processingtask.service.ProcessingTaskCommandService;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskExportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskFinishCheckResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskImportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskOrderImportResDTO;
import net.summerfarm.wms.application.processingtask.enums.OrderImportIsSuccessEnum;
import net.summerfarm.wms.application.processingtask.enums.ProcessingTaskCanFinishEnum;
import net.summerfarm.wms.application.processingtask.factory.ProcessingTaskCreateAggregateFactory;
import net.summerfarm.wms.application.processingtask.factory.ProcessingTaskCreateDTOFactory;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.download.enums.FileDownloadCenterRecordEnum;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingTaskDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskCreateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.SkuSpec;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskProductStatusEnum;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingConfigRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskProductRepository;
import net.summerfarm.wms.domain.processingtask.repository.SkuSpecRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingMaterialConfigQueryRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.manage.web.mapper.executor.ExecutorFactory;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProcessingTaskCommandServiceImpl implements ProcessingTaskCommandService {

    @Autowired
    private ProcessingTaskDomainService processingTaskDomainService;

    @Autowired
    private ProcessingTaskProductRepository processingTaskProductRepository;
    @Autowired
    private ProcessingConfigRepository processingConfigRepository;
    @Autowired
    private ProcessingMaterialConfigQueryRepository materialConfigQueryRepository;
    @Autowired
    private SkuSpecRepository skuSpecRepository;
    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ProcessingTaskExcelDealService processingTaskExcelDealService;


    @Override
    public CommonResult<ProcessingTaskOrderImportResDTO> orderImport(ProcessingTaskOrderImportReqDTO reqDTO) {
        // 解析excel文件成 加工任务模型
        ProcessingTaskOrderImportResDTO resDTO =
                processingTaskExcelDealService.readFile(reqDTO);
        // 全部失败
        if (OrderImportIsSuccessEnum.ALL_FAIL.equalsCode(
                resDTO.getIsSuccess())){
            resDTO.setFailFileAddress(!StringUtils.isEmpty(resDTO.getFailFileAddress()) ? resDTO.getFailFileAddress() : reqDTO.getFileAddress());
            resDTO.setFileMsg("导入订单全部失败，请检查后重新上传");
            return CommonResult.ok(resDTO);
        }

        resDTO.setCreateReqDTOList(resDTO.getCreateReqDTOList().stream()
                .filter(reqDTO1 -> reqDTO1.getProductSkuNeedQuantity() > 0)
                .collect(Collectors.toList()));
        try {
            createTask(resDTO.getCreateReqDTOList());
        } catch (Exception ex){
            log.error("orderImport exception", ex);
            resDTO.setFailFileAddress(reqDTO.getFileAddress());
            resDTO.setFileMsg("导入订单异常，请检查后重新上传");
            resDTO.setIsSuccess(OrderImportIsSuccessEnum.ALL_FAIL.getValue());
            resDTO.setRealFailMsg(ex.getMessage());
            return CommonResult.ok(resDTO);
        }

        return CommonResult.ok(resDTO);
    }

    @Override
    public CommonResult<Long> orderImportLocal(MultipartFile file, Integer warehouseNo, Long tenantId) {
        // 解析excel文件成 加工任务模型
        ProcessingTaskOrderImportResDTO resDTO =
                processingTaskExcelDealService.readFile(file, warehouseNo, tenantId);

        List<ProcessingTaskOrderCreateReqDTO> processingTaskOrderCreateReqDTOS =
                resDTO.getCreateReqDTOList();
        if (CollectionUtils.isEmpty(processingTaskOrderCreateReqDTOS)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "导入文件为空", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        processingTaskOrderCreateReqDTOS = processingTaskOrderCreateReqDTOS.stream()
                .filter(reqDTO1 -> reqDTO1.getProductSkuNeedQuantity() > 0)
                .collect(Collectors.toList());

        return createTask(processingTaskOrderCreateReqDTOS);
    }

    @Override
    public CommonResult<Long> createTask(List<ProcessingTaskOrderCreateReqDTO> processingTaskOrderCreateReqDTOS) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();

        // region 参数查询校验

        // 成品skuList
        List<String> skuCodeList = processingTaskOrderCreateReqDTOS
                .stream()
                .map(ProcessingTaskOrderCreateReqDTO::getProductSkuCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuCodeList)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "导入文件成品sku为空", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 成品加工规则
        Integer warehouseNo = processingTaskOrderCreateReqDTOS.get(0).getWarehouseNo();
        Integer type = processingTaskOrderCreateReqDTOS.get(0).getType();
        Map<String, List<ProcessingConfig>> mapProcessingConfig = processingConfigRepository
                .mapByWarehouseAndProductSkuCodeListAndType(warehouseNo, skuCodeList, type);
        List<Long> configIdList = mapProcessingConfig.values().stream()
                .flatMap(Collection::stream)
                .map(ProcessingConfig::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 加工原料规则
        Map<Long, List<ProcessingMaterialConfigEntity>> materialConfigGroup = materialConfigQueryRepository
                .mapByConfigIdList(configIdList);
        // 加工成品多规格规则
        Map<String, List<SkuSpec>> mapSkuSpec = skuSpecRepository.mapByWarehouseAndProductSkuCodeListAndType(
                warehouseNo, skuCodeList, type, configIdList
        );
        // endregion

        // 构建创建DTO
        ProcessingTaskCreateDTO processingTaskCreateDTO = ProcessingTaskCreateDTOFactory.newInstance(
                processingTaskOrderCreateReqDTOS, mapSkuSpec
        );

        // 构建创建聚合根
        ProcessingTaskCreateAggregate aggregate = ProcessingTaskCreateAggregateFactory.newInstance(
                processingTaskCreateDTO, mapProcessingConfig, materialConfigGroup);

        return CommonResult.ok(processingTaskDomainService.createProcessingTask(aggregate));
    }

    @Override
    public CommonResult<ProcessingTaskFinishCheckResDTO> finishCheck(ProcessingTaskFinishReqDTO reqDTO) {
        // 领料未完结
        List<ProcessingTaskProduct> processingTaskProductList = processingTaskProductRepository
                .queryByProcessingTaskCode(reqDTO.getProcessingTaskCode());
        if (processingTaskProductList.stream()
                .anyMatch(processingTaskProduct -> processingTaskProduct.getMaterialSkuReceiveQuantity() > 0 &&
                        !ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus()))){
            ProcessingTaskFinishCheckResDTO checkResDTO = new ProcessingTaskFinishCheckResDTO();
            checkResDTO.setCanFinish(ProcessingTaskCanFinishEnum.NOT_PROCESSED.getValue());
            checkResDTO.setFinishDesc(ProcessingTaskCanFinishEnum.NOT_PROCESSED.getDescription());
            return CommonResult.ok(checkResDTO);
        }

        Boolean notAllProcessing = processingTaskProductList.stream()
                .anyMatch(processingTaskProduct -> !ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(
                        processingTaskProduct.getStatus())
        );

        ProcessingTaskFinishCheckResDTO checkResDTO = new ProcessingTaskFinishCheckResDTO();
        checkResDTO.setCanFinish(notAllProcessing ? ProcessingTaskCanFinishEnum.PART_PROCESSING.getValue() :
                ProcessingTaskCanFinishEnum.ALL_PROCESSED.getValue());
        checkResDTO.setFinishDesc(notAllProcessing ? ProcessingTaskCanFinishEnum.PART_PROCESSING.getDescription() :
                ProcessingTaskCanFinishEnum.ALL_PROCESSED.getDescription());
        return CommonResult.ok(checkResDTO);
    }

    @Override
    public CommonResult<Long> finish(ProcessingTaskFinishReqDTO reqDTO) {
        // 领料未完结
        List<ProcessingTaskProduct> processingTaskProductList = processingTaskProductRepository
                .queryByProcessingTaskCode(reqDTO.getProcessingTaskCode());
        if (processingTaskProductList.stream()
                .anyMatch(processingTaskProduct -> processingTaskProduct.getMaterialSkuReceiveQuantity() > 0 &&
                        !ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus()))){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "未完成商品加工",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        return processingTaskDomainService.finishTask(reqDTO.getProcessingTaskCode(), processingTaskProductList, reqDTO.getRemark());
    }

    /**
     * 导出加工任务成品明细
     * @param processingTaskCode 加工任务编号
     * @return 响应对象
     */
    @Override
    public CommonResult<ProcessingTaskExportResDTO> exportProcessingTaskAndDetail(String processingTaskCode) {
        if (processingTaskCode == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "加工任务编号为空", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 上传参数设置
        String fileName = "加工任务" + processingTaskCode + "导出"
                + DateUtil.formatDateBasic(LocalDateTime.now()) + ".xls";
        DownloadCenterRecordDTO downloadCenterRecordDTO = new DownloadCenterRecordDTO();
        downloadCenterRecordDTO.setUserId(LoginInfoThreadLocal.getCurrentUserId());
        downloadCenterRecordDTO.setTenantId(LoginInfoThreadLocal.getTenantId());
        downloadCenterRecordDTO.setSource(WmsConstant.XIANMU_TENANT_ID.equals(LoginInfoThreadLocal.getTenantId()) ?
                        DownloadCenterEnum.RequestSource.XIANMU : DownloadCenterEnum.RequestSource.SAAS);
        downloadCenterRecordDTO.setBizType(WmsConstant.XIANMU_TENANT_ID.equals(LoginInfoThreadLocal.getTenantId()) ?
                FileDownloadCenterRecordEnum.PROCESSING_TASK_DOWNLOAD_XM.getType() :
                FileDownloadCenterRecordEnum.PROCESSING_TASK_DOWNLOAD_SAAS.getType());
        downloadCenterRecordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        downloadCenterRecordDTO.setFileName(fileName);

        // 异步写入文件
        Long resId = DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, downloadCenterRecordDTO)
                .asyncWriteWithOssResp(null, (x) -> {
                    ProcessingTaskExportResDTO uploadResult = processingTaskExcelDealService
                            .exportProcessingTaskAndDetail(processingTaskCode);

                    // 2、返回OSS文件地址
                    DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
                    downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
                    downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getOssUploadResult().getObjectOssKey());
                    return downloadCenterOssRespDTO;
                });

        return CommonResult.ok(new ProcessingTaskExportResDTO(null, null , resId));
    }

    @Override
    public CommonResult<ProcessingTaskImportResDTO> taskImport(ProcessingTaskImportReqDTO reqDTO) {

        // 解析excel文件成 加工任务模型
        ProcessingTaskImportResDTO resDTO =
                processingTaskExcelDealService.readFile(reqDTO);
        // 全部失败
        if (OrderImportIsSuccessEnum.ALL_FAIL.equalsCode(
                resDTO.getIsSuccess())){
            resDTO.setFailFileAddress(!StringUtils.isEmpty(resDTO.getFailFileAddress()) ? resDTO.getFailFileAddress() : reqDTO.getFileAddress());
            resDTO.setFileMsg("导入数据全部失败，请检查后重新上传");
            return CommonResult.ok(resDTO);
        }

        resDTO.setCreateReqDTOList(resDTO.getCreateReqDTOList().stream()
                .filter(reqDTO1 -> reqDTO1.getProductSkuNeedQuantity() > 0)
                .collect(Collectors.toList()));
        try {
            this.createTask(resDTO.getCreateReqDTOList());
        } catch (Exception ex){
            log.error("taskImport exception", ex);
            resDTO.setFailFileAddress(reqDTO.getFileAddress());
            resDTO.setFileMsg("导入数据异常，请检查后重新上传");
            resDTO.setIsSuccess(OrderImportIsSuccessEnum.ALL_FAIL.getValue());
            resDTO.setRealFailMsg(ex.getMessage());
            return CommonResult.ok(resDTO);
        }

        return CommonResult.ok(resDTO);
    }

    @Override
    public CommonResult<Long> taskImportLocal(MultipartFile file, Integer type,
                                              Integer warehouseNo, Long tenantId) {
        // 解析excel文件成 加工任务模型
        ProcessingTaskImportResDTO resDTO =
                processingTaskExcelDealService.readFile(file, type, warehouseNo, tenantId);

        List<ProcessingTaskOrderCreateReqDTO> processingTaskOrderCreateReqDTOS =
                resDTO.getCreateReqDTOList();
        if (CollectionUtils.isEmpty(processingTaskOrderCreateReqDTOS)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "导入文件为空", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        processingTaskOrderCreateReqDTOS = processingTaskOrderCreateReqDTOS.stream()
                .filter(reqDTO1 -> reqDTO1.getProductSkuNeedQuantity() > 0)
                .collect(Collectors.toList());

        return createTask(processingTaskOrderCreateReqDTOS);
    }
}
