package net.summerfarm.wms.application.processingtask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.processingtask.converter.ProcessingMaterialConfigResDTOConverter;
import net.summerfarm.wms.application.processingtask.service.ProcessingConfigQueryService;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigProductSkuSpecDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingConfigQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigQueryResDTO;
import net.summerfarm.wms.application.processingtask.converter.ProcessingConfigQueryResDTOConverter;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingConfig;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.SkuSpec;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingConfigTypeEnum;
import net.summerfarm.wms.domain.processingtask.domainobject.param.ProcessingConfigQueryParam;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingConfigRepository;
import net.summerfarm.wms.domain.processingtask.repository.SkuSpecRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingMaterialConfigQueryRepository;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProcessingConfigQueryServiceImpl implements ProcessingConfigQueryService {

    @Autowired
    private ProcessingConfigRepository processingConfigRepository;
    @Autowired
    private ProcessingMaterialConfigQueryRepository materialConfigQueryRepository;
    @Autowired
    private SkuSpecRepository skuSpecRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private WarehouseStorageRepository warehouseStorageRepository;

    /**
     * 分页查询加工规则
     * @param reqDTO query对象
     * @return 分页信息
     */
    @Override
    public PageInfo<ProcessingConfigQueryResDTO> page(ProcessingConfigQueryReqDTO reqDTO) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();

        if (reqDTO.getMaterialSkuSaasId() != null){
            Product product = productRepository.findProductFromGoodsCenter(null,
                    tenantId,
                    reqDTO.getMaterialSkuSaasId());
            if (product == null) {
                PageInfo<ProcessingConfigQueryResDTO> result = new PageInfo<>();
                result.setTotal(0);
                result.setPages(result.getPages());
                result.setList(new ArrayList<>());
                return result;
            }
            reqDTO.setMaterialSkuCode(product.getSku());
        }
        if (reqDTO.getProductSkuSaasId() != null){
            Product product = productRepository.findProductFromGoodsCenter(null,
                    tenantId,
                    reqDTO.getProductSkuSaasId());
            if (product == null) {
                PageInfo<ProcessingConfigQueryResDTO> result = new PageInfo<>();
                result.setTotal(0);
                result.setPages(result.getPages());
                result.setList(new ArrayList<>());
                return result;
            }
            reqDTO.setProductSkuCode(product.getSku());
        }

        ProcessingConfigQueryParam query = new ProcessingConfigQueryParam();
        query.setWarehouseNo(reqDTO.getWarehouseNo());
        query.setType(reqDTO.getType());
        query.setTypeList(Arrays.asList(ProcessingConfigTypeEnum.GOODS_PROCESSING.getValue(),
                ProcessingConfigTypeEnum.ASSEMBLY_PROCESSING.getValue()));
        query.setMaterialSkuCode(reqDTO.getMaterialSkuCode());
        query.setMaterialSkuName(reqDTO.getMaterialSkuName());
        query.setProductSkuCode(reqDTO.getProductSkuCode());
        query.setProductSkuName(reqDTO.getProductSkuName());
        query.setInvalid(reqDTO.getInvalid());

        PageInfo<ProcessingConfig> pageInfo = processingConfigRepository.queryAll(query
                , reqDTO.getPageIndex(), reqDTO.getPageSize());

        List<ProcessingConfigQueryResDTO> list = ProcessingConfigQueryResDTOConverter.convertList(pageInfo.getList());

        // 原料配置
        List<Long> configIdList = list.stream().map(ProcessingConfigQueryResDTO::getId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<ProcessingMaterialConfigEntity>> materialConfigGroup = materialConfigQueryRepository.mapByConfigIdList(configIdList);

        // 成品sku
        List productSkuList= list.stream()
                .map(ProcessingConfigQueryResDTO::getProductSkuCode)
               .distinct()
               .collect(Collectors.toList());
        Map<String, Product> productSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId, productSkuList);
        // 原料sku
        List<String> materialSkuCodeList = materialConfigGroup.values().stream()
                .flatMap(List::stream)
                .map(ProcessingMaterialConfigEntity::getMaterialSkuCode)
                .distinct()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, Product> materialSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId, materialSkuCodeList);

        // 仓库名称
        List<Integer> warehouseNoList = list.stream()
               .map(ProcessingConfigQueryResDTO::getWarehouseNo)
               .distinct()
               .collect(Collectors.toList());
        Map<Integer, WarehouseStorageCenterEntity> warehouseMap = warehouseStorageRepository
                .mapByWarehouseNoListByWarehouseNo(warehouseNoList);

        // 循环塞值
        for (ProcessingConfigQueryResDTO processingConfigQueryResDTO : list) {
            WarehouseStorageCenterEntity warehouseStorageCenter =
                    warehouseMap.get(processingConfigQueryResDTO.getWarehouseNo());

            Product productSpu = productSpuMap.get(processingConfigQueryResDTO.getProductSkuCode());
            processingConfigQueryResDTO.setProductSkuSaasId(productSpu != null ? productSpu.getSaasSkuId() : processingConfigQueryResDTO.getProductSkuSaasId());
            processingConfigQueryResDTO.setProductSkuPdId(productSpu != null ? productSpu.getSkuPdId() : processingConfigQueryResDTO.getProductSkuPdId());
            processingConfigQueryResDTO.setWarehouseName(warehouseStorageCenter != null ?
                    warehouseStorageCenter.getWarehouseName() : processingConfigQueryResDTO.getWarehouseName());
            List<ProcessingMaterialConfigEntity> processingMaterialConfigList =
                    materialConfigGroup.get(processingConfigQueryResDTO.getId());

            processingConfigQueryResDTO.setProcessingMaterialConfigList(
                    ProcessingMaterialConfigResDTOConverter.convertList(processingMaterialConfigList,
                            productSpuMap, materialSpuMap));
        }

        PageInfo<ProcessingConfigQueryResDTO> result = new PageInfo<>();
        result.setTotal(pageInfo.getTotal());
        result.setPages(result.getPages());
        result.setList(list);
        return result;

    }

    /**
     * 加工规格详情接口
     * @param id 加工规格id
     * @return 加工规格res对象
     */
    @Override
    public ProcessingConfigQueryResDTO detail(Long id) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();

        // 查询加工规则配置
        ProcessingConfig processingConfig = processingConfigRepository.queryById(id);
        if (Objects.isNull(processingConfig)) {
            return null;
        }

        WarehouseStorageCenterEntity warehouseStorageCenter =
                warehouseStorageRepository.selectByWarehouseNo(processingConfig.getWarehouseNo());

        // 转换query res对象
        ProcessingConfigQueryResDTO resDTO = ProcessingConfigQueryResDTOConverter.convert(processingConfig);
        resDTO.setWarehouseName(warehouseStorageCenter != null ?
                warehouseStorageCenter.getWarehouseName() : resDTO.getWarehouseName());
        // 查询成品SKU规格
        List<SkuSpec> skuSpecs = skuSpecRepository.queryAllByProcessingConfigId(processingConfig.getId());
        if (!CollectionUtils.isEmpty(skuSpecs)) {
            // 构建成品SKU规格list
            List<ProcessingConfigProductSkuSpecDTO> productSkuSpecList = new ArrayList<>();
            skuSpecs.forEach(skuSpec -> {
                ProcessingConfigProductSkuSpecDTO productSkuSpec = new ProcessingConfigProductSkuSpecDTO();
                productSkuSpec.setProductSkuSpecWeight(skuSpec.getProductSkuSpecWeight())
                        .setProductSkuSpecUnit(skuSpec.getProductSkuSpecUnit());
                productSkuSpecList.add(productSkuSpec);
            });
            resDTO.setProductSkuSpecList(productSkuSpecList);
        }

        // 原料配置
        Map<Long, List<ProcessingMaterialConfigEntity>> materialConfigGroup = materialConfigQueryRepository.mapByConfigIdList(
                Arrays.asList(resDTO.getId())
        );
        List<ProcessingMaterialConfigEntity> processingMaterialConfigList =
                materialConfigGroup.get(resDTO.getId());

        // 成品sku
        List<String> productSkuList= Arrays.asList(resDTO.getProductSkuCode());
        Map<String, Product> productSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId, productSkuList);
        Product productSpu = productSpuMap == null ? null : productSpuMap.get(resDTO.getProductSkuCode());
        resDTO.setProductSkuSaasId(productSpu != null ? productSpu.getSaasSkuId() : resDTO.getProductSkuSaasId());
        resDTO.setProductSkuPdId(productSpu != null ? productSpu.getSkuPdId() : resDTO.getProductSkuPdId());

        // 原料sku
        List<String> materialSkuCodeList = materialConfigGroup.values().stream()
                .flatMap(List::stream)
                .map(ProcessingMaterialConfigEntity::getMaterialSkuCode)
                .distinct()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, Product> materialSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId, materialSkuCodeList);
        resDTO.setProcessingMaterialConfigList(
                ProcessingMaterialConfigResDTOConverter.convertList(processingMaterialConfigList,
                        productSpuMap, materialSpuMap));

        return resDTO;
    }
}
