package net.summerfarm.wms.application.materialManage.controller.input.command;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Data
public class WmsMaterialBindingCommandInput implements Serializable{

	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * sku编码
	 */
	private String sku;

	/**
	 * skuSaasId
	 */
	private Long skuSaasId;

	/**
	 * sku比例
	 */
	private BigDecimal skuRatio;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 绑定明细
	 */
	private List<WmsMaterialBindingDetailCommandInput> detailCommandInputList;

}