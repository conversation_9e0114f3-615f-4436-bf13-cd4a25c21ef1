package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductSpecDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProductRecord;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 加工任务成品明细成品规格转换类
 * <AUTHOR>
 * @date 2023/02/15
 */
@Mapper
public interface ProcessingTaskProductSpecDTOConverter {

    ProcessingTaskProductSpecDTOConverter INSTANCE = Mappers.getMapper(ProcessingTaskProductSpecDTOConverter.class);

    /**
     * ProcessingTaskProductRecord -> ProcessingTaskProductSpecDTO
     * @param processingTaskProductRecord ProcessingTaskProductSpecDTO
     * @return ProcessingTaskProductSpecDTO
     */
    @Mapping(source = "id", target = "processingTaskProductSpecId")
    ProcessingTaskProductSpecDTO convert(ProcessingTaskProductRecord processingTaskProductRecord);

    /**
     * ProcessingTaskProductRecordList -> ProcessingTaskProductSpecDTOList
     * @param processingTaskProductRecordList ProcessingTaskProductRecordList
     * @return ProcessingTaskProductSpecDTOList
     */
    List<ProcessingTaskProductSpecDTO> convertList(List<ProcessingTaskProductRecord> processingTaskProductRecordList);
}
