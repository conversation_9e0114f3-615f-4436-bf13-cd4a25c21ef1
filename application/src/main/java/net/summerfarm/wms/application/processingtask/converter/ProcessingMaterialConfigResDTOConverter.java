package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingMaterialConfigResDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingMaterialConfigEntity;
import net.summerfarm.wms.domain.products.domainobject.Product;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface ProcessingMaterialConfigResDTOConverter {


    public static ProcessingMaterialConfigResDTO convert(ProcessingMaterialConfigEntity entity,
                                                         Map<String, Product> productSpuMap,
                                                         Map<String, Product> materialSpuMap) {
        if ( entity == null ) {
            return null;
        }

        ProcessingMaterialConfigResDTO processingMaterialConfigResDTO = new ProcessingMaterialConfigResDTO();

        processingMaterialConfigResDTO.setId( entity.getId() );
        processingMaterialConfigResDTO.setProcessingConfigId( entity.getProcessingConfigId() );
        processingMaterialConfigResDTO.setMaterialSkuCode( entity.getMaterialSkuCode() );
        processingMaterialConfigResDTO.setMaterialSkuName( entity.getMaterialSkuName() );
        processingMaterialConfigResDTO.setMaterialSkuWeight( entity.getMaterialSkuWeight() );
        processingMaterialConfigResDTO.setMaterialSkuUnit( entity.getMaterialSkuUnit() );
        processingMaterialConfigResDTO.setMaterialSkuUnitDesc( entity.getMaterialSkuUnitDesc() );
        processingMaterialConfigResDTO.setMaterialSkuRatioNum( entity.getMaterialSkuRatioNum() );
        processingMaterialConfigResDTO.setProductSkuCode( entity.getProductSkuCode() );
        processingMaterialConfigResDTO.setProductSkuName( entity.getProductSkuName() );
        processingMaterialConfigResDTO.setProductSkuWeight( entity.getProductSkuWeight() );
        processingMaterialConfigResDTO.setProductSkuUnit( entity.getProductSkuUnit() );
        processingMaterialConfigResDTO.setProductSkuUnitDesc( entity.getProductSkuUnitDesc() );

        Product productSpu = productSpuMap == null ? null : productSpuMap.get(entity.getProductSkuCode());
        Product materialSpu = materialSpuMap == null ? null : materialSpuMap.get(entity.getMaterialSkuCode());
        processingMaterialConfigResDTO.setProductSkuSaasId(productSpu != null ?
                productSpu.getSaasSkuId() : processingMaterialConfigResDTO.getProductSkuSaasId());
        processingMaterialConfigResDTO.setMaterialSkuSaasId(materialSpu != null ?
                materialSpu.getSaasSkuId() : processingMaterialConfigResDTO.getMaterialSkuSaasId());

        processingMaterialConfigResDTO.setProductSkuPdId(productSpu != null ? productSpu.getSkuPdId() :
                processingMaterialConfigResDTO.getProductSkuPdId());
        processingMaterialConfigResDTO.setMaterialSkuPdId(materialSpu != null ? materialSpu.getSkuPdId() :
                processingMaterialConfigResDTO.getMaterialSkuPdId());

        return processingMaterialConfigResDTO;
    }

    public static List<ProcessingMaterialConfigResDTO> convertList(List<ProcessingMaterialConfigEntity> entityList,
                                                                   Map<String, Product> productSpuMap,
                                                                   Map<String, Product> materialSpuMap) {
        if ( entityList == null ) {
            return null;
        }

        List<ProcessingMaterialConfigResDTO> list = new ArrayList<>( entityList.size() );
        for ( ProcessingMaterialConfigEntity processingMaterialConfigEntity : entityList ) {
            list.add( convert( processingMaterialConfigEntity, productSpuMap, materialSpuMap ) );
        }

        return list;
    }
}
