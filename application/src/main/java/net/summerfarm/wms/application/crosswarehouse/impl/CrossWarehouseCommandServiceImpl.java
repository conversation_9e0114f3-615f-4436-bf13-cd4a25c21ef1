package net.summerfarm.wms.application.crosswarehouse.impl;

import com.aliyun.odps.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.api.h5.crosswarehouse.CrossWarehouseCommandService;
import net.summerfarm.wms.application.mission.bizobject.CrossWarehouseMissionCreateDTO;
import net.summerfarm.wms.application.mission.bizobject.CrossWarehouseMissionDetailDTO;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.admin.AdminUtil;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.config.repository.WarehouseConfigRepository;
import net.summerfarm.wms.domain.instore.domainobject.InBoundOrder;
import net.summerfarm.wms.domain.instore.domainobject.InBoundOrderDetail;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.domainobject.query.InBoundTaskId;
import net.summerfarm.wms.domain.instore.repository.InBoundOrderQueryRepository;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.domain.mission.domainobject.ExecuteUnit;
import net.summerfarm.wms.domain.mission.domainobject.Mission;
import net.summerfarm.wms.domain.mission.domainobject.MissionDetail;
import net.summerfarm.wms.domain.mission.repository.MissionRepository;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 越库任务
 *
 * @author: dongcheng
 * @date: 2023/11/2
 */
@Service
@Slf4j
public class CrossWarehouseCommandServiceImpl implements CrossWarehouseCommandService {

    @Resource
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;
    @Resource
    private WarehouseConfigRepository warehouseConfigRepository;
    @Resource
    private InBoundOrderQueryRepository inBoundOrderQueryRepository;
    @Resource
    private MissionRepository missionRepository;
    @Resource
    private MqProducer mqProducer;


    /**
     * 生成越库投线任务
     *
     * @param stockTaskStorageId 入库任务id
     * @param containerNo        容器号
     * @param isPda              是否为pda挖成
     * @param state              入库任务状态
     */
    @Override
    public void triggerGenerateMission(Long stockTaskStorageId, String containerNo, Boolean isPda, Integer state) {
        log.info("入库任务触发生成投线任务:stockTaskStorageId:" + stockTaskStorageId + "  containerNo:" + containerNo
                + " isPda:" + isPda);
        if (stockTaskStorageId == null) {
            log.info("生成投线任务的入库任务id不能为空");
            return;
        }
        // 查询是入库任务
        StockTaskStorage stockTaskStorage = stockTaskStorageQueryRepository.queryStockTaskStorageById(stockTaskStorageId);
        if (stockTaskStorage == null) {
            log.info("入库任务不存在:stockTaskStorageId:" + stockTaskStorageId);
            return;
        }
        // 不是越库入库任务直接跳过
        if (OperationType.CROSS_WAREHOUSE_IN.getId() != stockTaskStorage.getType()) {
            return;
        }
        Boolean open = warehouseConfigRepository.openCabinetManagement(stockTaskStorage.getInWarehouseNo());
        if (!open) {
            // 非精细化仓不生成
            return;
        }
        // 生成的获取入库单列表
        InBoundTaskId inBoundTaskId = InBoundTaskId.builder().taskId(stockTaskStorage.getId()).build();
        List<InBoundOrder> inBoundOrders = inBoundOrderQueryRepository.findInBoundOrderByTaskId(inBoundTaskId);

        // 获取所有的投线任务
        List<Mission> missions = missionRepository.findMissionByPMissionNo(stockTaskStorage.getId().toString(), OperationType.CROSS_WAREHOUSE_IN.getId());
        // 创建投线任务 组装投线任务生成事件
        createCrossWarehouseMission(inBoundOrders, stockTaskStorage, missions, containerNo, isPda, state);
    }

    /**
     * 生成投线任务
     *
     * @param inBoundOrders    入库单列表
     * @param stockTaskStorage 入库任务信息
     * @param missions         已经生成过的任务信息
     * @param aimContainerNo   目标容器
     * @param isPda            是否为pda挖成
     * @param autoShelving     是否自动上架
     */
    private void createCrossWarehouseMission(List<InBoundOrder> inBoundOrders,
                                             StockTaskStorage stockTaskStorage,
                                             List<Mission> missions,
                                             String aimContainerNo,
                                             Boolean isPda,
                                             Integer state) {
        if (CollectionUtils.isEmpty(missions)) {
            // 第一次生成投线任务的情况
            firstCreateCrossWarehouseMission(inBoundOrders, stockTaskStorage, aimContainerNo, isPda, state);
            return;
        }
        // 按照任务号查询任务明细信息
        List<String> missionNoList = missions.stream().map(Mission::getMissionNo).collect(Collectors.toList());
        List<MissionDetail> missionDetailList = missionRepository.findDetailByMissionNoListAndSkuList(missionNoList, Lists.newArrayList());
        Map<String, List<MissionDetail>> missionDetailListMap = missionDetailList.stream().collect(Collectors.groupingBy(MissionDetail::getMissionNo));
        // 获取投线任务的所有容器信息
        List<ExecuteUnit> executeUnits = missionRepository.findExecuteUnits(missionNoList);
        Map<String, List<ExecuteUnit>> executeUnitMap = executeUnits.stream()
                .collect(Collectors.groupingBy(ExecuteUnit::getCarrierCode));
        // 入库单按照收货容器分组
        Map<String, List<InBoundOrder>> inboundOrderListMap = inBoundOrders.stream().collect(Collectors.groupingBy(InBoundOrder::getContainerNo));
        List<String> containerNoList = inBoundOrders.stream().map(InBoundOrder::getContainerNo).sorted().collect(Collectors.toList());
        // 最后一个容器
        String lastContainerNo = containerNoList.stream().reduce((a, b) -> b).orElseThrow(() -> new BizException("容器转换异常"));
        // 遍历容器合并收货单明细信息 按照容器生成越库投线任务
        for (String containerNo : containerNoList) {
            // 指定容器不为空 且当前容器不是目标容器 跳过
            if (!StringUtils.isBlank(aimContainerNo) && !aimContainerNo.equals(containerNo)) {
                continue;
            }

            // 获取容器的任务列表
            List<ExecuteUnit> executeUnitList = executeUnitMap.get(containerNo);
            // 容器代表的任务列表
            List<MissionDetail> tmpDetailList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(executeUnitList)) {
                List<String> tmpMissionNoList = executeUnitList.stream().map(ExecuteUnit::getMissionNo).distinct().collect(Collectors.toList());
                // 获取任务对应的明细列表
                for (String missionNo : tmpMissionNoList) {
                    List<MissionDetail> detailList = missionDetailListMap.get(missionNo);
                    if (!CollectionUtils.isEmpty(detailList)) {
                        tmpDetailList.addAll(detailList);
                    }
                }
            }
            // 对任务明细按照 sku效期分组
            Map<String, List<MissionDetail>> missionDetailListsMap = tmpDetailList.stream().collect(Collectors.groupingBy(MissionDetail::pcDuplicateKey));

            List<InBoundOrder> boundOrderList = inboundOrderListMap.get(containerNo);
            // 获取入库单的列表
            List<Long> boundOrderIdList = boundOrderList.stream().map(InBoundOrder::getId).collect(Collectors.toList());
            List<InBoundOrderDetail> inBoundOrderDetailList = inBoundOrderQueryRepository.findInBoundOrderDetails(boundOrderIdList);
            // 按照 sku 效期分组
            Map<String, List<InBoundOrderDetail>> inBoundOrderDetailListMap = inBoundOrderDetailList.stream().collect(Collectors.groupingBy(InBoundOrderDetail::dpKeyBySku));
            Set<String> dpKeySet = inBoundOrderDetailList.stream().map(InBoundOrderDetail::dpKeyBySku).collect(Collectors.toSet());
            // 分组完成之后 合并明细明细数量
            // 生成出库任务明细信息
            List<CrossWarehouseMissionDetailDTO> crossWarehouseDetailList = Lists.newArrayList();
            for (String dpKey : dpKeySet) {
                List<InBoundOrderDetail> detailList = inBoundOrderDetailListMap.get(dpKey);
                InBoundOrderDetail boundOrderDetail = detailList.get(0);
                // 入库的总数量
                int totalStockNum = detailList.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                // 已经生成任务的总数量
                List<MissionDetail> details = missionDetailListsMap.getOrDefault(dpKey, Lists.newArrayList());
                int totalStockGenerateNum = details.stream().mapToInt(MissionDetail::getShouldInQuantity).sum();
                if (totalStockNum <= totalStockGenerateNum) {
                    // 当入库数量等于已经生成数量的时候，不用生成新的明细
                    log.info("越库投线任务分配生成入库数量等于已经生成数量，不用生成新的明细");
                    continue;
                }
                // 剩余可以生成的数量
                int shouldQuantity = totalStockNum - totalStockGenerateNum;
                // 生成投线任务消息
                CrossWarehouseMissionDetailDTO crossWarehouseDetail = CrossWarehouseMissionDetailDTO.builder()
                        .batchNo(boundOrderDetail.getPurchaseNo())
                        .shelfLife(DateUtil.toLocalDate(boundOrderDetail.getShelfLife()))
                        .produceTime(DateUtil.toLocalDate(boundOrderDetail.getProduceAt()))
                        .sku(boundOrderDetail.getSku())
                        .shouldInQuantity(shouldQuantity)
                        .build();
                crossWarehouseDetailList.add(crossWarehouseDetail);
            }
            if (CollectionUtils.isEmpty(crossWarehouseDetailList)) {
                // 没有明细需要创建
                continue;
            }

            CrossWarehouseMissionCreateDTO dto = CrossWarehouseMissionCreateDTO.builder()
                    .containerNo(containerNo)
                    .pMissionType(OperationType.CROSS_WAREHOUSE_IN.getId())
                    .sourceOrderNo(stockTaskStorage.getId().toString())
                    .sourceType(stockTaskStorage.getType())
                    .details(crossWarehouseDetailList)
                    .tenantId(stockTaskStorage.getTenantId())
                    .operatorId(AdminUtil.getCurrentLoginAdminIdV2().toString())
                    .operatorName(AdminUtil.getCurrentLoginAdminNameV2())
                    .warehouseNo(Long.valueOf(stockTaskStorage.getInWarehouseNo()))
                    .pMissionNo(stockTaskStorage.getId().toString())
                    .psoNo(stockTaskStorage.getPsoNo())
                    .isPda(isPda)
                    .state(containerNo.equals(lastContainerNo) ? state : null) // 遍历到最后一个容器附带入库任务状态
                    .build();
            mqProducer.send(Global.STOCK_TASK, "tag_wms_cross_warehouse_mission_create", dto);
        }
    }

    /**
     * 第一次生成投线任务
     *
     * @param inBoundOrders    入库单列表
     * @param stockTaskStorage 入库任务信息
     * @param aimContainerNo   目标容器号
     * @param isPda            是否为pda挖成
     */
    private void firstCreateCrossWarehouseMission(List<InBoundOrder> inBoundOrders,
                                                  StockTaskStorage stockTaskStorage,
                                                  String aimContainerNo,
                                                  Boolean isPda,
                                                  Integer state) {
        log.info("第一次生成投线任务，入库任务【{}】", stockTaskStorage.getId());
        // 还未生成投线任务
        // 入库单按照收货容器分组
        Map<String, List<InBoundOrder>> inboundOrderListMap = inBoundOrders.stream().collect(Collectors.groupingBy(InBoundOrder::getContainerNo));
        List<String> containerNoList = inBoundOrders.stream().map(InBoundOrder::getContainerNo).sorted().collect(Collectors.toList());
        // 最后一个容器
        String lastContainerNo = containerNoList.stream().reduce((a, b) -> b).orElseThrow(() -> new BizException("容器转换异常"));
        // 遍历容器合并收货单明细信息 按照容器生成越库投线任务
        for (String containerNo : containerNoList) {
            // 指定容器不为空 且当前容器不是目标容器 跳过
            if (!StringUtils.isBlank(aimContainerNo) && !aimContainerNo.equals(containerNo)) {
                continue;
            }

            List<InBoundOrder> boundOrderList = inboundOrderListMap.get(containerNo);
            // 获取入库单的列表
            List<Long> boundOrderIdList = boundOrderList.stream().map(InBoundOrder::getId).collect(Collectors.toList());
            List<InBoundOrderDetail> inBoundOrderDetailList = inBoundOrderQueryRepository.findInBoundOrderDetails(boundOrderIdList);
            // 按照 sku 效期分组
            Map<String, List<InBoundOrderDetail>> inBoundOrderDetailListMap = inBoundOrderDetailList.stream().collect(Collectors.groupingBy(InBoundOrderDetail::dpKey));
            Set<String> dpKeySet = inBoundOrderDetailList.stream().map(InBoundOrderDetail::dpKey).collect(Collectors.toSet());
            // 分组完成之后 合并明细明细数量
            // 生成出库任务明细信息
            List<CrossWarehouseMissionDetailDTO> crossWarehouseDetailList = Lists.newArrayList();
            for (String dpKey : dpKeySet) {
                List<InBoundOrderDetail> detailList = inBoundOrderDetailListMap.get(dpKey);
                InBoundOrderDetail boundOrderDetail = detailList.get(0);
                // 入库的总数量
                int totalStockNum = detailList.stream().mapToInt(InBoundOrderDetail::getStockNum).sum();
                // 生成投线任务消息
                CrossWarehouseMissionDetailDTO crossWarehouseDetail = CrossWarehouseMissionDetailDTO.builder()
                        .batchNo(boundOrderDetail.getPurchaseNo())
                        .shelfLife(DateUtil.toLocalDate(boundOrderDetail.getShelfLife()))
                        .produceTime(DateUtil.toLocalDate(boundOrderDetail.getProduceAt()))
                        .sku(boundOrderDetail.getSku())
                        .shouldInQuantity(totalStockNum)
                        .build();
                crossWarehouseDetailList.add(crossWarehouseDetail);
            }
            CrossWarehouseMissionCreateDTO createDTO = CrossWarehouseMissionCreateDTO.builder()
                    .containerNo(containerNo)
                    .pMissionType(OperationType.CROSS_WAREHOUSE_IN.getId())
                    .sourceOrderNo(stockTaskStorage.getId().toString())
                    .sourceType(stockTaskStorage.getType())
                    .details(crossWarehouseDetailList)
                    .tenantId(stockTaskStorage.getTenantId())
                    .operatorId(AdminUtil.getCurrentLoginAdminIdV2().toString())
                    .operatorName(AdminUtil.getCurrentLoginAdminNameV2())
                    .warehouseNo(Long.valueOf(stockTaskStorage.getInWarehouseNo()))
                    .pMissionNo(stockTaskStorage.getId().toString())
                    .psoNo(stockTaskStorage.getPsoNo())
                    .isPda(isPda)
                    .state(containerNo.equals(lastContainerNo) ? state : null)
                    .build();
            mqProducer.send(Global.STOCK_TASK, "tag_wms_cross_warehouse_mission_create", createDTO);
        }
    }

}
