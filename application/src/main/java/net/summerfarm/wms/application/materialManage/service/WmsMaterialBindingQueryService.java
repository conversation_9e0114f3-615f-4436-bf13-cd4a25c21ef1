package net.summerfarm.wms.application.materialManage.service;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialBindingQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 *
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
public interface WmsMaterialBindingQueryService {

    /**
     * @return WmsMaterialBindingEntity
     * @description: 新增
     **/
    CommonResult<PageInfo<WmsMaterialBindingVO>> getPage(WmsMaterialBindingQueryInput input);


    List<WmsMaterialBindingVO> getList(WmsMaterialBindingQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    CommonResult<WmsMaterialBindingVO> getDetail(Long tenantId, Long id);

}