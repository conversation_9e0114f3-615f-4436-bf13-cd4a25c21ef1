package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 加工规则查询响应对象
 * <AUTHOR>
 * @date 2023/02/14
 */
@Data
public class ProcessingTaskQueryResDTO implements Serializable {

    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 库存仓编号
     */
    private String warehouseName;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    private Integer type;
    /**
     * 加工类型描述
     */
    private String typeDesc;
    /**
     * 任务状态：0未加工、1已加工、2部分加工
     */
    private Integer status;
    /**
     * 任务状态描述
     */
    private String statusDesc;
    /**
     * 完成时间
     */
    private String finishTime;
    /**
     * 完成备注
     */
    private String finishRemark;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 是否删除标识，0：否，1：是
     */
    private Integer deleteFlag;
}
