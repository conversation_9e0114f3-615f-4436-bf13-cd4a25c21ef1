package net.summerfarm.wms.application.materialManage.controller;

import com.alibaba.schedulerx.shade.scala.annotation.meta.param;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import net.summerfarm.wms.api.h5.filedown.resp.FileDownResp;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialBindingQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialBindingCommandService;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialBindingExportService;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialBindingQueryService;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;

import javax.validation.Valid;
import java.time.LocalDateTime;


/**
 * @Title 物料绑定
 * @Description 物料绑定功能模块
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 */
@RestController
@RequestMapping(value="/wmsMaterialBinding")
public class WmsMaterialBindingController{

	@Autowired
	private WmsMaterialBindingCommandService wmsMaterialBindingCommandService;
	@Autowired
	private WmsMaterialBindingQueryService wmsMaterialBindingQueryService;
	@Autowired
	private WmsMaterialBindingExportService wmsMaterialBindingExportService;

	/**
	 * 物料绑定列表
	 * 对于`@RequestBody`将使用`application/json`
	 * @return WmsMaterialBindingVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<WmsMaterialBindingVO>> getPage(@RequestBody WmsMaterialBindingQueryInput input){
		if (input.getPageIndex() == null || input.getPageSize() == null){
			return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请求参数为空");
		}

		input.setTenantId(LoginInfoThreadLocal.getTenantId());
		return wmsMaterialBindingQueryService.getPage(input);
	}

	/**
	* @Description 获取详情
	* @return WmsMaterialBindingVO
	*/
	@PostMapping(value = "/query/detail/{id}")
	public CommonResult<WmsMaterialBindingVO> detail(@PathVariable Long id){

		return wmsMaterialBindingQueryService.getDetail(LoginInfoThreadLocal.getTenantId(), id);
	}


	/**
	 * 新增物料绑定
	 * 对于`@RequestBody`将使用`application/json`
	 * @return WmsMaterialBindingVO
	 */
	@PostMapping(value = "/upsert/insert")
	public CommonResult<WmsMaterialBindingVO> insert(@RequestBody WmsMaterialBindingCommandInput input) {
		input.setTenantId(LoginInfoThreadLocal.getTenantId());
		input.setCreator(LoginInfoThreadLocal.getCurrentUserName());
		input.setCreateTime(LocalDateTime.now());
		input.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
		input.setUpdateTime(LocalDateTime.now());
		return wmsMaterialBindingCommandService.insert(input);
	}

	/**
	* @Description 删除
	* @return
	*/
	@PostMapping(value = "/upsert/delete/{id}")
	public CommonResult<Integer> delete(@PathVariable @Valid Long id){
		return CommonResult.ok(wmsMaterialBindingCommandService.delete(id));
	}

	/**
	 * 物料绑定导出
	 * @return WmsMaterialBindingVO
	 */
	@PostMapping(value="/export")
	public CommonResult<FileDownResp> export(@RequestBody WmsMaterialBindingQueryInput input){
		input.setTenantId(LoginInfoThreadLocal.getTenantId());

		return wmsMaterialBindingExportService.export(input);
	}

}

