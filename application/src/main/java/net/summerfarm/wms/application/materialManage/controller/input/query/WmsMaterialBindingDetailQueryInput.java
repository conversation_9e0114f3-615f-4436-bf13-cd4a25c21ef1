package net.summerfarm.wms.application.materialManage.controller.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Data
public class WmsMaterialBindingDetailQueryInput extends BasePageInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * sku编码
	 */
	private String sku;

	/**
	 * skuSaasId
	 */
	private Long skuSaasId;

	/**
	 * 物料sku编码
	 */
	private String materialSku;

	/**
	 * 物料skuSaasId
	 */
	private Long materialSkuSaasId;

	/**
	 * 物料sku名称
	 */
	private String materialSkuName;

	/**
	 * 物料sku比例
	 */
	private BigDecimal materialSkuRatio;

	/**
	 * 重复利用， 否，是
	 */
	private String reuse;

	/**
	 * 自动出库，否，是
	 */
	private String autoOutbound;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 是否删除标识，0：否，1：是
	 */
	private Integer deleteFlag;

	/**
	 * 绑定id
	 */
	private Long materialBingingId;


}