package net.summerfarm.wms.application.processingtask.service;

import net.summerfarm.wms.application.processingtask.dto.req.ProcessingConfigUpsetReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigUpsetResDTO;
import net.xianmu.common.result.CommonResult;

public interface ProcessingConfigCommandService {

    /**
     * 新增/更改
     *
     * @param reqDTO 请求对象
     * @return 操作结果
     */
    CommonResult<ProcessingConfigUpsetResDTO> upsert(ProcessingConfigUpsetReqDTO reqDTO);

    /**
     * 作废
     * @param processingConfigId 加工规则id
     * @return 操作结果
     */
    Long invalid(Long processingConfigId);
}
