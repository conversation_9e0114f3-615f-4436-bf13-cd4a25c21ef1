package net.summerfarm.wms.application.processingtask.service;

import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductFinishReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductPrintReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductSubmitReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductFinishCheckResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductPrintResDTO;
import net.xianmu.common.result.CommonResult;

import java.util.List;

public interface ProcessingTaskProductCommandService {

    /**
     * 成品提交
     *
     * @param reqDTO
     */
    CommonResult<Long> productSubmit(ProcessingTaskProductSubmitReqDTO reqDTO);

    /**
     * 成品完成
     *
     * @param reqDTO
     */
    CommonResult<Long>  productFinish(ProcessingTaskProductFinishReqDTO reqDTO);

    /**
     * 成品完成检查
     * @param reqDTO
     * @return
     */
    CommonResult<ProcessingTaskProductFinishCheckResDTO> productFinishCheck(ProcessingTaskProductFinishReqDTO reqDTO);

    /**
     * 加工任务成品明细打印
     * @param reqDTO 请求对象
     * @return 响应对象
     */
    CommonResult<List<ProcessingTaskProductPrintResDTO>> print(ProcessingTaskProductPrintReqDTO reqDTO);

}
