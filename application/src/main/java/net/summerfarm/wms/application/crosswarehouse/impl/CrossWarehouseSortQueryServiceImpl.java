package net.summerfarm.wms.application.crosswarehouse.impl;

import net.summerfarm.wms.api.h5.crosswarehouse.CrossWarehouseSortQueryService;
import net.summerfarm.wms.api.h5.crosswarehouse.dto.CrossWarehouseSortSimpleDTO;
import net.summerfarm.wms.domain.crosswarehouse.domainobject.CrossWarehouseSortInfo;
import net.summerfarm.wms.domain.crosswarehouse.repository.CrossWarehouseSortInfoRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 越库分拣明细明细查询
 *
 * @author: xdc
 * @date: 2024/3/11
 **/
@Service
public class CrossWarehouseSortQueryServiceImpl implements CrossWarehouseSortQueryService {

    @Resource
    private CrossWarehouseSortInfoRepository crossWarehouseSortInfoRepository;

    /**
     * 查询越库分拣明细简单信息
     *
     * @param psoNoList       ofc的批次号
     * @param saleOrderNoList 销售单号
     * @param skuCodeList     sku编码信息
     * @param fulfillmentNoList 履约单号
     */
    @Override
    public List<CrossWarehouseSortSimpleDTO> findSimpleByUnique(List<String> psoNoList, List<String> saleOrderNoList,
                                                                List<String> skuCodeList, List<String> fulfillmentNoList) {
        List<CrossWarehouseSortInfo> warehouseSortInfoList = crossWarehouseSortInfoRepository.findSimpleByUnique(psoNoList, saleOrderNoList,
                skuCodeList, fulfillmentNoList);
        if (CollectionUtils.isEmpty(warehouseSortInfoList)) {
            return Lists.newArrayList();
        }
        return warehouseSortInfoList.stream().map(it -> {
            return CrossWarehouseSortSimpleDTO.builder()
                    .psoNo(it.getPsoNo())
                    .saleOrderNo(it.getSaleOrderNo())
                    .sku(it.getSku())
                    .fulfillmentNo(it.getFulfillmentNo())
                    .build();
        }).collect(Collectors.toList());
    }

}
