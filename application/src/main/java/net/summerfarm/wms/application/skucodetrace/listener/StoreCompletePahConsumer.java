package net.summerfarm.wms.application.skucodetrace.listener;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.acl.mq.msg.StoreCompletePathMessage;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.domain.skucodetrace.service.SkuBatchCodeTraceCommandDomainService;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * Description: 城配仓完成排线通知<br/>
 * date: 2024/8/8 17:17<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
@MqListener(topic = WmsConstant.TMS_PATH,
        tag = WmsConstant.TAG_DELIVERY_STORE_COMPLETE_PATH,
        consumerGroup = Global.MQ_GROUP,
        maxReconsumeTimes = 5)
public class StoreCompletePahConsumer extends AbstractMqListener<StoreCompletePathMessage> {

    @Resource
    private SkuBatchCodeTraceCommandDomainService skuBatchCodeTraceCommandDomainService;

    @Override
    public void process(StoreCompletePathMessage storeCompletePathMessage) {
        Integer storeNo = storeCompletePathMessage.getStoreNo();
        LocalDate deliveryTime = storeCompletePathMessage.getDeliveryTime();
        if(storeNo == null){
            throw new BizException("城配仓编号不能为空");
        }
        if(deliveryTime == null){
            throw new BizException("配送日期不能为空");
        }
        //更新排线信息
        skuBatchCodeTraceCommandDomainService.updateDeliveryPathInfo(deliveryTime,storeNo);
    }
}
