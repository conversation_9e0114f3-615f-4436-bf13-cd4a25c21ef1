package net.summerfarm.wms.application.processingtask.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialWasteLossWeightReqDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.MaterialWasteLossWeightUpdateAggregate;

@Slf4j
public class MaterialWasteLossWeightUpdateAggregateFactory {


    public static MaterialWasteLossWeightUpdateAggregate newInstance(ProcessingTaskMaterialWasteLossWeightReqDTO reqDTO) {
        MaterialWasteLossWeightUpdateAggregate aggregate = new MaterialWasteLossWeightUpdateAggregate();

        aggregate.setMaterialReceiveId(reqDTO.getMaterialReceiveId());
        aggregate.setProcessingTaskProductId(reqDTO.getProcessingTaskProductId());
        aggregate.setProcessingTaskMaterialId(reqDTO.getProcessingTaskMaterialId());
        aggregate.setWasteLossWeight(reqDTO.getWasteLossWeight());


        return aggregate;
    }
}
