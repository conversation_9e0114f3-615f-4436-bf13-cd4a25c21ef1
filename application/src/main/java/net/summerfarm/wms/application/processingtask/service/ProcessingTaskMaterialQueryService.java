package net.summerfarm.wms.application.processingtask.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskMaterialResDTO;
import net.xianmu.common.result.CommonResult;

public interface ProcessingTaskMaterialQueryService {

    /**
     * 物料查询
     *
     * @param reqDTO
     * @return
     */
    CommonResult<PageInfo<ProcessingTaskMaterialResDTO>> materialQueryPage(ProcessingTaskMaterialQueryReqDTO reqDTO);
}
