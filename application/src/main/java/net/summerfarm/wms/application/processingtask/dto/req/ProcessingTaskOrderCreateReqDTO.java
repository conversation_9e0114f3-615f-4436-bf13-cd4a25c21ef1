package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProcessingTaskOrderCreateReqDTO implements Serializable {

    /**
     * 库存仓编号
     */
    @NotNull(message = "仓库号不能为空")
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    private Integer type;

    /**
     * 外部订单号
     */
    private String sourceId;

    /**
     * 成品sku编码
     */
    private String productSkuCode;

    /**
     * 成品sku需要数量
     */
    private Integer productSkuNeedQuantity;

    /**
     * 成品sku编码saasId
     */
    private Long productSkuSaasId;

    /**
     * 成品sku编码input
     */
    private String productSkuInput;
}
