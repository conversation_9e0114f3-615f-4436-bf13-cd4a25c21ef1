package net.summerfarm.wms.application.processingtask;

import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskOrderImportReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskImportReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskExportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskImportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskOrderImportResDTO;
import org.springframework.web.multipart.MultipartFile;

public interface ProcessingTaskExcelDealService {

    /**
     * 读取文件
     *
     * @param reqDTO
     * @return
     */
    ProcessingTaskOrderImportResDTO readFile(ProcessingTaskOrderImportReqDTO reqDTO);


    /**
     * 读取文件
     *
     * @param file
     * @param warehouseNo
     * @param tenantId
     * @return
     */
    ProcessingTaskOrderImportResDTO readFile(MultipartFile file, Integer warehouseNo, Long tenantId);

    /**
     * 导出加工任务明细
     * @param processingTaskCode 加工任务编码
     * @return 加工任务明细导出res对象
     */
    ProcessingTaskExportResDTO exportProcessingTaskAndDetail(String processingTaskCode);

    /**
     * 读取文件
     * @param reqDTO 请求对象
     * @return 文件读取res对象
     */
    ProcessingTaskImportResDTO readFile(ProcessingTaskImportReqDTO reqDTO);

    /**
     * 读取文件
     * @param file 文件
     * @param type 类型
     * @param warehouseNo 仓库编号
     * @return 响应对象
     */
    ProcessingTaskImportResDTO readFile(MultipartFile file, Integer type, Integer warehouseNo, Long tenantId);
}
