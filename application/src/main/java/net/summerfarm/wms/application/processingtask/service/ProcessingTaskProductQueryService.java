package net.summerfarm.wms.application.processingtask.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductQueryResDTO;

public interface ProcessingTaskProductQueryService {

    /**
     * 分页查询加工任务成品明细
     * @param reqDTO 请求对象
     * @return 分页数据
     */
    PageInfo<ProcessingTaskProductQueryResDTO> page(ProcessingTaskProductQueryReqDTO reqDTO);

    /**
     * 通过加工任务编码和加工任务成品id查询加工任务成品明细详情
     * @param processingTaskCode 加工任务编码
     * @param processingTaskProductId 加工任务成品id
     * @return 加工任务成品明细详情
     */
    ProcessingTaskProductQueryResDTO detail(Long processingTaskProductId, String processingTaskCode);

}
