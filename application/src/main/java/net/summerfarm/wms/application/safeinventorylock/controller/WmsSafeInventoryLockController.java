package net.summerfarm.wms.application.safeinventorylock.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryLockInput;
import net.summerfarm.wms.application.safeinventorylock.input.command.WmsSafeInventoryUnlockInput;
import net.summerfarm.wms.application.safeinventorylock.input.query.WmsSafeInventoryLockQueryInput;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockCommandService;
import net.summerfarm.wms.application.safeinventorylock.service.WmsSafeInventoryLockQueryService;
import net.summerfarm.wms.application.safeinventorylock.vo.WmsSafeInventoryLockVO;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.enums.SafeInventoryWarnStatusEnum;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.SerialNumberGenerator;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.inventory.domainobject.SafeInventoryWarn;
import net.summerfarm.wms.domain.inventory.domainobject.query.SafeInventoryWarnQuery;
import net.summerfarm.wms.domain.inventory.repository.SafeInventoryWarnRepository;
import net.summerfarm.wms.domain.safeinventorylock.enums.SafeInventoryLockStatusEnum;
import net.summerfarm.wms.domain.safeinventorylock.enums.SafeInventoryLockTypeEnum;
import net.summerfarm.wms.domain.safeinventorylock.param.WmsSafeInventoryLockCommandParam;
import net.summerfarm.wms.domain.safeinventorylock.repository.WmsSafeInventoryLockCommandRepository;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 安全库存精细化锁定锁定
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping(value = "/wmsSafeInventoryLock")
public class WmsSafeInventoryLockController {

    @Autowired
    private WmsSafeInventoryLockCommandService wmsSafeInventoryLockCommandService;

    @Autowired
    private WmsSafeInventoryLockQueryService wmsSafeInventoryLockQueryService;

    @Autowired
    private SafeInventoryWarnRepository safeInventoryWarnRepository;

    @Autowired
    private WmsSafeInventoryLockCommandRepository wmsSafeInventoryLockCommandRepository;

    @Autowired
    private SerialNumberGenerator serialNumberGenerator;

    /**
     * 安全库存锁定表列表
     * @param input 查询输入
     * @return 分页结果
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<WmsSafeInventoryLockVO>> getPage(@RequestBody WmsSafeInventoryLockQueryInput input) {
        if (input.getPageIndex() == null || input.getPageSize() == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请求参数为空");
        }
        return wmsSafeInventoryLockQueryService.getPage(input);
    }

    /**
     * 锁定库存
     * @param input 锁定输入
     * @return 锁定结果
     */
    @PostMapping(value = "/command/lock")
    public CommonResult<Boolean> lock(@Valid @RequestBody WmsSafeInventoryLockInput input) {
        input.setTenantId(LoginInfoThreadLocal.getTenantId());
        return wmsSafeInventoryLockCommandService.lock(input);
    }

    /**
     * 释放锁定
     * @param input 释放锁定输入
     * @return 释放结果
     */
    @PostMapping(value = "/command/unlock")
    public CommonResult<Boolean> unlock(@Valid @RequestBody WmsSafeInventoryUnlockInput input) {
        return wmsSafeInventoryLockCommandService.unlock(input);
    }

    /**
     * 库存锁定初始化数据工具接口
     */
    @PostMapping(value = "/tool/initInventoryLockFromWarn")
    public CommonResult<String> initInventoryLockFromWarn() {
        try {
            log.info("开始执行库存锁定初始化数据工具");

            SafeInventoryWarnQuery query = SafeInventoryWarnQuery.builder()
                    .warnStatus(SafeInventoryWarnStatusEnum.OPEN.getCode())
                    .build();
            List<SafeInventoryWarn> warnList = safeInventoryWarnRepository.list(query);

            if (CollectionUtils.isEmpty(warnList)) {
                return CommonResult.ok("未找到需要初始化的预警记录，初始化完成");
            }

            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);

            // 遍历预警记录，转换为库存锁定记录
            for (SafeInventoryWarn warn : warnList) {
                try {
                    WmsSafeInventoryLockCommandParam param = WmsSafeInventoryLockCommandParam.builder()
                            .warehouseNo(warn.getWarehouseNo())
                            .sku(warn.getSku())
                            .lockNo(serialNumberGenerator.createInternalNo(WmsConstant.INVENTORY_LOCK_PRE))
                            .initQuantity(warn.getLockQuantity())
                            .lockQuantity(warn.getLockQuantity())
                            .lockType(SafeInventoryLockTypeEnum.INVENTORY_LOCK.getCode())
                            .lockStatus(SafeInventoryLockStatusEnum.LOCKED.getCode())
                            .lockReason(warn.getLockReason())
                            .createOperator(warn.getCreateOperator())
                            .updateOperator(warn.getCreateOperator())
                            .tenantId(1L)
                            .createTime(DateUtil.toLocalDateTime(warn.getCreateTime()))
                            .updateTime(DateUtil.toLocalDateTime(warn.getCreateTime()))
                            .warehouseTenantId(1L)
                            .createOperatorId(warn.getCreateOperatorId())
                            .updateOperatorId(warn.getCreateOperatorId())
                            .build();
                    wmsSafeInventoryLockCommandRepository.insertSelective(param);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    log.error("初始化库存锁定记录异常，仓库：{}, SKU：{}, 异常：{}",
                            warn.getWarehouseNo(), warn.getSku(), e.getMessage(), e);
                }
            }

            String resultMessage = String.format("库存锁定初始化完成，成功：%d条，失败：%d条",
                    successCount.get(), failCount.get());
            log.info(resultMessage);

            return CommonResult.ok(resultMessage);

        } catch (Exception e) {
            log.error("执行库存锁定初始化数据工具失败", e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "初始化失败：" + e.getMessage());
        }
    }

}
