package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-09-03
 * @description
 */
@Data
public class BatchHelpOrderVO implements Serializable {
    /**
     * 序号
     */
    private Integer id;
    /**
     * mId
     */
    private Long mId;
    /**
     * 子账号id
     */
    private Long accountId;
    /**
     * 店铺名称
     */
    private String mname;
    /**
     * 客户类型
     */
    private String size;
    /**
     * 配送地址
     */
    private ContactVO contactVO;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单项
     */
    private List<ProcessingTaskOrderCreateReqDTO> orderItemList;
    /**
     * 该条数据是否继续走流程
     */
    private Boolean goOnFlag;
    /**
     * 备注
     */
    private String result;
    /**
     * 喜茶代下单字段：喜茶订单号
     */
    private String htOrderCode;
    /**
     * 代下单用户类型 1，喜茶 2，其他
     */
    private Integer userType;
}
