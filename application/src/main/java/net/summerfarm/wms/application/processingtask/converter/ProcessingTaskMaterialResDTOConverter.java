package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskMaterialResDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;
import net.summerfarm.wms.domain.products.domainobject.Product;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface ProcessingTaskMaterialResDTOConverter {

    public static ProcessingTaskMaterialResDTO convert(ProcessingTaskMaterial processingTaskMaterial,
                                                       Map<String, Product> materialSpuMap) {
        if ( processingTaskMaterial == null ) {
            return null;
        }

        Product materialSpu = materialSpuMap.get(processingTaskMaterial.getMaterialSkuCode());

        ProcessingTaskMaterialResDTO processingTaskMaterialResDTO = new ProcessingTaskMaterialResDTO();

        if ( processingTaskMaterial.getUpdateTime() != null ) {
            processingTaskMaterialResDTO.setUpdateTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( processingTaskMaterial.getUpdateTime() ) );
        }
        if ( processingTaskMaterial.getCreateTime() != null ) {
            processingTaskMaterialResDTO.setCreateTime( new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss" ).format( processingTaskMaterial.getCreateTime() ) );
        }
        processingTaskMaterialResDTO.setMaterialSkuTotalReceiveWeight( processingTaskMaterial.getMaterialSkuReceiveWeight() );
        processingTaskMaterialResDTO.setId( processingTaskMaterial.getId() );
        processingTaskMaterialResDTO.setWarehouseNo( processingTaskMaterial.getWarehouseNo() );
        processingTaskMaterialResDTO.setProcessingTaskCode( processingTaskMaterial.getProcessingTaskCode() );
        processingTaskMaterialResDTO.setProcessingTaskProductId( processingTaskMaterial.getProcessingTaskProductId() );
        processingTaskMaterialResDTO.setProductSkuCode( processingTaskMaterial.getProductSkuCode() );
        processingTaskMaterialResDTO.setMaterialSkuCode( processingTaskMaterial.getMaterialSkuCode() );
        processingTaskMaterialResDTO.setMaterialSkuSaasId(materialSpu == null ? null : materialSpu.getSaasSkuId());
        processingTaskMaterialResDTO.setMaterialSkuName( processingTaskMaterial.getMaterialSkuName() );
        processingTaskMaterialResDTO.setMaterialSkuWeight( processingTaskMaterial.getMaterialSkuWeight() );
        processingTaskMaterialResDTO.setMaterialSkuUnit( processingTaskMaterial.getMaterialSkuUnit() );
        processingTaskMaterialResDTO.setMaterialSkuQuantity( processingTaskMaterial.getMaterialSkuQuantity() );
        processingTaskMaterialResDTO.setMaterialSkuReceiveQuantity( processingTaskMaterial.getMaterialSkuReceiveQuantity() );
        processingTaskMaterialResDTO.setMaterialSkuReceiveWeight( processingTaskMaterial.getMaterialSkuReceiveWeight() );
        processingTaskMaterialResDTO.setMaterialSkuRestoreQuantity( processingTaskMaterial.getMaterialSkuRestoreQuantity() );
        processingTaskMaterialResDTO.setWasteLossWeight( processingTaskMaterial.getWasteLossWeight() );
        processingTaskMaterialResDTO.setSpecLossWeight( processingTaskMaterial.getSpecLossWeight() );
        processingTaskMaterialResDTO.setMaterialSkuRemainWeight( processingTaskMaterial.getMaterialSkuRemainWeight() );
        processingTaskMaterialResDTO.setMaterialSkuUnitDesc( processingTaskMaterial.getMaterialSkuUnitDesc() );
        processingTaskMaterialResDTO.setCreator( processingTaskMaterial.getCreator() );
        processingTaskMaterialResDTO.setUpdater( processingTaskMaterial.getUpdater() );
        processingTaskMaterialResDTO.setMaterialSkuRatioNum( processingTaskMaterial.getMaterialSkuRatioNum() );

        return processingTaskMaterialResDTO;
    }

    public static List<ProcessingTaskMaterialResDTO> convertList(List<ProcessingTaskMaterial> processingTaskMaterials,
                                                                 Map<String, Product> materialSpuMap) {
        if ( processingTaskMaterials == null ) {
            return null;
        }

        List<ProcessingTaskMaterialResDTO> list = new ArrayList<ProcessingTaskMaterialResDTO>( processingTaskMaterials.size() );
        for ( ProcessingTaskMaterial processingTaskMaterial : processingTaskMaterials ) {
            list.add( convert( processingTaskMaterial, materialSpuMap) );
        }

        return list;
    }
}
