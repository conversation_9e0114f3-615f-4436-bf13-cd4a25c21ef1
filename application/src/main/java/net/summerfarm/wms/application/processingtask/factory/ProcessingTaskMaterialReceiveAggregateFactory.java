package net.summerfarm.wms.application.processingtask.factory;

import lombok.Data;
import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialInventoryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialReceiveReqDTO;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialReceiveAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTask;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProcessingTaskMaterialReceiveAggregateFactory {


    public static ProcessingTaskMaterialReceiveAggregate newInstance(
            ProcessingTaskMaterialReceiveReqDTO reqDTO,
            ProcessingTask processingTask,
            ProcessingTaskMaterial processingTaskMaterial) {

        ProcessingTaskMaterialReceiveAggregate receiveAggregate = new ProcessingTaskMaterialReceiveAggregate();

        receiveAggregate.setTenantId(reqDTO.getTenantId());
        receiveAggregate.setProcessingTaskMaterial(processingTaskMaterial);
        receiveAggregate.setMaterialReceiveRecordList(
                convertMaterialBatchList(reqDTO, processingTask, processingTaskMaterial));

        receiveAggregate.setMaterialSkuReceiveQuantity(
                reqDTO.getMaterialReceiveList().stream()
                        .map(ProcessingTaskMaterialInventoryReqDTO::getMaterialSkuReceiveQuantity)
                        .reduce(0, Integer::sum)
        );
        return receiveAggregate;
    }

    private static List<ProcessingTaskMaterialReceiveRecord> convertMaterialBatchList(
            ProcessingTaskMaterialReceiveReqDTO reqDTO,
            ProcessingTask processingTask,
            ProcessingTaskMaterial processingTaskMaterial) {
        List<ProcessingTaskMaterialReceiveRecord> result = new ArrayList<>();

        for (ProcessingTaskMaterialInventoryReqDTO materialInventoryReqDTO : reqDTO.getMaterialReceiveList()) {
            ProcessingTaskMaterialReceiveRecord receiveRecord = new ProcessingTaskMaterialReceiveRecord();

            receiveRecord.setWarehouseNo(processingTaskMaterial.getWarehouseNo());
            receiveRecord.setProcessingTaskCode(processingTaskMaterial.getProcessingTaskCode());
            receiveRecord.setProcessingTaskProductId(processingTaskMaterial.getProcessingTaskProductId());

            receiveRecord.setProductSkuCode(processingTaskMaterial.getProductSkuCode());

            receiveRecord.setMaterialSkuCode(reqDTO.getMaterialSkuCode());
            receiveRecord.setMaterialSkuName(processingTaskMaterial.getMaterialSkuName());
            receiveRecord.setMaterialSkuWeight(processingTaskMaterial.getMaterialSkuWeight());
            receiveRecord.setMaterialSkuUnit(processingTaskMaterial.getMaterialSkuUnit());
            receiveRecord.setMaterialSkuQuantity(materialInventoryReqDTO.getMaterialSkuReceiveQuantity());
            receiveRecord.setMaterialSkuPurchaseBatch(materialInventoryReqDTO.getMaterialSkuPurchaseBatch());
            receiveRecord.setMaterialSkuProductionDate(
                    DateUtil.parseDateTimeByYMD(materialInventoryReqDTO.getMaterialSkuPurchaseBatchProductionDate())
            );
            receiveRecord.setMaterialSkuQualityDate(
                    DateUtil.parseDateTimeByYMD(materialInventoryReqDTO.getMaterialSkuPurchaseBatchQualityDate())
            );
            receiveRecord.setMaterialSkuCabinetCode(materialInventoryReqDTO.getMaterialSkuCabinetCode());

            receiveRecord.setMaterialSkuReceiveQuantity(materialInventoryReqDTO.getMaterialSkuReceiveQuantity());
            receiveRecord.setMaterialSkuRestoreQuantity(0);

            if (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType())){
                receiveRecord.setWasteLossWeight(BigDecimal.ZERO);
                receiveRecord.setSpecLossWeight(BigDecimal.ZERO);
                receiveRecord.setMaterialSkuRemainWeight(BigDecimal.ZERO);
            } else {
                receiveRecord.setWasteLossWeight(null);
                receiveRecord.setSpecLossWeight(null);
                receiveRecord.setMaterialSkuRemainWeight(BigDecimal.ZERO);
            }

            receiveRecord.setCreator(LoginInfoThreadLocal.getCurrentUserName());
            receiveRecord.setCreateTime(new Date());
            receiveRecord.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
            receiveRecord.setUpdateTime(new Date());
            receiveRecord.setDeleteFlag(DeleteFlagEnum.NO.getValue());

            result.add(receiveRecord);
        }

        return result;
    }
}
