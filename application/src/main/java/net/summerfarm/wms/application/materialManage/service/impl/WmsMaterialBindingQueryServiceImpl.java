package net.summerfarm.wms.application.materialManage.service.impl;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialBindingAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialBindingQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialBindingQueryService;
import net.summerfarm.wms.common.converter.PageInfoConverter;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingDetailEntity;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialBindingEntity;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialBindingQueryParam;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialBindingDetailQueryRepository;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialBindingQueryRepository;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.facade.goods.GoodsReadFacade;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
@Service
public class WmsMaterialBindingQueryServiceImpl implements WmsMaterialBindingQueryService {

    @Autowired
    private WmsMaterialBindingQueryRepository wmsMaterialBindingQueryRepository;
    @Autowired
    private WarehouseStorageRepository warehouseStorageRepository;
    @Autowired
    private WmsMaterialBindingDetailQueryRepository materialBindingDetailQueryRepository;
    @Autowired
    private GoodsReadFacade goodsReadFacade;

    @Override
    public CommonResult<PageInfo<WmsMaterialBindingVO>> getPage(WmsMaterialBindingQueryInput input) {
        WmsMaterialBindingQueryParam queryParam = WmsMaterialBindingAssembler.toWmsMaterialBindingQueryParam(input);
        PageInfo<WmsMaterialBindingEntity> page = wmsMaterialBindingQueryRepository.getPage(queryParam);

        if (page == null || CollectionUtils.isEmpty(page.getList())) {
            return CommonResult.ok(PageInfoConverter.toPageResp(page, wmsMaterialBindingEntity ->
                    WmsMaterialBindingAssembler.toWmsMaterialBindingVO(wmsMaterialBindingEntity,
                            new HashMap<>(), new HashMap<>(), new HashMap<>(), new HashMap<>())));
        }
        List<WmsMaterialBindingEntity> list = page.getList();

        List<WmsMaterialBindingVO> resultList = convertList(input.getTenantId(), list);

        return CommonResult.ok(PageInfoConverter.toPageResp(page, resultList));
    }

    @Override
    public List<WmsMaterialBindingVO> getList(WmsMaterialBindingQueryInput input) {
        WmsMaterialBindingQueryParam queryParam = WmsMaterialBindingAssembler.toWmsMaterialBindingQueryParam(input);
        List<WmsMaterialBindingEntity> list = wmsMaterialBindingQueryRepository.getListForExport(queryParam);
        return convertList(input.getTenantId(), list);
    }

    private List<WmsMaterialBindingVO> convertList(Long tenantId,
                                                   List<WmsMaterialBindingEntity> list) {
        if (CollectionUtils.isEmpty(list) || tenantId == null) {
            return new ArrayList<>();
        }

        List<Long> idList = list.stream()
                .map(WmsMaterialBindingEntity::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<WmsMaterialBindingDetailEntity>> detailEntityMap =
                materialBindingDetailQueryRepository.mapByBindingIdList(idList);

        List<Integer> warehouseNoList = list.stream()
                .map(WmsMaterialBindingEntity::getWarehouseNo)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, WarehouseStorageCenterEntity> warehouseStorageCenterEntityMap =
                warehouseStorageRepository.mapByWarehouseNoListByWarehouseNo(warehouseNoList);

        List<String> skuCodeList = list.stream()
                .map(WmsMaterialBindingEntity::getSku)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> skuGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                tenantId, skuCodeList);

        List<String> materialSkuCodeList = detailEntityMap.values().stream()
                .flatMap(Collection::stream)
                .map(WmsMaterialBindingDetailEntity::getMaterialSku)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> materialSkuGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                tenantId, materialSkuCodeList);

        List<WmsMaterialBindingVO> resultList = new ArrayList<>();
        for (WmsMaterialBindingEntity entity : list) {
            WmsMaterialBindingVO vo = WmsMaterialBindingAssembler.toWmsMaterialBindingVO(entity,
                    detailEntityMap, warehouseStorageCenterEntityMap,
                    skuGoodsInfoDTOMap, materialSkuGoodsInfoDTOMap);
            resultList.add(vo);
        }
        return resultList;
    }

    @Override
    public CommonResult<WmsMaterialBindingVO> getDetail(Long tenantId, Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        WmsMaterialBindingEntity entity = wmsMaterialBindingQueryRepository.selectById(id);
        if (entity == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请求信息为空");
        }

        Map<Long, List<WmsMaterialBindingDetailEntity>> detailEntityMap =
                materialBindingDetailQueryRepository.mapByBindingIdList(Collections.singletonList(entity.getId()));

        Map<Integer, WarehouseStorageCenterEntity> warehouseStorageCenterEntityMap =
                warehouseStorageRepository.mapByWarehouseNoListByWarehouseNo(Collections.singletonList(entity.getWarehouseNo()));

        Map<String, GoodsInfoDTO> skuGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                tenantId, Collections.singletonList(entity.getSku()));

        List<String> materialSkuCodeList = detailEntityMap.values().stream()
                .flatMap(Collection::stream)
                .map(WmsMaterialBindingDetailEntity::getMaterialSku)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, GoodsInfoDTO> materialSkuGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByTidAndSkuList(
                tenantId, materialSkuCodeList);

        return CommonResult.ok(WmsMaterialBindingAssembler.toWmsMaterialBindingVO(entity,
                detailEntityMap, warehouseStorageCenterEntityMap,
                skuGoodsInfoDTOMap, materialSkuGoodsInfoDTOMap));
    }
}