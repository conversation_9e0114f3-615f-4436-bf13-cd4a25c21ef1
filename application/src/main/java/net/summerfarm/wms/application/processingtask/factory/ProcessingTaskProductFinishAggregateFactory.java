package net.summerfarm.wms.application.processingtask.factory;

import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductFinishReqDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductFinishAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskTypeEnum;
import net.xianmu.common.exception.BizException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ProcessingTaskProductFinishAggregateFactory {
    public static ProcessingTaskProductFinishAggregate newInstance(
            ProcessingTaskProductFinishReqDTO reqDTO,
            ProcessingTaskProduct processingTaskProduct,
            ProcessingTask processingTask,
            List<ProcessingTaskMaterial> materialList,
            Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap) {

        ProcessingTaskProductFinishAggregate aggregate = new ProcessingTaskProductFinishAggregate();
        aggregate.setProcessingTaskProductId(reqDTO.getProcessingTaskProductId());
        aggregate.setProcessingTaskCode(reqDTO.getProcessingTaskCode());
        aggregate.setFinishRemark(reqDTO.getFinishRemark());

        if (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType())){
            aggregate.setReceiveSpecLoseUpdateList(new ArrayList<>());
        } else {
            aggregate.setReceiveSpecLoseUpdateList(buildMaterialReceiveSpecLoseUpdateList(
                    reqDTO, processingTaskProduct, materialList, materialReceiveRecordMap)
            );
        }

        aggregate.setSpecLossWeightTotal(aggregate.getReceiveSpecLoseUpdateList().stream()
                .map(MaterialReceiveSpecLoseUpdate::getSpecLossWeight)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO)
        );

        return aggregate;
    }

    private static List<MaterialReceiveSpecLoseUpdate> buildMaterialReceiveSpecLoseUpdateList(
            ProcessingTaskProductFinishReqDTO reqDTO,
            ProcessingTaskProduct processingTaskProduct,
            List<ProcessingTaskMaterial> materialList,
            Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap) {

        List<MaterialReceiveSpecLoseUpdate> result = new ArrayList<>();

        for (ProcessingTaskMaterial processingTaskMaterial : materialList) {
            List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList =
                    materialReceiveRecordMap.get(processingTaskMaterial.getId());
            materialReceiveRecordList = materialReceiveRecordList == null ? new ArrayList<>() : materialReceiveRecordList;
            materialReceiveRecordList = materialReceiveRecordList.stream()
                    .filter(s -> s.getMaterialSkuReceiveQuantity() > 0 &
                            s.getMaterialSkuReceiveQuantity() > s.getMaterialSkuRestoreQuantity())
                    .collect(Collectors.toList());

            // 成品加工所需原料重量
            BigDecimal totalMaterialNeedWeightDecimal = BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity())
                    .multiply(BigDecimal.valueOf(processingTaskMaterial.getMaterialSkuRatioNum()))
                    .multiply(processingTaskMaterial.getMaterialSkuWeight())
                    .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 2, RoundingMode.UP);

            // 总领料数量
            Integer materialTotalReceiveQuantity = materialReceiveRecordList.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuReceiveQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);
            // 总归还数量
            Integer materialTotalRestoreQuantity = materialReceiveRecordList.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuRestoreQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);
            // 剩余数量 = 总领料数量 - 总归还数量
            Integer materialRemainQuantity = materialTotalReceiveQuantity - materialTotalRestoreQuantity;
            if (materialRemainQuantity < 0){
                throw new BizException("领料数量不足");
            }
            BigDecimal materialRemainWeight = BigDecimal.valueOf(materialRemainQuantity)
                    .multiply(processingTaskMaterial.getMaterialSkuWeight());

            // 总废料损耗
            BigDecimal materialTotalWasteLossWeight = materialReceiveRecordList.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getWasteLossWeight)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);

            // 总规格损耗 = 剩余重量((总领料数量 - 总归还数量) * 单重) - 所需原料重量 - 总废料损耗
            BigDecimal materialTotalSpecLossWeight = materialRemainWeight
                    .subtract(totalMaterialNeedWeightDecimal)
                    .subtract(materialTotalWasteLossWeight);

            // 总原料使用重量 = 总领料重量 - 总归还重量 - 总废料损耗
            BigDecimal materialTotalUseWeight = BigDecimal.valueOf(materialTotalReceiveQuantity - materialTotalRestoreQuantity)
                    .multiply(processingTaskMaterial.getMaterialSkuWeight())
                    .subtract(materialTotalWasteLossWeight);

            BigDecimal materialTotalSpecLossWeightRemain = materialTotalSpecLossWeight;

            for (int i = 0; i < materialReceiveRecordList.size(); i++){
                ProcessingTaskMaterialReceiveRecord receiveRecord  = materialReceiveRecordList.get(i);

                MaterialReceiveSpecLoseUpdate record = new MaterialReceiveSpecLoseUpdate();

                // 分配原料领料规格损耗
                record.setMaterialReceiveRecordId(receiveRecord.getId());
                record.setProcessingTaskMaterialId(receiveRecord.getProcessingTaskMaterialId());
                record.setProcessingTaskProductId(receiveRecord.getProcessingTaskProductId());

                // 比例均摊
                BigDecimal materialSpecLossWeight = null;
                if (i == materialReceiveRecordList.size() - 1){
                    materialSpecLossWeight = materialTotalSpecLossWeightRemain;
                }else {
                    // 领料重量
                    BigDecimal receiveWeight = BigDecimal.valueOf(receiveRecord.getMaterialSkuReceiveQuantity())
                            .multiply(receiveRecord.getMaterialSkuWeight());
                    // 使用 = 领料 - 损耗 - 剩余归还
                    BigDecimal useWeight = receiveWeight.subtract(receiveRecord.getWasteLossWeight())
                            .subtract(receiveRecord.getMaterialSkuRemainWeight());

                    materialSpecLossWeight = materialTotalSpecLossWeight
                            .multiply(useWeight)
                            .divide(materialTotalUseWeight, 2, RoundingMode.DOWN);
                    // 剩余扣减
                    materialTotalSpecLossWeightRemain = materialTotalSpecLossWeightRemain.subtract(
                            materialSpecLossWeight);
                }

                record.setSpecLossWeight(materialSpecLossWeight);

                result.add(record);
            }
        }

        return result;
    }
}
