package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingConfigProductSkuSpecDTO;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProcessingConfigUpsetReqDTO implements Serializable {

    /**
     * 规格Id
     */
    private Long id;

    /**
     * 库存仓编号
     */
    @NotNull(message = "库存仓编号 不能为空")
    private Integer warehouseNo;

    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    @NotNull(message = "加工类型 不能为空")
    private Integer type;

    /**
     * 成品SKU
     */
//    @NotNull(message = "成品SKU 不能为空")
    private String productSkuCode;

    /**
     * 成品SKUid
     */
//    @NotNull(message = "成品SKU 不能为空")
    private Long productSkuSaasId;

    /**
     * 成品SKU重量
     */
    private BigDecimal productSkuWeight;

    /**
     * 成品SKU单位
     */
    private String productSkuUnit;

    /**
     * 跳过比例和重量校验
     */
    private Boolean skipRatioAndWeightCheck = false;

    /**
     * 成品sku比例数量
     */
    private Integer productSkuRatioNum = 1;
    /**
     * 成品重量列表
     */
    private List<ProcessingConfigProductSkuSpecDTO> productSkuSpecList;

    /**
     * 加工多原料信息
     */
    private List<ProcessingMaterialConfigUpsetReqDTO> processingMaterialConfigList;
}
