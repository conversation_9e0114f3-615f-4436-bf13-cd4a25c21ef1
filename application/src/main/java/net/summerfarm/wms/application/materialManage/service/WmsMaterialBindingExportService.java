package net.summerfarm.wms.application.materialManage.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.wms.api.h5.filedown.resp.FileDownResp;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialBindingAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialBindingQueryInput;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingExportVO;
import net.summerfarm.wms.application.materialManage.controller.vo.WmsMaterialBindingVO;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.download.enums.FileDownloadCenterRecordEnum;
import net.xianmu.common.result.CommonResult;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class WmsMaterialBindingExportService {

    @Resource
    private WmsMaterialBindingQueryService wmsMaterialBindingQueryService;

    public CommonResult<FileDownResp> export(WmsMaterialBindingQueryInput input) {
        String fileName = "物料导出" + DateUtil.formatYmd(System.currentTimeMillis()) + ".xlsx";
        DownloadCenterRecordDTO downloadCenterRecordDTO = new DownloadCenterRecordDTO();
        downloadCenterRecordDTO.setUserId(LoginInfoThreadLocal.getCurrentUserId());
        downloadCenterRecordDTO.setTenantId(WmsConstant.XIANMU_TENANT_ID);
        downloadCenterRecordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        downloadCenterRecordDTO.setBizType(FileDownloadCenterRecordEnum.MATERIAL_BINDING_EXPORT.getType());
        downloadCenterRecordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
        downloadCenterRecordDTO.setFileName(fileName);
        Long resId = DownloadCenterHelper.build(downloadCenterRecordDTO).asyncWrite(input,
                (param) -> this.queryAll(input), WmsMaterialBindingExportVO.class);
        return CommonResult.ok(FileDownResp.builder().resId(resId).build());
    }

    public List<WmsMaterialBindingExportVO> queryAll(WmsMaterialBindingQueryInput input) {
        List<WmsMaterialBindingVO> wmsMaterialBindingVOList = wmsMaterialBindingQueryService.getList(input);
        if (CollectionUtils.isEmpty(wmsMaterialBindingVOList)){
            return new ArrayList<>();
        }

        return WmsMaterialBindingAssembler.convert2Export(wmsMaterialBindingVOList);
    }
}
