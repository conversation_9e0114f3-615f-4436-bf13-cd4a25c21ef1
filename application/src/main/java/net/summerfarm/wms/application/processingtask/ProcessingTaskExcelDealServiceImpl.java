package net.summerfarm.wms.application.processingtask;


import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskExportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskImportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskOrderImportResDTO;
import net.summerfarm.wms.application.processingtask.dto.req.*;
import net.summerfarm.wms.application.processingtask.enums.OrderImportIsSuccessEnum;
import net.summerfarm.wms.common.constant.Global;
import net.summerfarm.wms.common.constant.WmsConstant;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.common.util.LongUtil;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.download.enums.FileDownloadCenterRecordEnum;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingConfigTypeEnum;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.summerfarm.wms.domain.wnc.WarehouseStorageRepository;
import net.summerfarm.wms.domain.wnc.domainobject.WarehouseStorageCenterEntity;
import net.summerfarm.wms.manage.web.mapper.executor.ExecutorFactory;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.common.util.ExcelUtils;
import net.summerfarm.wms.common.util.HttpReadUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskStatusEnum;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskTypeEnum;
import net.summerfarm.wms.domain.processingtask.repository.*;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProcessingTaskExcelDealServiceImpl implements ProcessingTaskExcelDealService {

    @Autowired
    private ProcessingConfigRepository processingConfigRepository;
    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProcessingTaskProductRepository processingTaskProductRepository;
    @Autowired
    private ProcessingTaskProductRecordRepository processingTaskProductRecordRepository;
    @Autowired
    private ProcessingTaskMaterialRepository processingTaskMaterialRepository;

    @Autowired
    private ProcessingTaskProductOrderRecordRepository productOrderRecordRepository;
    @Autowired
    private ProcessingTaskMaterialReceiveRecordRepository processingTaskMaterialReceiveRecordRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private WarehouseStorageRepository warehouseStorageRepository;

    private static final String KILOGRAM_STRING = "kg";

    private static final String DOUBLE_ZERO_STRING = "0.00";

    private static final String TO_STRING = "至";

    enum HelpOrderUserType {
        HEYTEA(1, "喜茶客户"),
        NORMAL(2, "普通用户"),
        ;

        int code;
        String value;

        HelpOrderUserType(int code, String 喜茶客户) {
        }

        public int getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }
    }

    @Override
    public ProcessingTaskOrderImportResDTO readFile(ProcessingTaskOrderImportReqDTO reqDTO) {
        HttpURLConnection httpUrl = null;
        InputStream inputStream = null;
        try {
            httpUrl = HttpReadUtil.urlToInputStream(
                    reqDTO.getFileAddress());
            inputStream = httpUrl.getInputStream();

            return readFile(inputStream, reqDTO.getWarehouseNo(), reqDTO.getTenantId());
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "解析导入文件失败，请检查是否导入错误文件";
            log.error(errorMsg, e);
            throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("导入关闭流异常", e);
                }
            }
            if (httpUrl != null) {
                httpUrl.disconnect();
            }
        }
    }

    @Override
    public ProcessingTaskOrderImportResDTO readFile(MultipartFile file, Integer warehouseNo, Long tenantId) {
        try {
            InputStream inputStream = file.getInputStream();

            return readFile(inputStream, warehouseNo, tenantId);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "解析导入文件失败，请检查是否导入错误文件";
            log.error(errorMsg, e);
            throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
        }
    }

    /**
     * 导出加工任务明细
     *
     * @param processingTaskCode 加工任务编码
     * @return 加工任务明细导出res对象
     */
    @Override
    public ProcessingTaskExportResDTO exportProcessingTaskAndDetail(String processingTaskCode) {
        // 查询加工任务
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(processingTaskCode);
        if (Objects.isNull(processingTask)) {
            throw new BizException("未查询到对应的加工任务", ErrorCodeNew.PARAM_ERROR);
        }
        // 查询加工任务成品
        List<ProcessingTaskProduct> processingTaskProducts = processingTaskProductRepository
                .queryByProcessingTaskCode(processingTask.getProcessingTaskCode());
        if (CollectionUtils.isEmpty(processingTaskProducts)) {
            throw new BizException("未查询到对应的加工任务成品信息", ErrorCodeNew.PARAM_ERROR);
        }
        List<Long> productIdList = processingTaskProducts.stream().map(ProcessingTaskProduct::getId)
                .distinct()
                .collect(Collectors.toList());
        // 查询加工任务成品明细
        Map<Long, List<ProcessingTaskProductRecord>> productRecordMap = processingTaskProductRecordRepository
                .mapByProcessingTaskProductIdList(productIdList);
        Map<Long, List<ProcessingTaskMaterial>> materialMap =
                processingTaskMaterialRepository.mapByProductIdList(productIdList);
        // 查询加工任务原料领用明细
        Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap =
                processingTaskMaterialReceiveRecordRepository.mapByProductIdsGroupMaterialId(productIdList);
        // 仓库
        WarehouseStorageCenterEntity warehouseStorageCenter =
                warehouseStorageRepository.selectByWarehouseNo(processingTask.getWarehouseNo());
        if (warehouseStorageCenter == null) {
            throw new BizException("未查询到对应的仓库信息", ErrorCodeNew.PARAM_ERROR);
        }
        Long tenantId = warehouseStorageCenter.getTenantId();
        // 生成excel
        OssUploadResult ossUploadResult = generateExportExcel(processingTask, processingTaskProducts, productRecordMap,
                materialMap, materialReceiveRecordMap, tenantId);
        return new ProcessingTaskExportResDTO(ossUploadResult == null ? null : ossUploadResult.getUrl(),
                ossUploadResult,  null);
    }

    @Override
    public ProcessingTaskImportResDTO readFile(ProcessingTaskImportReqDTO reqDTO) {
        HttpURLConnection httpUrl = null;
        InputStream inputStream = null;
        try {
            httpUrl = HttpReadUtil.urlToInputStream(
                    reqDTO.getFileAddress());
            inputStream = httpUrl.getInputStream();

            if (Objects.equals(reqDTO.getType(), ProcessingTaskTypeEnum.ORDER_PROCESSING.getValue())){
                return this.readOrderFile(inputStream, reqDTO.getWarehouseNo(), reqDTO.getTenantId());
            }

            if (Objects.equals(reqDTO.getType(), ProcessingTaskTypeEnum.SKU_PROCESSING.getValue()) ||
                    Objects.equals(reqDTO.getType(), ProcessingTaskTypeEnum.SKU_ASSEMBLY.getValue())){
                return this.readProductFile(inputStream, reqDTO.getType(), reqDTO.getWarehouseNo(), reqDTO.getTenantId());
            }

            throw new BizException("不支持的加工类型", ErrorCodeNew.PARAM_ERROR);

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "解析导入文件失败，请检查是否导入错误文件";
            log.error(errorMsg, e);
            throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("导入关闭流异常", e);
                }
            }
            if (httpUrl != null) {
                httpUrl.disconnect();
            }
        }
    }

    @Override
    public ProcessingTaskImportResDTO readFile(MultipartFile file, Integer type,
                                               Integer warehouseNo, Long tenantId) {
        try {
            InputStream inputStream = file.getInputStream();

            if (Objects.equals(type, ProcessingTaskTypeEnum.ORDER_PROCESSING.getValue())){
                return this.readOrderFile(inputStream, warehouseNo, tenantId);
            }

            if (Objects.equals(type, ProcessingTaskTypeEnum.SKU_PROCESSING.getValue()) ||
                    Objects.equals(type, ProcessingTaskTypeEnum.SKU_ASSEMBLY.getValue())){
                return this.readProductFile(inputStream, type, warehouseNo, tenantId);
            }

            throw new BizException("不支持的加工类型", ErrorCodeNew.PARAM_ERROR);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "解析导入文件失败，请检查是否导入错误文件";
            log.error(errorMsg, e);
            throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
        }
    }

    /**
     * 生成导出excel
     *
     * @param processingTask           加工任务
     * @param processingTaskProducts   加工任务成品
     * @param productRecordMap         加工任务成品明细map
     * @param materialReceiveRecordMap 加工任务原料领用明细map
     * @param tenantId
     */
    private OssUploadResult generateExportExcel(ProcessingTask processingTask, List<ProcessingTaskProduct> processingTaskProducts,
                                                Map<Long, List<ProcessingTaskProductRecord>> productRecordMap,
                                                Map<Long, List<ProcessingTaskMaterial>> materialMap,
                                                Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap,
                                                Long tenantId) {
        // 生成Excel
        Workbook workbook = new HSSFWorkbook();
        CellStyle baseCellStyle = workbook.createCellStyle();
        // 水平居中
        baseCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        baseCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 生成商品加工Sheet
        createProductRecordSheet(workbook, baseCellStyle, processingTask, processingTaskProducts,
                productRecordMap, materialMap, materialReceiveRecordMap, tenantId);
        // 生成原料批次Sheet
        createMaterialReceiveRecordSheet(workbook, baseCellStyle, processingTask, processingTaskProducts,
                materialMap, materialReceiveRecordMap, tenantId);

        File file = null;
        FileOutputStream out = null;

        try {
            String fileName = System.getProperty("java.io.tmpdir") + File.separator
                    + "加工任务" + processingTask.getProcessingTaskCode() + "导出"
                    + DateUtil.formatDateBasic(LocalDateTime.now()) + ".xls";
            file = new File(fileName);
            out = new FileOutputStream(file);
            // excel输出到文件
            workbook.write(out);
            // 上传文件到Oss
            return OssUploadUtil.uploadExpireThreeDay(fileName, file);
        } catch (IOException e) {
            log.error("生成失败导出文件异常", e);
            throw new DefaultServiceException("生成失败导出文件异常");
        }
    }

    /**
     * 生成加工任务商品加工明细Sheet
     *
     * @param workbook                 工作簿
     * @param baseCellStyle            基础单元格风格
     * @param processingTask           加工任务
     * @param processingTaskProducts   加工任务成品
     * @param productRecordMap         加工任务成品明细map
     * @param materialMap
     * @param materialReceiveRecordMap
     * @param tenantId
     */
    private void createProductRecordSheet(Workbook workbook, CellStyle baseCellStyle, ProcessingTask processingTask,
                                          List<ProcessingTaskProduct> processingTaskProducts,
                                          Map<Long, List<ProcessingTaskProductRecord>> productRecordMap,
                                          Map<Long, List<ProcessingTaskMaterial>> materialMap,
                                          Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap, Long tenantId) {
        // 创建Sheet
        Sheet sheet = workbook.createSheet(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "商品加工" : "货品加工");
        // 创建表头
        int index = createProductRecordExcelHead(processingTask, sheet, tenantId);
        // 填充内容
        packProductRecordContent(index, sheet, baseCellStyle, processingTaskProducts,
                productRecordMap, materialMap, materialReceiveRecordMap, processingTask);
    }

    /**
     * 生成加工任务原料批次消耗明细Sheet
     *
     * @param workbook                 工作簿
     * @param baseCellStyle            基础单元格风格
     * @param processingTask           加工任务
     * @param processingTaskProducts   加工任务成品
     * @param materialMap
     * @param materialReceiveRecordMap 加工任务原料领用明细map
     * @param tenantId
     */
    private void createMaterialReceiveRecordSheet(Workbook workbook, CellStyle baseCellStyle, ProcessingTask processingTask,
                                                  List<ProcessingTaskProduct> processingTaskProducts,
                                                  Map<Long, List<ProcessingTaskMaterial>> materialMap,
                                                  Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap, Long tenantId) {
        // 创建Sheet
        Sheet sheet = workbook.createSheet("原料批次消耗");
        // 创建表头
        int index = createMaterialReceiveRecordExcelHead(processingTask, sheet, tenantId);
        // 填充内容
        packMaterialReceiveRecordContent(index, sheet, baseCellStyle, processingTaskProducts,
                materialMap, materialReceiveRecordMap, processingTask, tenantId);
    }

    /**
     * 生成加工任务成品明细表头
     *
     * @param processingTask 加工任务
     * @param sheet          工作表
     * @param tenantId
     * @return 索引值
     */
    private Integer createProductRecordExcelHead(ProcessingTask processingTask, Sheet sheet, Long tenantId) {
        // 构建表头
        int rowIndex = createCommonHead(sheet, processingTask, tenantId);

        // 构建表头
        Row title = sheet.createRow(rowIndex++);
        title.createCell(0).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "成品商品" : "成品信息");
        title.createCell(1).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "成品SKU" : "成品ID");
        title.createCell(2).setCellValue("成品规格");
        title.createCell(3).setCellValue("成品所需库存");
        title.createCell(4).setCellValue("实际生产库存");
        title.createCell(5).setCellValue("成品总重");
        title.createCell(6).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料商品" : "货品信息");
        title.createCell(7).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料SKU" : "货品ID");
        title.createCell(8).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料规格" : "货品规格");
        title.createCell(9).setCellValue("领料数量");
        title.createCell(10).setCellValue("领料总重");
        title.createCell(11).setCellValue("规格损耗");
        title.createCell(12).setCellValue("废料损耗");
        title.createCell(13).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料剩余重量" : "货品剩余重量");
        title.createCell(14).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料库存返还" : "货品库存返还");
        return rowIndex;
    }

    /**
     * 生成加工任务原料领用明细表头
     *
     * @param processingTask 加工任务
     * @param sheet          工作表
     * @param tenantId
     * @return 索引值
     */
    private Integer createMaterialReceiveRecordExcelHead(ProcessingTask processingTask, Sheet sheet, Long tenantId) {
        // 构建表头
        int rowIndex = createCommonHead(sheet, processingTask, tenantId);

        // 构建标题
        Row title = sheet.createRow(rowIndex++);
        title.createCell(0).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "成品商品" : "成品信息");
        title.createCell(1).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "成品SKU" : "成品ID");
        title.createCell(2).setCellValue("成品规格");
        title.createCell(3).setCellValue("成品所需库存");
        title.createCell(4).setCellValue("实际生产库存");
        title.createCell(5).setCellValue("成品总重");
        title.createCell(6).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料商品" : "货品信息");
        title.createCell(7).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料SKU" : "货品ID");
        title.createCell(8).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料规格" : "货品规格");
        title.createCell(9).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                "原料批次" : "货品批次");
        if (WmsConstant.XIANMU_TENANT_ID.equals(tenantId)) {
            title.createCell(10).setCellValue("库位编码");
            title.createCell(11).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    "原料生止保质期" : "货品生止保质期");
            title.createCell(12).setCellValue("领料数量");
            title.createCell(13).setCellValue("领料总重");
            title.createCell(14).setCellValue("规格损耗");
            title.createCell(15).setCellValue("废料损耗");
            title.createCell(16).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    "原料剩余重量" : "货品剩余重量");
            title.createCell(17).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    "原料库存返还" : "货品库存返还");
        } else {
            title.createCell(10).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    "原料生止保质期" : "货品生止保质期");
            title.createCell(11).setCellValue("领料数量");
            title.createCell(12).setCellValue("领料总重");
            title.createCell(13).setCellValue("规格损耗");
            title.createCell(14).setCellValue("废料损耗");
            title.createCell(15).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    "原料剩余重量" : "货品剩余重量");
            title.createCell(16).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    "原料库存返还" : "货品库存返还");
        }
        return rowIndex;
    }

    /**
     * 构建公共表头
     * @param sheet 工作表
     * @param processingTask 加工任务
     * @return 行索引
     */
    private int createCommonHead(Sheet sheet, ProcessingTask processingTask, Long tenantId) {
        int rowIndex = 0;
        Row first = sheet.createRow(rowIndex++);
        first.createCell(0).setCellValue("加工任务");
        first.createCell(1).setCellValue(processingTask.getProcessingTaskCode());

        first.createCell(3).setCellValue("加工类型");
        first.createCell(4).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                ProcessingTaskTypeEnum.getDescriptionByValue(processingTask.getType()) :
                ProcessingTaskTypeEnum.getDescriptionByValue(processingTask.getType()).replace("商品", "货品"));

        Row second = sheet.createRow(rowIndex++);
        second.createCell(0).setCellValue("加工仓库");
        String warehouseName = Global.warehouseMap.get(Long.valueOf(processingTask.getWarehouseNo()));
        second.createCell(1).setCellValue(Objects.nonNull(warehouseName) ? warehouseName : "");

        Row third = sheet.createRow(rowIndex++);
        third.createCell(0).setCellValue("任务状态");
        third.createCell(1).setCellValue(ProcessingTaskStatusEnum.getDescriptionByValue(processingTask.getStatus()));

        // 表格向下空一行
        rowIndex++;

        return rowIndex;
    }

    /**
     * 填充加工任务成品明细内容
     *
     * @param index                    单元格索引
     * @param sheet                    工作表
     * @param baseCellStyle            基础单元格风格
     * @param processingTaskProducts   加工任务成品list
     * @param productRecordMap         加工任务成品明细map
     * @param materialMap
     * @param materialReceiveRecordMap 原料领用明细map
     * @param processingTask
     */
    private void packProductRecordContent(int index, Sheet sheet, CellStyle baseCellStyle,
                                          List<ProcessingTaskProduct> processingTaskProducts,
                                          Map<Long, List<ProcessingTaskProductRecord>> productRecordMap,
                                          Map<Long, List<ProcessingTaskMaterial>> materialMap,
                                          Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap,
                                          ProcessingTask processingTask) {
        for (ProcessingTaskProduct processingTaskProduct : processingTaskProducts) {
            // 获取原料
            List<ProcessingTaskMaterial> materialList = materialMap.get(processingTaskProduct.getId());
            materialList = materialList == null ?
                    new ArrayList<>() : materialList;
            // 获取当前加工任务成品对应的加工任务明细
//            List<ProcessingTaskProductRecord> processingTaskProductRecords = productRecordMap
//                    .get(processingTaskProduct.getId());
//            processingTaskProductRecords = processingTaskProductRecords == null ?
//                    new ArrayList<>() : processingTaskProductRecords;

//            int recordSize = processingTaskProductRecords.size() * materialList.size();
            int recordSize =  materialList.size();
            // 如果有多条明细记录，合并单元格
            if (recordSize > 1) {
                mergedProductRecordRegion(sheet, index, recordSize, 0, 5);
            }

            // 计算加工任务成品原料剩余总重
            for (ProcessingTaskMaterial processingTaskMaterial : materialList) {
                // 获取加工任务成品原料明细
                BigDecimal materialRemainTotalWeight = new BigDecimal("0.00");
                List<ProcessingTaskMaterialReceiveRecord> processingTaskMaterialReceiveRecords =
                        materialReceiveRecordMap.get(processingTaskMaterial.getId());
                if (!CollectionUtils.isEmpty(processingTaskMaterialReceiveRecords)) {
                    // 如果存在领料但是未填写原料剩余重量的情况，直接赋值0
                    processingTaskMaterialReceiveRecords.forEach(item -> {
                        if (Objects.isNull(item.getMaterialSkuRemainWeight())) {
                            item.setMaterialSkuRemainWeight(new BigDecimal(DOUBLE_ZERO_STRING));
                        }
                    });
                    materialRemainTotalWeight = processingTaskMaterialReceiveRecords.stream()
                                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuRemainWeight)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

//                if (CollectionUtils.isEmpty(processingTaskProductRecords)) {
//                    String errorMsg = "加工任务成品-" + processingTaskProduct.getId() + ":未查询到对应的成品明细";
//                    log.error(errorMsg);
//                    throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
//                }

//                for (ProcessingTaskProductRecord processingTaskProductRecord : processingTaskProductRecords) {
                    Row row = sheet.createRow(index);
                    int cellIndex = NumberUtils.INTEGER_ZERO;

                    // 成品商品
                    Cell productSkuName = row.createCell(cellIndex++);
                    productSkuName.setCellValue(processingTaskProduct.getProductSkuName());
                    productSkuName.setCellStyle(baseCellStyle);

                    // 成品SKU
                    Cell productSkuCode = row.createCell(cellIndex++);
                    productSkuCode.setCellValue(processingTaskProduct.getProductSkuCode());
                    productSkuCode.setCellStyle(baseCellStyle);

                    // 成品规格
                    Cell productSkuUnitDesc = row.createCell(cellIndex++);
                    productSkuUnitDesc.setCellValue(processingTaskProduct.getProductSkuUnitDesc());
                    productSkuUnitDesc.setCellStyle(baseCellStyle);

                    // 成品所需库存
                    Cell productSkuNeedQuantity = row.createCell(cellIndex++);
                    productSkuNeedQuantity.setCellValue(processingTaskProduct.getProductSkuNeedQuantity());
                    productSkuNeedQuantity.setCellStyle(baseCellStyle);

                    // 实际生产库存
                    Cell productSkuFinishQuantity = row.createCell(cellIndex++);
                    productSkuFinishQuantity.setCellValue(processingTaskProduct.getProductSkuFinishQuantity());
                    productSkuFinishQuantity.setCellStyle(baseCellStyle);

                    // 成品总重
                    Cell productSkuFinishWeight = row.createCell(cellIndex++);
                    productSkuFinishWeight.setCellValue("" +
                            processingTaskProduct.getProductSkuWeight().multiply(
                                BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity()))  +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                    KILOGRAM_STRING : processingTaskProduct.getProductSkuUnit()));
                    productSkuFinishWeight.setCellStyle(baseCellStyle);

                    // 原料商品
                    Cell materialSkuName = row.createCell(cellIndex++);
                    materialSkuName.setCellValue(processingTaskMaterial.getMaterialSkuName());
                    materialSkuName.setCellStyle(baseCellStyle);

                    // 原料SKU
                    Cell materialSkuCode = row.createCell(cellIndex++);
                    materialSkuCode.setCellValue(processingTaskMaterial.getMaterialSkuCode());
                    materialSkuCode.setCellStyle(baseCellStyle);

                    // 原料规格
                    Cell materialSkuSpec = row.createCell(cellIndex++);
                    materialSkuSpec.setCellValue(processingTaskMaterial.getMaterialSkuUnitDesc());
                    materialSkuSpec.setCellStyle(baseCellStyle);

                    // 领料数量
                    Cell materialSkuReceiveQuantity = row.createCell(cellIndex++);
                    materialSkuReceiveQuantity.setCellValue(processingTaskMaterial.getMaterialSkuReceiveQuantity());
                    materialSkuReceiveQuantity.setCellStyle(baseCellStyle);

                    // 领料总重
                    Cell materialSkuReceiveWeight = row.createCell(cellIndex++);
                    materialSkuReceiveWeight.setCellValue(processingTaskMaterial.getMaterialSkuReceiveWeight() +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    materialSkuReceiveWeight.setCellStyle(baseCellStyle);

                    // 成品规格重量
//                    Cell productSpecWeight = row.createCell(cellIndex++);
//                    BigDecimal productSpecTotalWeight = processingTaskProduct.getProductSkuSpecFinishWeight();
//                    productSpecWeight.setCellValue(productSpecTotalWeight + KILOGRAM_STRING);
//                    productSpecWeight.setCellStyle(baseCellStyle);

                    // 规格损耗
                    Cell specLossWeight = row.createCell(cellIndex++);
                    BigDecimal specLossWeightValue = processingTaskMaterial.getSpecLossWeight();
                    specLossWeight.setCellValue((Objects.isNull(specLossWeightValue) ?
                            new BigDecimal(DOUBLE_ZERO_STRING) : specLossWeightValue) +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    specLossWeight.setCellStyle(baseCellStyle);

                    // 废料损耗
                    Cell wasteLossWeight = row.createCell(cellIndex++);
                    BigDecimal wasteLossWeightValue = processingTaskMaterial.getWasteLossWeight();
                    wasteLossWeight.setCellValue((Objects.isNull(wasteLossWeightValue) ?
                            new BigDecimal(DOUBLE_ZERO_STRING) : wasteLossWeightValue) +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    wasteLossWeight.setCellStyle(baseCellStyle);

                    // 原料剩余重量
                    Cell materialRemainWeight = row.createCell(cellIndex++);
                    materialRemainWeight.setCellValue(materialRemainTotalWeight +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    materialRemainWeight.setCellStyle(baseCellStyle);

                    // 原料库存返还
                    Cell materialSkuRestoreQuantity = row.createCell(cellIndex++);
                    materialSkuRestoreQuantity.setCellValue(processingTaskMaterial.getMaterialSkuRestoreQuantity());
                    materialSkuRestoreQuantity.setCellStyle(baseCellStyle);

                    // 加工规格
//                    Cell processingSepc = row.createCell(cellIndex++);
//                    processingSepc.setCellValue(processingTaskProductRecord.getProductSkuSpecWeight()
//                            + processingTaskProductRecord.getProductSkuSpecUnit());
//                    processingSepc.setCellStyle(baseCellStyle);

                    // 预计加工数量
//                    Cell productSkuSpecNeedQuantity = row.createCell(cellIndex++);
//                    productSkuSpecNeedQuantity.setCellValue(processingTaskProductRecord.getProductSkuSpecNeedQuantity());
//                    productSkuSpecNeedQuantity.setCellStyle(baseCellStyle);

                    // 实际加工数量
//                    Cell productSkuSpecSubmitQuantity = row.createCell(cellIndex++);
//                    productSkuSpecSubmitQuantity.setCellValue(processingTaskProductRecord.getProductSkuSpecSubmitQuantity());
//                    productSkuSpecSubmitQuantity.setCellStyle(baseCellStyle);

                    index++;
//                }
            }
        }
    }

    /**
     * 填充加工任务原料消耗内容
     *
     * @param index                    索引值
     * @param sheet                    工作表
     * @param baseCellStyle            基础单元格风格
     * @param processingTaskProducts   成品明细表
     * @param materialMap
     * @param materialReceiveRecordMap 原料领用明细map
     * @param processingTask
     * @param tenantId
     */
    private void packMaterialReceiveRecordContent(int index, Sheet sheet, CellStyle baseCellStyle,
                                                  List<ProcessingTaskProduct> processingTaskProducts,
                                                  Map<Long, List<ProcessingTaskMaterial>> materialMap,
                                                  Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap,
                                                  ProcessingTask processingTask,
                                                  Long tenantId) {
        for (ProcessingTaskProduct processingTaskProduct : processingTaskProducts) {
            List<ProcessingTaskMaterial> materialList = materialMap.get(processingTaskProduct.getId());
            Integer totalProductSize = 0;
            for (ProcessingTaskMaterial processingTaskMaterial : materialList) {
                // 获取当前加工任务成品对应的加工任务明细
                List<ProcessingTaskMaterialReceiveRecord> processingTaskMaterialReceiveRecords = materialReceiveRecordMap
                        .get(processingTaskMaterial.getId());

                totalProductSize = totalProductSize +
                        (CollectionUtils.isEmpty(processingTaskMaterialReceiveRecords) ? 1 :
                                processingTaskMaterialReceiveRecords.size());
            }

            if (totalProductSize > 1){
                mergedProductRecordRegion(sheet, index, totalProductSize, 0, 5);
            }


            for (ProcessingTaskMaterial processingTaskMaterial : materialList) {
                // 获取当前加工任务成品对应的加工任务明细
                List<ProcessingTaskMaterialReceiveRecord> processingTaskMaterialReceiveRecords = materialReceiveRecordMap
                        .get(processingTaskMaterial.getId());

                // 如果没有查询到原料领用明细
                if (CollectionUtils.isEmpty(processingTaskMaterialReceiveRecords)) {
                    Row row = sheet.createRow(index);
                    int cellIndex = NumberUtils.INTEGER_ZERO;

                    // 成品商品
                    Cell productSkuName = row.createCell(cellIndex++);
                    productSkuName.setCellValue(processingTaskProduct.getProductSkuName());
                    productSkuName.setCellStyle(baseCellStyle);

                    // 成品SKU
                    Cell productSkuCode = row.createCell(cellIndex++);
                    productSkuCode.setCellValue(processingTaskProduct.getProductSkuCode());
                    productSkuCode.setCellStyle(baseCellStyle);

                    // 成品规格
                    Cell productSkuUnitDesc = row.createCell(cellIndex++);
                    productSkuUnitDesc.setCellValue(processingTaskProduct.getProductSkuUnitDesc());
                    productSkuUnitDesc.setCellStyle(baseCellStyle);

                    // 成品所需库存
                    Cell productSkuNeedQuantity = row.createCell(cellIndex++);
                    productSkuNeedQuantity.setCellValue(processingTaskProduct.getProductSkuNeedQuantity());
                    productSkuNeedQuantity.setCellStyle(baseCellStyle);

                    // 实际生产库存
                    Cell productSkuFinishQuantity = row.createCell(cellIndex++);
                    productSkuFinishQuantity.setCellValue(processingTaskProduct.getProductSkuFinishQuantity());
                    productSkuFinishQuantity.setCellStyle(baseCellStyle);

                    // 成品总重
                    Cell productSkuFinishWeight = row.createCell(cellIndex++);
                    productSkuFinishWeight.setCellValue(new BigDecimal(DOUBLE_ZERO_STRING) +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskProduct.getProductSkuUnit()));
                    productSkuFinishWeight.setCellStyle(baseCellStyle);

                    // 原料商品
                    Cell materialSkuName = row.createCell(cellIndex++);
                    materialSkuName.setCellValue(processingTaskMaterial.getMaterialSkuName());
                    materialSkuName.setCellStyle(baseCellStyle);

                    // 原料SKU
                    Cell materialSkuCode = row.createCell(cellIndex++);
                    materialSkuCode.setCellValue(processingTaskMaterial.getMaterialSkuCode());
                    materialSkuCode.setCellStyle(baseCellStyle);

                    // 原料规格
                    Cell materialSkuSpec = row.createCell(cellIndex++);
                    materialSkuSpec.setCellValue(processingTaskMaterial.getMaterialSkuUnitDesc());
                    materialSkuSpec.setCellStyle(baseCellStyle);

                    // 原料批次
                    Cell materialSkuPurchaseBatch = row.createCell(cellIndex++);
                    materialSkuPurchaseBatch.setCellValue(Global.CROSS_BAR);
                    materialSkuPurchaseBatch.setCellStyle(baseCellStyle);

                    if (WmsConstant.XIANMU_TENANT_ID.equals(tenantId)) {
                        // 库位编码
                        Cell materialSkuCabinetCode = row.createCell(cellIndex++);
                        materialSkuCabinetCode.setCellValue(Global.CROSS_BAR);
                        materialSkuCabinetCode.setCellStyle(baseCellStyle);
                    }

                    // 原料生止保质期
                    Cell materialFromTo = row.createCell(cellIndex++);
                    materialFromTo.setCellValue(Global.CROSS_BAR);
                    materialFromTo.setCellStyle(baseCellStyle);

                    // 领料数量
                    Cell materialSkuReceiveQuantity = row.createCell(cellIndex++);
                    materialSkuReceiveQuantity.setCellValue(NumberUtils.INTEGER_ZERO);
                    materialSkuReceiveQuantity.setCellStyle(baseCellStyle);

                    // 领料总重(领料数量 * SKU重量计算出)
                    Cell materialSkuReceiveWeight = row.createCell(cellIndex++);
                    materialSkuReceiveWeight.setCellValue(new BigDecimal(DOUBLE_ZERO_STRING) +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    materialSkuReceiveWeight.setCellStyle(baseCellStyle);

                    // 规格损耗
                    Cell specLossWeightCell = row.createCell(cellIndex++);
                    specLossWeightCell.setCellValue(new BigDecimal(DOUBLE_ZERO_STRING) +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    specLossWeightCell.setCellStyle(baseCellStyle);

                    // 废料损耗
                    Cell wasteLossWeightCell = row.createCell(cellIndex++);
                    wasteLossWeightCell.setCellValue(new BigDecimal(DOUBLE_ZERO_STRING) +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    wasteLossWeightCell.setCellStyle(baseCellStyle);

                    // 原料剩余重量
                    Cell materialSkuRemainWeight = row.createCell(cellIndex++);
                    materialSkuRemainWeight.setCellValue(new BigDecimal(DOUBLE_ZERO_STRING) +
                            (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                    materialSkuRemainWeight.setCellStyle(baseCellStyle);

                    // 原料库存返还
                    Cell materialSkuRestoreQuantity = row.createCell(cellIndex++);
                    materialSkuRestoreQuantity.setCellValue(NumberUtils.INTEGER_ZERO);
                    materialSkuRestoreQuantity.setCellStyle(baseCellStyle);

                    index++;
                } else {
                    // 如果有多条明细记录，合并单元格
                    if (processingTaskMaterialReceiveRecords.size() > 1) {
                        mergedProductRecordRegion(sheet, index, processingTaskMaterialReceiveRecords.size(), 6, 8);
                    }
                    for (ProcessingTaskMaterialReceiveRecord processingTaskMaterialReceiveRecord : processingTaskMaterialReceiveRecords) {
                        Row row = sheet.createRow(index);
                        int cellIndex = NumberUtils.INTEGER_ZERO;

                        // 成品商品
                        Cell productSkuName = row.createCell(cellIndex++);
                        productSkuName.setCellValue(processingTaskProduct.getProductSkuName());
                        productSkuName.setCellStyle(baseCellStyle);

                        // 成品SKU
                        Cell productSkuCode = row.createCell(cellIndex++);
                        productSkuCode.setCellValue(processingTaskProduct.getProductSkuCode());
                        productSkuCode.setCellStyle(baseCellStyle);

                        // 成品规格
                        Cell productSkuUnitDesc = row.createCell(cellIndex++);
                        productSkuUnitDesc.setCellValue(processingTaskProduct.getProductSkuUnitDesc());
                        productSkuUnitDesc.setCellStyle(baseCellStyle);

                        // 成品所需库存
                        Cell productSkuNeedQuantity = row.createCell(cellIndex++);
                        productSkuNeedQuantity.setCellValue(processingTaskProduct.getProductSkuNeedQuantity());
                        productSkuNeedQuantity.setCellStyle(baseCellStyle);

                        // 实际生产库存
                        Cell productSkuFinishQuantity = row.createCell(cellIndex++);
                        productSkuFinishQuantity.setCellValue(processingTaskProduct.getProductSkuFinishQuantity());
                        productSkuFinishQuantity.setCellStyle(baseCellStyle);

                        // 成品总重
                        Cell productSkuFinishWeight = row.createCell(cellIndex++);
                        productSkuFinishWeight.setCellValue("" +
                                processingTaskProduct.getProductSkuWeight().multiply(
                                        BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity()))  +
                                (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                    KILOGRAM_STRING : processingTaskProduct.getProductSkuUnit()));
                        productSkuFinishWeight.setCellStyle(baseCellStyle);

                        // 原料商品
                        Cell materialSkuName = row.createCell(cellIndex++);
                        materialSkuName.setCellValue(processingTaskMaterial.getMaterialSkuName());
                        materialSkuName.setCellStyle(baseCellStyle);

                        // 原料SKU
                        Cell materialSkuCode = row.createCell(cellIndex++);
                        materialSkuCode.setCellValue(processingTaskMaterial.getMaterialSkuCode());
                        materialSkuCode.setCellStyle(baseCellStyle);

                        // 原料规格
                        Cell materialSkuSpec = row.createCell(cellIndex++);
                        materialSkuSpec.setCellValue(processingTaskMaterial.getMaterialSkuUnitDesc());
                        materialSkuSpec.setCellStyle(baseCellStyle);

                        // 原料批次
                        Cell materialSkuPurchaseBatch = row.createCell(cellIndex++);
                        materialSkuPurchaseBatch.setCellValue(processingTaskMaterialReceiveRecord.getMaterialSkuPurchaseBatch());
                        materialSkuPurchaseBatch.setCellStyle(baseCellStyle);

                        if (WmsConstant.XIANMU_TENANT_ID.equals(tenantId)) {
                            // 库位编码
                            Cell materialSkuCabinetCode = row.createCell(cellIndex++);
                            materialSkuCabinetCode.setCellValue(processingTaskMaterialReceiveRecord.getMaterialSkuCabinetCode());
                            materialSkuCabinetCode.setCellStyle(baseCellStyle);
                        }

                        // 原料生止保质期
                        Cell materialFromTo = row.createCell(cellIndex++);
                        materialFromTo.setCellValue(DateUtil.formatYmdDate(processingTaskMaterialReceiveRecord.getMaterialSkuProductionDate())
                                + TO_STRING
                                + DateUtil.formatYmdDate(processingTaskMaterialReceiveRecord.getMaterialSkuQualityDate()));
                        materialFromTo.setCellStyle(baseCellStyle);

                        // 领料数量
                        Cell materialSkuReceiveQuantity = row.createCell(cellIndex++);
                        materialSkuReceiveQuantity.setCellValue(processingTaskMaterialReceiveRecord.getMaterialSkuReceiveQuantity());
                        materialSkuReceiveQuantity.setCellStyle(baseCellStyle);

                        // 领料总重(领料数量 * SKU重量计算出)
                        Cell materialSkuReceiveWeight = row.createCell(cellIndex++);
                        // 原料SKU重量
                        BigDecimal skuWeight = processingTaskMaterialReceiveRecord.getMaterialSkuWeight();
                        BigDecimal receiveTotalWeight = skuWeight.multiply(
                                new BigDecimal(processingTaskMaterialReceiveRecord.getMaterialSkuReceiveQuantity()));
                        materialSkuReceiveWeight.setCellValue(receiveTotalWeight +
                                (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                    KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                        materialSkuReceiveWeight.setCellStyle(baseCellStyle);

                        // 废料损耗
                        BigDecimal wasteLossWeight = processingTaskMaterialReceiveRecord.getWasteLossWeight();
                        // 规格损耗
                        BigDecimal specLossWeight = processingTaskMaterialReceiveRecord.getSpecLossWeight();
                        // 原料剩余重量
                        BigDecimal materialSkuWeight = processingTaskMaterialReceiveRecord.getMaterialSkuRemainWeight();
                        // 空值处理
                        wasteLossWeight = Objects.nonNull(wasteLossWeight) ? wasteLossWeight : new BigDecimal(DOUBLE_ZERO_STRING);
                        specLossWeight = Objects.nonNull(specLossWeight) ? specLossWeight : new BigDecimal(DOUBLE_ZERO_STRING);
                        materialSkuWeight = Objects.nonNull(materialSkuWeight) ? materialSkuWeight : new BigDecimal(DOUBLE_ZERO_STRING);

                        // 规格损耗
                        Cell specLossWeightCell = row.createCell(cellIndex++);
                        specLossWeightCell.setCellValue(specLossWeight +
                                (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                    KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                        specLossWeightCell.setCellStyle(baseCellStyle);

                        // 废料损耗
                        Cell wasteLossWeightCell = row.createCell(cellIndex++);
                        wasteLossWeightCell.setCellValue(wasteLossWeight +
                                (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                    KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                        wasteLossWeightCell.setCellStyle(baseCellStyle);

                        // 原料剩余重量
                        Cell materialSkuRemainWeight = row.createCell(cellIndex++);
                        materialSkuRemainWeight.setCellValue(materialSkuWeight +
                                (ProcessingTaskTypeEnum.SKU_ASSEMBLY.equalsCode(processingTask.getType()) ?
                                    KILOGRAM_STRING : processingTaskMaterial.getMaterialSkuUnit()));
                        materialSkuRemainWeight.setCellStyle(baseCellStyle);

                        // 原料库存返还
                        Cell materialSkuRestoreQuantity = row.createCell(cellIndex++);
                        materialSkuRestoreQuantity.setCellValue(processingTaskMaterialReceiveRecord.getMaterialSkuRestoreQuantity());
                        materialSkuRestoreQuantity.setCellStyle(baseCellStyle);

                        index++;
                    }
                }
            }
        }
    }

    /**
     * 合并加工任务成品公共单元格
     * @param sheet 工作表
     * @param index 索引值
     * @param size 明细list大小
     * @param lastColIndex 最后一列的索引值
     */
    private void mergedProductRecordRegion(Sheet sheet, Integer index, Integer size,
                                           Integer startColIndex, Integer lastColIndex) {
        for (int i = startColIndex; i <= lastColIndex; i++) {
            sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, i, i));
        }
    }

    private ProcessingTaskOrderImportResDTO readFile(InputStream inputStream,
                                                    Integer warehouseNo,
                                                     Long tenantId) {
        try {
            List<BatchHelpOrderVO> batchHelpOrderVOList = parseToBatchHelpOrderVO(inputStream);

            List<BatchHelpOrderVO> failList = new ArrayList<>();
            List<BatchHelpOrderVO> successList = new ArrayList<>();

            // 校验获取失败行、成功行
            Map<String, String> realFailMsg = new HashMap<>();
            dealFailAndSuccessList(batchHelpOrderVOList, failList, successList, warehouseNo, realFailMsg);

            ProcessingTaskOrderImportResDTO result = new ProcessingTaskOrderImportResDTO();
            result.setIsSuccess(successList.size() == 0 ? OrderImportIsSuccessEnum.ALL_FAIL.getValue() :
                    (failList.size() == 0 ? OrderImportIsSuccessEnum.ALL_SUCCESS.getValue() :
                            OrderImportIsSuccessEnum.PART_SUCCUESS.getValue()));
            result.setSuccessCount(successList.size());
            result.setFailCount(failList.size());

            // 失败处理，生成异常文件上传
            if (!CollectionUtils.isEmpty(failList)){
                String failFileName = "喜茶加工导入失败文件" + DateUtil.formatDateBasic(LocalDateTime.now()) + ".xls";
                String failFileUrl = generatorOssFileAddress(failFileName, failList);
                result.setFailFileAddress(failFileUrl);
                String failMsg = realFailMsg.values().stream()
                        .distinct()
                        .findFirst()
                        .orElse("喜茶加工导入失败");
                result.setRealFailMsg(failMsg);
                result.setFileMsg("存在失败订单");
            }

            // 成功处理
            if (!CollectionUtils.isEmpty(successList)) {
                List<ProcessingTaskOrderCreateReqDTO> dataList = convertList(warehouseNo, successList);
                result.setCreateReqDTOList(dataList);
            }

            return result;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "解析导入文件失败，请检查是否导入错误文件";
            log.error(errorMsg, e);
            throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
        }
    }

    private ProcessingTaskImportResDTO readOrderFile(InputStream inputStream,
                                                     Integer warehouseNo, Long tenantId) {
        try {
            List<BatchHelpOrderVO> batchHelpOrderVOList = parseToBatchHelpOrderVO(inputStream);

            List<BatchHelpOrderVO> failList = new ArrayList<>();
            List<BatchHelpOrderVO> successList = new ArrayList<>();

            // 校验获取失败行、成功行
            Map<String, String> realFailMsg = new HashMap<>();
            dealFailAndSuccessList(batchHelpOrderVOList, failList, successList, warehouseNo, realFailMsg);

            ProcessingTaskImportResDTO result = new ProcessingTaskImportResDTO();
            result.setIsSuccess(successList.size() == 0 ? OrderImportIsSuccessEnum.ALL_FAIL.getValue() :
                    (failList.size() == 0 ? OrderImportIsSuccessEnum.ALL_SUCCESS.getValue() :
                            OrderImportIsSuccessEnum.PART_SUCCUESS.getValue()));
            result.setSuccessCount(successList.size());
            result.setFailCount(failList.size());

            // 失败处理，生成异常文件上传
            if (!CollectionUtils.isEmpty(failList)){
                String failFileName = "喜茶加工导入失败文件" + DateUtil.formatDateBasic(LocalDateTime.now()) + ".xls";
                String failFileUrl = generatorOssFileAddress(failFileName, failList);
                result.setFailFileAddress(failFileUrl);
                String failMsg = realFailMsg.values().stream()
                        .distinct()
                        .findFirst()
                        .orElse("喜茶加工导入失败");
                result.setRealFailMsg(failMsg);
                result.setFileMsg("存在失败订单");
            }

            // 成功处理
            if (!CollectionUtils.isEmpty(successList)) {
                List<ProcessingTaskOrderCreateReqDTO> dataList = convertList(warehouseNo, successList);
                result.setCreateReqDTOList(dataList);
            }

            return result;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "解析导入文件失败，请检查是否导入错误文件";
            log.error(errorMsg, e);
            throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
        }
    }

    private ProcessingTaskImportResDTO readProductFile(InputStream inputStream, Integer type,
                                                       Integer warehouseNo, Long tenantId) {
        try {
            List<BatchProductVO>  dataList = extractedFile2Data(inputStream);

            List<BatchProductVO> failList = new ArrayList<>();
            List<BatchProductVO> successList = new ArrayList<>();

            List<String> skuCodeList = null;
            List<Long> skuSaasIdList = null;
            Boolean isXmTenant = WmsConstant.XIANMU_TENANT_ID.equals(tenantId);
            if (isXmTenant) {
                skuCodeList = dataList.stream()
                        .filter(BatchProductVO::getGoOnFlag)
                        .map(BatchProductVO::getProductSkuCode)
                        .distinct()
                        .collect(Collectors.toList());
            } else {
                skuSaasIdList = dataList.stream()
                        .filter(BatchProductVO::getGoOnFlag)
                        .map(s -> LongUtil.getValueCatch(s.getProductSkuCode()))
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
            }

            // saasId处理
            Map<Long, Product> productMapBySaasId = Maps.newHashMap();;
            if (!CollectionUtils.isEmpty(skuSaasIdList)) {
                //参数校验 查询成品sku信息
                productMapBySaasId = productRepository
                        .mapProductsBySaasSkuIdOnlyGoods(warehouseNo.longValue(), skuSaasIdList);
                skuCodeList = productMapBySaasId.values()
                        .stream()
                        .map(Product::getSku)
                        .distinct()
                        .collect(Collectors.toList());
            }

            Map<String, Product> productMap = Maps.newHashMap();
            Map<String, List<ProcessingConfig>> skuConfigMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(skuCodeList)){
                //参数校验 查询成品sku信息
                productMap = productRepository.mapProductsBySkusOnlyGoods(warehouseNo.longValue(), skuCodeList);
                //参数校验 查询加工规则信息
                skuConfigMap = processingConfigRepository.mapByWarehouseAndProductSkuCodeListAndType(
                        warehouseNo, skuCodeList, type);
            }

            for (BatchProductVO batchProductVO : dataList) {
                String sku;
                if (isXmTenant) {
                    sku = batchProductVO.getProductSkuCode();
                } else {
                    Product product = productMapBySaasId.get(
                            LongUtil.getValueCatch(batchProductVO.getProductSkuCode()));
                    sku = product == null ? null : product.getSku();
                }
                if (!batchProductVO.getGoOnFlag()){
                    failList.add(batchProductVO);
                    continue;
                }
                if (!productMap.containsKey(sku)) {
                    batchProductVO.setResult(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                            "成品sku不存在" : "成品货品ID不存在");
                    failList.add(batchProductVO);
                    continue;
                }
                if (!skuConfigMap.containsKey(sku)) {
                    batchProductVO.setResult("未查询到生效中的加工规则");
                    failList.add(batchProductVO);
                    continue;
                }

                // 校验加工比例
                List<ProcessingConfig> processingConfigList = skuConfigMap.get(sku);
                if (CollectionUtils.isEmpty(processingConfigList)) {
                    batchProductVO.setResult("未查询到生效中的加工规则.");
                    failList.add(batchProductVO);
                    continue;
                }
                Boolean existNeedQuantityError = false;
                String existNeedQuantityErrorMsg = "";
                for (ProcessingConfig processingConfig : processingConfigList) {
                    if (ProcessingConfigTypeEnum.ASSEMBLY_PROCESSING.equalsCode(type) &&
                            batchProductVO.getProductSkuNeedQuantity() % processingConfig.getProductSkuRatioNum() != 0) {
                        existNeedQuantityError = true;
                        existNeedQuantityErrorMsg = "加工的数量非加工规则配置中的成品比例倍数:" + processingConfig.getProductSkuRatioNum();
                        break;
                    }
                }
                if (existNeedQuantityError){
                    batchProductVO.setResult(existNeedQuantityErrorMsg);
                    failList.add(batchProductVO);
                    continue;
                }

                successList.add(batchProductVO);
            }

            ProcessingTaskImportResDTO result = new ProcessingTaskImportResDTO();
            result.setIsSuccess(successList.size() == 0 ? OrderImportIsSuccessEnum.ALL_FAIL.getValue() :
                    (failList.size() == 0 ? OrderImportIsSuccessEnum.ALL_SUCCESS.getValue() :
                            OrderImportIsSuccessEnum.PART_SUCCUESS.getValue()));
            result.setSuccessCount(successList.size());
            result.setFailCount(failList.size());

            // 失败处理，生成异常文件上传
            if (!CollectionUtils.isEmpty(failList)){
                // 生成异常文件上传
                String failFileName = "加工导入失败文件" + DateUtil.formatDateBasic(LocalDateTime.now()) + ".xls";
                DownloadCenterRecordDTO downloadCenterRecordDTO = new DownloadCenterRecordDTO();
                downloadCenterRecordDTO.setUserId(LoginInfoThreadLocal.getCurrentUserId());
                downloadCenterRecordDTO.setTenantId(LoginInfoThreadLocal.getTenantId());
                downloadCenterRecordDTO.setSource(WmsConstant.XIANMU_TENANT_ID.equals(LoginInfoThreadLocal.getTenantId()) ?
                        DownloadCenterEnum.RequestSource.XIANMU : DownloadCenterEnum.RequestSource.SAAS);
                downloadCenterRecordDTO.setBizType(WmsConstant.XIANMU_TENANT_ID.equals(LoginInfoThreadLocal.getTenantId()) ?
                        FileDownloadCenterRecordEnum.PROCESSING_TASK_IMPORT_FAIL_DOWNLOAD_XM.getType() :
                        FileDownloadCenterRecordEnum.PROCESSING_TASK_IMPORT_FAIL_DOWNLOAD_SAAS.getType());
                downloadCenterRecordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.THREE_DAY);
                downloadCenterRecordDTO.setFileName(failFileName);

                // 异步写入文件
                Long resId = DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, downloadCenterRecordDTO)
                        .asyncWriteWithOssResp(null, (x) -> {
                            OssUploadResult uploadResult = this.generatorOssFileAddressWithProduct(
                                    failFileName, failList, tenantId);

                            // 2、返回OSS文件地址
                            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
                            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
                            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
                            return downloadCenterOssRespDTO;
                        });

                result.setFailFileResId(resId);
//                result.setFailFileAddress(ossUploadResult.getUrl());
//                result.setFailFileOssUploadResult(ossUploadResult);
                result.setFileMsg("存在失败商品");
            }

            // 成功处理
            if (!CollectionUtils.isEmpty(successList)) {
                List<ProcessingTaskOrderCreateReqDTO> createReqDTOList = this.convertListWithProduct(
                        warehouseNo, type, successList, tenantId, productMapBySaasId);
                result.setCreateReqDTOList(createReqDTOList);
            }

            return result;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = "解析导入文件失败，请检查是否导入错误文件";
            log.error(errorMsg, e);
            throw new BizException(errorMsg, ErrorCodeNew.PARAM_ERROR);
        }
    }

    private static List<BatchProductVO> extractedFile2Data(InputStream inputStream) throws IOException {
        int DEFAULT_SHEET = 0;
        int DEFAULT_SHEET_TITLE_ROW = 0;
        int EXCEL_ROW_LIMIT = 100;
        List<BatchProductVO> dataList = new ArrayList<>();

        //读取文件
        Workbook workbook = WorkbookFactory.create(inputStream);
        Sheet sheet = workbook.getSheetAt(DEFAULT_SHEET);
        //处理加工商品数量信息
        int dataRow = DEFAULT_SHEET_TITLE_ROW + 1;
        int lastRowNum = sheet.getLastRowNum();
        int firstRowNum = sheet.getFirstRowNum();

        if (lastRowNum < DEFAULT_SHEET_TITLE_ROW || firstRowNum > 0){
            throw new BizException("请下载商品加工模板进行操作", ErrorCodeNew.PARAM_ERROR);
        }
        String row0WithCell0 = ExcelUtils.getCellValueStr(sheet.getRow(0).getCell(0));
        String row0WithCell1 = ExcelUtils.getCellValueStr(sheet.getRow(0).getCell(1));
        if ((!Objects.equals(row0WithCell0, "成品sku")  && !Objects.equals(row0WithCell0,"成品货品ID")
                && !Objects.equals(row0WithCell0,"成品ID"))
                || !Objects.equals(row0WithCell1, "加工数量")){
            throw new BizException("请下载商品加工模板进行操作", ErrorCodeNew.PARAM_ERROR);
        }


        while (dataRow <= sheet.getLastRowNum()) {
            Row data = sheet.getRow(dataRow++);
            if (data == null) {
                continue;
            }
            BatchProductVO vo = new BatchProductVO();
            vo.setGoOnFlag(true);
            String sku = ExcelUtils.getCellValueStr(data.getCell(0));
            vo.setProductSkuCode(sku);
            try {
                String amountStr = ExcelUtils.getCellValueStr(data.getCell(1));
                vo.setProductSkuNeedQuantityStr(amountStr);
                //空行直接跳过
                if (StrUtil.isBlank(sku) && StrUtil.isBlank(amountStr)) {
                    continue;
                }
                if (StrUtil.isBlank(sku)) {
                    vo.setResult("成品信息为空");
                    vo.setGoOnFlag(false);
                    dataList.add(vo);
                    continue;
                }
                if (StrUtil.isBlank(amountStr)) {
                    vo.setResult("加工数量为空");
                    vo.setGoOnFlag(false);
                    dataList.add(vo);
                    continue;
                }
                Integer amount = Integer.valueOf(amountStr.split("\\.")[0]);
                vo.setProductSkuNeedQuantity(amount);
            } catch (Exception e) {
                log.warn("加工数量内容异常导致无法解析", e);
                vo.setResult("加工数量异常");
                vo.setGoOnFlag(false);
            }
            dataList.add(vo);
        }

        if (CollectionUtils.isEmpty(dataList)){
            throw new BizException("无商品加工数据可处理", ErrorCodeNew.PARAM_ERROR);
        }

        if (dataList.size() > EXCEL_ROW_LIMIT){
            throw new BizException("单次导入数量最多100条", ErrorCodeNew.PARAM_ERROR);
        }

        return dataList;
    }

    private void dealFailAndSuccessList(List<BatchHelpOrderVO> batchHelpOrderVOList,
                                        List<BatchHelpOrderVO> failList,
                                        List<BatchHelpOrderVO> successList,
                                        Integer warehouseNo,
                                        Map<String, String> realFailMsg) {

        List<String> orderNoList = batchHelpOrderVOList.stream()
                .map(BatchHelpOrderVO::getHtOrderCode)
                .distinct()
                .collect(Collectors.toList());
        List<ProcessingTaskProductOrderRecord> productOrderRecordList = productOrderRecordRepository
                .listBySourceIdList(orderNoList);
        Set<String> orderSet = productOrderRecordList.stream()
                .map(ProcessingTaskProductOrderRecord::getSourceId)
                .collect(Collectors.toSet());

        for (BatchHelpOrderVO batchHelpOrderVO : batchHelpOrderVOList) {
            // 商品编码列表
            List<String> skuCodeList = batchHelpOrderVO.getOrderItemList()
                    .stream()
                    .filter(dto -> dto.getProductSkuNeedQuantity() > 0)
                    .map(ProcessingTaskOrderCreateReqDTO::getProductSkuCode)
                    .distinct()
                    .collect(Collectors.toList());

            // 无SKU数量则返回
            if (CollectionUtils.isEmpty(skuCodeList)){
                realFailMsg.put(batchHelpOrderVO.getHtOrderCode(), "请上传加工商品数据");
                batchHelpOrderVO.setResult("请上传加工商品数据");
                failList.add(batchHelpOrderVO);
                continue;
            }

            // 外部订单号
            if (StringUtils.isEmpty(batchHelpOrderVO.getHtOrderCode())){
                realFailMsg.put(batchHelpOrderVO.getHtOrderCode(), "外部订单号必填");
                batchHelpOrderVO.setResult("外部订单号必填");
                failList.add(batchHelpOrderVO);
                continue;
            }
            if (orderSet.contains(batchHelpOrderVO.getHtOrderCode())){
                realFailMsg.put(batchHelpOrderVO.getHtOrderCode(), "订单当前已导入生成加工任务，请确认");
                batchHelpOrderVO.setResult("订单当前已导入生成加工任务，请确认");
                failList.add(batchHelpOrderVO);
                continue;
            }

            // 仓库
            if (warehouseNo == null){
                realFailMsg.put(batchHelpOrderVO.getHtOrderCode(), "请填写完整信息");
                batchHelpOrderVO.setResult("请填写完整信息");
                failList.add(batchHelpOrderVO);
                continue;
            }

            // 成品SKU配置
            List<ProcessingConfig> processingConfigs = processingConfigRepository
                    .listByWarehouseAndProductSkuCodeListAndType(warehouseNo, skuCodeList, ProcessingTaskTypeEnum.ORDER_PROCESSING.getValue());
            if (CollectionUtils.isEmpty(processingConfigs) ||
                    processingConfigs.stream()
                            .map(ProcessingConfig::getProductSkuCode)
                            .distinct()
                            .collect(Collectors.toList()).size() != skuCodeList.size()){

                List<String> existSkuCodeList = processingConfigs.stream()
                        .map(ProcessingConfig::getProductSkuCode)
                        .distinct()
                        .collect(Collectors.toList());
                skuCodeList.removeAll(existSkuCodeList);
                realFailMsg.put(batchHelpOrderVO.getHtOrderCode(), "商品：" + skuCodeList + "加工规则不存在");
                batchHelpOrderVO.setResult("商品：" + skuCodeList + "加工规则不存在");

                failList.add(batchHelpOrderVO);
                continue;
            }

            successList.add(batchHelpOrderVO);
        }
    }

    private List<ProcessingTaskOrderCreateReqDTO> convertList(Integer warehouseNo, List<BatchHelpOrderVO> batchHelpOrderVOList) {
        if (CollectionUtils.isEmpty(batchHelpOrderVOList)) {
            return new ArrayList<>();
        }

        List<ProcessingTaskOrderCreateReqDTO> createReqDTOList = new ArrayList<>();

        for (BatchHelpOrderVO batchHelpOrderVO : batchHelpOrderVOList) {
            for (ProcessingTaskOrderCreateReqDTO createReqDTO : batchHelpOrderVO.getOrderItemList()) {

                ProcessingTaskOrderCreateReqDTO reqDTO1 = new ProcessingTaskOrderCreateReqDTO();

                reqDTO1.setType(ProcessingTaskTypeEnum.ORDER_PROCESSING.getValue());
                reqDTO1.setWarehouseNo(warehouseNo);
//                reqDTO.setProcessingTaskCode();
                reqDTO1.setSourceId(batchHelpOrderVO.getHtOrderCode());
                reqDTO1.setProductSkuCode(createReqDTO.getProductSkuCode());
                reqDTO1.setProductSkuNeedQuantity(createReqDTO.getProductSkuNeedQuantity());

                createReqDTOList.add(reqDTO1);
            }
        }

        return createReqDTOList;
    }

    private List<ProcessingTaskOrderCreateReqDTO> convertListWithProduct(Integer warehouseNo,
                                                                         Integer type,
                                                                         List<BatchProductVO> batchProductVOList,
                                                                         Long tenantId,
                                                                         Map<Long, Product> productMapBySaasId) {
        if (CollectionUtils.isEmpty(batchProductVOList)) {
            return new ArrayList<>();
        }

        List<ProcessingTaskOrderCreateReqDTO> createReqDTOList = new ArrayList<>();

        for (BatchProductVO batchProductVO : batchProductVOList) {
            ProcessingTaskOrderCreateReqDTO reqDTO = new ProcessingTaskOrderCreateReqDTO();

            reqDTO.setType(type);
            reqDTO.setWarehouseNo(warehouseNo);
            reqDTO.setProductSkuInput(batchProductVO.getProductSkuCode());
            if (WmsConstant.XIANMU_TENANT_ID.equals(tenantId)){
                reqDTO.setProductSkuCode(batchProductVO.getProductSkuCode());
            } else {
                Product product = productMapBySaasId.get(
                        Long.valueOf(batchProductVO.getProductSkuCode())
                );
                reqDTO.setProductSkuCode(product.getSku());
                reqDTO.setProductSkuSaasId(Long.valueOf(batchProductVO.getProductSkuCode()));
            }

            reqDTO.setProductSkuNeedQuantity(batchProductVO.getProductSkuNeedQuantity());

            createReqDTOList.add(reqDTO);
        }

        return createReqDTOList;
    }

    //导入数据处理
    private List<BatchHelpOrderVO> parseToBatchHelpOrderVO(InputStream inputStream) {
        Integer userType = 0;
        int DEFAULT_SHEET = 0;
        List<BatchHelpOrderVO> result = new ArrayList<>();
        try {
            //读取文件
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(DEFAULT_SHEET);
            // 处理sku位置信息
            // 如果是喜茶批量代下单，处理数据
            int startIndex = userType == HelpOrderUserType.HEYTEA.getCode() ? 7 : 6;
            int rowStartIndex = userType == HelpOrderUserType.HEYTEA.getCode() ? 1 : 0;
            List<String> skuList = new ArrayList<>();
            Row title = sheet.getRow(rowStartIndex);
            boolean reLoad = false;
            int cellIndex = startIndex;
            while (cellIndex < title.getLastCellNum()) {
                String cellStringValue = ExcelUtils.getCellValueStr(title.getCell(cellIndex++));
                if (ObjectUtils.isEmpty(cellStringValue)) {
                    break;
                }
                String sku = cellStringValue;
                if ("批量下单结果反馈".equals(sku)) {
                    reLoad = true;
                    break;
                }
                skuList.add(sku);
            }

            //处理下单数量信息
            int rowIndex = userType == HelpOrderUserType.HEYTEA.getCode() ? 2 : 1;
            while (rowIndex <= sheet.getLastRowNum()) {
                Row data = sheet.getRow(rowIndex++);
                if (data == null) {
                    continue;
                }
                BatchHelpOrderVO vo = new BatchHelpOrderVO();
                vo.setGoOnFlag(true);
                //基础信息
/*                if (data.getCell(0).getCellType() == Cell.CELL_TYPE_BLANK) {
                    continue;
                }*/

                Object o1 = ExcelUtils.getCellValueStr(data.getCell(0));
                vo.setMId(Long.parseLong(!StringUtils.isEmpty(o1) ? o1.toString().split("\\.")[0] : "0"));

                Object o2 = ExcelUtils.getCellValue(data.getCell(1));
                vo.setMname(o2.toString());

                Object o3 = ExcelUtils.getCellValue(data.getCell(2));
                vo.setSize(o3.toString());

                //地址信息
                ContactVO contact = new ContactVO();
                contact.setContact(ExcelUtils.getCellValueStr(data.getCell(3)).toString());
                contact.setPhone(ExcelUtils.getCellValueStr(data.getCell(4)));
                contact.setWholeAddress(ExcelUtils.getCellValueStr(data.getCell(5)).toString());
                vo.setContactVO(contact);

                if (userType == HelpOrderUserType.HEYTEA.getCode()) {
                    vo.setUserType(userType);
                    // 喜茶订单号
                    Object htOrderCode = ExcelUtils.getCellValueStr(data.getCell(6));
                    vo.setHtOrderCode(htOrderCode.toString().trim());
                }

                //订单项
                List<ProcessingTaskOrderCreateReqDTO> orderItemList = new ArrayList<>();
                int dataIndex = startIndex;

                while (dataIndex < title.getLastCellNum() - (reLoad ? 1 : 0)) {
                    int index = dataIndex - startIndex;
                    if (index >= skuList.size()){
                        vo.setResult("导入订单SKU模版有问题，请检查SKU标题");
                        vo.setGoOnFlag(false);
                    }

                    ProcessingTaskOrderCreateReqDTO orderItem = new ProcessingTaskOrderCreateReqDTO();
                    orderItem.setProductSkuCode(skuList.get(index));
                    orderItem.setProductSkuNeedQuantity(0);
                    orderItemList.add(orderItem);
                    if (StringUtils.isEmpty(ExcelUtils.getCellValueStr(data.getCell(dataIndex)))) {
                        dataIndex++;
                        continue;
                    }

                    try {
                        String oo = ExcelUtils.getCellValueStr(data.getCell(dataIndex));
                        int amount = Integer.parseInt(oo.split("\\.")[0]);
                        orderItem.setProductSkuNeedQuantity(amount);
                    } catch (Exception e) {
                        vo.setResult("订单数量内容有问题，请检查订单数量");
                        vo.setGoOnFlag(false);
                    }

                    dataIndex++;
                }

                vo.setOrderItemList(orderItemList);
                result.add(vo);
            }

        } catch (Exception e) {
            log.error("批量代下单异常：", e);
            throw new DefaultServiceException("导入异常");
        }

        return result;
    }

    /**
     * 生成Oss文件
     *
     * @param fileNameSuffix
     * @param failList
     * @return
     */
    public String generatorOssFileAddress(String fileNameSuffix,
                                          List<BatchHelpOrderVO> failList) {
        File file = null;
        FileOutputStream out = null;
        Workbook workbook = null;

        try {
            workbook = new HSSFWorkbook();
            String fileName = System.getProperty("java.io.tmpdir") + File.separator + fileNameSuffix;
            file = new File(fileName);
            out = new FileOutputStream(file);

            //文本格式
            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setDataFormat(workbook.createDataFormat().getFormat("@"));

            Sheet sheet = workbook.createSheet("批量下单");

            Row title = sheet.createRow(0);
            title.setRowStyle(textStyle);
            title.createCell(0).setCellValue("序号");
            title.createCell(1).setCellValue("mid（客户编号）");
            title.createCell(2).setCellValue("门店名称*");
            title.createCell(3).setCellValue("客户类型*");
            title.createCell(4).setCellValue("联系人*");
            title.createCell(5).setCellValue("联系方式*");
            title.createCell(6).setCellValue("配送地址*");

            //数据处理

            if (!CollectionUtils.isEmpty(failList)) {
                // 判断是否是喜茶订单
                BatchHelpOrderVO fvo = failList.get(0);
                Integer userType = fvo.getUserType();
                int index = 7;
                if (Objects.equals(HelpOrderUserType.HEYTEA.getCode(), userType)) {
                    title.createCell(7).setCellValue("喜茶订单号");
                    index = 8;
                }

                for (ProcessingTaskOrderCreateReqDTO oi : fvo.getOrderItemList()) {
                    title.createCell(index).setCellValue(oi.getProductSkuCode());
                    index++;
                }
                title.createCell(index).setCellValue("批量下单结果反馈");

                //数据填充
                int rowIndex = 1;
                for (BatchHelpOrderVO vo : failList) {
                    Row row = sheet.createRow(rowIndex);
                    row.createCell(0).setCellValue(rowIndex);
                    Cell cell = row.createCell(1);
                    cell.setCellStyle(textStyle);
                    cell.setCellValue(vo.getMId());
                    Cell c1 = row.createCell(2);
                    c1.setCellStyle(textStyle);
                    c1.setCellValue(vo.getMname());
                    Cell c2 = row.createCell(3);
                    c2.setCellStyle(textStyle);
                    c2.setCellValue(vo.getSize());
                    Cell c3 = row.createCell(4);
                    c3.setCellStyle(textStyle);
                    c3.setCellValue(vo.getContactVO().getContact());
                    Cell c4 = row.createCell(5);
                    c4.setCellStyle(textStyle);
                    c4.setCellValue(vo.getContactVO().getPhone());
                    Cell c5 = row.createCell(6);
                    c5.setCellStyle(textStyle);
                    c5.setCellValue(vo.getContactVO().getWholeAddress());
                    index = 7;
                    if (Objects.equals(HelpOrderUserType.HEYTEA.getCode(), userType)) {
                        Cell c6 = row.createCell(7);
                        c6.setCellStyle(textStyle);
                        c6.setCellValue(vo.getHtOrderCode());
                        index = 8;
                    }

                    for (ProcessingTaskOrderCreateReqDTO oi : vo.getOrderItemList()) {
                        row.createCell(index).setCellValue(oi.getProductSkuNeedQuantity());
                        index++;
                    }
                    Cell indexCell = row.createCell(index);
                    indexCell.setCellStyle(textStyle);
                    indexCell.setCellValue(vo.getResult());

                    rowIndex++;
                }
            }

            // excel输出到文件
            workbook.write(out);

            OssUploadResult ossUploadResult = OssUploadUtil.uploadExpireThreeDay(fileNameSuffix, file);
            return ossUploadResult.getUrl();
        } catch (IOException e) {
            log.error("生成失败导出文件异常", e);
            throw new DefaultServiceException("生成失败导出文件异常");
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭workbook失败", e);
                }
            }
            if (out != null){
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭out失败", e);
                }
            }
            if (file != null){
                file.deleteOnExit();
            }
        }
    }

    public OssUploadResult generatorOssFileAddressWithProduct(String fileNameSuffix,
                                                              List<BatchProductVO> failList,
                                                              Long tenantId) {
        File file = null;
        FileOutputStream out = null;
        Workbook workbook = null;

        try {
            workbook = new HSSFWorkbook();
            String fileName = System.getProperty("java.io.tmpdir") + File.separator + fileNameSuffix;
            file = new File(fileName);
            out = new FileOutputStream(file);

            //文本格式
            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setDataFormat(workbook.createDataFormat().getFormat("@"));

            Sheet sheet = workbook.createSheet("批量处理结果");

            Row title0 = sheet.createRow(0);
            title0.setRowStyle(textStyle);
            title0.createCell(0).setCellValue(WmsConstant.XIANMU_TENANT_ID.equals(tenantId) ?
                    "成品sku" : "成品货品ID");
            title0.createCell(1).setCellValue("加工数量");
            title0.createCell(2).setCellValue("备注");

            //数据处理
            if (!CollectionUtils.isEmpty(failList)) {
                //数据填充
                int dataRow = 1;
                for (BatchProductVO vo : failList) {
                    Row row = sheet.createRow(dataRow);
                    Cell cell0 = row.createCell(0);
                    cell0.setCellStyle(textStyle);
                    if(vo.getProductSkuCode() != null){
                        cell0.setCellValue(vo.getProductSkuCode());
                    }
                    Cell cell1 = row.createCell(1);
                    cell1.setCellStyle(textStyle);
                    if (vo.getProductSkuNeedQuantityStr() != null){
                        cell1.setCellValue(vo.getProductSkuNeedQuantityStr());
                    }
                    Cell cell2 = row.createCell(2);
                    cell2.setCellStyle(textStyle);
                    if (vo.getResult() != null){
                        cell2.setCellValue(vo.getResult());
                    }
                    dataRow++;
                }
            }

            // excel输出到文件
            workbook.write(out);

            return OssUploadUtil.uploadExpireThreeDay(fileNameSuffix, file);
        } catch (IOException e) {
            log.error("生成失败导出文件异常", e);
            throw new DefaultServiceException("生成失败导出文件异常");
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭workbook失败", e);
                }
            }
            if (out != null){
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭out失败", e);
                }
            }
            if (file != null){
                file.deleteOnExit();
            }
        }
    }

}
