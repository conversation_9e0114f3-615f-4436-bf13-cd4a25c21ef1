package net.summerfarm.wms.application.crosswarehouse.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillment.FulfillmentOrderChangeStoreDetailMessage;
import net.summerfarm.ofc.client.common.message.fulfillment.FulfillmentOrderChangeStoreMessage;
import net.summerfarm.wms.application.crosswarehouse.model.CrossRemarkJson;
import net.summerfarm.wms.domain.batch.enums.OperationType;
import net.summerfarm.wms.domain.crosswarehouse.domainobject.CrossWarehouseSortInfo;
import net.summerfarm.wms.domain.crosswarehouse.repository.CrossWarehouseSortInfoRepository;
import net.summerfarm.wms.domain.instore.domainobject.StockTaskStorage;
import net.summerfarm.wms.domain.instore.enums.StockTaskStorageReceivingStateEnum;
import net.summerfarm.wms.domain.instore.repository.StockTaskStorageQueryRepository;
import net.summerfarm.wms.facade.wnc.WarehouseLogisticsFacade;
import net.summerfarm.wms.facade.wnc.WarehouseStorageFacade;
import net.summerfarm.wms.facade.wnc.dto.WarehousLogisticsCenterRespDTO;
import net.summerfarm.wms.facade.wnc.dto.WarehouseLogisticsQueryReqDTO;
import net.summerfarm.wms.initConfig.FeishuUrlConfig;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/18 11:40
 * @Version 1.0
 */
@Slf4j
@Component
public class CrossWarehouseChangeStoreService {

    @Autowired
    private CrossWarehouseSortInfoRepository crossWarehouseSortInfoRepository;

    @Autowired
    private StockTaskStorageQueryRepository stockTaskStorageQueryRepository;

    @Autowired
    private WarehouseLogisticsFacade warehouseLogisticsFacade;

    @Autowired
    private WarehouseStorageFacade warehouseStorageFacade;

    @Autowired
    private FeishuUrlConfig feishuUrlConfig;

    /**
     * 处理切仓消息
     * @param changeStoreMessage 切仓消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleChangeStore(FulfillmentOrderChangeStoreMessage changeStoreMessage){
        List<String> skuCodeList = CollectionUtils.isEmpty(changeStoreMessage.getDetailList())
                ? null
                : changeStoreMessage.getDetailList().stream().map(FulfillmentOrderChangeStoreDetailMessage::getSkuCode).distinct().collect(Collectors.toList());
        List<CrossWarehouseSortInfo> crossWarehouseSortInfoList = crossWarehouseSortInfoRepository.findByFulfillmentNoAndSkuCodeList(String.valueOf(changeStoreMessage.getFulfillmentNo()), skuCodeList);
        if (CollectionUtils.isEmpty(crossWarehouseSortInfoList)) {
            log.info("未找到切仓越库分拣明细，处理结束。 >>> {}", changeStoreMessage.getFulfillmentNo());
            return;
        }
        log.info("找到切仓越库分拣明细，开始处理。 >>> {}", JSON.toJSONString(crossWarehouseSortInfoList));
        for (CrossWarehouseSortInfo crossWarehouseSortInfo : crossWarehouseSortInfoList) {
            if (null == crossWarehouseSortInfo) {
                continue;
            }
            List<StockTaskStorage> stockTaskStorages = stockTaskStorageQueryRepository.queryStockTaskStorage(
                    crossWarehouseSortInfo.getWarehouseNo(),
                    Arrays.asList(OperationType.PURCHASE_IN.getId(), OperationType.CROSS_WAREHOUSE_IN.getId()),
                    crossWarehouseSortInfo.getPsoNo()
            );
            if (CollectionUtils.isEmpty(stockTaskStorages)) {
                log.info("未找到入库任务，处理结束。 >>> {}", crossWarehouseSortInfo.getPsoNo());
                continue;
            }
            long count = stockTaskStorages.stream().filter(o -> !StockTaskStorageReceivingStateEnum.NOT_CONFIRMED.getId().equals(o.getReceivingState())).count();
            if (count > 0) {
                log.info("{} 已存在入库任务 {} 确认到货。", crossWarehouseSortInfo.getPsoNo(), JSON.toJSONString(stockTaskStorages));
                String crossRemarkJsonString = this.buildCrossRemarkJson(crossWarehouseSortInfo, changeStoreMessage);
                log.info("{} 更新备注信息为：{}", crossWarehouseSortInfo.getId(), crossRemarkJsonString);
                crossWarehouseSortInfoRepository.updateRemarkById(crossWarehouseSortInfo.getId(), crossRemarkJsonString);
            }else {
                log.info("{} 未确认到货，可以直接更新cross表的城配仓数据。cross id {}", crossWarehouseSortInfo.getPsoNo(), crossWarehouseSortInfo.getId());
                crossWarehouseSortInfoRepository.updateStoreNoById(crossWarehouseSortInfo.getId(), changeStoreMessage.getNewStoreNo());
            }
        }
    }

    /**
     * 构建cross表的备注json
     * @param crossWarehouseSortInfo 越库分拣信息
     * @param changeStoreMessage 切仓消息
     * @return 备注json String
     */
    private String buildCrossRemarkJson(CrossWarehouseSortInfo crossWarehouseSortInfo, FulfillmentOrderChangeStoreMessage changeStoreMessage) {
        CrossRemarkJson crossRemarkJson = new CrossRemarkJson();
        crossRemarkJson.setOldStoreNo(changeStoreMessage.getOldStoreNo());
        crossRemarkJson.setNewStoreNo(changeStoreMessage.getNewStoreNo());
        String currentSkuCode = crossWarehouseSortInfo.getSku();
        if (!CollectionUtils.isEmpty(changeStoreMessage.getDetailList())) {
            for (FulfillmentOrderChangeStoreDetailMessage detail : changeStoreMessage.getDetailList()) {
                if (currentSkuCode.equals(detail.getSkuCode())) {
                    crossRemarkJson.setOldWarehouseNo(detail.getOldWarehouseNo());
                    crossRemarkJson.setNewWarehouseNo(detail.getNewWarehouseNo());
                    break;
                }
            }
        }
        crossRemarkJson.setAddTime(System.currentTimeMillis());
        return JSON.toJSONString(crossRemarkJson);
    }

    /**
     * 发送切仓告警到飞书
     */
    public void sendChangeStoreNoAlarmToFeishu(){
        LocalDateTime startCreateTime = LocalDate.now().minusDays(1L).atStartOfDay();
        LocalDateTime endCreateTime = LocalDate.now().atStartOfDay();
        List<CrossWarehouseSortInfo> crossWarehouseSortInfoList = crossWarehouseSortInfoRepository.findNotNullRemarkDataByCreateTimeBetween(startCreateTime, endCreateTime);
        if (CollectionUtils.isEmpty(crossWarehouseSortInfoList)) {
            log.info("未找到需要发送告警的数据，处理结束。");
            return;
        }
        log.info("找到需要发送告警的数据，开始处理。 >>> {}", JSON.toJSONString(crossWarehouseSortInfoList));
        List<CrossRemarkJson> crossRemarkJsonList = this.withdrawCrossRemarkJson(crossWarehouseSortInfoList);
        if (CollectionUtils.isEmpty(crossRemarkJsonList)) {
            log.info("提取数据后，未找到需要发送告警的数据，处理结束。");
            return;
        }
        //region 获取城配仓信息
        List<Integer> storeNoList = crossRemarkJsonList.stream().map(CrossRemarkJson::getOldStoreNo).distinct().collect(Collectors.toList());
        storeNoList.addAll(crossRemarkJsonList.stream().map(CrossRemarkJson::getNewStoreNo).distinct().collect(Collectors.toList()));
        List<WarehousLogisticsCenterRespDTO> storeInfoList = warehouseLogisticsFacade.queryWarehouseLogisticsList(
                WarehouseLogisticsQueryReqDTO.builder()
                .storeNos(storeNoList)
                .build());
        if (CollectionUtils.isEmpty(storeInfoList)) {
            log.info("未找到城配仓信息，处理结束。");
            return;
        }
        Map<Integer, String> storeNoAndNameMap = storeInfoList.stream().collect(Collectors.toMap(WarehousLogisticsCenterRespDTO::getStoreNo, WarehousLogisticsCenterRespDTO::getStoreName, (k1, k2) -> k1));
        //endregion

        //region 获取仓库信息
        List<Integer> warehouseNoList = crossRemarkJsonList.stream().map(o -> o.getOldWarehouseNo().intValue()).distinct().collect(Collectors.toList());
        warehouseNoList.addAll(crossRemarkJsonList.stream().map(o -> o.getNewWarehouseNo().intValue()).distinct().collect(Collectors.toList()));
        Map<Integer, String> warehouseNoAndNameMap = warehouseStorageFacade.queryWarehouseStorageList(warehouseNoList);
        //endregion

        List<String> storeChangeAlarmList = new ArrayList<>();
        List<String> warehouseChangeAlarmList = new ArrayList<>();
        for (CrossRemarkJson crossRemarkJson : crossRemarkJsonList) {
            String storeChangeAlarm = storeNoAndNameMap.get(crossRemarkJson.getOldStoreNo()) + " -> " + storeNoAndNameMap.get(crossRemarkJson.getNewStoreNo());
            if (!storeChangeAlarmList.contains(storeChangeAlarm)) {
                storeChangeAlarmList.add(storeChangeAlarm);
            }
            String warehouseChangeAlarm = warehouseNoAndNameMap.get(crossRemarkJson.getOldWarehouseNo().intValue()) + " -> " + warehouseNoAndNameMap.get(crossRemarkJson.getNewWarehouseNo().intValue());
            if (!warehouseChangeAlarmList.contains(warehouseChangeAlarm)) {
                warehouseChangeAlarmList.add(warehouseChangeAlarm);
            }
        }
        FeishuBotUtil.sendMarkdownMsgAndAtAll(feishuUrlConfig.getStoreChangeAlarmFeiShuUrl(), "操作投线任务后产生切仓，请仓库人员及时调整投线实物数据\n" +
                "城配仓变更：\n" + String.join("\n", storeChangeAlarmList) + "\n" +
                "仓库变更：\n" + String.join("\n", warehouseChangeAlarmList));
    }

    private List<CrossRemarkJson> withdrawCrossRemarkJson(List<CrossWarehouseSortInfo> crossWarehouseSortInfoList) {
        List<CrossRemarkJson> crossRemarkJsonList = new ArrayList<>();
        for (CrossWarehouseSortInfo crossWarehouseSortInfo : crossWarehouseSortInfoList) {
            if (null == crossWarehouseSortInfo) {
                continue;
            }
            CrossRemarkJson crossRemarkJson = null;
            try {
                crossRemarkJson = JSON.parseObject(crossWarehouseSortInfo.getRemark(), CrossRemarkJson.class);
            } catch (Exception e) {
                log.error("解析cross表备注信息失败，cross {}", crossWarehouseSortInfo, e);
            }
            if (null == crossRemarkJson) {
                continue;
            }
            crossRemarkJsonList.add(crossRemarkJson);
            log.info("crossRemarkJson >>> {}", JSON.toJSONString(crossRemarkJson));
        }
        return crossRemarkJsonList;
    }

}
