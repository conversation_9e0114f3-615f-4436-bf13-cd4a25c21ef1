package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskQueryResDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTask;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 加工任务响应对象转换类
 * <AUTHOR>
 * @date 2023/02/14
 */
@Mapper
public interface ProcessingTaskQueryResDTOConverter {

    ProcessingTaskQueryResDTOConverter INSTANCE = Mappers.getMapper(ProcessingTaskQueryResDTOConverter.class);

    /**
     * entity -> res
     * @param processingTask entity
     * @return res
     */
    @Mappings({
            @Mapping(target = "createTime", source = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "updateTime", source = "updateTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "finishTime", source = "finishTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    })
    ProcessingTaskQueryResDTO convert(ProcessingTask processingTask);

    /**
     * entity list
     * @param processingTaskList entity list
     * @return res list
     */
    List<ProcessingTaskQueryResDTO> convertList(List<ProcessingTask> processingTaskList);
}
