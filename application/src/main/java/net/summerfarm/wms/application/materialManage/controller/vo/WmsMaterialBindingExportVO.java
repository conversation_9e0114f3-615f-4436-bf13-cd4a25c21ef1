package net.summerfarm.wms.application.materialManage.controller.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WmsMaterialBindingExportVO implements Serializable {
    /**
     * primary key
     */
    @ExcelProperty(value = "编号", index = 0)
    private Long id;
    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库", index = 1)
    private String warehouseName;

    /**
     * sku编码
     */
    @ExcelProperty(value = "货品sku", index = 2)
    private String sku;

    /**
     * sku名称
     */
    @ExcelProperty(value = "货品名称", index = 3)
    private String skuName;

    /**
     * sku规格
     */
    @ExcelProperty(value = "货品规格", index = 4)
    private String skuSpecification;

    /**
     * 物料sku编码
     */
    @ExcelProperty(value = "物资sku", index = 5)
    private String materialSku;

    /**
     * 物料sku名称
     */
    @ExcelProperty(value = "物资名称", index = 6)
    private String materialSkuName;

    /**
     * 物料SKU规格
     */
    @ExcelProperty(value = "物资规格", index = 7)
    private String materialSkuSpecification;

    /**
     * 重复利用， 否，是
     */
    @ExcelProperty(value = "重复利用", index = 8)
    private String reuseDesc;

    /**
     * 自动出库，否，是
     */
    @ExcelProperty(value = "自动出库", index = 9)
    private String autoOutboundDesc;

    /**
     * sku比例
     */
    @ExcelProperty(value = "货品比例", index = 10)
    private BigDecimal skuRatio;

    /**
     * 物料sku比例
     */
    @ExcelProperty(value = "物资比例", index = 11)
    private BigDecimal materialSkuRatio;

    /**
     * 状态，0：无效，1：有效
     */
    @ExcelProperty(value = "状态", index = 12)
    private String statusDesc;

}
