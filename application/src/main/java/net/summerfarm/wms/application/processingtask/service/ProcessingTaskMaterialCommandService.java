package net.summerfarm.wms.application.processingtask.service;

import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialReceiveReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialRemainUpdateReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialRestoreReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialWasteLossWeightReqDTO;
import net.xianmu.common.result.CommonResult;

public interface ProcessingTaskMaterialCommandService {


    /**
     * 物流领用
     *
     * @param reqDTO
     */
    CommonResult<Long> materialReceive(ProcessingTaskMaterialReceiveReqDTO reqDTO);

    /**
     * 物料规格损耗
     *
     * @param reqDTO
     */
    CommonResult<Long> materialWasteLossWeightUpdate(ProcessingTaskMaterialWasteLossWeightReqDTO reqDTO);

    /**
     * 物料多次返还
     * @param reqDTO
     * @return
     */
    CommonResult<Long> materialRestoreMulti(ProcessingTaskMaterialRestoreReqDTO reqDTO);
}
