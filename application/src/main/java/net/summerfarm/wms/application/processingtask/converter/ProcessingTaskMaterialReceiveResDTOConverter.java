package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskMaterialReceiveResDTO;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProcessingTaskMaterialReceiveResDTOConverter {

    ProcessingTaskMaterialReceiveResDTOConverter INSTANCE = Mappers.getMapper(ProcessingTaskMaterialReceiveResDTOConverter.class);

    @Mappings({
            @Mapping(target = "materialSkuProductionDate", source = "materialSkuProductionDate", dateFormat = "yyyy-MM-dd"),
            @Mapping(target = "materialSkuQualityDate", source = "materialSkuQualityDate", dateFormat = "yyyy-MM-dd")
    })
    ProcessingTaskMaterialReceiveResDTO convert(ProcessingTaskMaterialReceiveRecord materialReceiveRecord);

    /**
     * entity list
     * @param materialReceiveRecord entity list
     * @return res list
     */
    List<ProcessingTaskMaterialReceiveResDTO> convertList(List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecord);

}
