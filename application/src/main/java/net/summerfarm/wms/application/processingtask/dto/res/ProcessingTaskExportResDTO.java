package net.summerfarm.wms.application.processingtask.dto.res;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.oss.result.OssUploadResult;

/**
 * 加工任务详情导出响应对象
 * <AUTHOR>
 * @date 2023/02/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingTaskExportResDTO {

    /**
     * 导出文件地址
     */
    private String fileAddress;

    /**
     * oss上传结果
     */
    private OssUploadResult ossUploadResult;

    /**
     * 文件下载id
     */
    private Long resId;
}
