package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProcessingTaskOrderImportReqDTO implements Serializable {

    /**
     * oss文件链接
     */
    @NotNull(message = "上传文件 不可为空")
    private String fileAddress;

    /**
     * 仓库编码
     */
    @NotNull(message = "仓库编码 不可为空")
    private Integer warehouseNo;

    private Long tenantId;
}
