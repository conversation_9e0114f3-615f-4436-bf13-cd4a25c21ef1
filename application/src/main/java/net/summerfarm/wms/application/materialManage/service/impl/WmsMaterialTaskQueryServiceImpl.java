package net.summerfarm.wms.application.materialManage.service.impl;


import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialTaskAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.query.WmsMaterialTaskQueryInput;
import net.summerfarm.wms.application.materialManage.service.WmsMaterialTaskQueryService;
import net.summerfarm.wms.domain.materialManage.entity.WmsMaterialTaskEntity;
import net.summerfarm.wms.domain.materialManage.param.query.WmsMaterialTaskQueryParam;
import net.summerfarm.wms.domain.materialManage.repository.WmsMaterialTaskQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2025-03-18 15:49:28
* @version 1.0
*
*/
@Service
public class WmsMaterialTaskQueryServiceImpl implements WmsMaterialTaskQueryService {

    @Autowired
    private WmsMaterialTaskQueryRepository wmsMaterialTaskQueryRepository;

    @Override
    public PageInfo<WmsMaterialTaskEntity> getPage(WmsMaterialTaskQueryInput input) {
        WmsMaterialTaskQueryParam queryParam = WmsMaterialTaskAssembler.toWmsMaterialTaskQueryParam(input);
        return wmsMaterialTaskQueryRepository.getPage(queryParam);
    }

    @Override
    public WmsMaterialTaskEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return wmsMaterialTaskQueryRepository.selectById(id);
    }
}