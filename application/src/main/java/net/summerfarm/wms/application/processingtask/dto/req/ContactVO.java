package net.summerfarm.wms.application.processingtask.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;

import javax.validation.constraints.NotNull;

@Data
public class ContactVO  {
    private String mname;

    @ApiModelProperty(value = "指定日期内配送次数")
    private Integer times;

    private String wholeAddress;

    /**
     * 承运商id
     */
    private Long carrierId;

    @NotNull(message = "contact.null", groups = {Add.class})
    private String contact;

    @ApiModelProperty(value = "手机号")
    @NotNull(message = "phone.null", groups = {Add.class})
    private String phone;
}
