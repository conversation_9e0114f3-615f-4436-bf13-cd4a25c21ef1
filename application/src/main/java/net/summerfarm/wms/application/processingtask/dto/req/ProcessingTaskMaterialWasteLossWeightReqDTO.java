package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessingTaskMaterialWasteLossWeightReqDTO implements Serializable {

    /**
     * 原料领料Id
     */
    private Long materialReceiveId;

    /**
     * 原料SKU
     */
    private String materialSkuCode;

    /**
     * 加工任务原料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 加工任务成品ID
     */
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 废料损耗更新
     */
    private BigDecimal wasteLossWeight;

}
