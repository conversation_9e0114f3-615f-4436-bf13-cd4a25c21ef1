package net.summerfarm.wms.application.processingtask.factory;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DeleteFlagEnum;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductInventoryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductSpecReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductSubmitReqDTO;
import net.summerfarm.wms.domain.prove.domainobject.Prove;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.common.util.DateUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskProductSubmitAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ProcessingTaskProductSubmitAggregateFactory {

    public static ProcessingTaskProductSubmitAggregate newInstance(
            ProcessingTaskProductSubmitReqDTO reqDTO,
            ProcessingTaskProduct processingTaskProduct,
            Map<Long, ProcessingTaskProductRecord> processingTaskProductRecordMap,
            List<ProcessingTaskProductOrderRecord> processingTaskProductRecordList,
            BigDecimal productAvlBatchCost,
            Integer submitProductQuantity,
            Prove materialProve) {

        ProcessingTaskProductSubmitAggregate submitAggregate = new ProcessingTaskProductSubmitAggregate();

        submitAggregate.setProcessingTaskProductId(reqDTO.getProcessingTaskProductId());
        submitAggregate.setProcessingTaskCode(reqDTO.getProcessingTaskCode());
        submitAggregate.setMaterialProve(materialProve);
        submitAggregate.setWarehouseNo(processingTaskProduct.getWarehouseNo());
        submitAggregate.setTenantId(reqDTO.getTenantId());

        submitAggregate.setProductAvlBatchCost(productAvlBatchCost);

        // 成品提交明细
        submitAggregate.setProductSubmitRecordList(buildSubmitRecordList(
                reqDTO, processingTaskProductRecordMap, processingTaskProduct
        ));

        // 成品规格
        submitAggregate.setProductRecordUpdateList(buildSpecSubmitList(
                reqDTO, processingTaskProductRecordMap
        ));
        // 成品订单规格
        submitAggregate.setProductOrderRecordUpdateList(buildSpecOrderSubmitList(
                reqDTO, processingTaskProduct, processingTaskProductRecordMap, processingTaskProductRecordList
        ));

        // 提交重量&数量
        submitAggregate.setSubmitWeight(
                submitAggregate.getProductRecordUpdateList()
                        .stream()
                        .map(ProcessingTaskProductRecordUpdate::getSubmitWeight)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO)
        );
        submitAggregate.setSubmitQuantity(submitProductQuantity);

        return submitAggregate;
    }

    private static List<ProcessingTaskProductSubmitRecord> buildSubmitRecordList(
            ProcessingTaskProductSubmitReqDTO reqDTO,
            Map<Long, ProcessingTaskProductRecord> processingTaskProductRecordMap,
            ProcessingTaskProduct processingTaskProduct) {

        List<ProcessingTaskProductSubmitRecord> result = new ArrayList<>();

        for (ProcessingTaskProductSpecReqDTO processingTaskProductSpecReqDTO : reqDTO.getProcessingSpecList()) {

            ProcessingTaskProductRecord productRecord = processingTaskProductRecordMap.get(
                    processingTaskProductSpecReqDTO.getProcessingTaskProductSpecId());
            if (productRecord == null){
                String errorMsg = String.format("请求成品规格不存在: %s" + processingTaskProductSpecReqDTO.getProcessingTaskProductSpecId());
                log.error(errorMsg);
                throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), errorMsg);
            }

            for (ProcessingTaskProductInventoryReqDTO inventoryReqDTO : processingTaskProductSpecReqDTO.getProductInventoryList()) {

                ProcessingTaskProductSubmitRecord productSpecSubmit = new ProcessingTaskProductSubmitRecord();

                productSpecSubmit.setWarehouseNo(productRecord.getWarehouseNo());
                productSpecSubmit.setProcessingTaskCode(productRecord.getProcessingTaskCode());
                productSpecSubmit.setProcessingTaskProductId(productRecord.getProcessingTaskProductId());

                productSpecSubmit.setProductSkuCode(productRecord.getProductSkuCode());
                productSpecSubmit.setProductSkuName(productRecord.getProductSkuName());
                productSpecSubmit.setProductSkuWeight(processingTaskProduct.getProductSkuWeight());

                productSpecSubmit.setProductSkuSpecWeight(productRecord.getProductSkuSpecWeight());
                productSpecSubmit.setProductSkuSpecUnit(productRecord.getProductSkuSpecUnit());

//                productSpecSubmit.setProductSkuPurchaseBatch(inventoryReqDTO.getPurchaseBatchCode());
                productSpecSubmit.setProductSkuProductionDate(
                        DateUtil.parseDateTimeByYMD(inventoryReqDTO.getPurchaseBatchProductionDate()));
//                productSpecSubmit.setProductSkuQualityDate(
//                        DateUtil.parseDateTimeByYMD(inventoryReqDTO.getPurchaseBatchQualityDate()));

                productSpecSubmit.setProductSkuSpecSubmitQuantity(inventoryReqDTO.getSubmitQuantity());
                productSpecSubmit.setProductSkuSpecSubmitWeight(
                        productRecord.getProductSkuSpecWeight().multiply(
                                BigDecimal.valueOf(inventoryReqDTO.getSubmitQuantity())
                        )
                );
                productSpecSubmit.setProductSkuSpecPrintNumber(0);

                productSpecSubmit.setCreator(LoginInfoThreadLocal.getCurrentUserName());
                productSpecSubmit.setCreateTime(new Date());
                productSpecSubmit.setUpdater(LoginInfoThreadLocal.getCurrentUserName());
                productSpecSubmit.setUpdateTime(new Date());
                productSpecSubmit.setDeleteFlag(DeleteFlagEnum.NO.getValue());

                result.add(productSpecSubmit);
            }
        }

        return result;
    }


    private static List<ProcessingTaskProductRecordUpdate> buildSpecSubmitList(
            ProcessingTaskProductSubmitReqDTO reqDTO,
            Map<Long, ProcessingTaskProductRecord> processingTaskProductRecordMap) {

        List<ProcessingTaskProductRecordUpdate> result = new ArrayList<>();

        for (ProcessingTaskProductSpecReqDTO processingTaskProductSpecReqDTO : reqDTO.getProcessingSpecList()) {

            ProcessingTaskProductRecord productRecord = processingTaskProductRecordMap.get(
                    processingTaskProductSpecReqDTO.getProcessingTaskProductSpecId());
            if (productRecord == null){
                String errorMsg = String.format("请求成品规格不存在: %s" + processingTaskProductSpecReqDTO.getProcessingTaskProductSpecId());
                log.error(errorMsg);
                throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), errorMsg);
            }

            ProcessingTaskProductRecordUpdate productSpecSubmit = new ProcessingTaskProductRecordUpdate();

            productSpecSubmit.setProcessingTaskProductSpecId(productRecord.getId());
            productSpecSubmit.setSubmitWeight(
                    productRecord.getProductSkuSpecWeight().multiply(
                            BigDecimal.valueOf(processingTaskProductSpecReqDTO.getSubmitQuantity())
                    )
            );
            productSpecSubmit.setSubmitSpecQuantity(processingTaskProductSpecReqDTO.getSubmitQuantity());

            result.add(productSpecSubmit);
        }

        return result;
    }


    private static List<ProcessingTaskProductOrderRecordUpdate> buildSpecOrderSubmitList(
            ProcessingTaskProductSubmitReqDTO reqDTO,
            ProcessingTaskProduct processingTaskProduct,
            Map<Long, ProcessingTaskProductRecord> processingTaskProductRecordMap,
            List<ProcessingTaskProductOrderRecord> processingTaskProductRecordList) {

        if (CollectionUtils.isEmpty(processingTaskProductRecordList)){
            return Lists.newArrayList();
        }
        // 按照重量分组
        Map<BigDecimal, List<ProcessingTaskProductOrderRecord>> weightOrderMap = processingTaskProductRecordList.stream()
                .collect(Collectors.groupingBy(ProcessingTaskProductOrderRecord::getProductSkuSpecWeight));


        List<ProcessingTaskProductOrderRecordUpdate> result = new ArrayList<>();

        for (ProcessingTaskProductSpecReqDTO processingTaskProductSpecReqDTO : reqDTO.getProcessingSpecList()) {

            // 对应的成品规格
            ProcessingTaskProductRecord productRecord = processingTaskProductRecordMap.get(
                    processingTaskProductSpecReqDTO.getProcessingTaskProductSpecId());
            if (productRecord == null){
                String errorMsg = String.format("请求成品规格不存在: %s" + processingTaskProductSpecReqDTO.getProcessingTaskProductSpecId());
                log.error(errorMsg);
                throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), errorMsg);
            }

            // 对应的成品订单规格
            List<ProcessingTaskProductOrderRecord> orderRecordList = weightOrderMap.get(
                    productRecord.getProductSkuSpecWeight());

            Integer submitQuantity = processingTaskProductSpecReqDTO.getSubmitQuantity();

            for (ProcessingTaskProductOrderRecord orderRecord : orderRecordList) {
                Integer remainQuantity = orderRecord.getProductSkuSpecNeedQuantity() -
                        orderRecord.getProductSkuSpecSubmitQuantity();
                // 剩余不足
                if (remainQuantity <= 0){
                    continue;
                }

                // 剩余 >= 提交
                if (remainQuantity >= submitQuantity){
                    ProcessingTaskProductOrderRecordUpdate submit = new ProcessingTaskProductOrderRecordUpdate();

                    submit.setProcessingTaskProductOrderSpecId(orderRecord.getId());
                    submit.setProcessingTaskProductSpecId(productRecord.getId());
                    submit.setSubmitQuantity(submitQuantity);
                    submit.setSubmitWeight(productRecord.getProductSkuSpecWeight().multiply(
                            BigDecimal.valueOf(submitQuantity)
                    ));

                    result.add(submit);
                    submitQuantity -= submitQuantity;

                    break;
                }

                // 剩余 < 提交
                ProcessingTaskProductOrderRecordUpdate submit = new ProcessingTaskProductOrderRecordUpdate();

                submit.setProcessingTaskProductOrderSpecId(orderRecord.getId());
                submit.setProcessingTaskProductSpecId(productRecord.getId());
                submit.setSubmitQuantity(remainQuantity);
                submit.setSubmitWeight(productRecord.getProductSkuSpecWeight().multiply(
                        BigDecimal.valueOf(remainQuantity)
                ));

                result.add(submit);
                submitQuantity -= remainQuantity;
            }

            if (submitQuantity > 0){
                log.error("加工成品订单数量不足分配，请刷新后重试 {}", JSONObject.toJSONString(reqDTO));
                throw new BizException(ErrorCode.SYSTEM_ERROR.getCode(), "加工成品订单数量不足分配，请刷新后重试");
            }
        }

        return result;
    }
}
