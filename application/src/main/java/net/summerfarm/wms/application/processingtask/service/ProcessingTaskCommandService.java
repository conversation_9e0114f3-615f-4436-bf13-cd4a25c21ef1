package net.summerfarm.wms.application.processingtask.service;

import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskExportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskFinishCheckResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskImportResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskOrderImportResDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskFinishReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskImportReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskOrderCreateReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskOrderImportReqDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ProcessingTaskCommandService {
    /**
     * 订单导入
     *
     * @param reqDTO
     */
    CommonResult<ProcessingTaskOrderImportResDTO> orderImport(ProcessingTaskOrderImportReqDTO reqDTO);

    /**
     * 订单导入
     *
     * @param file
     * @param warehouseNo
     * @param tenantId
     */
    CommonResult<Long> orderImportLocal(MultipartFile file, Integer warehouseNo, Long tenantId);

    /**
     * 新建任务
     *
     * @param processingTaskOrderCreateReqDTOS
     * @return
     */
    CommonResult<Long>  createTask(List<ProcessingTaskOrderCreateReqDTO> processingTaskOrderCreateReqDTOS);

    /**
     * 完结检查
     *
     * @param reqDTO
     */
    CommonResult<ProcessingTaskFinishCheckResDTO> finishCheck(ProcessingTaskFinishReqDTO reqDTO);

    /**
     * 完结
     *
     * @param reqDTO
     */
    CommonResult<Long> finish(ProcessingTaskFinishReqDTO reqDTO);

    /**
     * 导出加工任务成品明细
     * @param processingTaskCode 加工任务编号
     * @return 响应对象
     */
    CommonResult<ProcessingTaskExportResDTO> exportProcessingTaskAndDetail(String processingTaskCode);

    /**
     * 加工任务导入
     * @param reqDTO 请求
     * @return 响应对象
     */
    CommonResult<ProcessingTaskImportResDTO> taskImport(ProcessingTaskImportReqDTO reqDTO);

    /**
     * 任务导入
     *
     * @param file        文件
     * @param type        类型
     * @param warehouseNo 仓库编号
     * @param tenantId
     * @return 响应对象
     */
    CommonResult<Long> taskImportLocal(MultipartFile file, Integer type, Integer warehouseNo, Long tenantId);
}
