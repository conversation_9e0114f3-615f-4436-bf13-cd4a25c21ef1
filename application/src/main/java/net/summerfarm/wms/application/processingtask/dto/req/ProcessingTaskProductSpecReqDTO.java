package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProcessingTaskProductSpecReqDTO implements Serializable {

    /**
     * 加工成品规格ID
     */
    private Long processingTaskProductSpecId;

    /**
     * 加工数量
     */
    private Integer submitQuantity;

    /**
     * 加工库存明细
     */
    private List<ProcessingTaskProductInventoryReqDTO> productInventoryList;
}
