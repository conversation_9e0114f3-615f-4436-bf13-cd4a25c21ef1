package net.summerfarm.wms.application.materialManage.service.factory;

import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialBindingAssembler;
import net.summerfarm.wms.application.materialManage.controller.assembler.WmsMaterialBindingDetailAssembler;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingCommandInput;
import net.summerfarm.wms.application.materialManage.controller.input.command.WmsMaterialBindingDetailCommandInput;
import net.summerfarm.wms.domain.materialManage.domianobject.aggregate.WmsMaterialBindingCreateAggregate;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingCommandParam;
import net.summerfarm.wms.domain.materialManage.param.command.WmsMaterialBindingDetailCommandParam;
import net.summerfarm.wms.facade.goods.dto.GoodsInfoDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class WmsMaterialBindingCreateAggregateFactory {

    public static WmsMaterialBindingCreateAggregate build(WmsMaterialBindingCommandInput input,
                                                          Map<String, GoodsInfoDTO> goodsInfoDTOMap,
                                                          Map<String, GoodsInfoDTO> materialGoodsInfoDTOMap) {
        WmsMaterialBindingCreateAggregate aggregate = new WmsMaterialBindingCreateAggregate();

        WmsMaterialBindingCommandParam wmsMaterialBindingCommandParam = WmsMaterialBindingAssembler
                .buildCreateParam(input, goodsInfoDTOMap);

        List<WmsMaterialBindingDetailCommandParam> detailCommandParamList = new ArrayList<>();
        for (WmsMaterialBindingDetailCommandInput wmsMaterialBindingDetailCommandInput : input.getDetailCommandInputList()) {
            WmsMaterialBindingDetailCommandParam detailCommandParam = WmsMaterialBindingDetailAssembler
                    .buildCreateParam(input, wmsMaterialBindingDetailCommandInput, goodsInfoDTOMap, materialGoodsInfoDTOMap);
            if (detailCommandParam != null){
                detailCommandParamList.add(detailCommandParam);
            }
        }

        aggregate.setWmsMaterialBindingCommandParam(wmsMaterialBindingCommandParam);
        aggregate.setWmsMaterialBindingDetailCommandParamList(detailCommandParamList);

        return aggregate;
    }
}
