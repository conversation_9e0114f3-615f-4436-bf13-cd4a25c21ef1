package net.summerfarm.wms.application.processingtask;

import net.summerfarm.common.util.BigDecimalUtils;
import net.summerfarm.wms.annotation.OperatorAnnotation;
import net.summerfarm.wms.application.processingtask.service.ProcessingTaskMaterialCommandService;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialReceiveReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialRestoreReqDTO;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskMaterialWasteLossWeightReqDTO;
import net.summerfarm.wms.application.processingtask.factory.MaterialWasteLossWeightUpdateAggregateFactory;
import net.summerfarm.wms.application.processingtask.factory.ProcessingTaskMaterialReceiveAggregateFactory;
import net.summerfarm.wms.application.processingtask.factory.ProcessingTaskMaterialRestoreAggregateFactory;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.common.util.IntegerUtil;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTask;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskRepository;
import net.xianmu.common.exception.BizException;
import net.summerfarm.wms.common.exceptions.ErrorCode;
import net.summerfarm.wms.domain.processingtask.domainService.ProcessingTaskMaterialDomainService;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.MaterialWasteLossWeightUpdateAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialReceiveAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.aggregate.ProcessingTaskMaterialRestoreAggregate;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterial;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskMaterialReceiveRecord;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.ProcessingTaskProduct;
import net.summerfarm.wms.domain.processingtask.domainobject.enums.ProcessingTaskProductStatusEnum;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskMaterialReceiveRecordRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskMaterialRepository;
import net.summerfarm.wms.domain.processingtask.repository.ProcessingTaskProductRepository;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class ProcessingTaskMaterialCommandServiceImpl implements ProcessingTaskMaterialCommandService {

    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProcessingTaskProductRepository processingTaskProductRepository;
    @Autowired
    private ProcessingTaskMaterialRepository materialRepository;
    @Autowired
    private ProcessingTaskMaterialReceiveRecordRepository materialReceiveRecordRepository;
    @Autowired
    private ProcessingTaskMaterialDomainService processingTaskMaterialDomainService;

    @OperatorAnnotation
    @Override
    public CommonResult<Long> materialReceive(ProcessingTaskMaterialReceiveReqDTO reqDTO) {
        if (reqDTO == null || reqDTO.getProcessingTaskProductId() == null ||
            reqDTO.getMaterialReceiveList() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "参数错误",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 加工成品
        ProcessingTaskProduct processingTaskProduct = processingTaskProductRepository.queryById(
                reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请求成品不存在",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        if (ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "成品已加工完成",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 加工任务
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(
                processingTaskProduct.getProcessingTaskCode());
        if (processingTask == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请求任务不存在",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 加工原料
        ProcessingTaskMaterial processingTaskMaterial = materialRepository.queryByProductIdAndMaterailSkuCode(
                processingTaskProduct.getProcessingTaskCode(),
                processingTaskProduct.getId(),
                reqDTO.getMaterialSkuCode()
        );

        ProcessingTaskMaterialReceiveAggregate materialReceiveAggregate =
                ProcessingTaskMaterialReceiveAggregateFactory.newInstance(
                        reqDTO, processingTask, processingTaskMaterial);

        return CommonResult.ok(processingTaskMaterialDomainService.materialReceive(materialReceiveAggregate));
    }

    @Override
    public CommonResult<Long> materialWasteLossWeightUpdate(ProcessingTaskMaterialWasteLossWeightReqDTO reqDTO) {
        if (reqDTO == null || reqDTO.getProcessingTaskProductId() == null ||
                reqDTO.getMaterialReceiveId() == null || reqDTO.getWasteLossWeight() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "参数有误", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }


        // region 参数查询
        // 成品
        ProcessingTaskProduct processingTaskProduct = processingTaskProductRepository.queryById(
                reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求成品不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        if (ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求成品已加工完成", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(
                processingTaskProduct.getProcessingTaskCode());
        if (processingTask == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求任务不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 原料
        ProcessingTaskMaterial material = materialRepository.queryById(
                reqDTO.getProcessingTaskMaterialId());
        if (material == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求原料不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 原料批次
        ProcessingTaskMaterialReceiveRecord materialReceiveRecord = materialReceiveRecordRepository.queryById(
                reqDTO.getMaterialReceiveId());
        if (materialReceiveRecord == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求原料领料记录不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        if (reqDTO.getWasteLossWeight().compareTo(
                materialReceiveRecord.getMaterialSkuWeight().multiply(
                        BigDecimal.valueOf(materialReceiveRecord.getMaterialSkuReceiveQuantity()))) > 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "废料损耗重量超过领料重量，请检查填写是否正确",
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 校验输入重量倍数
        BigDecimal divide = reqDTO.getWasteLossWeight()
                .divide(material.getMaterialSkuWeight(), 2, RoundingMode.HALF_DOWN);
        if (divide.compareTo(BigDecimal.valueOf(divide.intValue())) != 0){
            throw new BizException("输入原料损耗必须是原料单位重量的倍数：" + material.getMaterialSkuWeight(),
                    ErrorCodeNew.PARAM_ERROR);
        }

        // 废料差值
        BigDecimal wasteLossWeightDiff = reqDTO.getWasteLossWeight().subtract(
                BigDecimalUtils.defaultValue(materialReceiveRecord.getWasteLossWeight(), BigDecimal.ZERO));

        if (wasteLossWeightDiff.compareTo(BigDecimal.ZERO) > 0){
            // 原料领料列表
            Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap = materialReceiveRecordRepository
                    .mapByProductIdsGroupMaterialId(Arrays.asList(material.getProcessingTaskProductId()));
            List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList = materialReceiveRecordMap
                    .get(material.getId());

            // 总领料数量
            Integer totalReceiveQuantity = materialReceiveRecordList.stream()
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuReceiveQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);

            // 总归还数量
            Integer totalRestoreQuantity = materialReceiveRecordList.stream()
                    .filter(s -> s.getMaterialSkuRestoreQuantity() != null)
                    .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuRestoreQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);

            // 总废料损耗
            BigDecimal totalWasteLossWeight = materialReceiveRecordList.stream()
                    .filter(s -> s.getWasteLossWeight()!= null)
                    .map(ProcessingTaskMaterialReceiveRecord::getWasteLossWeight)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);

            // 成品已加工所需原料数量
            BigDecimal totalMaterialNeedWeight = BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity())
                    .multiply(BigDecimal.valueOf(material.getMaterialSkuRatioNum()))
                    .multiply(material.getMaterialSkuWeight())
                    .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 2, RoundingMode.UP);

            // 原料最大可损耗
            BigDecimal maxWasteLossMaterialWeight = BigDecimal.valueOf(totalReceiveQuantity - totalRestoreQuantity)
                    .multiply(material.getMaterialSkuWeight())
                    .subtract(totalWasteLossWeight)
                    .subtract(totalMaterialNeedWeight);
            Integer maxWasteLossMaterialQuantity = maxWasteLossMaterialWeight
                    .divide(material.getMaterialSkuWeight(), 0 , RoundingMode.DOWN)
                    .intValue();
            if (wasteLossWeightDiff.compareTo(maxWasteLossMaterialWeight) > 0){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "废料损耗重量超过最大可废料重量:" + BigDecimal.valueOf(maxWasteLossMaterialQuantity)
                                .multiply(material.getMaterialSkuWeight())
                                .add(BigDecimalUtils.defaultValue(materialReceiveRecord.getWasteLossWeight(), BigDecimal.ZERO)),
                        String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }
        }

        // 原料单行最大可废料数量
        Integer maxWasteLossQuantity = materialReceiveRecord.getMaterialSkuReceiveQuantity()
                - IntegerUtil.getValueDefault0(materialReceiveRecord.getMaterialSkuRestoreQuantity());
        BigDecimal maxWasteLossWeight = materialReceiveRecord.getMaterialSkuWeight()
                .multiply(BigDecimal.valueOf(maxWasteLossQuantity));
        if (reqDTO.getWasteLossWeight().compareTo(maxWasteLossWeight) > 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "废料损耗重量超过最大可废料重量:" + maxWasteLossWeight,
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // endregion

        MaterialWasteLossWeightUpdateAggregate aggregate = MaterialWasteLossWeightUpdateAggregateFactory
                .newInstance(reqDTO);
        processingTaskMaterialDomainService.materialWasteLossWeightUpdate(aggregate);
        return CommonResult.ok(1L);
    }

    @OperatorAnnotation
    @Override
    public CommonResult<Long> materialRestoreMulti(ProcessingTaskMaterialRestoreReqDTO reqDTO) {
        if (reqDTO == null || reqDTO.getProcessingTaskProductId() == null ||
                reqDTO.getMaterialReceiveId() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "参数有误", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        if (reqDTO.getMaterialSkuRestoreWeight() == null &&
                reqDTO.getMaterialSkuRestoreQuantity() == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "参数有误", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // region 参数查询
        // 成品
        ProcessingTaskProduct processingTaskProduct = processingTaskProductRepository.queryById(
                reqDTO.getProcessingTaskProductId());
        if (processingTaskProduct == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求成品不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        if (ProcessingTaskProductStatusEnum.PROCESSED.equalsCode(processingTaskProduct.getStatus())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求成品已加工完成", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(
                processingTaskProduct.getProcessingTaskCode());
        if (processingTask == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求任务不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 原料
        ProcessingTaskMaterial material = materialRepository.queryById(
                reqDTO.getProcessingTaskMaterialId());
        if (material == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求原料不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }
        // 原料批次
        ProcessingTaskMaterialReceiveRecord materialReceiveRecord = materialReceiveRecordRepository.queryById(
                reqDTO.getMaterialReceiveId());
        if (materialReceiveRecord == null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "请求原料领料记录不存在", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 原料重量转数量
        if (reqDTO.getMaterialSkuRestoreWeight() != null){
            // 检查返回重量是否整除
            BigDecimal divide = reqDTO.getMaterialSkuRestoreWeight()
                    .divide(material.getMaterialSkuWeight(), 2, RoundingMode.HALF_DOWN);
            Integer restoreQuantity = divide.intValue();
            if (divide.compareTo(BigDecimal.valueOf(divide.intValue()))!= 0){
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                        "输入返回原料重量必须是原料单位重量的倍数：" + material.getMaterialSkuWeight()
                            , String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
            }

            reqDTO.setMaterialSkuRestoreQuantity(restoreQuantity);
        }
        if (reqDTO.getMaterialSkuRestoreQuantity() < 0){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    "输入返还数量不能小于0", String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 原料领料列表
        Map<Long, List<ProcessingTaskMaterialReceiveRecord>> materialReceiveRecordMap = materialReceiveRecordRepository
                .mapByProductIdsGroupMaterialId(Arrays.asList(material.getProcessingTaskProductId()));
        List<ProcessingTaskMaterialReceiveRecord> materialReceiveRecordList = materialReceiveRecordMap.get(material.getId());

        // 总领料数量
        Integer totalReceiveQuantity = materialReceiveRecordList.stream()
               .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuReceiveQuantity)
               .reduce(Integer::sum)
               .orElse(0);

        // 总归还数量
        Integer totalRestoreQuantity = materialReceiveRecordList.stream()
                .filter(s -> s.getMaterialSkuRestoreQuantity() != null)
                .map(ProcessingTaskMaterialReceiveRecord::getMaterialSkuRestoreQuantity)
                .reduce(Integer::sum)
                .orElse(0);
        // 总废料损耗
        BigDecimal totalWasteLossWeight = materialReceiveRecordList.stream()
               .filter(s -> s.getWasteLossWeight()!= null)
               .map(ProcessingTaskMaterialReceiveRecord::getWasteLossWeight)
               .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);

        // 成品已加工所需原料数量
        BigDecimal totalMaterialNeedWeight = BigDecimal.valueOf(processingTaskProduct.getProductSkuFinishQuantity())
                .multiply(BigDecimal.valueOf(material.getMaterialSkuRatioNum()))
                .multiply(material.getMaterialSkuWeight())
                .divide(BigDecimal.valueOf(processingTaskProduct.getProductSkuRatioNum()), 2, RoundingMode.UP);

        // 原料最大可归还数量
        Integer maxRestoreMaterialQuantity = BigDecimal.valueOf(totalReceiveQuantity - totalRestoreQuantity)
                .multiply(material.getMaterialSkuWeight())
                .subtract(totalWasteLossWeight)
                .subtract(totalMaterialNeedWeight)
                .divide(material.getMaterialSkuWeight(), 0, RoundingMode.DOWN)
                .intValue();
        if (maxRestoreMaterialQuantity < reqDTO.getMaterialSkuRestoreQuantity()){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    reqDTO.getMaterialSkuRestoreWeight() == null ?
                            "成品已加工，当前可归还数量最多为：" + maxRestoreMaterialQuantity :
                            "成品已加工，当前可归还重量最多为：" + material.getMaterialSkuWeight().multiply(
                                    BigDecimal.valueOf(maxRestoreMaterialQuantity)),
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        // 原料单行最大可返还数量
        Integer maxRestoreQuantity = materialReceiveRecord.getMaterialSkuReceiveQuantity()
                - IntegerUtil.getValueDefault0(materialReceiveRecord.getMaterialSkuRestoreQuantity())
                - BigDecimalUtils.defaultValue(materialReceiveRecord.getWasteLossWeight(), BigDecimal.ZERO).divide(
                materialReceiveRecord.getMaterialSkuWeight(), 0, RoundingMode.HALF_DOWN).intValue()
                ;
        if (reqDTO.getMaterialSkuRestoreQuantity() > maxRestoreQuantity){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,
                    reqDTO.getMaterialSkuRestoreWeight() == null ?
                            "当前可归还数量最多为：" + maxRestoreQuantity :
                            "当前可归还重量最多为：" + material.getMaterialSkuWeight().multiply(
                                    BigDecimal.valueOf(maxRestoreQuantity)),
                    String.valueOf(ErrorCode.PARAM_ERROR.getCode()));
        }

        ProcessingTaskMaterialRestoreAggregate materialRestoreAggregate = ProcessingTaskMaterialRestoreAggregateFactory
                .newInstance(reqDTO, materialReceiveRecord, material);
        return CommonResult.ok(processingTaskMaterialDomainService.materialRestore(materialRestoreAggregate));
    }
}
