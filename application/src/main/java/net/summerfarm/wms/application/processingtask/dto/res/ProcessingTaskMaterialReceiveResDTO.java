package net.summerfarm.wms.application.processingtask.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProcessingTaskMaterialReceiveResDTO implements Serializable {


    /**
     * primary key
     */
    private Long id;
    /**
     * 库存仓编号
     */
    private Integer warehouseNo;
    /**
     * 加工任务编号
     */
    private String processingTaskCode;
    /**
     * 加工任务成品id
     */
    private Long processingTaskProductId;
    /**
     * 成品sku
     */
    private String productSkuCode;
    /**
     * 原料sku
     */
    private String materialSkuCode;
    /**
     * 原料sku编号
     */
    private String materialSkuName;
    /**
     * 原料sku重量
     */
    private BigDecimal materialSkuWeight;
    /**
     * 原料sku单位
     */
    private String materialSkuUnit;
    /**
     * 原料sku使用数量
     */
    private Integer materialSkuQuantity;
    /**
     * 原料sku总重
     */
    private BigDecimal materialSkuTotalWeight;
    /**
     * 原料sku采购批次
     */
    private String materialSkuPurchaseBatch;
    /**
     * 原料sku领料数量
     */
    private Integer materialSkuReceiveQuantity;
    /**
     * 原料sku领料总重
     */
    private BigDecimal materialSkuTotalReceiveWeight;
    /**
     * 原料sku归还数量
     */
    private Integer materialSkuRestoreQuantity;
    /**
     * 废料损耗重量，人工填写
     */
    private BigDecimal wasteLossWeight;
    /**
     * 规格损耗重量，加工实重-加工数量*规格重量
     */
    private BigDecimal specLossWeight;
    /**
     * 原料SKU剩余重量，领料总重-加工实重-废料损耗
     */
    private BigDecimal materialSkuRemainWeight;

    /**
     * 剩余原料数量
     */
    private Integer materialSkuRemainQuantity;

    /**
     * 物料SKU生产日期
     */
    private String materialSkuProductionDate;

    /**
     * 物料SKU保质期
     */
    private String materialSkuQualityDate;

    /**
     * 加工任务物料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 原料库位id
     */
    private Integer materialSkuCabinetId;

    /**
     * 原料库位编码
     */
    private String materialSkuCabinetCode;

    /**
     * 原来返回库位编码，固定JG01
     */
    private String materialSkuRestoreCabinetCode;

    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
