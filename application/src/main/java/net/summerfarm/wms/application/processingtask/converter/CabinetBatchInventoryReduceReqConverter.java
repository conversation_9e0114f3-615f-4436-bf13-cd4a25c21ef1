package net.summerfarm.wms.application.processingtask.converter;

import net.summerfarm.wms.api.h5.inventory.dto.req.cabinetBatchInventory.CabinetBatchInventoryReduceReq;
import net.summerfarm.wms.domain.processingtask.delegate.entity.CabinetBatchInventoryReduce;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CabinetBatchInventoryReduceReqConverter {

    CabinetBatchInventoryReduceReqConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryReduceReqConverter.class);

    CabinetBatchInventoryReduceReq convert(CabinetBatchInventoryReduce cabinetBatchInventoryReduce);
}
