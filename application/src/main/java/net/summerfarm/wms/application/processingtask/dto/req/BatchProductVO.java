package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

/**
 * Description:批量成品VO
 * date: 2024/3/4 18:15
 *
 * <AUTHOR>
 */
@Data
public class BatchProductVO {

    /**
     * 成品sku编码
     */
    private String productSkuCode;

    /**
     * 成品sku需要数量
     */
    private String productSkuNeedQuantityStr;

    /**
     * 成品sku需要数量
     */
    private Integer productSkuNeedQuantity;

    /**
     * 该条数据是否继续走流程
     */
    private Boolean goOnFlag;
    /**
     * 备注
     */
    private String result;
}
