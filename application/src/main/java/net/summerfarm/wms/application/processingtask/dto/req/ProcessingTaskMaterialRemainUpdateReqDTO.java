package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 加工任务原料剩余更新请求
 */
@Data
public class ProcessingTaskMaterialRemainUpdateReqDTO implements Serializable {

    /**
     * 原料领料Id
     */
    private Long materialReceiveId;

    /**
     * 原料SKU
     */
    private String materialSkuCode;

    /**
     * 加工任务原料ID
     */
    private Long processingTaskMaterialId;

    /**
     * 加工任务成品ID
     */
    private Long processingTaskProductId;

    /**
     * 加工任务编码
     */
    private String processingTaskCode;

    /**
     * 剩余原料更新
     */
    private BigDecimal materialSkuRemainWeight;
}
