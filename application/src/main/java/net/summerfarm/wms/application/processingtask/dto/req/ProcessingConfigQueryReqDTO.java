package net.summerfarm.wms.application.processingtask.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 加工规则分页查询请求对象
 * <AUTHOR>
 * @date 2023/02/13
 */
@Data
public class ProcessingConfigQueryReqDTO implements Serializable {

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 加工类型：1订单加工，2商品加工，3组拆装
     */
    private Integer type;

    /**
     * 原料SKU名称
     */
    private String materialSkuName;

    /**
     * 原料SKU
     */
    private String materialSkuCode;

    /**
     * 原料SKUid
     */
    private Long materialSkuSaasId;

    /**
     * 成品SKU名称
     */
    private String productSkuName;

    /**
     * 成品SKU
     */
    private String productSkuCode;

    /**
     * 成品SKUid
     */
    private Long productSkuSaasId;

    /**
     * 是否作废：0否，1是
     */
    private Integer invalid;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 页大小
     */
    private Integer pageSize;
}
