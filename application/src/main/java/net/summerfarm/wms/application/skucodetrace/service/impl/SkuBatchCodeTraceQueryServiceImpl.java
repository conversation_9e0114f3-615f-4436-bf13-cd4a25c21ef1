package net.summerfarm.wms.application.skucodetrace.service.impl;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wms.application.skucodetrace.input.SkuBatchCodeTraceQuery;
import net.summerfarm.wms.application.skucodetrace.assmbler.SkuBatchCodeTraceAssembler;
import net.summerfarm.wms.application.skucodetrace.service.SkuBatchCodeTraceQueryService;
import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceQueryParam;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceQueryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 唯一溯源码查询服务<br/>
 * date: 2024/8/7 14:37<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class SkuBatchCodeTraceQueryServiceImpl implements SkuBatchCodeTraceQueryService {

    @Resource
    private SkuBatchCodeTraceQueryRepository skuBatchCodeTraceQueryRepository;

    @Override
    public PageInfo<SkuBatchCodeTraceEntity> batchPrint(SkuBatchCodeTraceQuery query) {
        SkuBatchCodeTraceQueryParam queryParam = SkuBatchCodeTraceAssembler.assembleQuery(query);
        return skuBatchCodeTraceQueryRepository.getPage(queryParam);
    }

    @Override
    public SkuBatchCodeTraceEntity traceOnlyQuery(SkuBatchCodeTraceQuery query) {
        if (query == null || StringUtils.isEmpty(query.getSkuBatchTraceCode())){
            return null;
        }
        return  skuBatchCodeTraceQueryRepository.findBySkuBatchTraceCode(query.getSkuBatchTraceCode());
    }

    @Override
    public Map<String, LocalDate> findMerchantDeliveryTimeRecently(List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList) {
        if (CollectionUtils.isEmpty(skuBatchCodeTraceEntityList)) {
            return new HashMap<>();
        }
        // 商户最近一次配送时间
        Map<String, LocalDate> merchantDeliveryTimeRecentlyMap = new HashMap<>();
        Map<LocalDate, List<SkuBatchCodeTraceEntity>> groupDeliveryTimeTraceMap = skuBatchCodeTraceEntityList.stream().collect(Collectors.groupingBy(SkuBatchCodeTraceEntity::getDeliveryTime));
        groupDeliveryTimeTraceMap.forEach((deliveryTime, traceList) -> {
            List<String> merchantIdList = traceList.stream().map(SkuBatchCodeTraceEntity::getMerchantId).distinct().collect(Collectors.toList());
            List<SkuBatchCodeTraceEntity> deliveryTimeRecentlyTraceList = skuBatchCodeTraceQueryRepository.findDeliveryTimeRecentlyByMerchantId(merchantIdList, deliveryTime);
            merchantDeliveryTimeRecentlyMap.putAll(deliveryTimeRecentlyTraceList.stream().collect(Collectors.toMap(item -> item.getMerchantId() + "_" + deliveryTime, SkuBatchCodeTraceEntity::getDeliveryTime, (a, b) -> a)));
        });
        return merchantDeliveryTimeRecentlyMap;
    }

    @Override
    public Map<String, LocalDate> findMerchantDeliveryTimeFirst(List<SkuBatchCodeTraceEntity> skuBatchCodeTraceEntityList) {
        if (CollectionUtils.isEmpty(skuBatchCodeTraceEntityList)) {
            return new HashMap<>();
        }
        // 商户第一次配送时间
        List<String> merchantIdList = skuBatchCodeTraceEntityList.stream().map(SkuBatchCodeTraceEntity::getMerchantId).distinct().collect(Collectors.toList());
        List<SkuBatchCodeTraceEntity> deliveryTimeFirstTraceList = skuBatchCodeTraceQueryRepository.findDeliveryTimeFirstByMerchantId(merchantIdList);
        return deliveryTimeFirstTraceList.stream().collect(Collectors.toMap(SkuBatchCodeTraceEntity::getMerchantId, SkuBatchCodeTraceEntity::getDeliveryTime, (a, b) -> a));
    }

}
