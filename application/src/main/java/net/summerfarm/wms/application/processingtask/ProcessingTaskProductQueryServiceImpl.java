package net.summerfarm.wms.application.processingtask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wms.application.processingtask.converter.ProcessingTaskMaterialResDTOConverter;
import net.summerfarm.wms.application.processingtask.service.ProcessingTaskProductQueryService;
import net.summerfarm.wms.application.processingtask.dto.req.ProcessingTaskProductQueryReqDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductQueryResDTO;
import net.summerfarm.wms.application.processingtask.dto.res.ProcessingTaskProductSpecDTO;
import net.summerfarm.wms.application.processingtask.converter.ProcessingTaskProductQueryResDTOConverter;
import net.summerfarm.wms.application.processingtask.converter.ProcessingTaskProductSpecDTOConverter;
import net.summerfarm.wms.common.enums.DefaultCabinetCodeEnum;
import net.summerfarm.wms.common.exceptions.ErrorCodeNew;
import net.summerfarm.wms.domain.admin.LoginInfoThreadLocal;
import net.summerfarm.wms.domain.processingtask.domainobject.entity.*;
import net.summerfarm.wms.domain.processingtask.repository.*;
import net.summerfarm.wms.domain.products.ProductRepository;
import net.summerfarm.wms.domain.products.domainobject.Product;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProcessingTaskProductQueryServiceImpl implements ProcessingTaskProductQueryService {

    @Autowired
    private ProcessingTaskProductRepository taskProductRepository;
    @Autowired
    private ProcessingTaskProductRecordRepository processingTaskProductRecordRepository;
    @Autowired
    private ProcessingTaskMaterialRepository taskMaterialRepository;
    @Autowired
    private ProcessingTaskRepository processingTaskRepository;
    @Autowired
    private ProductRepository productRepository;


    /**
     * 分页查询加工任务成品明细
     * @param reqDTO 请求对象
     * @return 分页数据
     */
    @Override
    public PageInfo<ProcessingTaskProductQueryResDTO> page(ProcessingTaskProductQueryReqDTO reqDTO) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();

        //校验加工任务有效性
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(reqDTO.getProcessingTaskCode());
        if (processingTask == null){
            throw new BizException("未查询到加工任务", ErrorCodeNew.PARAM_ERROR);
        }

        // 根据加工任务编号查询加工任务成品明细
        PageInfo<ProcessingTaskProduct> pageInfo = taskProductRepository
                .pageByProcessingTaskCode(reqDTO.getProcessingTaskCode(),
                        reqDTO.getPageIndex(),
                        reqDTO.getPageSize());
        List<ProcessingTaskProduct> processingTaskProductList = pageInfo.getList();
        if (CollectionUtils.isEmpty(processingTaskProductList)){
            PageInfo<ProcessingTaskProductQueryResDTO> result = new PageInfo<>();
            result.setTotal(pageInfo.getTotal());
            result.setPages(pageInfo.getPages());
            result.setList(new ArrayList<>());
            return result;
        }

        // 成品编码列表
        List<String> productSkuCodeList = processingTaskProductList.stream()
                .map(ProcessingTaskProduct::getProductSkuCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Product> productSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId, productSkuCodeList);

        // 转换为Res DTO
        List<ProcessingTaskProductQueryResDTO> processingTaskProductQueryResDTOList = ProcessingTaskProductQueryResDTOConverter
                .convertList(processingTaskProductList, productSpuMap);

        // 加工原料
        List<Long> productIdList = processingTaskProductList.stream()
                .map(ProcessingTaskProduct::getId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<ProcessingTaskMaterial>> materialMap = taskMaterialRepository
                .mapByProductIdList(productIdList);
        List<String> materialSkuCodeList = materialMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(ProcessingTaskMaterial::getMaterialSkuCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Product> materialSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId, materialSkuCodeList);

        for (ProcessingTaskProductQueryResDTO processingTaskProductQueryResDTO : processingTaskProductQueryResDTOList) {
            // 查询加工任务成品明细
            List<ProcessingTaskProductRecord> processingTaskProductRecords = processingTaskProductRecordRepository
                    .queryByProcessingTaskProductId(processingTaskProductQueryResDTO.getId());
            // 转换为加工任务成品明细DTO对象
            List<ProcessingTaskProductSpecDTO> processingTaskProductSpecs = ProcessingTaskProductSpecDTOConverter
                    .INSTANCE.convertList(processingTaskProductRecords);
            // 按照加工规格正序排序
            if (!CollectionUtils.isEmpty(processingTaskProductSpecs)) {
                processingTaskProductSpecs = processingTaskProductSpecs
                        .stream()
                        .sorted(Comparator.comparing(ProcessingTaskProductSpecDTO::getProductSkuSpecWeight))
                        .collect(Collectors.toList());
            }
            processingTaskProductQueryResDTO.setProductSpecList(processingTaskProductSpecs);

            // 设置原料规格
            List<ProcessingTaskMaterial> materialList = materialMap.get(processingTaskProductQueryResDTO.getId());
            processingTaskProductQueryResDTO.setProcessingTaskMaterialList(
                    ProcessingTaskMaterialResDTOConverter.convertList(materialList, materialSpuMap)
            );

            //需要备注
            processingTaskProductQueryResDTO.setNeedRemark(
                    processingTaskProductQueryResDTO.getProductSkuNeedQuantity().compareTo(
                        processingTaskProductQueryResDTO.getProductSkuFinishQuantity()) != 0
            );
        }

        PageInfo<ProcessingTaskProductQueryResDTO> result = new PageInfo<>();
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        result.setList(processingTaskProductQueryResDTOList);
        return result;
    }

    /**
     * 通过加工任务编码和加工任务成品id查询加工任务成品明细详情
     * @param processingTaskCode      加工任务编码
     * @param processingTaskProductId 加工任务成品id
     * @return 加工任务成品明细详情
     */
    @Override
    public ProcessingTaskProductQueryResDTO detail(Long processingTaskProductId, String processingTaskCode) {
        Long tenantId = LoginInfoThreadLocal.getTenantId();
        // 查询加工任务成品
        ProcessingTaskProduct processingTaskProduct = taskProductRepository.queryById(processingTaskProductId);
        if (processingTaskProduct == null) {
            throw new BizException("未查询到当前加工任务的成品信息", ErrorCodeNew.PARAM_ERROR);
        }
        // 任务
        ProcessingTask processingTask = processingTaskRepository.queryByProcessingTaskCode(processingTaskCode);
        if (processingTask == null){
            throw new BizException("未查询到加工任务", ErrorCodeNew.PARAM_ERROR);
        }

        // 成品spu
        Map<String, Product> productSpuMap = productRepository.mapProductsBySkusOnlyGoodsAndTid(tenantId,
                Collections.singletonList(processingTaskProduct.getProductSkuCode()));

        // 查询加工任务成品明细
        List<ProcessingTaskProductRecord> processingTaskProductRecords = processingTaskProductRecordRepository
                .queryByProcessingTaskProductId(processingTaskProduct.getId());

        // 转换Res对象
        ProcessingTaskProductQueryResDTO resDTO = ProcessingTaskProductQueryResDTOConverter.convert(processingTaskProduct, productSpuMap);

        // 成品固定库位
        resDTO.setProductSkuCabinetCode(DefaultCabinetCodeEnum.JG.getCode());

        //需要备注
        resDTO.setNeedRemark(
                resDTO.getProductSkuNeedQuantity().compareTo(
                        resDTO.getProductSkuFinishQuantity()) != 0
        );

        // 填充明细信息
        if (!CollectionUtils.isEmpty(processingTaskProductRecords)) {
            List<ProcessingTaskProductSpecDTO> processingTaskProductSpecs = ProcessingTaskProductSpecDTOConverter
                    .INSTANCE.convertList(processingTaskProductRecords);
            if (!CollectionUtils.isEmpty(processingTaskProductSpecs)) {
                processingTaskProductSpecs = processingTaskProductSpecs
                        .stream()
                        .sorted(Comparator.comparing(ProcessingTaskProductSpecDTO::getProductSkuSpecWeight))
                        .collect(Collectors.toList());
            }
            resDTO.setProductSpecList(processingTaskProductSpecs);
        }
        return resDTO;
    }

}
