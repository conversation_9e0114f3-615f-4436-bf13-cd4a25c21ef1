package net.summerfarm.wms.application.skucodetrace.inbound.controller.skucodetrace;

import net.summerfarm.wms.domain.skucodetrace.entity.SkuBatchCodeTraceEntity;
import net.summerfarm.wms.domain.skucodetrace.enums.SkuBatchCodeTraceEnums;
import net.summerfarm.wms.domain.skucodetrace.param.SkuBatchCodeTraceQueryParam;
import net.summerfarm.wms.domain.skucodetrace.repository.SkuBatchCodeTraceQueryRepository;
import net.summerfarm.wms.domain.skucodetrace.service.SkuBatchCodeTraceCommandDomainService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Description: 后门接口<br/>
 * date: 2024/8/15 14:50<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/sku/sku-batch-code-trace/back-door")
public class SkuBatchCodeTraceBackDoorController {

    @Resource
    private SkuBatchCodeTraceCommandDomainService skuBatchCodeTraceCommandDomainService;
    @Resource
    private SkuBatchCodeTraceQueryRepository skuBatchCodeTraceQueryRepository;

    /**
     * 后门更新状态为订单待分配的溯源码-订单分配处理
     * @param storeNo 城配仓编号
     * @param deliveryTime 配送日期
     */
    @PostMapping(value = "/upsert/code-trace-order")
    public CommonResult<Void> updateCodeTraceOrderInfo(Integer storeNo,@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate deliveryTime){
        if(storeNo == null || deliveryTime == null){
            throw new BizException("城配仓编号和配送日期不能为空");
        }
        // 根据配送日期拉取溯源码-状态 路线待分配
        List<SkuBatchCodeTraceEntity> waitPathAllocationTraceList =  skuBatchCodeTraceQueryRepository.findList(SkuBatchCodeTraceQueryParam.builder()
                .deliveryTime(deliveryTime)
                .state(SkuBatchCodeTraceEnums.State.ORDER_WAIT_ALLOCATION.getValue())
                .storeNo(storeNo)
                .build());
        if(CollectionUtils.isEmpty(waitPathAllocationTraceList)){
            return CommonResult.ok();
        }
        // 更新订单信息
        skuBatchCodeTraceCommandDomainService.updateCodeTraceOrderInfo(waitPathAllocationTraceList);

        return CommonResult.ok();
    }


    /**
     * 后门更新状态为订单待分配的溯源码-城配仓分配处理
     * @param storeNo 城配仓编号
     * @param deliveryTime 配送日期
     */
    @PostMapping(value = "/upsert/code-trace-store")
    public CommonResult<Void> updateCodeTraceStoreInfo(Integer storeNo,@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate deliveryTime){
        if(storeNo == null || deliveryTime == null){
            throw new BizException("城配仓编号和配送日期不能为空");
        }
        // 根据配送日期拉取溯源码-状态 路线待分配
        List<SkuBatchCodeTraceEntity> waitPathAllocationTraceList =  skuBatchCodeTraceQueryRepository.findList(SkuBatchCodeTraceQueryParam.builder()
                .deliveryTime(deliveryTime)
                .state(SkuBatchCodeTraceEnums.State.ORDER_WAIT_ALLOCATION.getValue())
                .storeNo(storeNo)
                .build());
        if(CollectionUtils.isEmpty(waitPathAllocationTraceList)){
            return CommonResult.ok();
        }
        // 更新城配仓信息
        skuBatchCodeTraceCommandDomainService.updateCodeTraceStoreNoInfo(waitPathAllocationTraceList);

        return CommonResult.ok();
    }
}
