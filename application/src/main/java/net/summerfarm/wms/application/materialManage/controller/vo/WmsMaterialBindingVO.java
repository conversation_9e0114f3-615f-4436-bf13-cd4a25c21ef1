package net.summerfarm.wms.application.materialManage.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-18 15:49:28
 * @version 1.0
 *
 */
@Data
public class WmsMaterialBindingVO implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * 租户编码
	 */
	private Long tenantId;

	/**
	 * 库存仓编号
	 */
	private Integer warehouseNo;

	/**
	 * 仓库名称
	 */
	private String warehouseName;

	/**
	 * sku编码
	 */
	private String sku;

	/**
	 * skuSaasId
	 */
	private Long skuSaasId;

	/**
	 * sku名称
	 */
	private String skuName;

	/**
	 * sku比例
	 */
	private BigDecimal skuRatio;

	/**
	 * sku规格
	 */
	private String skuSpecification;

	/** 商品子类类型：1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销**/
	private Integer skuSubType;

	/**
	 * 状态，0：无效，1：有效
	 */
	private Integer status;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	private String updater;

	/**
	 * 更新时间
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime updateTime;

	/**
	 * 是否删除标识，0：否，1：是
	 */
	private Integer deleteFlag;

	/**
	 * 绑定明细
	 */
	private List<WmsMaterialBindingDetailVO> detailVOList;

}